// Script to rename sports image files to a more consistent format
const fs = require('fs');
const path = require('path');

// Path to the sports images directory
const sportsDir = path.join(__dirname, 'public', 'images', 'sports');

// Function to create a consistent filename
const createNewFilename = oldName => {
  // Determine if it's a background or card image
  const isBackground = oldName.startsWith('Background');
  const isCard = oldName.startsWith('Card');

  if (!isBackground && !isCard) {
    console.log(`Unknown image type: ${oldName}`);
    return oldName;
  }

  // Extract the sport name
  let match;
  if (isBackground) {
    match = oldName.match(/^Background - (.+?)@/);
  } else {
    // isCard
    match = oldName.match(/^Card - (.+?)@/);
  }

  if (!match) {
    console.log(`Could not parse: ${oldName}`);
    return oldName;
  }

  const [, sportName] = match;

  // Format the type (background or card) and sport name with underscores
  const type = isBackground ? 'background' : 'card';
  const formattedSportName = sportName.toLowerCase().replace(/[\s&/]+/g, '_');

  // Determine the dimensions for the filename
  const dimensions = isBackground ? '700x400' : '360x280';

  // Create new filename
  return `${type}_${formattedSportName}_${dimensions}.png`;
};

// Main function to rename files
const renameFiles = () => {
  try {
    // Get all files in the sports directory
    const files = fs.readdirSync(sportsDir);

    // Create a map to store old->new name mappings for reference
    const nameMap = {};

    // Process each file
    files.forEach(file => {
      const oldPath = path.join(sportsDir, file);

      // Skip directories
      if (fs.statSync(oldPath).isDirectory()) return;

      // Skip if not a PNG
      if (!file.toLowerCase().endsWith('.png')) return;

      // Get new filename
      const newFilename = createNewFilename(file);
      const newPath = path.join(sportsDir, newFilename);

      // Rename the file
      fs.renameSync(oldPath, newPath);

      // Store the mapping
      nameMap[file] = newFilename;

      console.log(`Renamed: ${file} -> ${newFilename}`);
    });

    // Save the name mapping to a JSON file for reference
    fs.writeFileSync(
      path.join(__dirname, 'sports-name-mapping.json'),
      JSON.stringify(nameMap, null, 2)
    );

    console.log('All files renamed successfully!');
    console.log('Name mapping saved to sports-name-mapping.json');
  } catch (error) {
    console.error('Error renaming files:', error);
  }
};

// Run the script
renameFiles();
