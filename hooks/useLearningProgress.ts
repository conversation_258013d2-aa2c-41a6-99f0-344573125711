import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/useAuth';
import {
  Badge,
  Certificate,
  LearningInvestment,
  learningProgressService,
  LearningStats,
} from '@/services/learning-progress.service';

/**
 * Type for user ID parameter
 * - Use number for actual user IDs (preferred for all user identifiers)
 * - Use null to automatically use the current authenticated user's ID
 */
export type UserIdParam = number | null;

/**
 * Hook for managing a user's learning progress data
 * @param userId - The ID of the user to get learning progress for:
 *                 - Pass a number for specific user IDs (preferred)
 *                 - Pass null or omit to use the current authenticated user's ID
 */
export function useLearningProgress(userId: UserIdParam = null) {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Resolve the actual user ID to use
  // If userId is null, use the current user's ID from auth
  const resolvedUserId = userId !== null ? userId : user?.id;

  // For query keys, we need a stable identifier
  // Use 'current' for the current user (when resolvedUserId is undefined)
  // This happens during initial load before auth is complete
  const userIdKey = resolvedUserId?.toString() || 'current';

  // Learning Stats
  const {
    data: stats,
    isLoading: isLoadingStats,
    error: statsError,
  } = useQuery({
    queryKey: ['learning', 'stats', userIdKey],
    queryFn: async () => {
      try {
        const result = await learningProgressService.getLearningStats(resolvedUserId);
        return (
          result || {
            coursesCompleted: 0,
            coursesInProgress: 0,
            certificatesEarned: 0,
            badgesEarned: 0,
            totalModulesCompleted: 0,
          }
        );
      } catch (error) {
        console.error('Error fetching learning stats:', error);
        return {
          coursesCompleted: 0,
          coursesInProgress: 0,
          certificatesEarned: 0,
          badgesEarned: 0,
          totalModulesCompleted: 0,
        };
      }
    },
    retry: 1,
    // Only run the query if we have a resolved user ID or we're fetching the current user
    enabled: !!resolvedUserId || userId === null,
  });

  // Certificates
  const {
    data: certificates = [],
    isLoading: isLoadingCertificates,
    error: certificatesError,
  } = useQuery({
    queryKey: ['learning', 'certificates', userIdKey],
    queryFn: async () => {
      try {
        const result = await learningProgressService.getCertificates(resolvedUserId);
        return result || [];
      } catch (error) {
        console.error('Error fetching certificates:', error);
        return []; // Return empty array on error
      }
    },
    retry: 1, // Limit retries since this endpoint might be returning 500
    enabled: !!resolvedUserId || userId === null,
  });

  // Badges
  const {
    data: badges = [],
    isLoading: isLoadingBadges,
    error: badgesError,
  } = useQuery({
    queryKey: ['learning', 'badges', userIdKey],
    queryFn: async () => {
      try {
        const result = await learningProgressService.getBadges(resolvedUserId);
        return result || [];
      } catch (error) {
        console.error('Error fetching badges:', error);
        return []; // Return empty array on error
      }
    },
    retry: 1,
    enabled: !!resolvedUserId || userId === null,
  });

  // Learning Investment
  const {
    data: investment,
    isLoading: isLoadingInvestment,
    error: investmentError,
  } = useQuery({
    queryKey: ['learning', 'investment', userIdKey],
    queryFn: async () => {
      try {
        const result = await learningProgressService.getLearningInvestment(resolvedUserId);
        return (
          result || {
            hoursSpent: 0,
            modulesCompleted: 0,
            lastActive: null,
            topics: [],
            primaryTopic: undefined,
            primaryTopicPercentage: undefined,
          }
        );
      } catch (error) {
        console.error('Error fetching learning investment:', error);
        // Return default investment data on error
        return {
          hoursSpent: 0,
          modulesCompleted: 0,
          lastActive: null,
          topics: [],
          primaryTopic: undefined,
          primaryTopicPercentage: undefined,
        };
      }
    },
    retry: 1,
    enabled: !!resolvedUserId || userId === null,
  });

  // Learning Progress Summary
  const {
    data: summary,
    isLoading: isLoadingSummary,
    error: summaryError,
  } = useQuery({
    queryKey: ['learning', 'summary', userIdKey],
    queryFn: async () => {
      try {
        return await learningProgressService.getLearningProgressSummary(resolvedUserId);
      } catch (error) {
        console.error('Error fetching learning progress summary:', error);
        return {
          certificates: [],
          stats: {
            coursesCompleted: 0,
            coursesInProgress: 0,
            certificatesEarned: 0,
            badgesEarned: 0,
            totalModulesCompleted: 0,
          },
          investment: {
            topics: [],
            primaryTopic: undefined,
            primaryTopicPercentage: undefined,
            hoursSpent: 0,
            modulesCompleted: 0,
            lastActive: null,
          },
          badges: [],
        };
      }
    },
    retry: 1,
    enabled: false, // Disabled by default, enable only when needed
  });

  /**
   * Refresh all learning progress data
   */
  const refreshAllData = () => {
    queryClient.invalidateQueries({ queryKey: ['learning', 'stats', userIdKey] });
    queryClient.invalidateQueries({ queryKey: ['learning', 'certificates', userIdKey] });
    queryClient.invalidateQueries({ queryKey: ['learning', 'badges', userIdKey] });
    queryClient.invalidateQueries({ queryKey: ['learning', 'investment', userIdKey] });
    queryClient.invalidateQueries({ queryKey: ['learning', 'summary', userIdKey] });
  };

  /**
   * Refresh specific learning progress data
   */
  const refreshData = (
    dataType: 'stats' | 'certificates' | 'badges' | 'investment' | 'summary'
  ) => {
    queryClient.invalidateQueries({ queryKey: ['learning', dataType, userIdKey] });
  };

  // Calculate derived stats
  const derivedStats = {
    ...stats,
    coursesCompleted: stats?.coursesCompleted || 0,
    certificatesEarned:
      stats?.certificatesEarned || (Array.isArray(certificates) ? certificates.length : 0),
    badgesEarned:
      stats?.badgesEarned || (Array.isArray(badges) ? badges.filter(b => b.isAchieved).length : 0),
    totalModulesCompleted:
      stats?.totalModulesCompleted || stats?.modulesCompleted || investment?.modulesCompleted || 0,
  };

  return {
    // Learning Stats
    stats: derivedStats,
    isLoadingStats,
    statsError,

    // Certificates
    certificates,
    isLoadingCertificates,
    certificatesError,

    // Badges
    badges,
    isLoadingBadges,
    badgesError,

    // Learning Investment
    investment,
    isLoadingInvestment,
    investmentError,

    // Learning Progress Summary
    summary,
    isLoadingSummary,
    summaryError,

    // Utility functions
    refreshAllData,
    refreshData,

    // Expose the resolved user ID for consumers
    userId: resolvedUserId,
  };
}
