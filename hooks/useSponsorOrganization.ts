import { useQuery, useQueryClient } from '@tanstack/react-query';
import type { Organization } from '@/services/sponsorOrganization.service';
import { sponsorOrganizationService } from '@/services/sponsorOrganization.service';

interface UseSponsorOrganizationProps {
  onSuccess?: (data: Organization, context: 'getOrganization' | 'getSpecificOrganization') => void;
  onError?: (error: unknown, context: 'getOrganization' | 'getSpecificOrganization') => void;
}

export function useSponsorOrganization({ onSuccess, onError }: UseSponsorOrganizationProps = {}) {
  const queryClient = useQueryClient();

  /**
   * Get the active organization for the logged-in sponsor
   */
  const getOrganization = (options: { enabled?: boolean; staleTime?: number } = {}) => {
    return useQuery({
      queryKey: ['sponsorOrganization'],
      queryFn: async () => {
        try {
          console.log('Fetching organization data...');
          const response = await sponsorOrganizationService.getOrganization();
          console.log('Organization data fetched:', response.data);
          onSuccess?.(response.data, 'getOrganization');
          return response.data;
        } catch (error) {
          console.error('Error fetching organization data:', error);
          onError?.(error, 'getOrganization');
          throw error;
        }
      },
      enabled: options.enabled !== false,
      staleTime: options.staleTime || 5 * 60 * 1000, // 5 minutes by default
      retry: 2,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
    });
  };

  /**
   * Get a specific organization by ID
   */
  const getSpecificOrganization = (
    id: number,
    options: { enabled?: boolean; staleTime?: number } = {}
  ) => {
    return useQuery({
      queryKey: ['sponsorOrganization', id],
      queryFn: async () => {
        try {
          console.log(`Fetching organization data for ID: ${id}...`);
          const response = await sponsorOrganizationService.getSpecificOrganization(id);
          console.log(`Organization data fetched for ID ${id}:`, response.data);
          onSuccess?.(response.data, 'getSpecificOrganization');
          return response.data;
        } catch (error) {
          console.error(`Error fetching organization data for ID ${id}:`, error);
          onError?.(error, 'getSpecificOrganization');
          throw error;
        }
      },
      enabled: !!id && options.enabled !== false,
      staleTime: options.staleTime || 5 * 60 * 1000, // 5 minutes by default
      retry: 2,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
    });
  };

  return {
    // Query hooks
    getOrganization,
    getSpecificOrganization,

    // Helper function to refresh organization data
    refreshOrganization: () => {
      queryClient.invalidateQueries({ queryKey: ['sponsorOrganization'] });
    },
  };
}
