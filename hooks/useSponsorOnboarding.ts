/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useState } from 'react'; // Import hooks
import { useMutation, useQuery } from '@tanstack/react-query';
import type { AxiosError, AxiosResponse } from 'axios';
import debounce from 'lodash/debounce'; // Import debounce

import type {
  ApiErrorResponse,
  OrganizationSearchPayload,
  SponsorAccountInfoPayload,
  SponsorCompleteResponse,
  SponsorOnboardingStepResponse,
  SponsorOrganizationInfoPayload,
} from '@/services/sponsorOnboarding.service';
import { sponsorOnboardingService } from '@/services/sponsorOnboarding.service';
import { useSponsorOnboardingStore } from '@/stores/sponsorOnboardingStore';
import { useSystemInviteStore } from '@/stores/systemInviteStore';
import { useWizardStore } from '@/stores/wizardStore'; // Assuming wizard is used
import type { OrganizationSearchResultDTO } from '@/types/organization';

// Define interfaces for hook props if needed, similar to usePositiveAthleteOnboarding
interface UseSponsorOnboardingProps<
  TData = SponsorOnboardingStepResponse | SponsorCompleteResponse | OrganizationSearchResultDTO[],
> {
  onSuccess?: (
    response: TData,
    // Add context for which mutation succeeded if needed
    context?: 'accountInfo' | 'orgInfo' | 'complete'
  ) => void;
  onError?: (
    error: unknown,
    // Add context for which mutation failed if needed
    context?: 'accountInfo' | 'orgInfo' | 'complete' | 'search' | 'getState'
  ) => void;
}

export function useSponsorOnboarding({ onSuccess, onError }: UseSponsorOnboardingProps = {}) {
  // --- Internal State for Search Debouncing ---
  const [searchInputValue, setSearchInputValue] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');

  // --- Debounce Handler ---
  const debouncedSetQuery = useCallback(
    debounce((value: string) => {
      setDebouncedSearchQuery(value);
    }, 300), // 300ms debounce delay
    []
  );

  // Update debounced query when input changes
  useEffect(() => {
    debouncedSetQuery(searchInputValue);
    // Cleanup function
    return () => {
      debouncedSetQuery.cancel();
    };
  }, [searchInputValue, debouncedSetQuery]);

  // --- Mutations and Queries ---

  const getOnboardingState = (options: { enabled?: boolean } = {}) =>
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useQuery({
      queryKey: [
        'sponsorOnboardingState',
        useSystemInviteStore.getState().inviteData?.invite?.data?.token,
      ],
      queryFn: async () => {
        const response = await sponsorOnboardingService.getOnboardingState();
        return response.data;
      },
      enabled: !!useSystemInviteStore.getState().inviteData?.invite?.data?.token && options.enabled,
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
      // Consider adding specific onError handling if needed
      // onError: (error) => onError?.(error, 'getState'),
    });

  const submitAccountInfo = useMutation<
    AxiosResponse<SponsorOnboardingStepResponse>,
    AxiosError<ApiErrorResponse>,
    SponsorAccountInfoPayload
  >({
    mutationFn: (data: SponsorAccountInfoPayload) =>
      sponsorOnboardingService.submitAccountInfo(data),
    onSuccess: response => {
      (onSuccess as (res: SponsorOnboardingStepResponse, ctx: 'accountInfo') => void)?.(
        response.data,
        'accountInfo'
      );
    },
    onError: error => onError?.(error, 'accountInfo'),
  });

  const submitOrganizationInfo = useMutation<
    AxiosResponse<SponsorOnboardingStepResponse>,
    AxiosError<ApiErrorResponse>,
    SponsorOrganizationInfoPayload | FormData
  >({
    mutationFn: (data: SponsorOrganizationInfoPayload | FormData) =>
      sponsorOnboardingService.submitOrganizationInfo(data),
    onSuccess: response => {
      (onSuccess as (res: SponsorOnboardingStepResponse, ctx: 'orgInfo') => void)?.(
        response.data,
        'orgInfo'
      );
    },
    onError: error => onError?.(error, 'orgInfo'),
  });

  // Internal mutation for search, not directly exposed
  const searchOrganizationsMutation = useMutation<
    AxiosResponse<OrganizationSearchResultDTO[]>,
    AxiosError<ApiErrorResponse>,
    OrganizationSearchPayload
  >({
    mutationFn: (data: OrganizationSearchPayload) =>
      sponsorOnboardingService.searchOrganizations(data),
    // We don't call the main onSuccess/onError here, just handle results/errors locally
    // onError: (error) => onError?.(error, 'search'),
  });

  // --- Trigger Search Effect ---
  useEffect(() => {
    if (debouncedSearchQuery.trim().length > 1) {
      // Trigger the internal mutation
      searchOrganizationsMutation.mutate({ query: debouncedSearchQuery });
    }
    // Reset if query becomes too short? Optional.
    // else {
    //   searchOrganizationsMutation.reset();
    // }
  }, [debouncedSearchQuery]); // Removed mutation from dependency array as it's stable

  // --- Completion Logic ---
  const clearInviteData = useSystemInviteStore(state => state.clearInviteData);
  const resetWizard = useWizardStore(state => state.resetWizard);
  const resetSponsorStore = useSponsorOnboardingStore(state => state.reset);

  const completeOnboarding = useMutation<
    AxiosResponse<SponsorCompleteResponse>,
    AxiosError<ApiErrorResponse>
  >({
    mutationFn: () => sponsorOnboardingService.completeOnboarding(),
    onSuccess: response => {
      clearInviteData();
      resetWizard();
      resetSponsorStore();
      (onSuccess as (res: SponsorCompleteResponse, ctx: 'complete') => void)?.(
        response.data,
        'complete'
      );
    },
    onError: error => onError?.(error, 'complete'),
  });

  return {
    getOnboardingState, // Expose the query function
    submitAccountInfo,
    submitOrganizationInfo,
    completeOnboarding,

    // Search Related Exports
    setSearchQueryInput: setSearchInputValue, // Function to update search input from component
    searchResults: searchOrganizationsMutation.data, // The actual search results data
    isSearching: searchOrganizationsMutation.isPending, // Loading state for search
    searchError: searchOrganizationsMutation.error, // Error state for search
  };
}
