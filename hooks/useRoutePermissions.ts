import { usePathname } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { getAllowedRoutes, getDefaultRoute, hasRoutePermission } from '@/utils/route-rules';

/**
 * Custom hook for checking route permissions in components
 *
 * This hook provides utility functions to check if the current user
 * has permission to access specific routes based on their profile type.
 */
export const useRoutePermissions = () => {
  const { profileType, isLoggedIn } = useAuth();
  const pathname = usePathname();

  /**
   * Check if the current user has permission to access a specific route
   * @param route The route to check permission for
   * @returns Boolean indicating whether the user has permission
   */
  const canAccess = (route: string): boolean => {
    if (!isLoggedIn) return false;
    return hasRoutePermission(profileType, route);
  };

  /**
   * Check if the current user has permission to access the current route
   * @returns Boolean indicating whether the user has permission for the current route
   */
  const hasPermissionForCurrentRoute = (): boolean => {
    // If user is not logged in or pathname is null, they cannot access the route
    if (!isLoggedIn || !pathname) return false;
    return hasRoutePermission(profileType, pathname);
  };

  /**
   * Get all routes the current user has permission to access
   * @returns Array of route paths the user can access
   */
  const getAccessibleRoutes = (): string[] => {
    if (!isLoggedIn) return [];
    return getAllowedRoutes(profileType);
  };

  /**
   * Get the default/fallback route for the current user's profile type
   * This is used for redirects when a user tries to access an unauthorized route
   * @returns The default route for the user's profile type
   */
  const getDefaultRouteForCurrentUser = (): string => {
    return getDefaultRoute(profileType);
  };

  return {
    canAccess,
    hasPermissionForCurrentRoute,
    getAccessibleRoutes,
    getDefaultRouteForCurrentUser,
  };
};
