import { useCallback, useMemo } from 'react';
import { useMutation } from '@tanstack/react-query';
import type { AxiosError } from 'axios';
import type {
  ApiErrorResponse,
  ParentAccountInfoPayload,
  ParentInviteData,
  ParentOnboardingStepResponse,
} from '@/services/parentOnboarding.service';
import { parentOnboardingService } from '@/services/parentOnboarding.service';
import { useParentOnboardingStore } from '@/stores/parentOnboardingStore';
import { useSystemInviteStore } from '@/stores/systemInviteStore';
import { useToastNotify } from '@/stores/toastNotify.store';
import { useWizardStore } from '@/stores/wizardStore';

interface UseParentOnboardingProps {
  onSuccess?: (response: ParentOnboardingStepResponse) => void;
  onError?: (error: unknown) => void;
}

export function useParentOnboarding({ onSuccess, onError }: UseParentOnboardingProps = {}) {
  const { toastNotify } = useToastNotify();
  const clearInviteData = useSystemInviteStore(state => state.clearInviteData);
  const inviteData = useSystemInviteStore(state => state.inviteData);
  const resetWizard = useWizardStore(state => state.resetWizard);
  const resetParentStore = useParentOnboardingStore(state => state.resetAccountInfo);

  // Get athlete name from the invite data
  const athleteName = useMemo(() => {
    const data = inviteData?.invite?.data as ParentInviteData | undefined;
    return data?.athlete_name || 'your child';
  }, [inviteData]);

  // Initialize parent onboarding with intro screen
  const startOnboarding = useMutation<ParentOnboardingStepResponse, AxiosError<ApiErrorResponse>>({
    mutationFn: async () => {
      const response = await parentOnboardingService.intro();
      return response.data;
    },
    onSuccess: data => {
      onSuccess?.(data);
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      if (error.response?.data?.error) {
        toastNotify(error.response.data.error, 'error');
      } else {
        toastNotify('An unexpected error occurred while starting onboarding.', 'error');
      }
      onError?.(error);
    },
  });

  // Submit account info mutation
  const submitAccountInfo = useMutation<
    ParentOnboardingStepResponse,
    AxiosError<ApiErrorResponse>,
    ParentAccountInfoPayload
  >({
    mutationFn: async (data: ParentAccountInfoPayload) => {
      const response = await parentOnboardingService.submitAccountInfo(data);
      return response.data;
    },
    onSuccess: data => {
      onSuccess?.(data);
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      if (error.response?.data?.error) {
        toastNotify(error.response.data.error, 'error');
      } else {
        toastNotify('An unexpected error occurred while submitting account information.', 'error');
      }
      onError?.(error);
    },
  });

  return {
    athleteName,
    startOnboarding,
    submitAccountInfo,
    clearStoreData: useCallback(() => {
      clearInviteData();
      resetWizard();
      resetParentStore();
    }, [clearInviteData, resetWizard, resetParentStore]),
  };
}
