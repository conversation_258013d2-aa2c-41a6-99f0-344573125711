import { useMutation } from '@tanstack/react-query';
import { requestPasswordReset, resetPassword } from '@/services/auth.service';

interface RequestPasswordResetOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

interface ResetPasswordOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export function useForgotPassword() {
  // Mutation for requesting password reset email
  const requestPasswordResetMutation = useMutation({
    mutationFn: (email: string) => requestPasswordReset(email),
  });

  // Mutation for resetting password with token
  const resetPasswordMutation = useMutation({
    mutationFn: (data: {
      token: string;
      email: string;
      password: string;
      password_confirmation: string;
    }) => resetPassword(data),
  });

  const handleRequestPasswordReset = (email: string, options?: RequestPasswordResetOptions) => {
    return requestPasswordResetMutation.mutate(email, {
      onSuccess: options?.onSuccess,
      onError: options?.onError,
    });
  };

  const handleResetPassword = (
    data: {
      token: string;
      email: string;
      password: string;
      password_confirmation: string;
    },
    options?: ResetPasswordOptions
  ) => {
    return resetPasswordMutation.mutate(data, {
      onSuccess: options?.onSuccess,
      onError: options?.onError,
    });
  };

  return {
    // Request password reset
    requestPasswordReset: handleRequestPasswordReset,
    isRequestingPasswordReset: requestPasswordResetMutation.isPending,
    requestPasswordResetError: requestPasswordResetMutation.error,
    requestPasswordResetSuccess: requestPasswordResetMutation.isSuccess,

    // Reset password
    resetPassword: handleResetPassword,
    isResettingPassword: resetPasswordMutation.isPending,
    resetPasswordError: resetPasswordMutation.error,
    resetPasswordSuccess: resetPasswordMutation.isSuccess,

    // Reset mutations (for cleanup)
    resetRequestPasswordReset: () => requestPasswordResetMutation.reset(),
    resetResetPassword: () => resetPasswordMutation.reset(),
  };
}
