import { useAuth } from '@/hooks/useAuth';
import { canUserConnect } from '@/utils/networking';

/**
 * Hook to determine if the current user can connect with another user
 *
 * @returns A function that takes a target user's profile type and returns whether the current user can connect with them
 */
export const useConnectionPermissions = () => {
  const { profileType } = useAuth();

  /**
   * Check if the current user can connect with a user of the given profile type
   *
   * @param targetUserProfileType The profile type of the target user
   * @returns boolean indicating if the current user can connect with the target user
   */
  const checkCanConnect = (targetUserProfileType: string | null): boolean => {
    return canUserConnect(profileType, targetUserProfileType);
  };

  return checkCanConnect;
};
