import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  BlockUserRequest,
  Connection,
  CreateConnectionRequest,
  networkingService,
} from '@/services/networking.service';

/**
 * Hook for managing networking data and interactions
 */
export function useNetworking() {
  const queryClient = useQueryClient();

  // Get all connections
  const {
    data: connections = [],
    isLoading: isLoadingConnections,
    error: connectionsError,
    refetch: refetchConnections,
  } = useQuery({
    queryKey: ['networking', 'connections'],
    queryFn: async () => {
      try {
        return await networkingService.getConnections();
      } catch (error) {
        console.error('Error fetching connections:', error);
        return [];
      }
    },
  });

  // Get accepted connections
  const {
    data: acceptedConnections = [],
    isLoading: isLoadingAcceptedConnections,
    error: acceptedConnectionsError,
    refetch: refetchAcceptedConnections,
  } = useQuery({
    queryKey: ['networking', 'connections', 'accepted'],
    queryFn: async () => {
      try {
        return await networkingService.getAcceptedConnections();
      } catch (error) {
        console.error('Error fetching accepted connections:', error);
        return [];
      }
    },
  });

  // Get pending connections
  const {
    data: pendingConnections = [],
    isLoading: isLoadingPendingConnections,
    error: pendingConnectionsError,
    refetch: refetchPendingConnections,
  } = useQuery({
    queryKey: ['networking', 'connections', 'pending'],
    queryFn: async () => {
      try {
        return await networkingService.getPendingConnections();
      } catch (error) {
        console.error('Error fetching pending connections:', error);
        return [];
      }
    },
  });

  // Function to check if the authenticated user can connect with another user
  const checkCanConnect = async (targetUserId: number): Promise<boolean> => {
    try {
      return await networkingService.canConnect(targetUserId);
    } catch (error) {
      console.error('Error checking if users can connect:', error);
      return false;
    }
  };

  // Hook to check if the authenticated user can connect with another user
  const useCanConnect = (targetUserId: number) => {
    return useQuery({
      queryKey: ['networking', 'canConnect', targetUserId],
      queryFn: () => checkCanConnect(targetUserId),
      // Don't refetch on window focus since connection rules don't change frequently
      refetchOnWindowFocus: false,
      // Enable the query only if targetUserId is valid
      enabled: typeof targetUserId === 'number' && targetUserId > 0,
    });
  };

  // Create connection request mutation
  const createConnectionRequestMutation = useMutation({
    mutationFn: (request: CreateConnectionRequest) =>
      networkingService.createConnectionRequest(request),
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['networking', 'connections'] });
      queryClient.invalidateQueries({ queryKey: ['networking', 'connections', 'pending'] });
    },
  });

  // Accept connection request mutation
  const acceptConnectionRequestMutation = useMutation({
    mutationFn: async (connectionId: number) => {
      const response = await networkingService.acceptConnectionRequest(connectionId);
      // Return both the response and the connectionId for use in onSuccess
      return { response, connectionId };
    },
    onSuccess: data => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['networking', 'connections'] });
      queryClient.invalidateQueries({ queryKey: ['networking', 'connections', 'accepted'] });
      queryClient.invalidateQueries({ queryKey: ['networking', 'connections', 'pending'] });

      // Also invalidate messages queries to update conversation status
      queryClient.invalidateQueries({ queryKey: ['messages', 'conversations'] });

      // Invalidate all message conversations to ensure they're refreshed
      queryClient.invalidateQueries({ queryKey: ['messages'] });
    },
  });

  // Reject connection request mutation
  const rejectConnectionRequestMutation = useMutation({
    mutationFn: (connectionId: number) => networkingService.rejectConnectionRequest(connectionId),
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['networking', 'connections'] });
      queryClient.invalidateQueries({ queryKey: ['networking', 'connections', 'pending'] });
    },
  });

  // Block user mutation
  const blockUserMutation = useMutation({
    mutationFn: (request: BlockUserRequest) => networkingService.blockUser(request),
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['networking', 'connections'] });
      queryClient.invalidateQueries({ queryKey: ['networking', 'connections', 'accepted'] });
      queryClient.invalidateQueries({ queryKey: ['messages', 'conversations'] });
    },
  });

  // Unblock user mutation
  const unblockUserMutation = useMutation({
    mutationFn: (request: BlockUserRequest) => networkingService.unblockUser(request),
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['networking', 'connections'] });
      queryClient.invalidateQueries({ queryKey: ['networking', 'connections', 'accepted'] });
      queryClient.invalidateQueries({ queryKey: ['messages', 'conversations'] });
    },
  });

  /**
   * Create a connection request
   * @param recipientId - The ID of the user to connect with
   */
  const createConnectionRequest = (recipientId: number) => {
    createConnectionRequestMutation.mutate({ recipientId });
  };

  /**
   * Accept a connection request
   * @param connectionId - The ID of the connection to accept
   * @returns A promise that resolves when the connection is accepted
   */
  const acceptConnectionRequest = (connectionId: number) => {
    return acceptConnectionRequestMutation.mutateAsync(connectionId);
  };

  /**
   * Reject a connection request
   * @param connectionId - The ID of the connection to reject
   */
  const rejectConnectionRequest = (connectionId: number) => {
    rejectConnectionRequestMutation.mutate(connectionId);
  };

  /**
   * Block a user
   * @param userId - The ID of the user to block
   */
  const blockUser = (userId: number) => {
    blockUserMutation.mutate({ userId });
  };

  /**
   * Unblock a user
   * @param userId - The ID of the user to unblock
   */
  const unblockUser = (userId: number) => {
    unblockUserMutation.mutate({ userId });
  };

  /**
   * Refresh all networking data
   */
  const refreshAllData = () => {
    queryClient.invalidateQueries({ queryKey: ['networking'] });
  };

  /**
   * Refresh specific networking data
   */
  const refreshData = (dataType: 'all' | 'accepted' | 'pending') => {
    switch (dataType) {
      case 'all':
        queryClient.invalidateQueries({ queryKey: ['networking', 'connections'] });
        break;
      case 'accepted':
        queryClient.invalidateQueries({ queryKey: ['networking', 'connections', 'accepted'] });
        break;
      case 'pending':
        queryClient.invalidateQueries({ queryKey: ['networking', 'connections', 'pending'] });
        break;
    }
  };

  return {
    // Connections
    connections,
    isLoadingConnections,
    connectionsError,
    refetchConnections,

    // Accepted connections
    acceptedConnections,
    isLoadingAcceptedConnections,
    acceptedConnectionsError,
    refetchAcceptedConnections,

    // Pending connections
    pendingConnections,
    isLoadingPendingConnections,
    pendingConnectionsError,
    refetchPendingConnections,

    // Can Connect
    checkCanConnect,
    useCanConnect,

    // Mutations
    createConnectionRequest,
    isCreatingConnection: createConnectionRequestMutation.isPending,
    createConnectionError: createConnectionRequestMutation.error,

    acceptConnectionRequest,
    isAcceptingConnection: acceptConnectionRequestMutation.isPending,
    acceptConnectionError: acceptConnectionRequestMutation.error,

    rejectConnectionRequest,
    isRejectingConnection: rejectConnectionRequestMutation.isPending,
    rejectConnectionError: rejectConnectionRequestMutation.error,

    blockUser,
    isBlockingUser: blockUserMutation.isPending,
    blockUserError: blockUserMutation.error,

    unblockUser,
    isUnblockingUser: unblockUserMutation.isPending,
    unblockUserError: unblockUserMutation.error,

    // Utility functions
    refreshAllData,
    refreshData,
  };
}
