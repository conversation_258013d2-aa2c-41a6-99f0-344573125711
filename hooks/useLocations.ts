import { useCallback, useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import debounce from 'lodash/debounce';
import { locationService, type LocationOption } from '@/services/location.service';

export function useLocations() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLocationId, setSelectedLocationId] = useState<string | null>(null);

  // Create a debounced search function to avoid unnecessary API calls
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchQuery(value);
    }, 300),
    []
  );

  // Clean up the debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  // Query for searching locations
  const {
    data: locations = [],
    isLoading: isSearching,
    error: searchError,
  } = useQuery({
    queryKey: ['locations', searchQuery],
    queryFn: () => locationService.searchCities(searchQuery),
    enabled: searchQuery.length >= 2, // Only search when we have at least 2 characters
  });

  // Query for getting a specific location's details
  const {
    data: selectedLocation,
    isLoading: isLoadingDetails,
    error: detailsError,
  } = useQuery({
    queryKey: ['location', selectedLocationId],
    queryFn: () =>
      selectedLocationId ? locationService.getLocationById(selectedLocationId) : null,
    enabled: selectedLocationId !== null,
  });

  const selectLocation = useCallback((locationId: string) => {
    setSelectedLocationId(locationId);
  }, []);

  return {
    // Search functionality
    locations,
    isSearching,
    searchError,
    searchQuery,
    setSearchInput: debouncedSearch,

    // Details functionality
    selectedLocation,
    isLoadingDetails,
    detailsError,
    selectLocation,
  };
}
