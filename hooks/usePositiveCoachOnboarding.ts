import { useMutation } from '@tanstack/react-query';
import type { AxiosError, AxiosResponse } from 'axios';
import { useToastNotify } from '@/stores/toastNotify.store';
import type {
  AccountInfoPayload,
  DetailsPayload,
  InvolvementPayload,
  OnboardingStepResponse,
  SportsPayload,
  StoryPayload,
  TeamSuccessesPayload,
} from '../services/positiveCoachOnboarding.service';
import { positiveCoachOnboardingService } from '../services/positiveCoachOnboarding.service';

interface UsePositiveCoachOnboardingProps {
  onSuccess?: (response: OnboardingStepResponse | { redirect: string }) => void;
  onError?: (error: unknown) => void;
}

export function usePositiveCoachOnboarding({
  onSuccess,
  onError,
}: UsePositiveCoachOnboardingProps = {}) {
  const { toastNotify } = useToastNotify();

  const startOnboarding = useMutation({
    mutationFn: () => positiveCoachOnboardingService.startPositiveCoachOnboarding(),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitAccountInfo = useMutation({
    mutationFn: (data: AccountInfoPayload) =>
      positiveCoachOnboardingService.submitAccountInfo(data),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitDetails = useMutation({
    mutationFn: (data: DetailsPayload | FormData) =>
      positiveCoachOnboardingService.submitDetails(data),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitSports = useMutation({
    mutationFn: (data: SportsPayload) => positiveCoachOnboardingService.submitSports(data),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitCommunityInvolvement = useMutation({
    mutationFn: (data: InvolvementPayload) =>
      positiveCoachOnboardingService.submitCommunityInvolvement(data),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitTeamSuccesses = useMutation({
    mutationFn: (data: TeamSuccessesPayload) =>
      positiveCoachOnboardingService.submitTeamSuccesses(data),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitStory = useMutation({
    mutationFn: (data: StoryPayload) => positiveCoachOnboardingService.submitStory(data),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
  });

  const completeOnboarding = useMutation<
    AxiosResponse<{ redirect: string }>,
    AxiosError<any>,
    AccountInfoPayload
  >({
    mutationFn: (data: AccountInfoPayload) =>
      positiveCoachOnboardingService.completeOnboarding(data),
    onSuccess: (response: AxiosResponse<{ redirect: string }>) => onSuccess?.(response.data),
    onError: (error: AxiosError<any>) => {
      if (error.response?.data?.message) {
        toastNotify(error.response.data.message, 'error');
      } else {
        toastNotify('An unexpected error occurred while completing onboarding.', 'error');
      }
      onError?.(error);
    },
  });

  return {
    startOnboarding,
    submitAccountInfo,
    submitDetails,
    submitSports,
    submitCommunityInvolvement,
    submitTeamSuccesses,
    submitStory,
    completeOnboarding,
  };
}
