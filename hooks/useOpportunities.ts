import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  BookmarkStatus,
  opportunitiesService,
  Opportunity,
} from '@/services/opportunities.service';

/**
 * Hook for managing opportunities data and interactions
 */
export function useOpportunities() {
  const queryClient = useQueryClient();

  // Get a single opportunity by ID
  const useOpportunity = (id: string) => {
    return useQuery({
      queryKey: ['opportunities', 'detail', id],
      queryFn: async () => {
        try {
          return await opportunitiesService.getOpportunity(id);
        } catch (error) {
          console.error(`Error fetching opportunity ${id}:`, error);
          throw error;
        }
      },
      enabled: !!id,
    });
  };

  // Get bookmarked opportunity IDs
  const {
    data: bookmarkedIds = [],
    isLoading: isLoadingBookmarkedIds,
    error: bookmarkedIdsError,
    refetch: refetchBookmarkedIds,
  } = useQuery({
    queryKey: ['opportunities', 'bookmarked', 'ids'],
    queryFn: async () => {
      try {
        return await opportunitiesService.getBookmarkedIds();
      } catch (error) {
        console.error('Error fetching bookmarked opportunity IDs:', error);
        return [];
      }
    },
  });

  // Get bookmarked opportunities
  const {
    data: bookmarkedOpportunities = [],
    isLoading: isLoadingBookmarkedOpportunities,
    error: bookmarkedOpportunitiesError,
    refetch: refetchBookmarkedOpportunities,
  } = useQuery({
    queryKey: ['opportunities', 'bookmarked'],
    queryFn: async () => {
      try {
        return await opportunitiesService.getBookmarkedOpportunities();
      } catch (error) {
        console.error('Error fetching bookmarked opportunities:', error);
        return [];
      }
    },
  });

  // Toggle bookmark mutation
  const toggleBookmarkMutation = useMutation({
    mutationFn: (opportunityId: string) => opportunitiesService.toggleBookmark(opportunityId),
    onSuccess: data => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['opportunities', 'bookmarked'] });
      queryClient.invalidateQueries({ queryKey: ['opportunities', 'bookmarked', 'ids'] });

      // Update the specific opportunity in the cache if it exists
      queryClient.setQueryData(
        ['opportunities', 'detail', data.opportunityId],
        (oldData: Opportunity | undefined) => {
          if (!oldData) return undefined;
          return {
            ...oldData,
            isBookmarked: data.isBookmarked,
          };
        }
      );
    },
  });

  // Save opportunity mutation
  const saveOpportunityMutation = useMutation({
    mutationFn: ({ data, id }: { data: any; id?: string }) =>
      opportunitiesService.createOrUpdateOpportunity(data, id),
    onSuccess: savedOpportunity => {
      // Invalidate relevant queries
      if (savedOpportunity?.id) {
        // Update the cache for this specific opportunity
        queryClient.setQueryData(
          ['opportunities', 'detail', savedOpportunity.id],
          savedOpportunity
        );

        // Invalidate any list queries that might include this opportunity
        queryClient.invalidateQueries({ queryKey: ['opportunities', 'list'] });
      }
    },
  });

  /**
   * Check if an opportunity is bookmarked
   * @param opportunityId - The ID of the opportunity to check
   * @returns True if the opportunity is bookmarked, false otherwise
   */
  const isOpportunityBookmarked = (opportunityId: string): boolean => {
    return bookmarkedIds.includes(opportunityId);
  };

  /**
   * Toggle the bookmark status of an opportunity
   * @param opportunityId - The ID of the opportunity to toggle
   */
  const toggleBookmark = (opportunityId: string) => {
    toggleBookmarkMutation.mutate(opportunityId);
  };

  /**
   * Save an opportunity (create new or update existing)
   * @param data - The opportunity data to save
   * @param id - Optional ID for updating an existing opportunity
   * @returns A promise that resolves to the saved opportunity
   */
  const saveOpportunity = async (data: any, id?: string) => {
    try {
      return await saveOpportunityMutation.mutateAsync({ data, id });
    } catch (error) {
      console.error('Error saving opportunity:', error);
      throw error;
    }
  };

  /**
   * Refresh all opportunities data
   */
  const refreshAllData = () => {
    queryClient.invalidateQueries({ queryKey: ['opportunities'] });
  };

  /**
   * Refresh specific opportunities data
   */
  const refreshData = (dataType: 'detail' | 'bookmarked' | 'bookmarkedIds') => {
    switch (dataType) {
      case 'detail':
        queryClient.invalidateQueries({ queryKey: ['opportunities', 'detail'] });
        break;
      case 'bookmarked':
        queryClient.invalidateQueries({ queryKey: ['opportunities', 'bookmarked'] });
        break;
      case 'bookmarkedIds':
        queryClient.invalidateQueries({ queryKey: ['opportunities', 'bookmarked', 'ids'] });
        break;
    }
  };

  return {
    // Hooks
    useOpportunity,

    // Bookmarked IDs
    bookmarkedIds,
    isLoadingBookmarkedIds,
    bookmarkedIdsError,
    refetchBookmarkedIds,

    // Bookmarked Opportunities
    bookmarkedOpportunities,
    isLoadingBookmarkedOpportunities,
    bookmarkedOpportunitiesError,
    refetchBookmarkedOpportunities,

    // Mutations
    toggleBookmark,
    isToggling: toggleBookmarkMutation.isPending,
    toggleError: toggleBookmarkMutation.error,

    // Save opportunity
    saveOpportunity,
    isSaving: saveOpportunityMutation.isPending,
    saveError: saveOpportunityMutation.error,

    // Utility functions
    isOpportunityBookmarked,
    refreshAllData,
    refreshData,
  };
}
