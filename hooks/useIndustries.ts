import { useQuery } from '@tanstack/react-query';
import { industryService } from '@/services/industry.service';

/**
 * Hook for searching industries
 */
export function useIndustries(query: string) {
  const { data: industries = [], isLoading } = useQuery({
    queryKey: ['industries', 'search', query],
    queryFn: () => industryService.searchIndustries(query),
    enabled: query.length > 0,
  });

  return {
    industries,
    isLoading,
  };
}
