import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import type {
  CollegeAthleteDetails,
  UpdateCollegeAthleteDetailsRequest,
} from '@/services/college-athlete-profile.service';
import { collegeAthleteProfileService } from '@/services/college-athlete-profile.service';

interface UpdateDetailsOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

/**
 * Hook for managing College Athlete profile details
 */
export function useCollegeAthleteProfile() {
  const queryClient = useQueryClient();

  // Profile Details Management
  const { data: details, isLoading: isLoadingDetails } = useQuery({
    queryKey: ['college-athlete', 'profile', 'details'],
    queryFn: () => collegeAthleteProfileService.getProfileDetails(),
  });

  const updateDetailsMutation = useMutation({
    mutationFn: (data: UpdateCollegeAthleteDetailsRequest) =>
      collegeAthleteProfileService.updateProfileDetails(data),
    onSuccess: () => {
      // Invalidate all relevant profile-related queries
      queryClient.invalidateQueries({
        queryKey: ['college-athlete', 'profile', 'details'],
      });

      // Also invalidate positive athlete profile cache for backward compatibility
      queryClient.invalidateQueries({
        queryKey: ['positive-athlete', 'profile', 'details'],
      });

      // Invalidate any public profile queries that might exist
      queryClient.invalidateQueries({
        queryKey: ['public-profile'],
      });

      // Invalidate profile store data by refetching profile
      queryClient.invalidateQueries({
        queryKey: ['profile'],
      });
    },
  });

  const updateDetails = (
    data: UpdateCollegeAthleteDetailsRequest,
    options?: UpdateDetailsOptions
  ) => {
    return updateDetailsMutation.mutateAsync(data).then(
      result => {
        // Call success callback if provided
        options?.onSuccess?.();
        return result;
      },
      error => {
        // Call error callback if provided
        options?.onError?.(error);
        throw error;
      }
    );
  };

  return {
    details,
    isLoadingDetails,
    updateDetails,
    isUpdatingDetails: updateDetailsMutation.isPending,
    detailsUpdateError: updateDetailsMutation.error,
  };
}
