'use client';

import { useEffect, useState } from 'react';
import { EndorsementFromAPI, endorsementsListService } from '@/services/endorsementsList.service';

interface UseEndorsementsListReturn {
  endorsements: EndorsementFromAPI[];
  isLoading: boolean;
  error: Error | null;
}

export function useEndorsementsList(): UseEndorsementsListReturn {
  const [endorsements, setEndorsements] = useState<EndorsementFromAPI[]>([]);
  const [isLoading, setIsLoading] = useState(true); // Start with loading true
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchEndorsements = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const results = await endorsementsListService.getEndorsements();
        setEndorsements(results || []); // Ensure results is an array, default to [] if null/undefined
      } catch (err) {
        console.error('Error fetching endorsements:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch endorsements'));
        setEndorsements([]); // Also set to empty array on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchEndorsements();
  }, []); // Empty dependency array to run only on mount

  return {
    endorsements,
    isLoading,
    error,
  };
}
