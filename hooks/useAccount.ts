import { useMutation, useQuery } from '@tanstack/react-query';
import {
  accountService,
  DeleteAccountRequest,
  UpdateAddressRequest,
  UpdatePasswordRequest,
  UpdateProfileRequest,
} from '@/services/account.service';

interface MutationOptions<T> {
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
}

/**
 * Hook for managing user account operations
 */
export function useAccount() {
  // Profile Queries
  const profileQuery = useQuery({
    queryKey: ['profile'],
    queryFn: () => accountService.getProfile(),
    staleTime: 0, // Don't cache the data
  });

  // Address Queries
  const addressQuery = useQuery({
    queryKey: ['address'],
    queryFn: () => accountService.getAddress(),
    staleTime: 0, // Don't cache the data
  });

  // Profile Management
  const updateProfileMutation = useMutation({
    mutationFn: (data: UpdateProfileRequest) => accountService.updateProfile(data),
    onSuccess: () => {
      // Invalidate the profile query to refetch the latest data
      profileQuery.refetch();
    },
  });

  const updateProfile = (data: UpdateProfileRequest, options?: MutationOptions<any>) => {
    return updateProfileMutation.mutate(data, {
      onSuccess: options?.onSuccess,
      onError: options?.onError,
    });
  };

  // Address Management
  const updateAddressMutation = useMutation({
    mutationFn: (data: UpdateAddressRequest) => accountService.updateAddress(data),
    onSuccess: () => {
      // Invalidate the address query to refetch the latest data
      addressQuery.refetch();
    },
  });

  const updateAddress = (data: UpdateAddressRequest, options?: MutationOptions<any>) => {
    return updateAddressMutation.mutate(data, {
      onSuccess: options?.onSuccess,
      onError: options?.onError,
    });
  };

  // Password Management
  const updatePasswordMutation = useMutation({
    mutationFn: (data: UpdatePasswordRequest) => accountService.updatePassword(data),
  });

  const updatePassword = (data: UpdatePasswordRequest, options?: MutationOptions<void>) => {
    return updatePasswordMutation.mutate(data, {
      onSuccess: options?.onSuccess,
      onError: options?.onError,
    });
  };

  // Account Deletion
  const deleteAccountMutation = useMutation({
    mutationFn: (data: DeleteAccountRequest) => accountService.deleteAccount(data),
  });

  const deleteAccount = (data: DeleteAccountRequest, options?: MutationOptions<void>) => {
    return deleteAccountMutation.mutate(data, {
      onSuccess: options?.onSuccess,
      onError: options?.onError,
    });
  };

  return {
    // Profile Data
    profile: profileQuery.data,
    isLoadingProfile: profileQuery.isLoading,
    profileQueryError: profileQuery.error,
    refetchProfile: profileQuery.refetch,

    // Address Data
    address: addressQuery.data,
    isLoadingAddress: addressQuery.isLoading,
    addressQueryError: addressQuery.error,
    refetchAddress: addressQuery.refetch,

    // Profile
    updateProfile,
    isUpdatingProfile: updateProfileMutation.isPending,
    profileError: updateProfileMutation.error,

    // Address
    updateAddress,
    isUpdatingAddress: updateAddressMutation.isPending,
    addressError: updateAddressMutation.error,

    // Password
    updatePassword,
    isUpdatingPassword: updatePasswordMutation.isPending,
    passwordError: updatePasswordMutation.error,

    // Account Deletion
    deleteAccount,
    isDeletingAccount: deleteAccountMutation.isPending,
    deleteError: deleteAccountMutation.error,
  };
}
