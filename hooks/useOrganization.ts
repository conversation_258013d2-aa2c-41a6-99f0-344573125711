import { useEffect, useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import type { AxiosError, AxiosResponse } from 'axios';
import {
  organizationService,
  type OrganizationData,
  type UpdateOrganizationRequest,
} from '@/services/organization.service';

interface UseOrganizationOptions {
  onSuccess?: (data: OrganizationData) => void;
  onError?: (error: unknown) => void;
}

export function useOrganization(options: UseOrganizationOptions = {}) {
  // Query to fetch organization data
  const {
    data: organization,
    isLoading: isLoadingOrganization,
    error: organizationError,
    refetch,
  } = useQuery({
    queryKey: ['organization'],
    queryFn: async () => {
      try {
        const response = await organizationService.getOrganization();
        return response.data;
      } catch (error) {
        console.error('Error fetching organization:', error);
        if (options.onError) {
          options.onError(error);
        }
        // Re-throw to let React Query handle it
        throw error;
      }
    },
  });

  // Effect to handle organization errors
  useEffect(() => {
    if (organizationError && options.onError) {
      options.onError(organizationError);
    }
  }, [organizationError, options]);

  // Mutation to update organization data
  const {
    mutate: updateOrganization,
    isPending: isUpdatingOrganization,
    error: updateError,
  } = useMutation<
    AxiosResponse<OrganizationData>,
    AxiosError,
    UpdateOrganizationRequest | FormData
  >({
    mutationFn: data => organizationService.updateOrganization(data),
    onSuccess: response => {
      if (options.onSuccess) {
        options.onSuccess(response.data);
      }
      refetch();
    },
    onError: error => {
      console.error('Error updating organization:', error);
      if (options.onError) {
        options.onError(error);
      }
    },
  });

  return {
    organization,
    isLoadingOrganization,
    organizationError,
    updateOrganization,
    isUpdatingOrganization,
    updateError,
    refetch,
  };
}
