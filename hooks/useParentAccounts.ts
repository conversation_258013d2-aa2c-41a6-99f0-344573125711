import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  LinkParentRequest,
  ParentAccountData,
  positiveAthleteAccountService,
} from '@/services/positive-athlete-account.service';

interface MutationOptions<T> {
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
}

/**
 * Hook for managing parent accounts linked to a positive athlete
 */
export function useParentAccounts() {
  const queryClient = useQueryClient();

  // Get parent accounts
  const {
    data: allParents = [],
    isLoading: isLoadingParents,
    error: parentsError,
    refetch: refetchParents,
  } = useQuery({
    queryKey: ['athlete', 'account', 'parents'],
    queryFn: () => positiveAthleteAccountService.getParents(),
  });

  // Separate active parents and pending invites
  const activeParents = allParents.filter(parent => parent.status === 'active');
  const pendingInvites = allParents.filter(parent => parent.status === 'pending');
  const expiredInvites = allParents.filter(parent => parent.status === 'expired');

  // Link parent account
  const linkParentMutation = useMutation({
    mutationFn: (data: LinkParentRequest) => positiveAthleteAccountService.linkParent(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['athlete', 'account', 'parents'] });
    },
  });

  const linkParent = (data: LinkParentRequest, options?: MutationOptions<ParentAccountData>) => {
    return linkParentMutation.mutate(data, {
      onSuccess: options?.onSuccess,
      onError: options?.onError,
    });
  };

  // Unlink parent account
  const unlinkParentMutation = useMutation({
    mutationFn: (parentId: number) => positiveAthleteAccountService.unlinkParent(parentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['athlete', 'account', 'parents'] });
    },
  });

  const unlinkParent = (parentId: number, options?: MutationOptions<void>) => {
    return unlinkParentMutation.mutate(parentId, {
      onSuccess: options?.onSuccess,
      onError: options?.onError,
    });
  };

  // Unlink parent account by email
  const unlinkParentByEmailMutation = useMutation({
    mutationFn: (email: string) => positiveAthleteAccountService.unlinkParentByEmail(email),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['athlete', 'account', 'parents'] });
    },
  });

  const unlinkParentByEmail = (email: string, options?: MutationOptions<void>) => {
    return unlinkParentByEmailMutation.mutate(email, {
      onSuccess: options?.onSuccess,
      onError: options?.onError,
    });
  };

  // Resend invitation
  const resendInviteMutation = useMutation({
    mutationFn: (data: LinkParentRequest) => positiveAthleteAccountService.linkParent(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['athlete', 'account', 'parents'] });
    },
  });

  const resendInvite = (
    parent: ParentAccountData,
    options?: MutationOptions<ParentAccountData>
  ) => {
    const data: LinkParentRequest = {
      first_name: parent.first_name,
      last_name: parent.last_name,
      email: parent.email,
      phone: parent.phone,
    };

    return resendInviteMutation.mutate(data, {
      onSuccess: options?.onSuccess,
      onError: options?.onError,
    });
  };

  return {
    // All parents and invites
    allParents,

    // Separated by status
    activeParents,
    pendingInvites,
    expiredInvites,

    // Loading and error states
    isLoadingParents,
    parentsError,
    refetchParents,

    // Link parent
    linkParent,
    isLinkingParent: linkParentMutation.isPending,
    linkParentError: linkParentMutation.error,

    // Unlink parent
    unlinkParent,
    isUnlinkingParent: unlinkParentMutation.isPending,
    unlinkParentError: unlinkParentMutation.error,

    // Unlink parent by email
    unlinkParentByEmail,
    isUnlinkingParentByEmail: unlinkParentByEmailMutation.isPending,
    unlinkParentByEmailError: unlinkParentByEmailMutation.error,

    // Resend invitation
    resendInvite,
    isResendingInvite: resendInviteMutation.isPending,
    resendInviteError: resendInviteMutation.error,
  };
}
