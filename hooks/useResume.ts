import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { triggerResumePdfSync } from '@/components/profile/positive-athlete/ResumeBuilder';
import { ResumeService } from '@/services/resume';
import type { Resume, ResumeSectionContent, ResumeSectionType } from '@/types/resume';

const RESUME_KEYS = {
  all: ['resumes'] as const,
  lists: () => [...RESUME_KEYS.all, 'list'] as const,
  list: (filters: string) => [...RESUME_KEYS.lists(), { filters }] as const,
  details: () => [...RESUME_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...RESUME_KEYS.details(), id] as const,
};

export function useResume() {
  const queryClient = useQueryClient();

  const {
    data: resume,
    isLoading,
    error,
  } = useQuery<Resume | null, Error>({
    queryKey: RESUME_KEYS.lists(),
    queryFn: async () => {
      try {
        const response = await ResumeService.getResumes();
        return response.data;
      } catch (err) {
        if (err instanceof AxiosError && err.response?.status === 404) {
          return null;
        }
        throw err;
      }
    },
    staleTime: Infinity,
  });

  const { data: avatarData, isLoading: isLoadingAvatar } = useQuery({
    queryKey: [...RESUME_KEYS.details(), resume?.id, 'avatar'],
    queryFn: async () => {
      if (!resume?.id) return null;
      try {
        const response = await ResumeService.getResumeAvatar(resume.id);
        return response.data;
      } catch (err) {
        if (err instanceof AxiosError && err.response?.status === 404) {
          return null;
        }
        throw err;
      }
    },
    enabled: !!resume?.id,
  });

  const { mutate: createResume, isPending: isCreating } = useMutation<
    Resume,
    Error,
    Parameters<typeof ResumeService.createResume>[0]
  >({
    mutationFn: async data => {
      const response = await ResumeService.createResume(data);
      return response.data;
    },
    onSuccess: data => {
      queryClient.setQueryData<Resume>(RESUME_KEYS.lists(), data);
      // Add a delay and trigger PDF sync after resume creation
      setTimeout(() => {
        triggerResumePdfSync();
      }, 1000);
    },
  });

  const { mutate: updateResume } = useMutation<Resume, Error, { name: string }>({
    mutationFn: async ({ name }) => {
      if (!resume?.id) throw new Error('No resume found');
      const response = await ResumeService.updateResume(resume.id, name);
      return response.data;
    },
    onSuccess: data => {
      queryClient.setQueryData<Resume>(RESUME_KEYS.lists(), data);
      // Add a delay and trigger PDF sync after resume update
      setTimeout(() => {
        triggerResumePdfSync();
      }, 500);
    },
  });

  const { mutate: deleteResume } = useMutation<void, Error, void>({
    mutationFn: async () => {
      if (!resume?.id) throw new Error('No resume found');
      await ResumeService.deleteResume(resume.id);
    },
    onSuccess: () => {
      queryClient.resetQueries({ queryKey: RESUME_KEYS.lists() });
    },
  });

  const { mutate: updateSection, isPending: isUpdating } = useMutation<
    Resume,
    Error,
    {
      type: ResumeSectionType;
      content: ResumeSectionContent;
      is_enabled?: boolean;
      avatar?: File;
      onSuccess?: () => void;
      onError?: (error: Error) => void;
      onSettled?: () => void;
    }
  >({
    mutationFn: async ({ type, content, is_enabled, avatar }) => {
      if (!resume?.id) throw new Error('No resume found');

      const response = await ResumeService.updateResumeSection(resume.id, type, {
        content,
        is_enabled,
        avatar,
      });
      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.refetchQueries({ queryKey: RESUME_KEYS.lists() });
      // Trigger PDF sync when section is updated
      setTimeout(() => {
        triggerResumePdfSync();
      }, 500);
      variables.onSuccess?.();
    },
    onError: (error, variables) => {
      variables.onError?.(error);
    },
    onSettled: (_, __, variables) => {
      variables.onSettled?.();
    },
  });

  const { mutate: uploadResumePdf, isPending: isUploadingPdf } = useMutation<
    { message: string; file_url: string; resume_id: number },
    Error,
    { pdfFile: Blob; fileName?: string }
  >({
    mutationFn: async ({ pdfFile, fileName }) => {
      const response = await ResumeService.uploadResumePdf(pdfFile, fileName);
      if (response.data.resume_id && !resume?.id) {
        // If we got a resume ID back and didn't have one before, update our resume
        queryClient.refetchQueries({ queryKey: RESUME_KEYS.details() });
      }
      return response.data;
    },
    onSuccess: data => {
      // If we got a resume ID back from the server, we may need to refetch the resume
      if (data.resume_id) {
        // Refetch the resume details to ensure we have the latest data
        queryClient.refetchQueries({ queryKey: RESUME_KEYS.lists() });
      }
      // Refetch PDF URL to update UI
      refetchPdf();
    },
    onError: error => {
      console.error('Error uploading resume PDF:', error);
    },
  });

  // Query for resume PDF URL
  const {
    data: resumePdfData,
    isLoading: isLoadingPdf,
    refetch: refetchPdf,
  } = useQuery({
    queryKey: [...RESUME_KEYS.details(), 'pdf'],
    queryFn: async () => {
      try {
        const response = await ResumeService.getResumePdf();
        return response.data;
      } catch (err) {
        if (err instanceof AxiosError && err.response?.status === 404) {
          return null;
        }
        throw err;
      }
    },
    enabled: true,
  });

  // Mutation for deleting resume PDF
  const { mutate: deleteResumePdf, isPending: isDeletingPdf } = useMutation<unknown, Error, void>({
    mutationFn: async () => {
      await ResumeService.deleteResumePdf();
    },
    onSuccess: () => {
      // Refetch the PDF data to update the UI
      refetchPdf();
    },
    onError: error => {
      console.error('Error deleting resume PDF:', error);
    },
  });

  return {
    resume,
    isLoading,
    error,
    createResume,
    isCreating,
    updateResume,
    deleteResume,
    updateSection,
    isUpdating,
    avatarUrl: avatarData?.url,
    isLoadingAvatar,
    uploadResumePdf,
    isUploadingPdf,
    resumePdfUrl: resumePdfData?.pdf_url,
    isLoadingPdf,
    deleteResumePdf,
    isDeletingPdf,
  };
}
