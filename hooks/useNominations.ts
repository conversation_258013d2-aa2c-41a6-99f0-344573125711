import { useQuery, useQueryClient } from '@tanstack/react-query';
import { UserNomination, nominationService } from '@/services/nomination.service';

/**
 * Hook for managing a user's nominations data
 * @param userId The ID of the user to get nominations for
 */
export function useNominations(userId?: number) {
  const queryClient = useQueryClient();

  // User Nominations
  const {
    data: nominations = [],
    isLoading: isLoadingNominations,
    error: nominationsError,
  } = useQuery({
    queryKey: ['nominations', userId],
    queryFn: async () => {
      if (!userId) return [];

      try {
        const result = await nominationService.getUserNominations(userId);
        return result || [];
      } catch (error) {
        console.error('Error fetching nominations:', error);
        return []; // Return empty array on error
      }
    },
    enabled: !!userId, // Only run the query if userId is provided
    retry: 1,
  });

  /**
   * Refresh nominations data
   */
  const refreshNominations = () => {
    if (userId) {
      queryClient.invalidateQueries({ queryKey: ['nominations', userId] });
    }
  };

  // Calculate derived stats
  const nominationStats = {
    totalNominations: nominations.length,
    latestNomination: nominations.length > 0
      ? nominations.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0]
      : undefined,
    hasSportNominations: nominations.some(nom => !!nom.sport),
  };

  return {
    // Nominations
    nominations,
    isLoadingNominations,
    nominationsError,

    // Stats
    nominationStats,

    // Utility functions
    refreshNominations,
  };
}
