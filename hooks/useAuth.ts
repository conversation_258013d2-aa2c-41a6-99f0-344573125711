import { useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { login as loginService, logout as logoutService } from '@/services/auth.service';
import { ProfileType, useAuthStore } from '@/stores/auth.store';
import { useProfileStore } from '@/stores/profile.store';
import { LoginCredentials } from '@/types/auth';
import { getPostLoginRedirect } from '@/utils/navigation';

export const useAuth = () => {
  const router = useRouter();
  const { user, isAuthenticated, profileType, login: setAuth, logout: clearAuth } = useAuthStore();
  const { setProfile, clearProfile } = useProfileStore();

  const isLoggedIn = useMemo(() => {
    return !!user;
  }, [user]);

  const handleLogin = useCallback(
    async (credentials: LoginCredentials) => {
      try {
        console.log('Attempting login...');
        const response = await loginService(credentials);

        if (!response.success || !response.data?.user) {
          throw new Error(response.message || 'Login failed');
        }

        // Store auth data
        setAuth(response.data);

        // Store profile data
        setProfile(response.data.user);

        // Get the redirect path based on profile type
        const redirectPath = getPostLoginRedirect(response.data.user.profile_type as ProfileType);
        console.log('Redirecting to:', redirectPath);

        // Use Next.js router for navigation
        router.push(redirectPath);

        return response;
      } catch (error) {
        console.error('Login error:', error);
        // Re-throw the error so the login page can handle it
        throw error;
      }
    },
    [setAuth, setProfile, router]
  );

  const handleLogout = useCallback(async () => {
    try {
      await logoutService();
      clearAuth();
      clearProfile();
      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear auth and redirect even if the API call fails
      clearAuth();
      clearProfile();
      router.push('/login');
    }
  }, [clearAuth, clearProfile, router]);

  return {
    user,
    isAuthenticated,
    profileType,
    isLoggedIn,
    login: handleLogin,
    logout: handleLogout,
  };
};
