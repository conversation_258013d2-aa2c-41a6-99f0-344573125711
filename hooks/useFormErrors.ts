import { useCallback, useState } from 'react';

interface ValidationErrors {
  message: string;
  errors: Record<string, string[]>;
}

export function useFormErrors() {
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  const setErrors = useCallback((validationErrors: ValidationErrors) => {
    const newFieldErrors = Object.entries(validationErrors.errors).reduce(
      (acc, [field, messages]) => ({
        ...acc,
        [field]: messages[0], // Take the first message for each field
      }),
      {}
    );
    setFieldErrors(newFieldErrors);
  }, []);

  const clearError = useCallback((field: string) => {
    setFieldErrors(prev => {
      const { [field]: _, ...rest } = prev;
      return rest;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setFieldErrors({});
  }, []);

  return {
    fieldErrors,
    setErrors,
    clearError,
    clearAllErrors,
  };
}
