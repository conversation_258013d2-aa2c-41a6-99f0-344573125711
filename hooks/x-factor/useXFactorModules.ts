import { useQuery, useQueryClient } from '@tanstack/react-query';
import type { LaravelPagination } from '@/services/types';
import { xFactorCourseService } from '@/services/x-factor-course.service';
import type {
  CourseEnhancedModule,
  TopicModules,
  XFactorModule,
} from '@/services/x-factor-module.service';
import { xFactorModuleService } from '@/services/x-factor-module.service';

/**
 * Hook for managing X-Factor module data and interactions
 */
export function useXFactorModules(searchQuery?: string) {
  // Fetch all topic modules data
  const {
    data: topicModules,
    isLoading: isLoadingTopics,
    error: topicsError,
  } = useQuery({
    queryKey: ['x-factor', 'modules', 'topics'],
    queryFn: () => xFactorModuleService.getAllModulesByTopics(),
    enabled: !searchQuery, // Only fetch topic data when not searching
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: 2,
  });

  // Fetch search results when query is present
  const {
    data: searchData,
    isLoading: isLoadingSearch,
    error: searchError,
  } = useQuery({
    queryKey: ['x-factor', 'modules', 'search', searchQuery],
    queryFn: () => xFactorModuleService.searchModules(searchQuery ?? ''),
    enabled: !!searchQuery, // Only search when query is present
    retry: 2,
  });

  // Only show loading state when the enabled queries are loading
  const isLoading = (isLoadingTopics && !searchQuery) || (isLoadingSearch && !!searchQuery);

  return {
    // Topic data
    topicModules: topicModules ?? [],

    // Search data
    searchResults: searchData?.data ?? [],
    totalResults: searchData?.total ?? 0,

    // Loading states
    isLoading,

    // Errors - only return errors for enabled queries
    error: searchQuery ? searchError : topicsError,
  };
}

/**
 * Hook for fetching a single module's details, optionally in the context of a course
 */
export function useXFactorModule(moduleId: number, courseId?: number) {
  const queryClient = useQueryClient();

  // Get course data if we're in course context
  const {
    data: courseData,
    isLoading: isLoadingCourse,
    error: courseError,
  } = useQuery({
    queryKey: ['x-factor', 'courses', courseId],
    queryFn: () => xFactorCourseService.getCourseDetail(courseId?.toString() ?? ''),
    enabled: !!courseId,
  });

  // If we're in course context, use the module data from the course
  const moduleFromCourse = courseData?.modules.find(m => m.id === moduleId);

  // Only fetch module detail if we're not in course context or haven't found the module in course data
  const {
    data: standaloneModule,
    isLoading: isLoadingModule,
    error: moduleError,
  } = useQuery({
    queryKey: ['x-factor', 'modules', moduleId],
    queryFn: () => xFactorModuleService.getModuleDetail(moduleId),
    enabled: !courseId || !moduleFromCourse,
  });

  // Use course module data if available, otherwise use standalone module data
  const module = moduleFromCourse ?? standaloneModule;

  // Find next module in course sequence if in course context
  const nextModule =
    courseId && courseData?.modules && moduleFromCourse
      ? courseData.modules.find(
          m =>
            m.course_id === courseId &&
            courseData.modules.indexOf(m) === courseData.modules.indexOf(moduleFromCourse) + 1
        )
      : undefined;

  // Extract course module data from the module if it exists
  const courseModule =
    courseId && moduleFromCourse?.course_id
      ? {
          id: moduleFromCourse.id,
          course_id: moduleFromCourse.course_id,
          module_id: moduleFromCourse.id,
          order: courseData?.modules.findIndex(m => m.id === moduleFromCourse.id) ?? 0,
        }
      : undefined;

  return {
    module,
    courseModule,
    nextModule,
    course: courseId ? courseData : undefined,
    isLoading: courseId ? isLoadingCourse : isLoadingModule,
    error: courseError ?? moduleError,
  };
}
