import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import type { LaravelPagination } from '@/services/types';
import type {
  CourseBrowseFilters,
  TopicSection,
  XFactorCourse,
} from '@/services/x-factor-course.service';
import { xFactorCourseService } from '@/services/x-factor-course.service';

/**
 * Hook for managing X-Factor course data and interactions
 */
export function useXFactorCourses(filters?: CourseBrowseFilters) {
  // Fetch featured courses - always enabled
  const {
    data: featuredData,
    isLoading: isLoadingFeatured,
    error: featuredError,
  } = useQuery({
    queryKey: ['x-factor', 'courses', 'featured'],
    queryFn: () => xFactorCourseService.getFeaturedCourses(),
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });

  // Fetch initial browse data
  const {
    data: browseData,
    isLoading: isLoadingBrowse,
    error: browseError,
  } = useQuery({
    queryKey: ['x-factor', 'courses', 'browse'],
    queryFn: () => xFactorCourseService.getBrowseIndex(),
    enabled: !filters?.search, // Only fetch browse data when not searching
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: 2,
  });

  // Fetch search results when filters are present
  const {
    data: searchData,
    isLoading: isLoadingSearch,
    error: searchError,
  } = useQuery({
    queryKey: ['x-factor', 'courses', 'search', filters],
    queryFn: () => xFactorCourseService.searchCourses(filters ?? {}),
    enabled: !!filters?.search, // Only search when query is present
    retry: 2,
  });

  // Only show loading state when the enabled queries are loading
  const isLoading =
    isLoadingFeatured ||
    (isLoadingBrowse && !filters?.search) ||
    (isLoadingSearch && !!filters?.search);

  return {
    // Featured courses - ensure we never return undefined
    featured: featuredData ?? [],

    // Browse data - ensure we never return undefined
    topicSections: browseData ?? [],

    // Search data - ensure we never return undefined
    searchResults: searchData?.data ?? [],
    totalResults: searchData?.total ?? 0,
    currentPage: searchData?.current_page ?? 1,
    lastPage: searchData?.last_page ?? 1,
    hasNextPage: searchData?.current_page ? searchData.current_page < searchData.last_page : false,
    hasPrevPage: searchData?.current_page ? searchData.current_page > 1 : false,

    // Loading states
    isLoading,

    // Errors - only return errors for enabled queries
    error: filters?.search ? searchError : featuredError || browseError,
  };
}

/**
 * Hook for managing courses within a specific topic
 */
export function useXFactorTopicCourses(topic: string) {
  const { data, isLoading, error, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useInfiniteQuery({
      queryKey: ['x-factor', 'courses', 'topic', topic],
      queryFn: async ({ pageParam }) => {
        return xFactorCourseService.getTopicCourses(topic, pageParam);
      },
      initialPageParam: 1,
      getNextPageParam: (lastPage: LaravelPagination<XFactorCourse>) =>
        lastPage.current_page < lastPage.last_page ? lastPage.current_page + 1 : undefined,
    });

  const courses = data?.pages.flatMap(page => page.data) ?? [];

  return {
    courses,
    hasMore: !!hasNextPage,
    isLoading,
    error,
    loadMore: () => fetchNextPage(),
    isLoadingMore: isFetchingNextPage,
  };
}
