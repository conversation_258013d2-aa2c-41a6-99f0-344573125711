import { useInfiniteQuery, type InfiniteData } from '@tanstack/react-query';
import {
  xFactorLeaderboardService,
  type XFactorLeaderboardApiResponse,
  type XFactorLeaderboardFilters,
} from '@/services/x-factor-leaderboard.service';

/**
 * Hook for managing X-Factor leaderboard data and interactions with infinite scrolling.
 */
export function useXFactorLeaderboard(filters?: XFactorLeaderboardFilters) {
  // Remove page from filters for the queryKey if it exists, as infinite query handles paging
  const { page, ...queryKeyFilters } = filters || {};

  const { data, fetchNextPage, hasNextPage, isLoading, isFetchingNextPage, error, refetch } =
    useInfiniteQuery<
      XFactorLeaderboardApiResponse, // TData: Type of data fetched by queryFn
      Error, // TError: Type of error
      XFactorLeaderboardApiResponse, // TQueryFnData: Type returned by queryFn (same as TData here)
      any[], // TQueryKey: Type of the query key, now an array
      number // TPageParam: Type of the page parameter (page number)
    >({
      queryKey: ['x-factor', 'leaderboard', queryKeyFilters], // Use filters without page for stable key
      queryFn: async ({ pageParam = 1 }) => {
        // pageParam is the current page number
        return xFactorLeaderboardService.getLeaderboard({
          ...filters,
          page: pageParam,
          per_page: 20,
        }); // Ensure per_page is 20
      },
      initialPageParam: 1, // Start with page 1
      getNextPageParam: (lastPageResponse: XFactorLeaderboardApiResponse) => {
        // Explicitly type lastPageResponse
        if (lastPageResponse.current_page < lastPageResponse.last_page) {
          return lastPageResponse.current_page + 1;
        }
        return undefined; // No more pages
      },
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    });

  // Correctly typed data for useInfiniteQuery
  const typedData = data as InfiniteData<XFactorLeaderboardApiResponse, number> | undefined;

  // Consolidate items from all fetched pages
  const leaderboardItems =
    typedData?.pages.flatMap((pageData: XFactorLeaderboardApiResponse) => pageData.rankings) ?? [];

  // Extract metadata from the first page, assuming it's consistent
  const firstPageData = typedData?.pages[0];

  return {
    leaderboardItems,
    currentUser: firstPageData?.current_user ?? null,
    currentUserRank: firstPageData?.current_user_rank ?? null,
    totalItems: firstPageData?.total ?? 0,
    // perPage is now fixed at 20 in queryFn, can get from firstPageData if needed
    perPage: firstPageData?.per_page ?? 20,
    // currentPage and lastPage are less relevant in infinite scroll, but totalItems and items.length are key

    fetchNextPage,
    hasNextPage: !!hasNextPage, // Ensure boolean
    isLoading, // Initial loading state
    isFetchingNextPage, // Loading state for subsequent pages
    error,
    refetchLeaderboard: refetch, // Keep refetch functionality

    // Metadata (can be taken from first page or remain consistent)
    regionName: firstPageData?.region_name,
    academicYearDisplay: firstPageData?.academic_year,
    isAllTime: firstPageData?.all_time ?? (filters?.all_time === true || filters?.all_time === 1),
  };
}
