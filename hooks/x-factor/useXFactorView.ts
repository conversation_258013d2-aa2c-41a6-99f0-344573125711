import { useCallback } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

export type XFactorTab = 'dashboard' | 'browse' | 'completed';
export type XFactorSort = 'recent' | 'progress' | 'alphabetical';
export type XFactorStatus = 'not_started' | 'in_progress' | 'completed';

export interface XFactorFilters {
  activeTab: XFactorTab;
  query?: string;
  topics?: string[];
  sort?: XFactorSort;
  status?: XFactorStatus;
}

export function useXFactorView() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const filters: XFactorFilters = {
    activeTab: (searchParams?.get('tab') as XFactorTab) || 'dashboard',
    query: searchParams?.get('query') || undefined,
    topics: searchParams?.get('topics')?.split(',') || undefined,
    sort: (searchParams?.get('sort') as XFactorSort) || undefined,
    status: (searchParams?.get('status') as XFactorStatus) || undefined,
  };

  const updateFilters = useCallback(
    (newFilters: Partial<XFactorFilters>) => {
      const params = new URLSearchParams(searchParams?.toString());

      // Update each filter if provided
      Object.entries(newFilters).forEach(([key, value]) => {
        if (value === undefined) {
          params.delete(key);
        } else if (Array.isArray(value)) {
          params.set(key, value.join(','));
        } else {
          // Convert activeTab to tab in URL parameters
          const paramKey = key === 'activeTab' ? 'tab' : key;
          params.set(paramKey, value.toString());
        }
      });

      router.push(`${pathname}?${params.toString()}`);
    },
    [pathname, router, searchParams]
  );

  return {
    filters,
    updateFilters,
  };
}
