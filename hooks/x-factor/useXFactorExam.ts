import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { TestStatus } from '@/enums/test-status.enum';
import { xFactorExamService } from '@/services/x-factor-exam.service';
import type { Exam, ExamAttempt, SubmitExamRequest } from '@/services/x-factor-exam.service';

/**
 * Hook for managing X-Factor exam data and interactions
 */
export function useXFactorExam(moduleId: number) {
  const queryClient = useQueryClient();
  const examKey = ['x-factor', 'modules', moduleId, 'exam'];

  const examQuery = useQuery<Exam>({
    queryKey: examKey,
    queryFn: () => xFactorExamService.getExamByModule(moduleId),
  });

  const startAttemptMutation = useMutation<ExamAttempt>({
    mutationFn: () => xFactorExamService.startExamAttempt(moduleId),
    onSuccess: data => {
      queryClient.setQueryData<Exam>(examKey, old => {
        if (!old) return old;
        return {
          ...old,
          latestAttempt: data,
        };
      });
    },
  });

  const submitExamMutation = useMutation<ExamAttempt, unknown, SubmitExamRequest>({
    mutationFn: request => xFactorExamService.submitExam(moduleId, request),
    onSuccess: data => {
      queryClient.setQueryData<Exam>(examKey, old => {
        if (!old) return old;
        return {
          ...old,
          latestAttempt: data,
          lastCompletedAttempt:
            data.status !== TestStatus.InProgress ? data : old.lastCompletedAttempt,
        };
      });
    },
  });

  const currentAttempt = examQuery.data?.latestAttempt;
  const completedAttempts = examQuery.data?.lastCompletedAttempt
    ? [examQuery.data.lastCompletedAttempt]
    : [];

  return {
    exam: examQuery.data,
    isLoading: examQuery.isLoading,
    error: examQuery.error,
    currentAttempt,
    completedAttempts,
    startAttempt: startAttemptMutation.mutate,
    isStartingAttempt: startAttemptMutation.isPending,
    startAttemptError: startAttemptMutation.error,
    submitExam: submitExamMutation.mutate,
    isSubmittingExam: submitExamMutation.isPending,
    submitExamError: submitExamMutation.error,
  };
}
