import { useQuery } from '@tanstack/react-query';
import type { XFactorCourse } from '@/services/x-factor-course.service';
import { xFactorCourseService } from '@/services/x-factor-course.service';

/**
 * Hook for fetching and managing a single X-Factor course detail
 */
export function useXFactorCourseDetail(courseId: string) {
  const {
    data: course,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['x-factor', 'courses', courseId],
    queryFn: () => xFactorCourseService.getCourseDetail(courseId),
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    retry: 2,
  });

  return {
    course,
    isLoading,
    error,
  };
}
