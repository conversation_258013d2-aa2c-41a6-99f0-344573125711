import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { QuestionType } from '@/enums/question-type.enum';
import { TestStatus } from '@/enums/test-status.enum';
import { xFactorQuizService } from '@/services/x-factor-quiz.service';
import { useQuizStore } from '@/stores/quizStore';
import type { TestAttempt as ExamTestAttempt, TestStatus as ExamTestStatus } from '@/types/exam';
import type { Module } from '@/types/module';
import type { TestAttempt as QuizTestAttempt, TestType } from '@/types/quiz';
import { useXFactorModule } from './useXFactorModules';

// Convert quiz attempt to exam attempt format
const convertQuizAttempt = (attempt: QuizTestAttempt | null): ExamTestAttempt | null => {
  if (!attempt) return null;

  // Map quiz status to exam status
  let status: ExamTestStatus;
  switch (attempt.status) {
    case 'complete':
      status = 'completed';
      break;
    case 'in_progress':
      status = 'in_progress';
      break;
    case 'pending_review':
      status = 'pending_review';
      break;
    default:
      status = 'in_progress';
  }

  return {
    id: attempt.id,
    testId: attempt.testId,
    userId: attempt.userId,
    status,
    score: attempt.score,
    startedAt: attempt.startedAt || new Date().toISOString(),
    endsAt: attempt.endsAt || new Date(Date.now() + 3600000).toISOString(), // Default 1 hour
    completedAt: attempt.completedAt,
    gradedAt: attempt.completedAt, // Use completedAt as gradedAt for quizzes
    feedback: null,
    responses: [], // Quiz responses are not tracked in the same way as exams
  };
};

/**
 * Hook for managing X-Factor quiz data and interactions
 */
export const useXFactorQuiz = (moduleId: number) => {
  const queryClient = useQueryClient();
  const { module, isLoading: isLoadingModule, error: moduleError } = useXFactorModule(moduleId);
  const { startAttempt: setAttemptInStore } = useQuizStore();

  // Query for fetching the quiz for a module
  const quizQuery = useQuery({
    queryKey: ['quiz', moduleId],
    queryFn: () => xFactorQuizService.getQuiz(moduleId),
  });

  // Mutation for starting a new quiz attempt
  const startAttemptMutation = useMutation({
    mutationFn: (testId: number) => xFactorQuizService.startQuizAttempt(moduleId),
    onSuccess: attempt => {
      // Update the store with the new attempt
      setAttemptInStore(attempt);
      // Invalidate any relevant queries
      queryClient.invalidateQueries({ queryKey: ['quiz', moduleId] });
    },
  });

  // Mutation for submitting quiz responses
  const submitResponsesMutation = useMutation({
    mutationFn: (responses: Array<{ questionId: number; answerId: number }>) =>
      xFactorQuizService.completeQuizAttempt(moduleId, responses),
    onSuccess: () => {
      // Invalidate quiz data to refresh completion status
      queryClient.invalidateQueries({ queryKey: ['quiz', moduleId] });
    },
  });

  // Complete a quiz attempt
  const {
    mutate: completeAttempt,
    isPending: isCompletingAttempt,
    error: completeAttemptError,
  } = useMutation({
    mutationFn: (attemptId: string) => xFactorQuizService.completeQuizAttempt(moduleId, []),
    onSuccess: () => {
      // Invalidate both quiz and module data to refresh completion status
      queryClient.invalidateQueries({
        queryKey: ['x-factor', 'modules', moduleId],
      });
      queryClient.invalidateQueries({
        queryKey: ['x-factor', 'modules', moduleId, 'quiz'],
      });
    },
  });

  // Loading state that covers all operations
  const isLoading =
    isLoadingModule ||
    quizQuery.isLoading ||
    startAttemptMutation.isPending ||
    submitResponsesMutation.isPending ||
    isCompletingAttempt;

  // Combine all potential errors
  const error =
    moduleError ||
    quizQuery.error ||
    startAttemptMutation.error ||
    submitResponsesMutation.error ||
    completeAttemptError;

  // Transform data into the required module structure
  const moduleData: Module | undefined =
    module && quizQuery.data
      ? {
          id: module.id,
          title: module.name,
          category: module.topics?.[0] || 'Uncategorized',
          description: module.description,
          content: {
            videoUrl: module.cover_image || undefined,
          },
          hasQuiz: module.has_quiz ?? false,
          completed: module.completed_at !== null,
          duration: module.duration_minutes ? `${module.duration_minutes} minutes` : undefined,
          locked: !module.published,
          test: {
            id: quizQuery.data.id,
            moduleId: quizQuery.data.moduleId,
            type: 'quiz' as TestType,
            status: (() => {
              if (!quizQuery.data.latestAttempt?.status) return 'not_started';
              switch (quizQuery.data.latestAttempt.status) {
                case 'complete':
                  return 'completed';
                case 'in_progress':
                  return 'in_progress';
                case 'pending_review':
                  return 'pending_review';
                default:
                  return 'not_started';
              }
            })(),
            score: quizQuery.data.latestAttempt?.score || null,
            attempts: [
              convertQuizAttempt(quizQuery.data.latestAttempt),
              ...(quizQuery.data.lastCompletedAttempt &&
              quizQuery.data.lastCompletedAttempt.id !== quizQuery.data.latestAttempt?.id
                ? [convertQuizAttempt(quizQuery.data.lastCompletedAttempt)]
                : []),
            ].filter((a): a is ExamTestAttempt => a !== null),
            questions: quizQuery.data.questions.map(q => ({
              id: q.id,
              testId: quizQuery.data.id,
              type: QuestionType.MultipleChoice,
              question: q.question,
              answers: q.answers.map(a => ({
                id: a.id,
                answer: a.answer,
                questionId: q.id,
                isCorrect: a.isCorrect,
              })),
            })),
          },
        }
      : undefined;

  return {
    // Transformed data
    moduleData,

    // Original data
    module,
    quiz: quizQuery.data,
    currentAttempt: quizQuery.data?.latestAttempt,

    // Operations
    startAttempt: startAttemptMutation.mutate,
    submitResponses: submitResponsesMutation.mutate,
    completeAttempt,

    // Loading states
    isLoading,
    isLoadingModule,
    isLoadingQuiz: quizQuery.isLoading,
    isStartingAttempt: startAttemptMutation.isPending,
    isSubmittingResponse: submitResponsesMutation.isPending,
    isCompletingAttempt,

    // Error states
    error,
    moduleError,
    quizError: quizQuery.error,
    startAttemptError: startAttemptMutation.error,
    submitResponseError: submitResponsesMutation.error,
    completeAttemptError,
  };
};
