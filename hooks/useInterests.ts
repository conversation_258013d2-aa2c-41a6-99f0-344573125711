'use client';

import { useCallback, useEffect, useState } from 'react';
import debounce from 'lodash/debounce';
import { Interest, interestService } from '@/services/interest.service';

interface UseInterestsReturn {
  interests: Interest[];
  isLoading: boolean;
  error: Error | null;
  setSearchInput: (query: string) => void;
}

export function useInterests(initialQuery: string = ''): UseInterestsReturn {
  const [interests, setInterests] = useState<Interest[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [searchQuery, setSearchQuery] = useState(initialQuery);

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchQuery(value);
    }, 300),
    []
  );

  useEffect(() => {
    // Cleanup debounced function on unmount
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  useEffect(() => {
    const fetchInterests = async () => {
      if (!searchQuery) {
        setInterests([]);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const results = await interestService.searchInterests(searchQuery);
        setInterests(results);
      } catch (err) {
        console.error('Error fetching interests:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch interests'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchInterests();
  }, [searchQuery]);

  return {
    interests,
    isLoading,
    error,
    setSearchInput: debouncedSearch,
  };
}
