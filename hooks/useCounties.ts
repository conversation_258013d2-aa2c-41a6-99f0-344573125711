import { useCallback, useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import debounce from 'lodash/debounce';
import { countyService, type County, type CountyDetails } from '@/services/county.service';
import type { StateCode } from '@/utils/constants/states';

export interface CountyWithState extends County {
  state_name: string | null;
}

export function useCounties() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedState, setSelectedState] = useState<StateCode | null>(null);
  const [selectedCountyId, setSelectedCountyId] = useState<number | null>(null);

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchQuery(value);
    }, 300),
    []
  );

  useEffect(() => {
    // Cleanup debounced function on unmount
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const {
    data: counties = [],
    isLoading: isSearching,
    error: searchError,
  } = useQuery({
    queryKey: ['counties', searchQuery, selectedState],
    queryFn: () => countyService.searchCounties(searchQuery, selectedState),
    enabled: selectedState !== null, // Allow empty query searches when state is selected
  });

  const {
    data: selectedCounty,
    isLoading: isLoadingDetails,
    error: detailsError,
  } = useQuery({
    queryKey: ['county', selectedCountyId],
    queryFn: () => (selectedCountyId ? countyService.getCountyDetails(selectedCountyId) : null),
    enabled: selectedCountyId !== null,
  });

  const selectCounty = useCallback((countyId: number) => {
    setSelectedCountyId(countyId);
  }, []);

  const setState = useCallback((state: StateCode | null) => {
    setSelectedState(state);
    setSearchQuery(''); // Reset search when state changes
    setSelectedCountyId(null); // Reset selected county when state changes
  }, []);

  return {
    // Search functionality
    counties,
    isSearching,
    searchError,
    searchQuery,
    setSearchInput: debouncedSearch,
    selectedState,
    setState,

    // Details functionality
    selectedCounty,
    isLoadingDetails,
    detailsError,
    selectCounty,
  };
}
