import { useMemo } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useCollegeAthleteProfile } from '@/hooks/useCollegeAthleteProfile';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import { useProfessionalProfile } from '@/hooks/useProfessionalProfile';
import type { CollegeAthleteDetails } from '@/services/college-athlete-profile.service';
import type { Interest } from '@/services/interest.service';
import type { ProfileDetails } from '@/services/positive-athlete-profile.service';
import type { ProfessionalDetails } from '@/services/professional-profile.service';
import { ProfileTypes } from '@/stores/auth.store';

// Unified interface for all profile types
export interface UnifiedProfileDetails {
  // Common fields across all profile types
  state?: string | null;
  county_id?: number | null;
  county_name?: string | null;
  school_id?: number | null;
  school_name?: string | null;
  graduation_year?: number | null;
  gpa?: number | null;
  class_rank?: string | null;
  gender?: string | null;
  height_in_inches?: number | null;
  weight?: number | null;
  interests?: Interest[];
  interest_ids?: number[];

  // Alumni-specific fields
  college?: string | null;
  employer?: string | null;
  job_title?: string | null;

  // Social media fields
  instagram?: string | null;
  facebook?: string | null;
  twitter?: string | null;
  hudl?: string | null;
  website?: string | null;
  custom_link?: string | null;
}

export interface UnifiedUpdateRequest {
  state?: string | null;
  county_id?: number | null;
  school_id?: number | null;
  school_name?: string | null;
  graduation_year?: number | null;
  gpa?: number | null;
  class_rank?: string | null;
  gender?: string | null;
  height_in_inches?: number | null;
  weight?: number | null;
  interest_ids?: number[];

  // Alumni-specific fields
  college?: string | null;
  employer?: string | null;
  job_title?: string | null;

  // Social media fields
  instagram?: string | null;
  facebook?: string | null;
  twitter?: string | null;
  hudl?: string | null;
  website?: string | null;
  custom_link?: string | null;
}

interface UpdateDetailsOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

// Type guards
function isProfileDetails(details: any): details is ProfileDetails {
  return details && typeof details === 'object';
}

function isCollegeAthleteDetails(details: any): details is CollegeAthleteDetails {
  return details && typeof details === 'object' && 'college' in details;
}

function isProfessionalDetails(details: any): details is ProfessionalDetails {
  return details && typeof details === 'object' && 'employer' in details;
}

/**
 * Unified hook for managing profile details across all profile types.
 * Automatically selects the appropriate hook based on the user's profile type.
 */
export function useUnifiedProfile() {
  const { profileType } = useAuth();

  // Call all hooks (following Rules of Hooks)
  const positiveAthleteProfile = usePositiveAthleteProfile();
  const collegeAthleteProfile = useCollegeAthleteProfile();
  const professionalProfile = useProfessionalProfile();

  // Select appropriate profile data based on profile type
  const currentProfile = useMemo(() => {
    switch (profileType) {
      case ProfileTypes.COLLEGE_ATHLETE:
        return collegeAthleteProfile;
      case ProfileTypes.PROFESSIONAL:
        return professionalProfile;
      default:
        return positiveAthleteProfile;
    }
  }, [profileType, positiveAthleteProfile, collegeAthleteProfile, professionalProfile]);

  // Data adapters to normalize different profile data structures
  const normalizedDetails = useMemo((): UnifiedProfileDetails | undefined => {
    if (!currentProfile.details) return undefined;

    const details = currentProfile.details;

    // Base normalization with safe property access
    const normalized: UnifiedProfileDetails = {
      instagram: details.instagram || null,
      facebook: details.facebook || null,
      twitter: details.twitter || null,
      hudl: details.hudl || null,
      custom_link: details.custom_link || null,
    };

    // Handle ProfileDetails (positive athlete)
    if (
      isProfileDetails(details) &&
      profileType !== ProfileTypes.COLLEGE_ATHLETE &&
      profileType !== ProfileTypes.PROFESSIONAL
    ) {
      normalized.state = details.state || null;
      normalized.county_id = details.county_id || null;
      normalized.county_name = details.county_name || null;
      normalized.school_id = details.school_id || null;
      normalized.school_name = details.school_name || null;
      normalized.graduation_year = details.graduation_year || null;
      normalized.gpa = details.gpa || null;
      normalized.class_rank = details.class_rank || null;
      normalized.gender = details.gender || null;
      normalized.height_in_inches = details.height_in_inches || null;
      normalized.weight = details.weight || null;
      normalized.interests = details.interests || [];
      normalized.interest_ids = details.interest_ids || [];
      normalized.website = (details as any).website || null;
      normalized.college = details.college || null;
      normalized.employer = details.employer || null;
    }

    // Handle CollegeAthleteDetails
    if (isCollegeAthleteDetails(details)) {
      normalized.state = details.state || null;
      normalized.college = details.college || null;
      normalized.graduation_year = details.graduation_year || null;
      normalized.gender = details.gender || null;
      normalized.gpa = details.gpa || null;
      normalized.weight = details.weight || null;
      // Convert height string to inches if needed
      if (details.height && typeof details.height === 'string') {
        // Assuming height format like "6'2" - convert to inches
        const heightMatch = details.height.match(/(\d+)'(\d+)/);
        if (heightMatch) {
          const feet = parseInt(heightMatch[1]);
          const inches = parseInt(heightMatch[2]);
          normalized.height_in_inches = feet * 12 + inches;
        }
      }
      // Map career_interests (now full objects) to both interests and interest_ids
      if (details.career_interests) {
        normalized.interests = details.career_interests;
        normalized.interest_ids = details.career_interests.map(interest => interest.id);
      } else {
        normalized.interests = [];
        normalized.interest_ids = [];
      }
    }

    // Handle ProfessionalDetails
    if (isProfessionalDetails(details)) {
      normalized.state = details.state || null;
      normalized.employer = details.employer || null;
      normalized.job_title = details.job_title || null;
      // Map interests (now full objects) to both interests and interest_ids
      if (details.interests) {
        normalized.interests = details.interests;
        normalized.interest_ids = details.interests.map(interest => interest.id);
      } else {
        normalized.interests = [];
        normalized.interest_ids = [];
      }
    }

    return normalized;
  }, [currentProfile.details, profileType]);

  // Unified update function
  const updateDetails = async (data: UnifiedUpdateRequest, options?: UpdateDetailsOptions) => {
    // Transform the unified request to the appropriate format for each profile type
    let transformedData: any = data;

    if (profileType === ProfileTypes.COLLEGE_ATHLETE) {
      transformedData = {
        state: data.state,
        college: data.college,
        graduation_year: data.graduation_year,
        gender: data.gender,
        gpa: data.gpa,
        weight: data.weight,
        career_interests: data.interest_ids,
        twitter: data.twitter,
        instagram: data.instagram,
        facebook: data.facebook,
        hudl: data.hudl,
        custom_link: data.custom_link,
      };
      // Convert height inches back to string format if needed
      if (data.height_in_inches) {
        const feet = Math.floor(data.height_in_inches / 12);
        const inches = data.height_in_inches % 12;
        transformedData.height = `${feet}'${inches}`;
      }
    } else if (profileType === ProfileTypes.PROFESSIONAL) {
      transformedData = {
        state: data.state,
        employer: data.employer,
        job_title: data.job_title,
        interests: data.interest_ids,
        twitter: data.twitter,
        instagram: data.instagram,
        facebook: data.facebook,
        hudl: data.hudl,
        custom_link: data.custom_link,
      };
    }
    // For positive athletes, use data as-is since it matches ProfileDetails

    return currentProfile.updateDetails(transformedData, options);
  };

  return {
    details: normalizedDetails,
    isLoadingDetails: currentProfile.isLoadingDetails,
    updateDetails,
    isUpdatingDetails: currentProfile.isUpdatingDetails,
    detailsUpdateError: currentProfile.detailsUpdateError,
    profileType, // Expose profile type for conditional rendering
  };
}
