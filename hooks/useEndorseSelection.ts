'use client';

import { useMutation, type UseMutationOptions } from '@tanstack/react-query';
import {
  endorseSelectionService,
  type PublicEndorsementRequestData,
  type PublicEndorsementResponseData,
} from '@/services/endorse-selection.service';

interface UseSubmitEndorsementOptions {
  // You can add specific onSuccess, onError, onSettled callbacks here if needed
  // For example: onSuccess?: (data: PublicEndorsementResponseData) => void;
}

export function useSubmitEndorsement(
  options?: UseMutationOptions<
    PublicEndorsementResponseData,
    Error, // Default error type
    PublicEndorsementRequestData
  >
) {
  return useMutation<PublicEndorsementResponseData, Error, PublicEndorsementRequestData>({
    mutationFn: (payload: PublicEndorsementRequestData) =>
      endorseSelectionService.submitEndorsement(payload),
    ...options,
  });
}
