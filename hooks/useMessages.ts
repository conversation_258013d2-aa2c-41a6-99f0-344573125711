import { useEffect, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/useAuth';
import {
  Conversation,
  ConversationWithConnection,
  EditMessageRequest,
  Message,
  messagesService,
  SendMessageRequest,
} from '@/services/messages.service';

/**
 * Check if the message was deleted by its sender
 */
export function isDeletedBySender(message: Message): boolean {
  return !!message.deletedBySenderAt;
}

/**
 * Check if the message was deleted by its recipient
 */
export function isDeletedByRecipient(message: Message): boolean {
  return !!message.deletedByRecipientAt;
}

/**
 * Check if the conversation is read-only (colleague conversation)
 */
export function isReadOnlyConversation(conversation: Conversation): boolean {
  return !!conversation.isReadonly;
}

/**
 * Check if the message should be visible to the current user
 *
 * @param message - The message to check
 * @param currentUserId - The ID of the current user
 */
export function shouldShowMessage(message: Message, currentUserId: number): boolean {
  // Messages that are flagged should only be shown to the sender
  if (message.isFlagged && message.recipientId === currentUserId) {
    return false;
  }

  return true;
}

/**
 * Get display content based on message deletion status
 *
 * @param message - The message to get content for
 * @param currentUserId - The ID of the current user
 */
export function getMessageDisplayContent(message: Message, currentUserId: number): string {
  // For flagged messages
  if (message.isFlagged && message.recipientId === currentUserId) {
    return 'This message has been flagged and is not visible';
  }

  // If the message was deleted by the sender (and the current user is the recipient)
  if (isDeletedBySender(message) && message.recipientId === currentUserId) {
    return 'This message was deleted by the sender';
  }

  // If the message was deleted by the recipient (and the current user is the sender)
  if (isDeletedByRecipient(message) && message.senderId === currentUserId) {
    return 'This message was deleted by the recipient';
  }

  // If the user deleted their own message (either as sender or recipient)
  if (
    (isDeletedBySender(message) && message.senderId === currentUserId) ||
    (isDeletedByRecipient(message) && message.recipientId === currentUserId)
  ) {
    return 'You deleted this message';
  }

  // Return the actual content if not deleted
  return message.content || '';
}

/**
 * Hook for managing messages data and interactions
 */
export function useMessages() {
  const queryClient = useQueryClient();
  const { user } = useAuth(); // Get the current user from auth context

  // Add pagination state for conversations
  const [conversationsPage, setConversationsPage] = useState<number>(1);
  const [allConversations, setAllConversations] = useState<Conversation[]>([]);
  const [hasMoreConversations, setHasMoreConversations] = useState<boolean>(true);

  // Get all conversations with pagination support
  const {
    data: conversationsData = [],
    isLoading: isLoadingConversations,
    error: conversationsError,
    refetch: refetchConversations,
    isFetching: isFetchingMoreConversations,
  } = useQuery({
    queryKey: ['messages', 'conversations', conversationsPage],
    queryFn: async () => {
      try {
        return await messagesService.getConversations(15, conversationsPage);
      } catch (error) {
        console.error('Error fetching conversations:', error);
        return [];
      }
    },
  });

  // Update allConversations when data changes - optimize to minimize re-renders
  useEffect(() => {
    if (!conversationsData || conversationsData.length === 0) {
      // If no data is returned, there are no more conversations to load
      if (conversationsPage > 1) {
        setHasMoreConversations(false);
      }
      return;
    }

    // Directly update state - React will batch these updates automatically
    if (conversationsPage === 1) {
      // For the first page, just set the data directly
      setAllConversations(conversationsData);
    } else {
      // For subsequent pages, append new conversations using functional update
      // This ensures we have the latest state without creating a dependency on allConversations
      setAllConversations(prevConversations => {
        // Only add conversations that aren't already in the list
        const existingIds = new Set(prevConversations.map(c => c.otherUserId));
        const newUniqueConversations = conversationsData.filter(
          c => !existingIds.has(c.otherUserId)
        );

        // If there are no new conversations, don't update state
        if (newUniqueConversations.length === 0) {
          return prevConversations;
        }

        // Return a new array with all conversations
        return [...prevConversations, ...newUniqueConversations];
      });
    }

    // Update hasMoreConversations based on the number of conversations received
    setHasMoreConversations(conversationsData.length >= 15);
  }, [conversationsData, conversationsPage]);

  // Function to load more conversations - simplified to use less state changes
  const loadMoreConversations = (onSuccess?: () => void) => {
    if (!isLoadingConversations && hasMoreConversations && !isFetchingMoreConversations) {
      // Increase page number to get older conversations
      setConversationsPage(prevPage => prevPage + 1);

      // If a success callback is provided, run it after a short delay
      if (onSuccess) {
        setTimeout(onSuccess, 300);
      }
    }
  };

  // Reset conversations pagination
  const resetConversations = () => {
    setConversationsPage(1);
    setAllConversations([]);
    setHasMoreConversations(true);
  };

  // Get a specific conversation with pagination support
  const useConversation = (userId: number, sponsorId?: number) => {
    // Start with page 1 for most recent messages
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [allMessages, setAllMessages] = useState<Message[]>([]);
    const [hasMore, setHasMore] = useState<boolean>(true);
    const [isReadOnly, setIsReadOnly] = useState<boolean>(false);

    // Check if this is a read-only conversation by looking at the conversations list
    const conversation = queryClient
      .getQueryData<Conversation[]>(['messages', 'conversations'])
      ?.find(c => c.otherUserId === userId);

    // If this is a read-only conversation but no sponsorId was provided, try to get it from the conversation data
    const effectiveSponsorId =
      sponsorId || (conversation?.isReadonly && conversation?.sponsorUser?.id);

    // Query for fetching a specific page of messages
    const { data, isLoading, error, refetch, isFetching } = useQuery({
      queryKey: ['messages', 'conversation', userId, effectiveSponsorId, currentPage],
      queryFn: async () => {
        try {
          // If sponsorId is provided, we're viewing a sponsor's conversation
          if (effectiveSponsorId) {
            return await messagesService.getSponsorConversation(
              effectiveSponsorId,
              userId,
              15,
              currentPage
            );
          }

          // Otherwise, it's a standard conversation
          // The backend is expected to send newest messages first when using pagination
          // (page 1 has newest messages, higher page numbers have older messages)
          return await messagesService.getConversation(userId, 15, currentPage);
        } catch (error) {
          console.error(`Error fetching conversation with user ${userId}:`, error);
          throw error;
        }
      },
      enabled: !!userId && userId > 0, // Only run the query if we have a valid userId
      retry: 1, // Only retry once to avoid excessive API calls on error
    });

    // Update the isReadOnly state when data changes
    useEffect(() => {
      if (data) {
        setIsReadOnly(!!data.isReadOnly);
      } else if (sponsorId) {
        setIsReadOnly(true); // Always read-only when viewing as sponsor
      } else {
        // Otherwise, check if the conversation in the list is marked as read-only
        const conversation = queryClient
          .getQueryData<Conversation[]>(['messages', 'conversations'])
          ?.find(c => c.otherUserId === userId);

        setIsReadOnly(!!conversation?.isReadonly);
      }
    }, [userId, sponsorId, data, queryClient]);

    // Update the allMessages state when data changes
    useEffect(() => {
      if (data?.messages) {
        if (currentPage === 1) {
          setAllMessages(data.messages);
        } else {
          // For subsequent pages (older messages that come before current ones), append them at the beginning
          const messageIds = new Set(allMessages.map(msg => msg.id));
          const newUniqueMessages = data.messages.filter(msg => !messageIds.has(msg.id));

          // Combine & sort all messages chronologically (oldest to newest) for display
          const combinedMessages = [...newUniqueMessages.reverse(), ...allMessages].sort(
            (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );

          setAllMessages(combinedMessages);
        }

        // Update hasMore state based on the number of messages received
        setHasMore(data.messages.length >= 15);
      }
    }, [data, currentPage]);

    // Function to load more (older) messages
    const loadOlderMessages = () => {
      if (!isLoading && hasMore) {
        // Increase page number to get older messages
        setCurrentPage(prevPage => prevPage + 1);
      }
    };

    // Function to reset to first page
    const resetMessages = () => {
      setCurrentPage(1);
      setAllMessages([]);
      setHasMore(true);
    };

    return {
      data: {
        messages: allMessages,
        connection: data?.connection || null,
      },
      originalData: data,
      isLoading,
      error,
      refetch,
      isFetching,
      currentPage,
      hasMore,
      isReadOnly,
      loadOlderMessages, // Renamed from loadMoreMessages to be more explicit
      resetMessages,
    };
  };

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: (messageData: SendMessageRequest) => {
      // Get the conversation to check if it's read-only
      const conversation = queryClient
        .getQueryData<Conversation[]>(['messages', 'conversations'])
        ?.find(c => c.otherUserId === messageData.recipientId);

      // Don't allow sending messages in read-only conversations
      if (conversation?.isReadonly) {
        throw new Error('Cannot send messages in a read-only conversation');
      }

      return messagesService.sendMessage(messageData);
    },
    onSuccess: (data, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['messages', 'conversations'] });
      queryClient.invalidateQueries({
        queryKey: ['messages', 'conversation', variables.recipientId],
      });
    },
  });

  // Mark as read mutation
  const markAsReadMutation = useMutation({
    mutationFn: (messageId: number) => messagesService.markAsRead(messageId),
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['messages', 'conversations'] });
    },
  });

  // Delete message mutation
  const deleteMessageMutation = useMutation({
    mutationFn: (messageId: number) => {
      // Find the active conversation
      const activeConversationUserId = queryClient.getQueryData<any>([
        'messages',
        'conversation',
      ])?.otherUserId;

      if (activeConversationUserId) {
        // Check if the conversation is read-only
        const conversation = queryClient
          .getQueryData<Conversation[]>(['messages', 'conversations'])
          ?.find(c => c.otherUserId === activeConversationUserId);

        if (conversation?.isReadonly) {
          throw new Error('Cannot delete messages in a read-only conversation');
        }
      }

      // Find the message in the active conversation
      const currentConversation = queryClient.getQueryData<any>([
        'messages',
        'conversation',
        activeConversationUserId,
      ]);

      const message = currentConversation?.messages?.find((msg: Message) => msg.id === messageId);

      if (!message || !user) {
        console.error('Message not found or user not authenticated:', messageId);
        return messagesService.deleteMessage(messageId);
      }

      // Determine if the current user is the sender or recipient
      const isCurrentUserSender = message.senderId === user.id;
      const deleteAs = isCurrentUserSender ? 'sender' : 'recipient';

      return messagesService.deleteMessage(messageId, deleteAs);
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['messages', 'conversations'] });

      // Invalidate all active conversation queries to ensure the deleted message is removed from the view
      queryClient.invalidateQueries({ queryKey: ['messages', 'conversation'] });
    },
  });

  // Edit message mutation
  const editMessageMutation = useMutation({
    mutationFn: ({ messageId, data }: { messageId: number; data: EditMessageRequest }) => {
      // Find the active conversation
      const activeConversationUserId = queryClient.getQueryData<any>([
        'messages',
        'conversation',
      ])?.otherUserId;

      if (activeConversationUserId) {
        // Check if the conversation is read-only
        const conversation = queryClient
          .getQueryData<Conversation[]>(['messages', 'conversations'])
          ?.find(c => c.otherUserId === activeConversationUserId);

        if (conversation?.isReadonly) {
          throw new Error('Cannot edit messages in a read-only conversation');
        }
      }

      return messagesService.editMessage(messageId, data);
    },
    onSuccess: updatedMessage => {
      // Get the conversation participants
      const senderId = updatedMessage.senderId;
      const recipientId = updatedMessage.recipientId;

      // Instead of complex cache manipulation, simply invalidate the relevant queries
      // to force a refetch of the latest data from the server

      // Invalidate the conversations list
      queryClient.invalidateQueries({ queryKey: ['messages', 'conversations'] });

      // Invalidate the specific conversation from both user perspectives
      queryClient.invalidateQueries({ queryKey: ['messages', 'conversation', senderId] });
      queryClient.invalidateQueries({ queryKey: ['messages', 'conversation', recipientId] });
    },
  });

  // Pin conversation mutation
  const pinConversationMutation = useMutation({
    mutationFn: (userId: number) => messagesService.pinConversation(userId),
    onSuccess: (_, userId) => {
      // Update the specific conversation in the cache
      queryClient.setQueryData(
        ['messages', 'conversations'],
        (oldData: Conversation[] | undefined) => {
          if (!oldData) return undefined;
          return oldData.map(conversation => {
            if (conversation.otherUserId === userId) {
              return {
                ...conversation,
                isPinned: true,
              };
            }
            return conversation;
          });
        }
      );
    },
  });

  // Unpin conversation mutation
  const unpinConversationMutation = useMutation({
    mutationFn: (userId: number) => messagesService.unpinConversation(userId),
    onSuccess: (_, userId) => {
      // Update the specific conversation in the cache
      queryClient.setQueryData(
        ['messages', 'conversations'],
        (oldData: Conversation[] | undefined) => {
          if (!oldData) return undefined;
          return oldData.map(conversation => {
            if (conversation.otherUserId === userId) {
              return {
                ...conversation,
                isPinned: false,
              };
            }
            return conversation;
          });
        }
      );
    },
  });

  /**
   * Send a message to another user
   * @param messageData - The message data to send
   */
  const sendMessage = (messageData: SendMessageRequest) => {
    sendMessageMutation.mutate(messageData);
  };

  /**
   * Mark a message as read
   * @param messageId - The ID of the message to mark as read
   */
  const markAsRead = (messageId: number) => {
    markAsReadMutation.mutate(messageId);
  };

  /**
   * Delete a message
   * @param messageId - The ID of the message to delete
   */
  const deleteMessage = (messageId: number) => {
    deleteMessageMutation.mutate(messageId);
  };

  /**
   * Edit a message
   * @param messageId - The ID of the message to edit
   * @param content - The new content for the message
   */
  const editMessage = (messageId: number, content: string) => {
    editMessageMutation.mutate({
      messageId,
      data: { content },
    });
  };

  /**
   * Pin a conversation
   * @param userId - The ID of the user whose conversation to pin
   */
  const pinConversation = (userId: number) => {
    pinConversationMutation.mutate(userId);
  };

  /**
   * Unpin a conversation
   * @param userId - The ID of the user whose conversation to unpin
   */
  const unpinConversation = (userId: number) => {
    unpinConversationMutation.mutate(userId);
  };

  /**
   * Refresh all messages data
   */
  const refreshAllData = () => {
    queryClient.invalidateQueries({ queryKey: ['messages'] });
  };

  /**
   * Refresh specific messages data
   */
  const refreshData = (dataType: 'conversations' | 'conversation', userId?: number) => {
    switch (dataType) {
      case 'conversations':
        queryClient.invalidateQueries({ queryKey: ['messages', 'conversations'] });
        break;
      case 'conversation':
        if (userId) {
          queryClient.invalidateQueries({ queryKey: ['messages', 'conversation', userId] });
        }
        break;
    }
  };

  return {
    // Helper functions
    isDeletedBySender,
    isDeletedByRecipient,
    isReadOnlyConversation,
    shouldShowMessage,
    getMessageDisplayContent,

    // Hooks
    useConversation,

    // Conversations
    conversations: allConversations,
    isLoadingConversations,
    conversationsError,
    refetchConversations,
    currentConversationsPage: conversationsPage,
    hasMoreConversations,
    isFetchingMoreConversations,
    loadMoreConversations,
    resetConversations,

    // Mutations
    sendMessage,
    isSending: sendMessageMutation.isPending,
    sendError: sendMessageMutation.error,

    editMessage,
    isEditing: editMessageMutation.isPending,
    editError: editMessageMutation.error,

    markAsRead,
    isMarking: markAsReadMutation.isPending,
    markError: markAsReadMutation.error,

    deleteMessage,
    isDeleting: deleteMessageMutation.isPending,
    deleteError: deleteMessageMutation.error,

    pinConversation,
    isPinning: pinConversationMutation.isPending,
    pinError: pinConversationMutation.error,

    unpinConversation,
    isUnpinning: unpinConversationMutation.isPending,
    unpinError: unpinConversationMutation.error,

    // Utility functions
    refreshAllData,
    refreshData,
  };
}
