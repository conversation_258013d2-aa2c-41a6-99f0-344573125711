import { useMutation } from '@tanstack/react-query';
import type { AxiosError, AxiosResponse } from 'axios';
import { usePositiveAthleteOnboardingStore } from '@/stores/positiveAthleteOnboardingStore';
import { useSystemInviteStore } from '@/stores/systemInviteStore';
import { useToastNotify } from '@/stores/toastNotify.store';
import { useWizardStore } from '@/stores/wizardStore';
import type {
  AccountInfoPayload,
  DetailsPayload,
  InvolvementPayload,
  OnboardingStepResponse,
  SportsPayload,
  StoryPayload,
  WorkExperiencePayload,
} from '../services/positiveAthleteOnboarding.service';
import { positiveAthleteOnboardingService } from '../services/positiveAthleteOnboarding.service';

interface UsePositiveAthleteOnboardingProps {
  onSuccess?: (response: OnboardingStepResponse | { redirect: string }) => void;
  onError?: (error: unknown) => void;
}

export function usePositiveAthleteOnboarding({
  onSuccess,
  onError,
}: UsePositiveAthleteOnboardingProps = {}) {
  const { toastNotify } = useToastNotify();

  const startOnboarding = useMutation({
    mutationFn: () => positiveAthleteOnboardingService.startPositiveAthleteOnboarding(),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitAccountInfo = useMutation({
    mutationFn: (data: AccountInfoPayload) =>
      positiveAthleteOnboardingService.submitAccountInfo(data),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const resetOnboardingStore = usePositiveAthleteOnboardingStore(state => state.reset);
  const clearInviteData = useSystemInviteStore(state => state.clearInviteData);
  const resetWizard = useWizardStore(state => state.resetWizard);

  const completeOnboarding = useMutation<
    AxiosResponse<{ redirect: string }>,
    AxiosError<any>,
    AccountInfoPayload
  >({
    mutationFn: (data: AccountInfoPayload) =>
      positiveAthleteOnboardingService.completeOnboarding(data),
    onSuccess: (response: AxiosResponse<{ redirect: string }>) => {
      // Clear all local storage used for onboarding
      resetOnboardingStore();
      clearInviteData();
      resetWizard();

      // Call the provided onSuccess callback
      onSuccess?.(response.data);
    },
    onError: (error: AxiosError<any>) => {
      if (error.response?.data?.message) {
        toastNotify(error.response.data.message, 'error');
      } else {
        toastNotify('An unexpected error occurred while completing onboarding.', 'error');
      }
      onError?.(error);
    },
  });

  const submitDetails = useMutation({
    mutationFn: (data: DetailsPayload | FormData) =>
      positiveAthleteOnboardingService.submitDetails(data),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitSports = useMutation({
    mutationFn: (data: SportsPayload) => positiveAthleteOnboardingService.submitSports(data),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitInvolvement = useMutation({
    mutationFn: (data: InvolvementPayload) =>
      positiveAthleteOnboardingService.submitInvolvement(data),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitWorkExperience = useMutation({
    mutationFn: (data: WorkExperiencePayload) =>
      positiveAthleteOnboardingService.submitWorkExperience(data),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitStory = useMutation({
    mutationFn: (data: StoryPayload) => positiveAthleteOnboardingService.submitStory(data),
    onSuccess: (response: AxiosResponse<OnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  return {
    startOnboarding,
    submitAccountInfo,
    completeOnboarding,
    submitDetails,
    submitSports,
    submitInvolvement,
    submitWorkExperience,
    submitStory,
  };
}
