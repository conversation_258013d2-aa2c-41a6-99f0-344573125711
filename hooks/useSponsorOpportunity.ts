import { useCallback, useEffect, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import type { AxiosError, AxiosResponse } from 'axios';
import debounce from 'lodash/debounce';
import type {
  ApiErrorResponse,
  CreateOpportunityPayload,
  GetOpportunitiesParams,
  OpportunityData,
  UpdateOpportunityPayload,
} from '@/services/sponsorOpportunity.service';
import {
  OpportunityStatus,
  sponsorOpportunityService,
} from '@/services/sponsorOpportunity.service';

interface UseSponsorOpportunityProps<TData = OpportunityData | OpportunityData[]> {
  onSuccess?: (
    response: TData,
    context?:
      | 'create'
      | 'update'
      | 'delete'
      | 'duplicate'
      | 'toggleStatus'
      | 'toggleFeatured'
      | 'get'
  ) => void;
  onError?: (
    error: unknown,
    context?:
      | 'create'
      | 'update'
      | 'delete'
      | 'duplicate'
      | 'toggleStatus'
      | 'toggleFeatured'
      | 'get'
      | 'search'
  ) => void;
}

export function useSponsorOpportunity({ onSuccess, onError }: UseSponsorOpportunityProps = {}) {
  const queryClient = useQueryClient();

  // --- Internal State for Search Debouncing ---
  const [searchParams, setSearchParams] = useState<GetOpportunitiesParams>({});
  const [debouncedSearchParams, setDebouncedSearchParams] = useState<GetOpportunitiesParams>({});

  // --- Debounce Handler ---
  const debouncedSetParams = useCallback(
    debounce((params: GetOpportunitiesParams) => {
      setDebouncedSearchParams(params);
    }, 300), // 300ms debounce delay
    []
  );

  // Update debounced query when searchParams changes
  useEffect(() => {
    debouncedSetParams(searchParams);

    // Cleanup function
    return () => {
      debouncedSetParams.cancel();
    };
  }, [searchParams, debouncedSetParams]);

  // --- Queries ---

  /**
   * Get opportunities with optional filtering
   */
  const getOpportunities = (options: { enabled?: boolean } = {}) => {
    return useQuery({
      queryKey: ['sponsorOpportunities', debouncedSearchParams],
      queryFn: async () => {
        const response = await sponsorOpportunityService.getOpportunities(debouncedSearchParams);
        return response.data;
      },
      enabled: options.enabled !== false,
    });
  };

  /**
   * Get a specific opportunity by ID
   */
  const getOpportunity = (id: number, options: { enabled?: boolean } = {}) => {
    return useQuery({
      queryKey: ['sponsorOpportunity', id],
      queryFn: async () => {
        const response = await sponsorOpportunityService.getOpportunity(id);
        return response.data;
      },
      enabled: !!id && options.enabled !== false,
    });
  };

  // --- Mutations ---

  /**
   * Create a new opportunity
   */
  const createOpportunity = useMutation<
    AxiosResponse<OpportunityData>,
    AxiosError<ApiErrorResponse>,
    CreateOpportunityPayload
  >({
    mutationFn: (data: CreateOpportunityPayload) =>
      sponsorOpportunityService.createOpportunity(data),
    onSuccess: response => {
      // Invalidate the opportunities list query to reflect the new opportunity
      queryClient.invalidateQueries({ queryKey: ['sponsorOpportunities'] });

      // Call the custom success handler if provided
      (onSuccess as (data: OpportunityData, context: 'create') => void)?.(response.data, 'create');
    },
    onError: error => onError?.(error, 'create'),
  });

  /**
   * Update an existing opportunity
   */
  const updateOpportunity = useMutation<
    AxiosResponse<OpportunityData>,
    AxiosError<ApiErrorResponse>,
    { id: number; data: UpdateOpportunityPayload }
  >({
    mutationFn: ({ id, data }) => sponsorOpportunityService.updateOpportunity(id, data),
    onSuccess: response => {
      // Invalidate both the list and the specific opportunity queries
      queryClient.invalidateQueries({ queryKey: ['sponsorOpportunities'] });
      queryClient.invalidateQueries({ queryKey: ['sponsorOpportunity', response.data.id] });

      // Call the custom success handler if provided
      (onSuccess as (data: OpportunityData, context: 'update') => void)?.(response.data, 'update');
    },
    onError: error => onError?.(error, 'update'),
  });

  /**
   * Delete an opportunity
   */
  const deleteOpportunity = useMutation<void, AxiosError<ApiErrorResponse>, number>({
    mutationFn: (id: number) => {
      return sponsorOpportunityService.deleteOpportunity(id).then(() => {});
    },
    onSuccess: (_, id) => {
      // Invalidate the opportunities list query
      queryClient.invalidateQueries({ queryKey: ['sponsorOpportunities'] });

      // Remove the specific opportunity from cache
      queryClient.removeQueries({ queryKey: ['sponsorOpportunity', id] });

      // Call the custom success handler if provided - safely cast to unknown first
      if (onSuccess) {
        // Using unknown as an intermediate type to safely cast
        (onSuccess as unknown as (data: null, context: 'delete') => void)(null, 'delete');
      }
    },
    onError: error => onError?.(error, 'delete'),
  });

  /**
   * Duplicate an opportunity
   */
  const duplicateOpportunity = useMutation<
    AxiosResponse<OpportunityData>,
    AxiosError<ApiErrorResponse>,
    number
  >({
    mutationFn: (id: number) => sponsorOpportunityService.duplicateOpportunity(id),
    onSuccess: response => {
      // Invalidate the opportunities list query
      queryClient.invalidateQueries({ queryKey: ['sponsorOpportunities'] });

      // Call the custom success handler if provided
      (onSuccess as (data: OpportunityData, context: 'duplicate') => void)?.(
        response.data,
        'duplicate'
      );
    },
    onError: error => onError?.(error, 'duplicate'),
  });

  /**
   * Toggle an opportunity's status (LISTED/UNLISTED)
   */
  const toggleOpportunityStatus = useMutation<
    AxiosResponse<OpportunityData>,
    AxiosError<ApiErrorResponse>,
    number
  >({
    mutationFn: (id: number) => sponsorOpportunityService.toggleOpportunityStatus(id),
    onSuccess: response => {
      // Invalidate both the list and the specific opportunity queries
      queryClient.invalidateQueries({ queryKey: ['sponsorOpportunities'] });
      queryClient.invalidateQueries({ queryKey: ['sponsorOpportunity', response.data.id] });

      // Call the custom success handler if provided
      (onSuccess as (data: OpportunityData, context: 'toggleStatus') => void)?.(
        response.data,
        'toggleStatus'
      );
    },
    onError: error => onError?.(error, 'toggleStatus'),
  });

  /**
   * Toggle an opportunity's featured status
   */
  const toggleOpportunityFeatured = useMutation<
    AxiosResponse<OpportunityData>,
    AxiosError<ApiErrorResponse>,
    { id: number; isFeatured: boolean }
  >({
    mutationFn: ({ id, isFeatured }) =>
      sponsorOpportunityService.updateOpportunity(id, { isFeatured }),
    onSuccess: response => {
      // Invalidate both the list and the specific opportunity queries
      queryClient.invalidateQueries({ queryKey: ['sponsorOpportunities'] });
      queryClient.invalidateQueries({ queryKey: ['sponsorOpportunity', response.data.id] });

      // Call the custom success handler if provided
      (onSuccess as (data: OpportunityData, context: 'toggleFeatured') => void)?.(
        response.data,
        'toggleFeatured'
      );
    },
    onError: error => onError?.(error, 'toggleFeatured'),
  });

  return {
    // Query hooks
    getOpportunities,
    getOpportunity,

    // Search params state updater
    setSearchParams,

    // Mutations
    createOpportunity,
    updateOpportunity,
    deleteOpportunity,
    duplicateOpportunity,
    toggleOpportunityStatus,
    toggleOpportunityFeatured,

    // Mutation states
    isCreating: createOpportunity.isPending,
    isUpdating: updateOpportunity.isPending,
    isDeleting: deleteOpportunity.isPending,
    isDuplicating: duplicateOpportunity.isPending,
    isTogglingStatus: toggleOpportunityStatus.isPending,
    isTogglingFeatured: toggleOpportunityFeatured.isPending,

    // Mutation errors
    createError: createOpportunity.error,
    updateError: updateOpportunity.error,
    deleteError: deleteOpportunity.error,
    duplicateError: duplicateOpportunity.error,
    toggleStatusError: toggleOpportunityStatus.error,
    toggleFeaturedError: toggleOpportunityFeatured.error,

    // Helper function to refresh all opportunity data
    refreshOpportunities: () => {
      queryClient.invalidateQueries({ queryKey: ['sponsorOpportunities'] });
    },
  };
}
