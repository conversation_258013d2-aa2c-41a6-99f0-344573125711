import { useQuery, useQueryClient } from '@tanstack/react-query';
import { endorsementService, EndorsementSummary } from '@/services/endorsement.service';

/**
 * Hook for managing a user's endorsements data
 * @param userId The ID of the user to get endorsements for
 */
export function useEndorsements(userId?: number) {
  const queryClient = useQueryClient();

  // User Endorsements
  const {
    data: endorsements = [],
    isLoading: isLoadingEndorsements,
    error: endorsementsError,
  } = useQuery({
    queryKey: ['endorsements', userId],
    queryFn: async () => {
      if (!userId) return [];

      try {
        const result = await endorsementService.getUserEndorsements(userId);
        return result || [];
      } catch (error) {
        console.error('Error fetching endorsements:', error);
        return []; // Return empty array on error
      }
    },
    enabled: !!userId, // Only run the query if userId is provided
    retry: 1,
  });

  /**
   * Refresh endorsements data
   */
  const refreshEndorsements = () => {
    if (userId) {
      queryClient.invalidateQueries({ queryKey: ['endorsements', userId] });
    }
  };

  // Calculate derived stats
  const endorsementStats = {
    totalEndorsements: endorsements.reduce((sum, category) => sum + category.count, 0),
    categoryCount: endorsements.length,
    mostEndorsedCategory:
      endorsements.length > 0
        ? endorsements.reduce((prev, current) => (prev.count > current.count ? prev : current)).name
        : undefined,
  };

  return {
    // Endorsements
    endorsements,
    isLoadingEndorsements,
    endorsementsError,

    // Stats
    endorsementStats,

    // Utility functions
    refreshEndorsements,
  };
}
