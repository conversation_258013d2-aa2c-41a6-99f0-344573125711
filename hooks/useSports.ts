'use client';

import { useCallback, useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import debounce from 'lodash/debounce';
import type { Sport } from '@/services/positive-athlete-profile.service';
import { searchSports as searchPublicSports } from '@/services/sports.service';

/**
 * Hook for searching platform-curated sports (public sports only).
 * This hook does not include custom sports in the search results.
 * For profile-specific sports search (including custom sports), use the profile service directly.
 */
export function useSports(initialQuery = '') {
  const [searchQuery, setSearchQuery] = useState(initialQuery);

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchQuery(value);
    }, 300),
    []
  );

  useEffect(() => {
    // Cleanup debounced function on unmount
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const {
    data: sports = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ['public-sports-search', searchQuery],
    queryFn: async () => {
      if (!searchQuery || searchQuery.length < 2) {
        return [];
      }
      return searchPublicSports(searchQuery);
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });

  return {
    sports,
    isLoading,
    error,
    searchQuery,
    setSearchInput: debouncedSearch,
  };
}
