import { useState } from 'react';
import { UploadResult } from '@/components/shared/AvatarEditor';

interface UseAvatarUploadOptions {
  uploadFn: (file: File) => Promise<any>;
  onSuccess?: (result: any) => void;
  onError?: (error: Error | string) => void;
}

export function useAvatarUpload({ uploadFn, onSuccess, onError }: UseAvatarUploadOptions) {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string[]> | null>(null);

  const upload = async (file: File): Promise<UploadResult> => {
    try {
      setIsUploading(true);
      setError(null);
      setValidationErrors(null);

      const result = await uploadFn(file);

      if (onSuccess) {
        onSuccess(result);
      }

      return {
        success: true,
        data: result,
      };
    } catch (error: any) {
      let errorMessage = 'Upload failed';
      let errors: Record<string, string[]> | null = null;

      if (error?.response?.status === 422 && error?.response?.data?.errors) {
        // Handle Laravel validation errors
        errors = error.response.data.errors;
        const firstError = Object.values(errors || {})[0];
        errorMessage = Array.isArray(firstError) ? firstError[0] : firstError;
      } else if (error?.response?.data?.message) {
        // Handle other API errors
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        // Handle general errors
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        // Handle string errors
        errorMessage = error;
      }

      setError(errorMessage);
      setValidationErrors(errors);

      if (onError) {
        onError(error);
      }

      return {
        success: false,
        error: errorMessage,
        validationErrors: errors || undefined,
      };
    } finally {
      setIsUploading(false);
    }
  };

  const clearError = () => {
    setError(null);
    setValidationErrors(null);
  };

  return {
    upload,
    isUploading,
    error,
    validationErrors,
    clearError,
  };
}
