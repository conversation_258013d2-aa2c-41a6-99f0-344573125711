import { useCallback, useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import debounce from 'lodash/debounce';
import { schoolService, type School, type SchoolSearchParams } from '@/services/school.service';

interface UseSchoolsOptions {
  countyId?: number;
  stateCode?: string;
}

export function useSchools(options: UseSchoolsOptions = {}) {
  const [searchQuery, setSearchQuery] = useState('');
  const { countyId, stateCode } = options;

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchQuery(value);
    }, 300),
    []
  );

  useEffect(() => {
    // Cleanup debounced function on unmount
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const searchParams: SchoolSearchParams = {
    query: searchQuery,
    county_id: countyId,
    state_code: stateCode,
  };

  const {
    data: schools = [],
    isLoading,
    error: searchError,
    refetch,
  } = useQuery({
    queryKey: ['schools', searchParams],
    queryFn: () => schoolService.searchSchools(searchParams),
    enabled: searchQuery.length > 0 || Boolean(countyId) || Boolean(stateCode),
  });

  // Function to fetch a school by ID
  const fetchSchoolById = async (id: number): Promise<School | null> => {
    try {
      return await schoolService.getSchoolById(id);
    } catch (error) {
      console.error('Error fetching school by ID:', error);
      return null;
    }
  };

  return {
    schools,
    isLoading,
    searchError,
    searchQuery,
    setSearchInput: debouncedSearch,
    fetchSchoolById,
    refetch,
  };
}
