import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { userSettingsService, type ProfileVisibility } from '@/services/user-settings.service';

interface UpdateProfileVisibilityOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export function useUserSettings() {
  const queryClient = useQueryClient();

  // Profile Visibility Management
  const { data: profileVisibility, isLoading: isLoadingProfileVisibility } = useQuery({
    queryKey: ['user', 'settings', 'profile-visibility'],
    queryFn: () => userSettingsService.getProfileVisibility(),
  });

  const updateProfileVisibilityMutation = useMutation({
    mutationFn: (data: ProfileVisibility) => userSettingsService.updateProfileVisibility(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['user', 'settings', 'profile-visibility'],
      });
    },
  });

  const updateProfileVisibility = (isPublic: boolean, options?: UpdateProfileVisibilityOptions) => {
    return updateProfileVisibilityMutation.mutate(
      { public_profile: isPublic },
      {
        onSuccess: options?.onSuccess,
        onError: options?.onError,
      }
    );
  };

  return {
    isProfilePublic: profileVisibility?.public_profile ?? false,
    isLoadingProfileVisibility,
    updateProfileVisibility,
    isUpdatingProfileVisibility: updateProfileVisibilityMutation.isPending,
    profileVisibilityUpdateError: updateProfileVisibilityMutation.error,
  };
}
