'use client';

import { useEffect, useState } from 'react';
import { getAllOpportunities, testMeiliSearchConnection } from '@/config/meilisearch';

interface ConnectionStatus {
  tested: boolean;
  success?: boolean;
  data?: any;
  error?: any;
}

/**
 * Custom hook to test MeiliSearch connection and configure initial setup
 */
export const useMeiliSearchConnectionTest = () => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    tested: false,
  });

  useEffect(() => {
    const testConnection = async () => {
      const result = await testMeiliSearchConnection();
      setConnectionStatus({
        tested: true,
        success: result.success,
        data: result.success ? result.data : undefined,
        error: result.success ? undefined : result.error,
      });

      // If connection is successful, get all opportunities
      if (result.success) {
        console.log('MeiliSearch connection successful');

        // Get all opportunities
        const allOpportunities = await getAllOpportunities();
        console.log('All opportunities data:', allOpportunities.data?.hits);
      }
    };

    testConnection();
  }, []);

  return connectionStatus;
};

export default useMeiliSearchConnectionTest;
