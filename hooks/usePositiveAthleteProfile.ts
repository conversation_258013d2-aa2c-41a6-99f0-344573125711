import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import type {
  Sport,
  UpdateAvatarRequest,
  UpdateInvolvementsRequest,
  UpdateProfileDetailsRequest,
  UpdateProfilePhotosRequest,
  UpdateStoryRequest,
} from '@/services/positive-athlete-profile.service';
import { positiveAthleteProfileService } from '@/services/positive-athlete-profile.service';

interface UpdatePhotosOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

interface UpdateAvatarOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

interface UpdateStoryOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

interface UpdateProfileDetailsOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

interface UpdateInvolvementsOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

interface SearchApiResponse {
  success: boolean;
  data: {
    results: {
      platform: Sport[];
      custom: Sport[];
    };
  };
}

interface UpdateRecruiterOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

/**
 * Hook for managing a Positive Athlete's profile data
 */
export function usePositiveAthleteProfile() {
  const queryClient = useQueryClient();

  // Photos Management
  const { data: photos = [], isLoading: isLoadingPhotos } = useQuery({
    queryKey: ['positive-athlete', 'profile', 'photos'],
    queryFn: () => positiveAthleteProfileService.getProfilePhotos(),
  });

  const updatePhotosMutation = useMutation({
    mutationFn: (data: UpdateProfilePhotosRequest) =>
      positiveAthleteProfileService.updateProfilePhotos(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['positive-athlete', 'profile', 'photos'],
      });
    },
  });

  const updatePhotos = (data: UpdateProfilePhotosRequest, options?: UpdatePhotosOptions) => {
    return updatePhotosMutation.mutate(data, {
      onSuccess: options?.onSuccess,
      onError: options?.onError,
    });
  };

  // Avatar Management
  const { data: avatarUrl = null, isLoading: isLoadingAvatar } = useQuery({
    queryKey: ['positive-athlete', 'profile', 'avatar'],
    queryFn: () => positiveAthleteProfileService.getAvatar(),
  });

  const updateAvatarMutation = useMutation({
    mutationFn: (data: UpdateAvatarRequest) => positiveAthleteProfileService.updateAvatar(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['positive-athlete', 'profile', 'avatar'],
      });
    },
  });

  const updateAvatar = (data: UpdateAvatarRequest, options?: UpdateAvatarOptions) => {
    return updateAvatarMutation.mutateAsync(data).then(
      result => {
        // Call success callback if provided
        options?.onSuccess?.();
        return result;
      },
      error => {
        // Call error callback if provided
        options?.onError?.(error);
        throw error;
      }
    );
  };

  // Story Management
  const { data: story, isLoading: isLoadingStory } = useQuery({
    queryKey: ['positive-athlete', 'profile', 'story'],
    queryFn: () => positiveAthleteProfileService.getStory(),
  });

  const updateStoryMutation = useMutation({
    mutationFn: (data: UpdateStoryRequest) => positiveAthleteProfileService.updateStory(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['positive-athlete', 'profile', 'story'],
      });
    },
  });

  const updateStory = (data: UpdateStoryRequest, options?: UpdateStoryOptions) => {
    return updateStoryMutation.mutate(data, {
      onSuccess: options?.onSuccess,
      onError: options?.onError,
    });
  };

  // Profile Details Management
  const { data: details, isLoading: isLoadingDetails } = useQuery({
    queryKey: ['positive-athlete', 'profile', 'details'],
    queryFn: () => positiveAthleteProfileService.getProfileDetails(),
  });

  const updateDetailsMutation = useMutation({
    mutationFn: (data: UpdateProfileDetailsRequest) =>
      positiveAthleteProfileService.updateProfileDetails(data),
    onSuccess: () => {
      // Invalidate all relevant profile-related queries
      queryClient.invalidateQueries({
        queryKey: ['positive-athlete', 'profile', 'details'],
      });

      // Also invalidate alumni-specific profile caches for backward compatibility
      queryClient.invalidateQueries({
        queryKey: ['college-athlete', 'profile', 'details'],
      });

      queryClient.invalidateQueries({
        queryKey: ['professional', 'profile', 'details'],
      });

      // Invalidate any public profile queries that might exist
      queryClient.invalidateQueries({
        queryKey: ['public-profile'],
      });

      // Invalidate profile store data by refetching profile
      queryClient.invalidateQueries({
        queryKey: ['profile'],
      });
    },
  });

  const updateDetails = (
    data: UpdateProfileDetailsRequest,
    options?: UpdateProfileDetailsOptions
  ) => {
    return updateDetailsMutation.mutate(data, {
      onSuccess: options?.onSuccess,
      onError: options?.onError,
    });
  };

  // Community Involvement Management
  const { data: involvements = [], isLoading: isLoadingInvolvements } = useQuery({
    queryKey: ['positive-athlete', 'profile', 'involvements'],
    queryFn: () => positiveAthleteProfileService.getCommunityInvolvements(),
  });

  const updateInvolvementsMutation = useMutation({
    mutationFn: (data: UpdateInvolvementsRequest) =>
      positiveAthleteProfileService.updateCommunityInvolvements(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['positive-athlete', 'profile', 'involvements'],
      });
    },
  });

  const updateInvolvements = (
    data: UpdateInvolvementsRequest,
    options?: UpdateInvolvementsOptions
  ) => {
    return updateInvolvementsMutation.mutateAsync(data).then(
      result => {
        // Call success callback if provided
        options?.onSuccess?.();
        return result;
      },
      error => {
        // Call error callback if provided
        options?.onError?.(error);
        throw error;
      }
    );
  };

  const {
    data: workExperiences = [],
    isLoading: isLoadingWorkExperiences,
    error: workExperiencesError,
  } = useQuery({
    queryKey: ['profile', 'work-experiences'],
    queryFn: () => positiveAthleteProfileService.getWorkExperiences(),
  });

  const {
    mutate: updateWorkExperiences,
    isPending: isUpdatingWorkExperiences,
    error: workExperiencesUpdateError,
  } = useMutation({
    mutationFn: positiveAthleteProfileService.updateWorkExperiences,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['profile', 'work-experiences'] });
    },
  });

  // Sports Management
  const { data: sports = [], isLoading: isLoadingSports } = useQuery({
    queryKey: ['positive-athlete', 'profile', 'sports'],
    queryFn: () => positiveAthleteProfileService.getSports(),
  });

  const updateSportsMutation = useMutation({
    mutationFn: positiveAthleteProfileService.updateSports,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['positive-athlete', 'profile', 'sports'] });
    },
  });

  const searchSports = async (query: string) => {
    if (!query || query.length < 2) return { platform: [], custom: [] };
    try {
      const response = await positiveAthleteProfileService.searchSports(query);
      return response; // The service already returns the correct format
    } catch (error) {
      console.error('Search failed:', error);
      return { platform: [], custom: [] };
    }
  };

  // Recruiter Management
  const { data: recruiterStatus, isLoading: isLoadingRecruiter } = useQuery({
    queryKey: ['positive-athlete', 'profile', 'recruiter'],
    queryFn: () => positiveAthleteProfileService.getRecruiterStatus(),
  });

  const updateRecruiterMutation = useMutation({
    mutationFn: (data: { enabled: boolean }) =>
      positiveAthleteProfileService.updateRecruiterStatus(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['positive-athlete', 'profile', 'recruiter'],
      });
    },
  });

  const updateRecruiter = (enabled: boolean, options?: UpdateRecruiterOptions) => {
    return updateRecruiterMutation.mutate(
      { enabled },
      {
        onSuccess: options?.onSuccess,
        onError: options?.onError,
      }
    );
  };

  return {
    photos,
    isLoadingPhotos,
    updatePhotos,
    isUpdatingPhotos: updatePhotosMutation.isPending,
    photoUpdateError: updatePhotosMutation.error,

    avatarUrl,
    isLoadingAvatar,
    updateAvatar,
    isUpdatingAvatar: updateAvatarMutation.isPending,
    avatarUpdateError: updateAvatarMutation.error,

    story,
    isLoadingStory,
    updateStory,
    isUpdatingStory: updateStoryMutation.isPending,
    storyUpdateError: updateStoryMutation.error,

    details,
    isLoadingDetails,
    updateDetails,
    isUpdatingDetails: updateDetailsMutation.isPending,
    detailsUpdateError: updateDetailsMutation.error,

    involvements,
    isLoadingInvolvements,
    updateInvolvements,
    isUpdatingInvolvements: updateInvolvementsMutation.isPending,
    involvementsUpdateError: updateInvolvementsMutation.error,

    workExperiences,
    isLoadingWorkExperiences,
    workExperiencesError,
    updateWorkExperiences,
    isUpdatingWorkExperiences,
    workExperiencesUpdateError,

    sports,
    isLoadingSports,
    searchSports,
    updateSports: updateSportsMutation.mutate,
    isUpdatingSports: updateSportsMutation.isPending,
    sportsUpdateError: updateSportsMutation.error,

    recruiterEnabled: recruiterStatus?.enabled ?? false,
    isLoadingRecruiter,
    updateRecruiter,
    isUpdatingRecruiter: updateRecruiterMutation.isPending,
    recruiterUpdateError: updateRecruiterMutation.error,
  };
}
