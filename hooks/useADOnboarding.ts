import { useMutation } from '@tanstack/react-query';
import type { AxiosError, AxiosResponse } from 'axios';
import { useADOnboardingStore } from '@/stores/aDOnboardingStore';
import { useSystemInviteStore } from '@/stores/systemInviteStore';
import { useToastNotify } from '@/stores/toastNotify.store';
import { useWizardStore } from '@/stores/wizardStore';
import type {
  ADAccountInfoPayload,
  ADBioPayload,
  ADCommunityInvolvementPayload,
  ADDetailsFormData,
  ADDetailsPayload,
  ADOnboardingStepResponse,
  ADSchoolSuccessesPayload,
} from '../services/aDOnboarding.service';
import { aDOnboardingService } from '../services/aDOnboarding.service';

interface UseADOnboardingProps {
  onSuccess?: (
    response: ADOnboardingStepResponse | { redirect?: string; message?: string }
  ) => void;
  onError?: (error: unknown) => void;
}

export function useADOnboarding({ onSuccess, onError }: UseADOnboardingProps = {}) {
  const { toastNotify } = useToastNotify();
  const resetADOnboardingStore = useADOnboardingStore(state => state.reset);
  const clearInviteData = useSystemInviteStore(state => state.clearInviteData);
  const resetWizard = useWizardStore(state => state.resetWizard);

  const startADOnboarding = useMutation<AxiosResponse<ADOnboardingStepResponse>, AxiosError, void>({
    mutationFn: () => aDOnboardingService.startADOnboarding(),
    onSuccess: (response: AxiosResponse<ADOnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitADAccountInfo = useMutation<
    AxiosResponse<ADOnboardingStepResponse>,
    AxiosError,
    ADAccountInfoPayload
  >({
    mutationFn: (data: ADAccountInfoPayload) => aDOnboardingService.submitADAccountInfo(data),
    onSuccess: (response: AxiosResponse<ADOnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitADDetails = useMutation<
    AxiosResponse<ADOnboardingStepResponse>,
    AxiosError,
    ADDetailsPayload | ADDetailsFormData
  >({
    mutationFn: (data: ADDetailsPayload | ADDetailsFormData) =>
      aDOnboardingService.submitADDetails(data),
    onSuccess: (response: AxiosResponse<ADOnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitADBio = useMutation<
    AxiosResponse<ADOnboardingStepResponse>,
    AxiosError,
    ADBioPayload
  >({
    mutationFn: (data: ADBioPayload) => aDOnboardingService.submitADBio(data),
    onSuccess: (response: AxiosResponse<ADOnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const submitADSchoolSuccesses = useMutation<
    AxiosResponse<ADOnboardingStepResponse>,
    AxiosError,
    ADSchoolSuccessesPayload
  >({
    mutationFn: (data: ADSchoolSuccessesPayload) =>
      aDOnboardingService.submitADSchoolSuccesses(data),
    onSuccess: (response: AxiosResponse<ADOnboardingStepResponse>) => {
      if (onSuccess) {
        onSuccess(response.data);
      }
    },
    onError: (error: AxiosError<any>) => {
      if (error.response?.data?.message) {
        toastNotify(error.response.data.message, 'error');
      } else {
        toastNotify('An error occurred while saving school successes.', 'error');
      }
      onError?.(error);
    },
  });

  const submitADCommunityInvolvement = useMutation<
    AxiosResponse<ADOnboardingStepResponse>,
    AxiosError,
    ADCommunityInvolvementPayload
  >({
    mutationFn: (data: ADCommunityInvolvementPayload) =>
      aDOnboardingService.submitADCommunityInvolvement(data),
    onSuccess: (response: AxiosResponse<ADOnboardingStepResponse>) => onSuccess?.(response.data),
    onError,
  });

  const completeADOnboarding = useMutation<
    AxiosResponse<ADOnboardingStepResponse>,
    AxiosError,
    void
  >({
    mutationFn: () => aDOnboardingService.completeADOnboarding(),
    onSuccess: (response: AxiosResponse<ADOnboardingStepResponse>) => {
      resetADOnboardingStore();
      clearInviteData();
      resetWizard();

      if (onSuccess) {
        onSuccess({ ...response.data, message: 'Onboarding completed successfully!' });
      }
    },
    onError: (error: AxiosError<any>) => {
      if (error.response?.data?.message) {
        toastNotify(error.response.data.message, 'error');
      } else {
        toastNotify('An error occurred while completing onboarding.', 'error');
      }
      onError?.(error);
    },
  });

  return {
    startADOnboarding,
    submitADAccountInfo,
    submitADDetails,
    submitADBio,
    submitADSchoolSuccesses,
    submitADCommunityInvolvement,
    completeADOnboarding,
  };
}
