import { useMutation, useQuery } from '@tanstack/react-query';
import type { AxiosError, AxiosResponse } from 'axios';
import {
  alumniOnboardingService,
  type AlumniAccountInfoPayload,
  type AlumniCollegeDetailsPayload,
  type AlumniLifeStagePayload,
  type AlumniOnboardingStepResponse,
  type AlumniProfessionalDetailsPayload,
} from '@/services/alumni-onboarding.service';
import { useAlumniOnboardingStore } from '@/stores/alumniOnboardingStore';
import { useSystemInviteStore } from '@/stores/systemInviteStore';
import { useToastNotify } from '@/stores/toastNotify.store';
import { useWizardStore } from '@/stores/wizardStore';

interface UseAlumniOnboardingProps {
  onSuccess?: (response: AlumniOnboardingStepResponse | { redirect: string }) => void;
  onError?: (error: AxiosError<any>) => void; // Specify AxiosError type
}

export function useAlumniOnboarding({ onSuccess, onError }: UseAlumniOnboardingProps = {}) {
  const queryClientKey = ['alumniOnboardingNextStep'];
  const { toastNotify } = useToastNotify();

  const getNextStepQuery = useQuery<AlumniOnboardingStepResponse, AxiosError>({
    queryKey: queryClientKey,
    queryFn: async () => {
      const response = await alumniOnboardingService.getNextStep();
      return response.data;
    },
    enabled: false, // Typically, you'd enable this when needed, e.g., on component mount
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const submitLifeStageMutation = useMutation<
    AxiosResponse<AlumniOnboardingStepResponse>,
    AxiosError,
    AlumniLifeStagePayload
  >({
    mutationFn: (data: AlumniLifeStagePayload) => alumniOnboardingService.submitLifeStage(data),
    onSuccess: (response: AxiosResponse<AlumniOnboardingStepResponse>) =>
      onSuccess?.(response.data),
    onError,
  });

  const submitAccountInfoMutation = useMutation<
    AxiosResponse<AlumniOnboardingStepResponse>,
    AxiosError,
    AlumniAccountInfoPayload
  >({
    mutationFn: (data: AlumniAccountInfoPayload) => alumniOnboardingService.submitAccountInfo(data),
    onSuccess: (response: AxiosResponse<AlumniOnboardingStepResponse>) =>
      onSuccess?.(response.data),
    onError,
  });

  const submitCollegeDetailsMutation = useMutation<
    AxiosResponse<AlumniOnboardingStepResponse>,
    AxiosError,
    AlumniCollegeDetailsPayload | FormData
  >({
    mutationFn: (data: AlumniCollegeDetailsPayload | FormData) =>
      alumniOnboardingService.submitCollegeDetails(data),
    onSuccess: (response: AxiosResponse<AlumniOnboardingStepResponse>) =>
      onSuccess?.(response.data),
    onError,
  });

  const submitProfessionalDetailsMutation = useMutation<
    AxiosResponse<AlumniOnboardingStepResponse>,
    AxiosError,
    AlumniProfessionalDetailsPayload | FormData
  >({
    mutationFn: (data: AlumniProfessionalDetailsPayload | FormData) =>
      alumniOnboardingService.submitProfessionalDetails(data),
    onSuccess: (response: AxiosResponse<AlumniOnboardingStepResponse>) =>
      onSuccess?.(response.data),
    onError,
  });

  const resetAlumniOnboardingStore = useAlumniOnboardingStore(state => state.reset);
  const clearInviteData = useSystemInviteStore(state => state.clearInviteData);
  const resetWizard = useWizardStore(state => state.resetWizard);

  const completeOnboardingMutation = useMutation<
    AxiosResponse<{ redirect: string }>,
    AxiosError<any>
  >({
    mutationFn: () => alumniOnboardingService.completeOnboarding(),
    onSuccess: (response: AxiosResponse<{ redirect: string }>) => {
      resetAlumniOnboardingStore();
      clearInviteData();
      resetWizard();
      onSuccess?.(response.data);
    },
    onError: (error: AxiosError<any>) => {
      if (error.response?.data?.message) {
        toastNotify(error.response.data.message, 'error');
      } else {
        toastNotify('An unexpected error occurred during completion.', 'error');
      }
      onError?.(error);
    },
  });

  return {
    getNextStep: getNextStepQuery,
    submitLifeStage: submitLifeStageMutation,
    submitAccountInfo: submitAccountInfoMutation,
    submitCollegeDetails: submitCollegeDetailsMutation,
    submitProfessionalDetails: submitProfessionalDetailsMutation,
    completeOnboarding: completeOnboardingMutation,
  };
}
