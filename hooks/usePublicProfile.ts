import { useQuery } from '@tanstack/react-query';
import type {
  PublicAvatar,
  PublicCareerInterest,
  PublicCareerInterests,
  PublicDetails,
  PublicInvolvements,
  PublicPhotos,
  PublicProfile,
  PublicSports,
  PublicStory,
  PublicWorkExperiences,
  UserAchievements,
} from '@/services/public-profile.service';
import { publicProfileService } from '@/services/public-profile.service';
import { ProfileType } from '@/stores/auth.store';

/**
 * Hook for accessing a public profile data
 * @param userId The ID of the user whose profile to fetch
 */
export function usePublicProfile(userId: string) {
  // Basic Profile
  const {
    data: profile,
    isLoading: isLoadingProfile,
    error: profileError,
  } = useQuery({
    queryKey: ['public-profile', userId, 'basic'],
    queryFn: () => publicProfileService.getProfile(userId),
    enabled: !!userId,
  });

  // Profile Details
  const {
    data: details,
    isLoading: isLoadingDetails,
    error: detailsError,
  } = useQuery({
    queryKey: ['public-profile', userId, 'details'],
    queryFn: () => publicProfileService.getProfileDetails(userId),
    enabled: !!userId,
  });

  // Sports
  const {
    data: sports,
    isLoading: isLoadingSports,
    error: sportsError,
  } = useQuery({
    queryKey: ['public-profile', userId, 'sports'],
    queryFn: () => publicProfileService.getSports(userId),
    enabled: !!userId,
  });

  // Story
  const {
    data: story,
    isLoading: isLoadingStory,
    error: storyError,
  } = useQuery({
    queryKey: ['public-profile', userId, 'story'],
    queryFn: () => publicProfileService.getStory(userId),
    enabled: !!userId,
  });

  // Involvements
  const {
    data: involvements,
    isLoading: isLoadingInvolvements,
    error: involvementsError,
  } = useQuery({
    queryKey: ['public-profile', userId, 'involvements'],
    queryFn: () => publicProfileService.getInvolvements(userId),
    enabled: !!userId,
  });

  // Avatar
  const {
    data: avatar,
    isLoading: isLoadingAvatar,
    error: avatarError,
  } = useQuery({
    queryKey: ['public-profile', userId, 'avatar'],
    queryFn: () => publicProfileService.getAvatar(userId),
    enabled: !!userId,
  });

  // Photos
  const {
    data: photos,
    isLoading: isLoadingPhotos,
    error: photosError,
  } = useQuery({
    queryKey: ['public-profile', userId, 'photos'],
    queryFn: () => publicProfileService.getPhotos(userId),
    enabled: !!userId,
  });

  // Career Interests
  const {
    data: careerInterests,
    isLoading: isLoadingCareerInterests,
    error: careerInterestsError,
  } = useQuery({
    queryKey: ['public-profile', userId, 'career-interests'],
    queryFn: () => publicProfileService.getCareerInterests(userId),
    enabled: !!userId,
  });

  // Work Experiences
  const {
    data: workExperiences,
    isLoading: isLoadingWorkExperiences,
    error: workExperiencesError,
  } = useQuery({
    queryKey: ['public-profile', userId, 'work-experiences'],
    queryFn: () => publicProfileService.getWorkExperiences(userId),
    enabled: !!userId,
  });

  // User Achievements
  const {
    data: achievements,
    isLoading: isLoadingAchievements,
    error: achievementsError,
  } = useQuery<UserAchievements>({
    queryKey: ['public-profile', userId, 'achievements'],
    queryFn: () => publicProfileService.getUserAchievements(userId),
    enabled: !!userId,
  });

  // Loading state for all data
  const isLoading =
    isLoadingProfile ||
    isLoadingDetails ||
    isLoadingSports ||
    isLoadingStory ||
    isLoadingInvolvements ||
    isLoadingAvatar ||
    isLoadingPhotos ||
    isLoadingCareerInterests ||
    isLoadingWorkExperiences ||
    isLoadingAchievements;

  // Error state for any data
  const hasError =
    !!profileError ||
    !!detailsError ||
    !!sportsError ||
    !!storyError ||
    !!involvementsError ||
    !!avatarError ||
    !!photosError ||
    !!careerInterestsError ||
    !!workExperiencesError ||
    !!achievementsError;

  return {
    // Profile data
    profile,
    details,
    sports: sports?.sports || [],
    story,
    involvements: involvements?.involvements || [],
    avatar,
    photos: photos?.photos || [],
    careerInterests: careerInterests?.interests || [],
    workExperiences: workExperiences?.experiences || [],
    profileType: profile?.profile_type as ProfileType,
    achievements,

    // Loading states
    isLoading,
    isLoadingProfile,
    isLoadingDetails,
    isLoadingSports,
    isLoadingStory,
    isLoadingInvolvements,
    isLoadingAvatar,
    isLoadingPhotos,
    isLoadingCareerInterests,
    isLoadingWorkExperiences,
    isLoadingAchievements,

    // Error states
    hasError,
    profileError,
    detailsError,
    sportsError,
    storyError,
    involvementsError,
    avatarError,
    photosError,
    careerInterestsError,
    workExperiencesError,
    achievementsError,
  };
}
