<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('nominations', function (Blueprint $table) {
            // Change grade column from integer to string to support values like "Sophomore", "Junior", etc.
            $table->string('grade')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('nominations', function (Blueprint $table) {
            // Revert back to integer (this might cause data loss if non-numeric values exist)
            $table->integer('grade')->nullable()->change();
        });
    }
};
