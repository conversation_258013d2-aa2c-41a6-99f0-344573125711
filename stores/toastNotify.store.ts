import { create } from 'zustand';

export type ToastType = 'success' | 'info' | 'warning' | 'error';

interface ToastNotifyState {
  // Toast state
  isVisible: boolean;
  message: string;
  type: ToastType;
  timeout: NodeJS.Timeout | null;

  // Actions
  toastNotify: (message: string, type: ToastType, duration?: number) => void;
  closeToastNotify: () => void;
}

export const useToastNotify = create<ToastNotifyState>((set, get) => ({
  // Initial state
  isVisible: false,
  message: '',
  type: 'info',
  timeout: null,

  // Show a toast notification
  toastNotify: (message, type = 'info', duration = 5000) => {
    // Clear any existing timeout to prevent memory leaks
    const { timeout } = get();
    if (timeout) {
      clearTimeout(timeout);
    }

    // Set up a new timeout for auto-dismissal
    const newTimeout = setTimeout(() => {
      set({ isVisible: false, timeout: null });
    }, duration);

    // Update state with new toast data
    set({
      isVisible: true,
      message,
      type,
      timeout: newTimeout,
    });
  },

  // Close the toast notification manually
  closeToastNotify: () => {
    const { timeout } = get();
    if (timeout) {
      clearTimeout(timeout);
    }

    set({
      isVisible: false,
      timeout: null,
    });
  },
}));
