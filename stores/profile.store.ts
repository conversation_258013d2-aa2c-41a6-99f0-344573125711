import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User } from '@/types/auth';

interface ParentLinkedAthlete {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  recruiter_enabled: boolean;
}

interface ProfileState {
  id: number | null;
  email: string | null;
  firstName: string | null;
  lastName: string | null;
  profileType: string | null;
  athlete?: ParentLinkedAthlete | null;
  setProfile: (user: User | null) => void;
  clearProfile: () => void;
}

export const useProfileStore = create<ProfileState>()(
  persist(
    set => ({
      id: null,
      email: null,
      firstName: null,
      lastName: null,
      profileType: null,
      setProfile: (user: User | null) => {
        if (!user) {
          set({
            id: null,
            email: null,
            firstName: null,
            lastName: null,
            profileType: null,
          });
          return;
        }

        set({
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          profileType: user.profile_type,
        });
      },
      clearProfile: () => {
        set({
          id: null,
          email: null,
          firstName: null,
          lastName: null,
          profileType: null,
        });
      },
    }),
    {
      name: 'profile-storage',
    }
  )
);
