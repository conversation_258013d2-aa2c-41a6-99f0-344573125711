import { create } from 'zustand';

// Define the section names for type safety
export enum AccordionSection {
  NETWORK = 'network',
  OPPORTUNITIES = 'opportunities',
}

// Define the store state
interface AccordionState {
  // Store accordion states as arrays for each section
  openAccordions: Record<AccordionSection, string[]>;
  isAccordionOpen: (section: AccordionSection, accordionTitle: string) => boolean;
  toggleAccordion: (section: AccordionSection, accordionTitle: string) => void;
  setAccordionOpen: (section: AccordionSection, accordionTitle: string, isOpen: boolean) => void;
  resetSection: (section: AccordionSection) => void;
}

// Create the Zustand store without persistence
export const useAccordionStore = create<AccordionState>((set, get) => ({
  // Initialize with default state
  openAccordions: {
    [AccordionSection.NETWORK]: [], // Start with none open by default
    [AccordionSection.OPPORTUNITIES]: [], // Start with none open by default
  },

  // Check if an accordion is open
  isAccordionOpen: (section, accordionTitle) => {
    return get().openAccordions[section].includes(accordionTitle);
  },

  // Toggle an accordion's open state
  toggleAccordion: (section, accordionTitle) => {
    set(state => {
      const currentAccordions = [...state.openAccordions[section]];
      const index = currentAccordions.indexOf(accordionTitle);

      // If found, remove it; otherwise add it
      if (index !== -1) {
        currentAccordions.splice(index, 1);
      } else {
        currentAccordions.push(accordionTitle);
      }

      return {
        openAccordions: {
          ...state.openAccordions,
          [section]: currentAccordions,
        },
      };
    });
  },

  // Set an accordion's open state explicitly
  setAccordionOpen: (section, accordionTitle, isOpen) => {
    set(state => {
      const currentAccordions = [...state.openAccordions[section]];
      const index = currentAccordions.indexOf(accordionTitle);

      // If should be open but not found, add it
      if (isOpen && index === -1) {
        currentAccordions.push(accordionTitle);
      }
      // If should be closed but found, remove it
      else if (!isOpen && index !== -1) {
        currentAccordions.splice(index, 1);
      }

      return {
        openAccordions: {
          ...state.openAccordions,
          [section]: currentAccordions,
        },
      };
    });
  },

  // Reset a section to default state
  resetSection: section => {
    set(state => ({
      openAccordions: {
        ...state.openAccordions,
        [section]: [], // Reset to empty
      },
    }));
  },
}));
