import { format } from 'date-fns';
import { create } from 'zustand';

/**
 * Types of opportunity impressions we track
 */
export enum ImpressionType {
  VIEW = 'view', // Opportunity was displayed in a list
  CLICK = 'click', // User clicked "Learn More" on an opportunity
  ACTION = 'action', // User clicked the "Apply" CTA button
}

/**
 * Structure of an impression event
 */
interface ImpressionEvent {
  opportunityId: string;
  type: ImpressionType;
  timestamp: number;
  formattedTimestamp: string;
}

interface OpportunityImpressionsState {
  // Track which opportunities have already been viewed to avoid duplicate view events
  viewedOpportunities: Set<string>;

  // Methods for tracking different impression types
  trackView: (opportunityId: string) => void;
  trackClick: (opportunityId: string) => void;
  trackAction: (opportunityId: string) => void;

  // Internal method that will be replaced with actual API calls
  trackImpression: (opportunityId: string, type: ImpressionType) => void;
}

export const useOpportunityImpressions = create<OpportunityImpressionsState>((set, get) => ({
  viewedOpportunities: new Set<string>(),

  trackView: (opportunityId: string) => {
    const { viewedOpportunities, trackImpression } = get();

    // Only track view once per session to avoid duplicate counts
    if (!viewedOpportunities.has(opportunityId)) {
      // Add to viewed set
      set(state => {
        const newSet = new Set(state.viewedOpportunities);
        newSet.add(opportunityId);
        return { viewedOpportunities: newSet };
      });

      // Track the impression
      trackImpression(opportunityId, ImpressionType.VIEW);
    }
  },

  trackClick: (opportunityId: string) => {
    const { trackImpression } = get();
    trackImpression(opportunityId, ImpressionType.CLICK);
  },

  trackAction: (opportunityId: string) => {
    const { trackImpression } = get();
    trackImpression(opportunityId, ImpressionType.ACTION);
  },

  trackImpression: (opportunityId: string, type: ImpressionType) => {
    // Get current timestamp
    const now = new Date();

    // Create the impression event
    const event: ImpressionEvent = {
      opportunityId,
      type,
      timestamp: now.getTime(),
      formattedTimestamp: format(now, 'yyyy-MM-dd HH:mm:ss'),
    };

    // For now, just log to console
    console.log('Tracked opportunity impression:', event);

    // TODO: When backend is ready, replace with actual API call
    // Example:
    // api.post('/opportunity-impressions', event);
  },
}));
