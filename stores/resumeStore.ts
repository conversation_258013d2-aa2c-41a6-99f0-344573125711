import { create } from 'zustand';
import { SportEntry } from '@/types/profile';

interface ResumeItem {
  id: string;
  title: string;
  dateRange: string;
  description: string;
}

interface ResumeState {
  bio: string;
  contact: {
    email: string;
    phone: string;
    location: string;
  };
  education: Array<{
    id: string;
    schoolName: string;
    years: string;
    gpa: string;
    classRank: string;
  }>;
  involvement: ResumeItem[];
  workExperience: ResumeItem[];
  sports: SportEntry[];
  enabledSections: {
    education: boolean;
    involvement: boolean;
    workExperience: boolean;
    sports: boolean;
  };
  setBio: (bio: string) => void;
  setContact: (contact: Partial<ResumeState['contact']>) => void;
  addEducation: (item: Omit<ResumeState['education'][0], 'id'>) => void;
  updateEducation: (id: string, updates: Partial<Omit<ResumeState['education'][0], 'id'>>) => void;
  removeEducation: (id: string) => void;
  updateInvolvement: (items: ResumeItem[]) => void;
  updateWorkExperience: (items: ResumeItem[]) => void;
  updateSports: (sports: SportEntry[]) => void;
  toggleSection: (section: keyof ResumeState['enabledSections']) => void;
  resetAll: () => void;
}

const initialState: Omit<
  ResumeState,
  | 'setBio'
  | 'setContact'
  | 'addEducation'
  | 'updateEducation'
  | 'removeEducation'
  | 'updateInvolvement'
  | 'updateWorkExperience'
  | 'updateSports'
  | 'toggleSection'
  | 'resetAll'
> = {
  bio: '',
  contact: {
    email: '',
    phone: '',
    location: '',
  },
  education: [],
  involvement: [],
  workExperience: [],
  sports: [],
  enabledSections: {
    education: true,
    involvement: true,
    workExperience: true,
    sports: true,
  },
};

export const useResumeStore = create<ResumeState>((set, get) => ({
  ...initialState,

  setBio: bio => set({ bio }),

  setContact: contact =>
    set(state => ({
      contact: { ...state.contact, ...contact },
    })),

  addEducation: item =>
    set(state => ({
      education: [...state.education, { ...item, id: Math.random().toString(36).substr(2, 9) }],
    })),

  updateEducation: (id, updates) =>
    set(state => ({
      education: state.education.map(item => (item.id === id ? { ...item, ...updates } : item)),
    })),

  removeEducation: id =>
    set(state => ({
      education: state.education.filter(item => item.id !== id),
    })),

  updateInvolvement: items => set({ involvement: items }),

  updateWorkExperience: items => set({ workExperience: items }),

  updateSports: sports => set({ sports }),

  toggleSection: section =>
    set(state => ({
      enabledSections: {
        ...state.enabledSections,
        [section]: !state.enabledSections[section],
      },
    })),

  resetAll: () =>
    set(() => ({
      ...initialState,
    })),
}));
