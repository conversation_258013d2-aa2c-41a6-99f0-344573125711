import { create } from 'zustand';
import type { Question, Test, TestAttempt } from '@/types/quiz';

interface QuizState {
  // Quiz Data
  test: Test | null;
  testAttempt: TestAttempt | null;

  // Navigation State
  currentQuestionIndex: number;
  visitedQuestions: Set<number>;

  // User Responses
  responses: Record<number, number>; // questionId -> answerId

  // Quiz Status
  isComplete: boolean;

  // Actions
  setTest: (test: Test) => void;
  startAttempt: (attempt: TestAttempt) => void;
  selectAnswer: (questionId: number, answerId: number) => void;
  nextQuestion: () => void;
  previousQuestion: () => void;
  setCurrentQuestion: (index: number) => void;
  completeQuiz: () => { responses: Array<{ questionId: number; answerId: number }> } | null;
  reset: () => void;
}

export const useQuizStore = create<QuizState>((set, get) => ({
  // Initial State
  test: null,
  testAttempt: null,
  currentQuestionIndex: 0,
  visitedQuestions: new Set(),
  responses: {},
  isComplete: false,

  // Actions
  setTest: (test: Test) => {
    set({
      test,
      currentQuestionIndex: 0,
      visitedQuestions: new Set(),
      responses: {},
      isComplete: false,
    });
  },

  startAttempt: (attempt: TestAttempt) => {
    set({ testAttempt: attempt });
  },

  selectAnswer: (questionId: number, answerId: number) => {
    set(state => {
      // Mark question as visited
      const visitedQuestions = new Set(state.visitedQuestions);
      visitedQuestions.add(questionId);

      return {
        responses: {
          ...state.responses,
          [questionId]: answerId,
        },
        visitedQuestions,
      };
    });
  },

  nextQuestion: () => {
    const { currentQuestionIndex, test } = get();
    if (test && currentQuestionIndex < test.questions.length - 1) {
      set({ currentQuestionIndex: currentQuestionIndex + 1 });
    }
  },

  previousQuestion: () => {
    const { currentQuestionIndex } = get();
    if (currentQuestionIndex > 0) {
      set({ currentQuestionIndex: currentQuestionIndex - 1 });
    }
  },

  setCurrentQuestion: (index: number) => {
    const { test } = get();
    if (test && index >= 0 && index < test.questions.length) {
      set({ currentQuestionIndex: index });
    }
  },

  completeQuiz: () => {
    const { test, responses } = get();

    if (!test) {
      console.error('Cannot complete quiz: missing test data');
      return null;
    }

    // Prepare submission payload - only sending responses array
    const submissionData = {
      responses: Object.entries(responses).map(([questionId, answerId]) => ({
        questionId: parseInt(questionId),
        answerId: parseInt(answerId as unknown as string),
      })),
    };

    set({
      isComplete: true,
      testAttempt: {
        ...get().testAttempt!,
        completedAt: new Date().toISOString(),
        status: 'complete',
      },
    });

    return submissionData;
  },

  reset: () => {
    set({
      test: null,
      testAttempt: null,
      currentQuestionIndex: 0,
      visitedQuestions: new Set(),
      responses: {},
      isComplete: false,
    });
  },
}));
