import { create } from 'zustand';

interface WorkExperienceItem {
  id: string;
  title: string;
  dateRange: string;
  description: string;
}

interface WorkExperienceState {
  isEditing: boolean;
  items: WorkExperienceItem[];
  initialItems: WorkExperienceItem[];
  isDirty: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  startEditing: () => void;
  cancelEditing: () => void;
  updateItems: (items: WorkExperienceItem[]) => void;
  addItem: () => void;
  removeItem: (id: string) => void;
  updateItem: (id: string, updates: Partial<WorkExperienceItem>) => void;
  saveItems: (userId: string) => Promise<void>;
}

// Mock data store
let mockWorkExperienceData: Record<string, WorkExperienceItem[]> = {};

const createNewItem = (): WorkExperienceItem => ({
  id: Math.random().toString(36).substring(2),
  title: '',
  dateRange: '',
  description: '',
});

export const useWorkExperienceStore = create<WorkExperienceState>((set, get) => ({
  isEditing: false,
  items: [],
  initialItems: [],
  isDirty: false,
  isLoading: false,
  error: null,

  startEditing: () => set({ isEditing: true }),

  cancelEditing: () =>
    set(state => ({
      isEditing: false,
      isDirty: false,
      error: null,
      items: [...state.initialItems], // Restore initial items
    })),

  updateItems: (items: WorkExperienceItem[]) =>
    set(state => ({
      items,
      initialItems: state.isEditing ? state.initialItems : items,
      isDirty: JSON.stringify(items) !== JSON.stringify(state.initialItems),
    })),

  addItem: () =>
    set(state => {
      const newItems = [...state.items, createNewItem()];
      return {
        items: newItems,
        isDirty: JSON.stringify(newItems) !== JSON.stringify(state.initialItems),
      };
    }),

  removeItem: (id: string) =>
    set(state => {
      const newItems = state.items.filter(item => item.id !== id);
      return {
        items: newItems,
        isDirty: JSON.stringify(newItems) !== JSON.stringify(state.initialItems),
      };
    }),

  updateItem: (id: string, updates: Partial<WorkExperienceItem>) =>
    set(state => {
      const newItems = state.items.map(item => (item.id === id ? { ...item, ...updates } : item));
      return {
        items: newItems,
        isDirty: JSON.stringify(newItems) !== JSON.stringify(state.initialItems),
      };
    }),

  saveItems: async (userId: string) => {
    set({ isLoading: true, error: null });

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update mock data
      mockWorkExperienceData[userId] = get().items;

      set(state => ({
        isLoading: false,
        isEditing: false,
        isDirty: false,
        initialItems: [...state.items], // Update initial items after successful save
      }));

      // Log for development
      console.log('Saved work experience data to mock:', {
        userId,
        items: mockWorkExperienceData[userId],
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to save work experience data',
      });
    }
  },
}));
