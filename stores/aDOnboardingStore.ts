import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

// Assuming these specific DTOs will be defined in a types file or within the service
// For now, we'll define simplified versions here or rely on service file definitions.

// Payload for Account Information
interface ADAccountInfoData {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  password?: string;
  notification_email?: string;
  // Add address fields if the generic AccountInfoDTO from backend is strictly followed for ADs
  // street_address?: string;
  // unit?: string;
  // city?: string;
  // state_address?: string; // to avoid conflict with 'state' for school/county context
  // zip_code?: string;
}

// Payload for Details (title, school, photo, socials)
interface ADDetailsData {
  title?: string;
  state?: string; // State for school location
  county?: string; // County for school location
  school_id?: number;
  profile_photo_url?: string | null; // For client-side preview
  // Social Links
  twitter?: string;
  instagram?: string;
  facebook?: string;
  hudl?: string;
  custom_link?: string;
}

// Payload for Bio (text content only)
interface ADBioData {
  content?: string;
}

// Structure for individual success items
interface SuccessItem {
  name: string;
  date_range: string;
  description: string;
  order: number;
}

// Payload for School Successes
interface ADSchoolSuccessesData {
  successes: SuccessItem[];
}

// Structure for individual involvement items (reusing Positive Athlete's definition if identical)
interface InvolvementItem {
  name: string;
  date_range: string;
  description: string;
  order: number;
}

// Payload for Community Involvement
interface ADCommunityInvolvementData {
  items: InvolvementItem[];
}

interface ADOnboardingState {
  currentStep: string;
  accountInfo: Partial<ADAccountInfoData>;
  detailsData: Partial<ADDetailsData>;
  bioData: Partial<ADBioData>;
  schoolSuccesses: Partial<ADSchoolSuccessesData>;
  communityInvolvement: Partial<ADCommunityInvolvementData>;
  errors: Record<string, string[]> | null;

  setCurrentStep: (step: string) => void;
  setAccountInfo: (data: Partial<ADAccountInfoData>) => void;
  setDetailsData: (data: Partial<ADDetailsData>) => void;
  setBioData: (data: Partial<ADBioData>) => void;
  setSchoolSuccesses: (data: Partial<ADSchoolSuccessesData>) => void;
  setCommunityInvolvement: (data: Partial<ADCommunityInvolvementData>) => void;
  setErrors: (errors: { message?: string; errors: Record<string, string[]> }) => void;
  clearErrors: () => void;
  reset: () => void;
}

const initialState = {
  currentStep: '',
  accountInfo: {},
  detailsData: { profile_photo_url: null },
  bioData: { content: '' },
  schoolSuccesses: { successes: [] as SuccessItem[] },
  communityInvolvement: { items: [] as InvolvementItem[] },
  errors: null,
};

export const useADOnboardingStore = create<ADOnboardingState>()(
  persist(
    (set, get) => ({
      ...initialState,
      setCurrentStep: currentStep => set({ currentStep, errors: null }),
      setAccountInfo: data =>
        set(state => ({
          accountInfo: { ...state.accountInfo, ...data },
          errors: null,
        })),
      setDetailsData: data =>
        set(state => ({
          detailsData: {
            ...state.detailsData,
            ...data,
            profile_photo_url:
              data.profile_photo_url !== undefined
                ? data.profile_photo_url
                : state.detailsData.profile_photo_url,
          },
          errors: null,
        })),
      setBioData: data =>
        set(state => ({
          bioData: { ...state.bioData, ...data },
          errors: null,
        })),
      setSchoolSuccesses: data =>
        set(state => ({
          schoolSuccesses: { ...state.schoolSuccesses, ...data },
          errors: null,
        })),
      setCommunityInvolvement: data =>
        set(state => ({
          communityInvolvement: { ...state.communityInvolvement, ...data },
          errors: null,
        })),
      setErrors: ({ errors }) => set({ errors }),
      clearErrors: () => set({ errors: null }),
      reset: () => set({ ...initialState }),
    }),
    {
      name: 'ad-onboarding-flow', // Changed name to avoid conflict with PA store
      storage: createJSONStorage(() => localStorage),
    }
  )
);
