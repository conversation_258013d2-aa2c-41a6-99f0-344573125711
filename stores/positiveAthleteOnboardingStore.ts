import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import type { InvolvementItem, Sport } from '@/services/positiveAthleteOnboarding.service';
import type {
  AccountInfoDTO,
  DetailsDTO,
  InvolvementDTO,
  SportsDTO,
  StoryDTO,
  WorkExperienceDTO,
} from '../types/onboarding';

interface StudentDetailsDTO {
  state: string;
  county: string;
  high_school: string;
  school_id?: string;
  graduation_year: string;
  current_gpa: string;
  current_class_rank: string;
  gender: string;
  height: string;
  weight: string;
  career_interests: string[];
  profile_photo_url?: string | null;
}

interface SportsStepDTO {
  sports: Sport[];
}

interface InvolvementStepDTO {
  items: InvolvementItem[];
}

interface WorkExperienceStepDTO {
  items: InvolvementItem[]; // Same structure as InvolvementItem
}

interface PositiveAthleteOnboardingState {
  currentStep: string;
  accountInfo: Partial<AccountInfoDTO>;
  details: Partial<DetailsDTO>;
  sports: Partial<SportsStepDTO>;
  involvement: Partial<InvolvementStepDTO>;
  workExperience: Partial<WorkExperienceStepDTO>;
  story: Partial<StoryDTO>;
  studentDetails: Partial<StudentDetailsDTO>;
  errors: Record<string, string[]> | null;
  setCurrentStep: (step: string) => void;
  setAccountInfo: (data: Partial<AccountInfoDTO>) => void;
  setDetails: (data: Partial<DetailsDTO>) => void;
  setSports: (data: Partial<SportsStepDTO>) => void;
  setInvolvement: (data: Partial<InvolvementStepDTO>) => void;
  setWorkExperience: (data: Partial<WorkExperienceStepDTO>) => void;
  setStory: (data: Partial<StoryDTO>) => void;
  setStudentDetails: (data: Partial<StudentDetailsDTO>) => void;
  setErrors: (errors: { message: string; errors: Record<string, string[]> }) => void;
  reset: () => void;
}

export const usePositiveAthleteOnboardingStore = create<PositiveAthleteOnboardingState>()(
  persist(
    (set, get) => {
      return {
        currentStep: '',
        accountInfo: {},
        details: {},
        sports: {},
        involvement: {},
        workExperience: {},
        story: {},
        studentDetails: { profile_photo_url: null },
        errors: null,
        setCurrentStep: currentStep => set({ currentStep }),
        setAccountInfo: data =>
          set(state => ({
            accountInfo: { ...state.accountInfo, ...data },
          })),
        setDetails: data =>
          set(state => ({
            details: { ...state.details, ...data },
          })),
        setSports: data =>
          set(state => ({
            sports: { ...state.sports, ...data },
          })),
        setInvolvement: data =>
          set(state => ({
            involvement: { ...state.involvement, ...data },
          })),
        setWorkExperience: data =>
          set(state => ({
            workExperience: { ...state.workExperience, ...data },
          })),
        setStory: data =>
          set(state => ({
            story: { ...state.story, ...data },
          })),
        setStudentDetails: data =>
          set(state => ({
            studentDetails: {
              ...state.studentDetails,
              ...data,
              profile_photo_url:
                data.profile_photo_url !== undefined
                  ? data.profile_photo_url
                  : state.studentDetails.profile_photo_url,
            },
          })),
        setErrors: ({ errors }) => set({ errors }),
        reset: () =>
          set({
            currentStep: '',
            accountInfo: {},
            details: {},
            sports: {},
            involvement: {},
            workExperience: {},
            story: {},
            studentDetails: { profile_photo_url: null },
            errors: null,
          }),
      };
    },
    {
      name: 'positive-athlete-onboarding',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
