import { create } from 'zustand';

interface ScrollState {
  scrollY: number;
  previousY: number;
  isInitialScroll: boolean;
  isAboveFold: boolean;
  isScrollEnd: boolean;
  isScrollStart: boolean;
  isScrollUp: boolean;
  scrollUpDistance: number;
  inView: boolean;
  setInView: (inView: boolean) => void;
  initializeScroll: () => void;
  updateScroll: (newScrollY: number) => void;
}

export const useScrollStore = create<ScrollState>((set, get) => ({
  scrollY: 0,
  previousY: 0,
  isInitialScroll: true,
  isAboveFold: true,
  isScrollEnd: false,
  isScrollStart: true,
  isScrollUp: false,
  scrollUpDistance: 0,
  inView: false,

  setInView: inView => set({ inView }),

  initializeScroll: () => {
    const initialScroll = window.innerHeight * 0.0125;
    const foldScroll = window.innerHeight * 0.5625 + 10;
    const maxScroll = document.body.scrollHeight - window.innerHeight - 10;

    set(state => ({
      ...state,
      isInitialScroll: state.scrollY <= initialScroll,
      isAboveFold: state.scrollY <= foldScroll,
      isScrollEnd: state.scrollY >= maxScroll,
    }));
  },

  updateScroll: newScrollY => {
    const previousY = get().scrollY;
    const isScrollUp = newScrollY < previousY;

    set(state => ({
      scrollY: newScrollY,
      previousY,
      isScrollUp,
      isScrollStart: newScrollY === 0,
      scrollUpDistance: isScrollUp ? state.scrollUpDistance + (previousY - newScrollY) : 0,
    }));

    // Recalculate fold-based values
    get().initializeScroll();
  },
}));
