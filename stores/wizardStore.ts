import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface WizardState {
  currentStep: number;
  totalSteps: number;
  lastUpdated: number;
  isInitialized: boolean;
  // Actions
  nextStep: () => void;
  previousStep: () => void;
  setStep: (step: number) => void;
  resetWizard: () => void;
  initialize: () => void;
  setTotalSteps: (steps: number) => void;
}

const INITIAL_STATE = {
  currentStep: 0,
  totalSteps: 1,
  lastUpdated: Date.now(),
  isInitialized: false,
};

export const useWizardStore = create<WizardState>()(
  persist(
    (set, get) => ({
      ...INITIAL_STATE,

      setTotalSteps: (steps: number) => {
        set(state => ({
          ...state,
          totalSteps: steps,
        }));
      },

      setStep: (step: number) => {
        set(state => ({
          ...state,
          currentStep: Math.min(Math.max(0, step), state.totalSteps - 1),
          lastUpdated: Date.now(),
        }));
      },

      nextStep: () => {
        set(state => ({
          ...state,
          currentStep: Math.min(state.currentStep + 1, state.totalSteps - 1),
          lastUpdated: Date.now(),
        }));
      },

      previousStep: () => {
        set(state => ({
          ...state,
          currentStep: Math.max(0, state.currentStep - 1),
          lastUpdated: Date.now(),
        }));
      },

      resetWizard: () => {
        set({ ...INITIAL_STATE, isInitialized: true });
      },

      initialize: () => {
        const state = get();
        const now = Date.now();

        if (now - state.lastUpdated > 24 * 60 * 60 * 1000) {
          // 24 hours
          set({ ...INITIAL_STATE, isInitialized: true });
        } else {
          set({
            ...state,
            isInitialized: true,
            lastUpdated: now,
          });
        }
      },
    }),
    {
      name: 'wizard-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: state => ({
        currentStep: state.currentStep,
        totalSteps: state.totalSteps,
        lastUpdated: state.lastUpdated,
        isInitialized: state.isInitialized,
      }),
    }
  )
);
