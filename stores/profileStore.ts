import { create } from 'zustand';
import type { Involvement as FormInvolvement } from '@/components/profile/InvolvementForm';
import type { WorkExperience as FormWorkExperience } from '@/components/profile/WorkExperienceForm';
import { updateProfile } from '@/services/profile.service';
import { useCareerInterestStore } from '@/stores/careerInterestStore';
import type {
  Involvement as APIInvolvement,
  WorkExperience as APIWorkExperience,
  CareerInterest,
  ProfileDetails,
  SportEntry,
} from '@/types/profile';

// Adapter functions
const toAPIInvolvement = (involvement: FormInvolvement): APIInvolvement => ({
  id: parseInt(involvement.id),
  name: involvement.title,
  role: involvement.dateRange,
  description: involvement.description,
});

const toAPIWorkExperience = (work: FormWorkExperience): APIWorkExperience => {
  const [startDate, endDate] = work.dateRange.split(' - ');
  return {
    id: parseInt(work.id),
    title: work.title,
    organization: '', // This field is missing in the form type
    startDate,
    endDate: endDate || undefined,
    description: work.description,
  };
};

interface ProfileSection {
  isEditing: boolean;
  isLoading: boolean;
  error: string | null;
  isDirty: boolean;
}

interface ProfileState {
  story: string;
  details: ProfileDetails;
  sports: SportEntry[];
  workExperience: FormWorkExperience[];
  involvement: FormInvolvement[];
  careerInterests: CareerInterest[];
  sections: {
    story: ProfileSection;
    details: ProfileSection;
    sports: ProfileSection;
    workExperience: ProfileSection;
    involvement: ProfileSection;
    careerInterests: ProfileSection;
  };
  startEditing: (section: keyof ProfileState['sections']) => void;
  cancelEditing: (section: keyof ProfileState['sections']) => void;
  updateStory: (story: string) => void;
  updateDetails: (details: ProfileDetails) => void;
  updateSports: (sports: SportEntry[]) => void;
  updateWorkExperience: (items: FormWorkExperience[]) => void;
  updateInvolvement: (items: FormInvolvement[]) => void;
  updateCareerInterests: (interests: CareerInterest[]) => void;
  saveStory: (userId: number) => Promise<void>;
  saveDetails: (userId: number) => Promise<void>;
  saveSports: (userId: number) => Promise<void>;
  saveWorkExperience: (userId: number) => Promise<void>;
  saveInvolvement: (userId: number) => Promise<void>;
  saveCareerInterests: (userId: number) => Promise<void>;
  inRecruiterDatabase: boolean;
  updateRecruiterDatabase: (value: boolean) => void;
  saveRecruiterDatabase: (profileId: number) => Promise<void>;
}

export const useProfileStore = create<ProfileState>((set, get) => ({
  story: '',
  details: {} as ProfileDetails,
  sports: [] as SportEntry[],
  workExperience: [],
  involvement: [],
  careerInterests: [],
  sections: {
    story: {
      isEditing: false,
      isLoading: false,
      error: null,
      isDirty: false,
    },
    details: {
      isEditing: false,
      isLoading: false,
      error: null,
      isDirty: false,
    },
    sports: {
      isEditing: false,
      isLoading: false,
      error: null,
      isDirty: false,
    },
    workExperience: {
      isEditing: false,
      isLoading: false,
      error: null,
      isDirty: false,
    },
    involvement: {
      isEditing: false,
      isLoading: false,
      error: null,
      isDirty: false,
    },
    careerInterests: {
      isEditing: false,
      isLoading: false,
      error: null,
      isDirty: false,
    },
  },

  startEditing: section =>
    set(state => ({
      sections: {
        ...state.sections,
        [section]: {
          ...state.sections[section],
          isEditing: true,
          error: null,
        },
      },
    })),

  cancelEditing: section =>
    set(state => ({
      sections: {
        ...state.sections,
        [section]: {
          ...state.sections[section],
          isEditing: false,
          error: null,
          isDirty: false,
        },
      },
    })),

  updateStory: story =>
    set(state => ({
      story: story ?? '',
      sections: {
        ...state.sections,
        story: {
          ...state.sections.story,
          isDirty: true,
        },
      },
    })),

  updateDetails: details =>
    set(state => ({
      details,
      sections: {
        ...state.sections,
        details: {
          ...state.sections.details,
          isDirty: true,
        },
      },
    })),

  updateSports: sports =>
    set(state => ({
      sports: sports ?? [],
      sections: {
        ...state.sections,
        sports: {
          ...state.sections.sports,
          isDirty: true,
        },
      },
    })),

  updateWorkExperience: items =>
    set(state => ({
      workExperience: items ?? [],
      sections: {
        ...state.sections,
        workExperience: {
          ...state.sections.workExperience,
          isDirty: true,
        },
      },
    })),

  updateInvolvement: items =>
    set(state => ({
      involvement: items ?? [],
      sections: {
        ...state.sections,
        involvement: {
          ...state.sections.involvement,
          isDirty: true,
        },
      },
    })),

  updateCareerInterests: interests =>
    set(state => ({
      careerInterests: interests ?? [],
      sections: {
        ...state.sections,
        careerInterests: {
          ...state.sections.careerInterests,
          isDirty: true,
        },
      },
    })),

  saveStory: async userId => {
    const state = get();
    set(state => ({
      sections: {
        ...state.sections,
        story: {
          ...state.sections.story,
          isLoading: true,
          error: null,
        },
      },
    }));

    try {
      await updateProfile(userId, { story: state.story });
      set(state => ({
        sections: {
          ...state.sections,
          story: {
            ...state.sections.story,
            isLoading: false,
            isEditing: false,
            isDirty: false,
          },
        },
      }));
    } catch (error) {
      set(state => ({
        sections: {
          ...state.sections,
          story: {
            ...state.sections.story,
            isLoading: false,
            error: 'Failed to save story',
          },
        },
      }));
      throw error;
    }
  },

  saveDetails: async userId => {
    const state = get();
    set(state => ({
      sections: {
        ...state.sections,
        details: {
          ...state.sections.details,
          isLoading: true,
          error: null,
        },
      },
    }));

    try {
      await updateProfile(userId, { details: state.details });
      set(state => ({
        sections: {
          ...state.sections,
          details: {
            ...state.sections.details,
            isLoading: false,
            isEditing: false,
            isDirty: false,
          },
        },
      }));
    } catch (error) {
      set(state => ({
        sections: {
          ...state.sections,
          details: {
            ...state.sections.details,
            isLoading: false,
            error: 'Failed to save details',
          },
        },
      }));
      throw error;
    }
  },

  saveSports: async userId => {
    const state = get();
    set(state => ({
      sections: {
        ...state.sections,
        sports: {
          ...state.sections.sports,
          isLoading: true,
          error: null,
        },
      },
    }));

    try {
      await updateProfile(userId, { sports: state.sports });
      set(state => ({
        sections: {
          ...state.sections,
          sports: {
            ...state.sections.sports,
            isLoading: false,
            isEditing: false,
            isDirty: false,
          },
        },
      }));
    } catch (error) {
      set(state => ({
        sections: {
          ...state.sections,
          sports: {
            ...state.sections.sports,
            isLoading: false,
            error: 'Failed to save sports',
          },
        },
      }));
      throw error;
    }
  },

  saveWorkExperience: async userId => {
    const state = get();
    set(state => ({
      sections: {
        ...state.sections,
        workExperience: {
          ...state.sections.workExperience,
          isLoading: true,
          error: null,
        },
      },
    }));

    try {
      await updateProfile(userId, {
        workExperience: state.workExperience.map(toAPIWorkExperience),
      });
      set(state => ({
        sections: {
          ...state.sections,
          workExperience: {
            ...state.sections.workExperience,
            isLoading: false,
            isEditing: false,
            isDirty: false,
          },
        },
      }));
    } catch (error) {
      set(state => ({
        sections: {
          ...state.sections,
          workExperience: {
            ...state.sections.workExperience,
            isLoading: false,
            error: 'Failed to save work experience',
          },
        },
      }));
      throw error;
    }
  },

  saveInvolvement: async userId => {
    const state = get();
    set(state => ({
      sections: {
        ...state.sections,
        involvement: {
          ...state.sections.involvement,
          isLoading: true,
          error: null,
        },
      },
    }));

    try {
      await updateProfile(userId, {
        involvement: state.involvement.map(toAPIInvolvement),
      });
      set(state => ({
        sections: {
          ...state.sections,
          involvement: {
            ...state.sections.involvement,
            isLoading: false,
            isEditing: false,
            isDirty: false,
          },
        },
      }));
    } catch (error) {
      set(state => ({
        sections: {
          ...state.sections,
          involvement: {
            ...state.sections.involvement,
            isLoading: false,
            error: 'Failed to save involvement',
          },
        },
      }));
      throw error;
    }
  },

  saveCareerInterests: async userId => {
    const state = get();
    set(state => ({
      sections: {
        ...state.sections,
        careerInterests: {
          ...state.sections.careerInterests,
          isLoading: true,
          error: null,
        },
      },
    }));

    try {
      await updateProfile(userId, { careerInterests: state.careerInterests });
      set(state => ({
        sections: {
          ...state.sections,
          careerInterests: {
            ...state.sections.careerInterests,
            isLoading: false,
            isEditing: false,
            isDirty: false,
          },
        },
      }));
    } catch (error) {
      set(state => ({
        sections: {
          ...state.sections,
          careerInterests: {
            ...state.sections.careerInterests,
            isLoading: false,
            error: 'Failed to save career interests',
          },
        },
      }));
      throw error;
    }
  },

  inRecruiterDatabase: false,
  updateRecruiterDatabase: value => set({ inRecruiterDatabase: value }),
  saveRecruiterDatabase: async profileId => {
    const state = get();
    await updateProfile(profileId, { inRecruiterDatabase: state.inRecruiterDatabase });
  },
}));
