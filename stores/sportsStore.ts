import { IconName } from '@fortawesome/fontawesome-svg-core';
import { create } from 'zustand';

export interface Sport {
  label: string;
  slug: string;
  icon: IconName;
}

interface SportsStore {
  sports: Sport[];
}

export const useSportsStore = create<SportsStore>(() => ({
  sports: [
    { label: 'Basketball', slug: 'basketball', icon: 'basketball-ball' },
    { label: 'Track & Field', slug: 'track-field', icon: 'running' },
    { label: 'Football', slug: 'football', icon: 'football-ball' },
    { label: 'Badminton', slug: 'badminton', icon: 'badminton' },
    { label: 'Equestrian', slug: 'equestrian', icon: 'horse' },
    { label: 'Baseball', slug: 'baseball', icon: 'baseball-ball' },
    { label: 'Volleyball', slug: 'volleyball', icon: 'volleyball-ball' },
    { label: 'Swimming', slug: 'swimming', icon: 'swimmer' },
  ],
}));
