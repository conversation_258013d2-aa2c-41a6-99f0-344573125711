import { create } from 'zustand';

interface ModalState {
  isOpen: boolean;
  component: React.ReactNode | null;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  open: (component: React.ReactNode, maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl') => void;
  close: () => void;
  setModal: (component: React.ReactNode) => void;
}

export const useModalStore = create<ModalState>(set => ({
  isOpen: false,
  component: null,
  maxWidth: 'md',
  open: (component, maxWidth = 'lg') => set({ isOpen: true, component, maxWidth }),
  close: () => set({ isOpen: false, component: null }),
  setModal: (component: React.ReactNode) => set({ isOpen: true, component }),
}));
