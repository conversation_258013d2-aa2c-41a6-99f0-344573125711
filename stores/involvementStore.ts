import { create } from 'zustand';

interface InvolvementItem {
  id: string;
  title: string;
  dateRange: string;
  description: string;
}

interface InvolvementState {
  isEditing: boolean;
  items: InvolvementItem[];
  initialItems: InvolvementItem[];
  isDirty: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  startEditing: () => void;
  cancelEditing: () => void;
  updateItems: (items: InvolvementItem[]) => void;
  addItem: () => void;
  removeItem: (id: string) => void;
  updateItem: (id: string, updates: Partial<InvolvementItem>) => void;
  saveItems: (userId: string) => Promise<void>;
}

// Mock data store
let mockInvolvementData: Record<string, InvolvementItem[]> = {};

const createNewItem = (): InvolvementItem => ({
  id: Math.random().toString(36).substring(2),
  title: '',
  dateRange: '',
  description: '',
});

export const useInvolvementStore = create<InvolvementState>((set, get) => ({
  isEditing: false,
  items: [],
  initialItems: [],
  isDirty: false,
  isLoading: false,
  error: null,

  startEditing: () => set({ isEditing: true }),

  cancelEditing: () =>
    set(state => ({
      isEditing: false,
      isDirty: false,
      error: null,
      items: [...state.initialItems], // Restore initial items
    })),

  updateItems: (items: InvolvementItem[]) =>
    set(state => ({
      items,
      initialItems: state.isEditing ? state.initialItems : items,
      isDirty: JSON.stringify(items) !== JSON.stringify(state.initialItems),
    })),

  addItem: () =>
    set(state => {
      const newItems = [...state.items, createNewItem()];
      return {
        items: newItems,
        isDirty: JSON.stringify(newItems) !== JSON.stringify(state.initialItems),
      };
    }),

  removeItem: (id: string) =>
    set(state => {
      const newItems = state.items.filter(item => item.id !== id);
      return {
        items: newItems,
        isDirty: JSON.stringify(newItems) !== JSON.stringify(state.initialItems),
      };
    }),

  updateItem: (id: string, updates: Partial<InvolvementItem>) =>
    set(state => {
      const newItems = state.items.map(item => (item.id === id ? { ...item, ...updates } : item));
      return {
        items: newItems,
        isDirty: JSON.stringify(newItems) !== JSON.stringify(state.initialItems),
      };
    }),

  saveItems: async (userId: string) => {
    set({ isLoading: true, error: null });

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update mock data
      mockInvolvementData[userId] = get().items;

      set(state => ({
        isLoading: false,
        isEditing: false,
        isDirty: false,
        initialItems: [...state.items], // Update initial items after successful save
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to save involvement data',
      });
    }
  },
}));
