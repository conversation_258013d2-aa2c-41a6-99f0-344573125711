import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import type { SponsorAccountInfoPayload } from '@/services/sponsorOnboarding.service';

interface SponsorOnboardingState {
  currentStep: string;
  accountInfo: Partial<SponsorAccountInfoPayload>;
  errors: Record<string, string[]> | null;
  setCurrentStep: (step: string) => void;
  setAccountInfo: (data: Partial<SponsorAccountInfoPayload>) => void;
  setErrors: (errors: { message: string; errors: Record<string, string[]> }) => void;
  reset: () => void;
}

export const useSponsorOnboardingStore = create<SponsorOnboardingState>()(
  persist(
    set => {
      return {
        currentStep: '',
        accountInfo: {},
        errors: null,
        setCurrentStep: currentStep => set({ currentStep }),
        setAccountInfo: data =>
          set(state => ({
            accountInfo: { ...state.accountInfo, ...data },
          })),
        setErrors: ({ errors }) => set({ errors }),
        reset: () =>
          set({
            currentStep: '',
            accountInfo: {},
            errors: null,
          }),
      };
    },
    {
      name: 'sponsor-onboarding',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
