import { create } from 'zustand';

// Keep the form data type for reference, but not used in the store itself
export interface PostingFormData {
  title: string;
  type: string;
  category?: string;
  location_type: string; // API: locationType
  location: string;
  location_display?: string; // API: formatted location display (city, state)
  description: string;
  qualifications: string;
  responsibilities: string;
  benefits: string;
  organization_name: string; // API: organizationName
  details: string;
  industries: string[];
  interests: number[]; // Added for interest selection
  industry?: string; // Single selected industry for dropdown
  action_button_link?: string; // API: applyUrl
  apply_url?: string; // API: applyUrl
  action_button_label?: string; // API: actionButtonLabel
  term?: string;
  organization_website?: string; // API: organizationWebsite
  organization_logo?: string | File; // API: organizationLogo
  preferred_graduation_year?: string; // API: preferredGraduationYear
  preferred_states?: string[]; // API: preferredStates
  start_date?: string; // From date picker UI component
  end_date?: string; // From date picker UI component
  visibleStartDate?: string | null; // For API backend (same value as start_date)
  visibleEndDate?: string | null; // For API backend (same value as end_date)
}

interface PostingStore {
  // Edit state
  isEditing: boolean;
  editingId: string | null;

  // Actions
  setEditing: (id: string | null) => void;
  clearEditing: () => void;
}

export const usePostingStore = create<PostingStore>(set => ({
  // State
  isEditing: false,
  editingId: null,

  // Actions
  setEditing: id =>
    set({
      isEditing: true,
      editingId: id,
    }),

  clearEditing: () =>
    set({
      isEditing: false,
      editingId: null,
    }),
}));
