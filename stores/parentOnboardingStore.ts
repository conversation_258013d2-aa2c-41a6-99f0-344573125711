import { create } from 'zustand';

// Define the store type
interface ParentAccountInfoState {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  password: string;
}

interface ParentOnboardingState {
  accountInfo: ParentAccountInfoState;
  setAccountInfo: (data: Partial<ParentAccountInfoState>) => void;
  resetAccountInfo: () => void;
}

const initialAccountInfo: ParentAccountInfoState = {
  first_name: '',
  last_name: '',
  email: '',
  phone: '',
  password: '',
};

// Create the store
export const useParentOnboardingStore = create<ParentOnboardingState>(set => ({
  accountInfo: initialAccountInfo,

  setAccountInfo: data =>
    set(state => ({
      accountInfo: { ...state.accountInfo, ...data },
    })),

  resetAccountInfo: () => set(() => ({ accountInfo: initialAccountInfo })),
}));
