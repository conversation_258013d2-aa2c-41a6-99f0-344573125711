import { create } from 'zustand';

interface StoryState {
  isEditing: boolean;
  content: string;
  initialContent: string;
  isDirty: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  startEditing: () => void;
  cancelEditing: () => void;
  updateContent: (content: string) => void;
  saveContent: (userId: string) => Promise<void>;
}

// Mock data store - this simulates our database
let mockStoryData: Record<string, string> = {};

export const useStoryStore = create<StoryState>((set, get) => ({
  isEditing: false,
  content: '',
  initialContent: '',
  isDirty: false,
  isLoading: false,
  error: null,

  startEditing: () => set({ isEditing: true }),

  cancelEditing: () =>
    set({
      isEditing: false,
      isDirty: false,
      error: null,
      content: get().initialContent, // Restore the initial content
    }),

  updateContent: (content: string) =>
    set(state => ({
      content,
      initialContent: state.isEditing ? state.initialContent : content, // Only update initial content when not in edit mode
      isDirty: content !== state.initialContent,
    })),

  saveContent: async (userId: string) => {
    set({ isLoading: true, error: null });

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update mock data
      mockStoryData[userId] = get().content;

      set(state => ({
        isLoading: false,
        isEditing: false,
        isDirty: false,
        initialContent: state.content, // Update initial content after successful save
      }));

      // Log for development
      console.log('Saved story to mock data:', {
        userId,
        content: mockStoryData[userId],
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to save story',
      });
    }
  },
}));
