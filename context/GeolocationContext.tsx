'use client';

import React, { createContext, ReactNode, useContext } from 'react';
import { useGeolocation } from '@/hooks/useGeolocation';

interface GeolocationContextType {
  position: {
    lat: number;
    lng: number;
  } | null;
  error: string | null;
  loading: boolean;
}

const GeolocationContext = createContext<GeolocationContextType | undefined>(undefined);

export function GeolocationProvider({ children }: { children: ReactNode }) {
  const geolocation = useGeolocation();

  return <GeolocationContext.Provider value={geolocation}>{children}</GeolocationContext.Provider>;
}

export function useGeolocationContext() {
  const context = useContext(GeolocationContext);
  if (context === undefined) {
    throw new Error('useGeolocationContext must be used within a GeolocationProvider');
  }
  return context;
}
