'use client';

import Button from './Button';

interface FormActionsProps {
  onSave: () => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  saveLabel?: string;
  cancelLabel?: string;
  className?: string;
}

export function FormActions({
  onSave,
  onCancel,
  isLoading,
  saveLabel = 'Save',
  cancelLabel = 'Cancel',
  className = '',
}: FormActionsProps) {
  return (
    <div className={`flex justify-end gap-4 ${className}`}>
      <Button color="white" variant="text" onClick={onCancel}>
        {cancelLabel}
      </Button>
      <Button color="blue" onClick={onSave} disabled={isLoading}>
        {saveLabel}
      </Button>
    </div>
  );
}
