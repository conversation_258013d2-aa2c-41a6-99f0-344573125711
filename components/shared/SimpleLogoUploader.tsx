'use client';

import React, { useCallback, useState } from 'react';
import { FileRejection, useDropzone } from 'react-dropzone';
import Image from 'next/image';
import { faCloudArrowUp } from '@fortawesome/pro-light-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useToastNotify } from '@/stores/toastNotify.store';

export interface SimpleLogoUploaderProps {
  onFileSelect: (file: File) => void;
  onReadyToSave?: (isReady: boolean) => void;
  className?: string;
  dropzoneText?: {
    default: string;
    active: string;
    subText: string;
  };
  currentImageUrl?: string;
}

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes

export function SimpleLogoUploader({
  onFileSelect,
  onReadyToSave,
  className = '',
  dropzoneText = {
    default: 'Drag & drop your logo here',
    active: 'Drop your logo here',
    subText: 'or click to browse',
  },
  currentImageUrl,
}: SimpleLogoUploaderProps) {
  const [selectedImage, setSelectedImage] = useState<string | null>(currentImageUrl || null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const { toastNotify } = useToastNotify();

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles && acceptedFiles.length > 0) {
        const file = acceptedFiles[0];

        // Create preview URL
        const url = URL.createObjectURL(file);
        setSelectedImage(url);

        // Pass the file to parent component
        onFileSelect(file);

        // Signal that we're ready to save if needed
        if (onReadyToSave) {
          onReadyToSave(true);
        }
      }
    },
    [onFileSelect, onReadyToSave]
  );

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file size (max 5MB)
      if (file.size > MAX_FILE_SIZE) {
        toastNotify(
          `File Too Large: Image must be smaller than ${MAX_FILE_SIZE / 1024 / 1024}MB.`,
          'error'
        );
        // Reset the input value
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      const url = URL.createObjectURL(file);
      setSelectedImage(url);
      onFileSelect(file);

      if (onReadyToSave) {
        onReadyToSave(true);
      }
    }
  };

  const onDropRejected = useCallback(
    (fileRejections: FileRejection[]) => {
      fileRejections.forEach(({ file, errors }) => {
        errors.forEach(err => {
          if (err.code === 'file-too-large') {
            toastNotify(
              `File Too Large: "${file.name}" is larger than ${MAX_FILE_SIZE / 1024 / 1024}MB.`,
              'error'
            );
          } else if (err.code === 'file-invalid-type') {
            toastNotify(
              `Invalid File Type: "${file.name}" is not a supported image type.`,
              'error'
            );
          } else {
            toastNotify(`Upload Error: ${err.message}`, 'error');
          }
        });
      });
    },
    [toastNotify]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    onDropRejected,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif'],
    },
    maxSize: MAX_FILE_SIZE,
    maxFiles: 1,
    noClick: true,
  });

  React.useEffect(() => {
    return () => {
      // Clean up the URL when component unmounts
      if (selectedImage && selectedImage !== currentImageUrl) {
        URL.revokeObjectURL(selectedImage);
      }
    };
  }, [selectedImage, currentImageUrl]);

  return (
    <div className={className}>
      <div className="relative w-full aspect-square rounded-2xl border border-neutral-300 overflow-hidden">
        {!selectedImage ? (
          <div
            {...getRootProps()}
            onClick={handleClick}
            className={`flex flex-col items-center justify-center w-full h-full cursor-pointer bg-neutral-100 hover:bg-neutral-50 transition-colors ${
              isDragActive ? 'border-2 border-dashed border-blue-500 bg-blue-50' : ''
            }`}
          >
            <input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              ref={fileInputRef}
              className="hidden"
            />
            <div className="text-neutral-500 text-center">
              <FontAwesomeIcon icon={faCloudArrowUp} className="h-8 w-8 mb-2" />
              <div className="mb-2">
                {isDragActive ? dropzoneText.active : dropzoneText.default}
              </div>
              <div className="text-sm">{dropzoneText.subText}</div>
            </div>
          </div>
        ) : (
          <div className="relative w-full h-full">
            <Image
              src={selectedImage}
              alt="Selected logo"
              fill
              className="object-contain"
              sizes="(max-width: 500px) 100vw, 500px"
            />
          </div>
        )}
      </div>
    </div>
  );
}
