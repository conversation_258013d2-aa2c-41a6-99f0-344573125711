'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { faSpinnerThird } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { MEDIA_QUERY_DESKTOP, useMediaQuery } from '@/hooks/utils/useMediaQuery';
import ErrorBoundary from './ErrorBoundary';
import type { SplineViewerProps } from './types';

const SplineViewer: React.FC<SplineViewerProps> = ({
  scene,
  className = '',
  style = {},
  onLoad,
  onSplineError,
  fallbackImage,
  fallbackImageWidth = 160,
  fallbackImageHeight = 160,
  fallbackImageClassName = 'object-contain',
  ...splineProps
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [SplineComponent, setSplineComponent] = useState<any>(null);
  const [fallbackError, setFallbackError] = useState<boolean>(false);
  const [bufferErrors, setBufferErrors] = useState<number>(0);
  const [retryCount, setRetryCount] = useState<number>(0);
  const MAX_RETRIES = 2;

  // Use media query to determine if we're on desktop
  const isDesktop = useMediaQuery(MEDIA_QUERY_DESKTOP);

  // Only run on client-side
  useEffect(() => {
    setIsMounted(true);

    // Safety check for browser environment
    if (typeof window === 'undefined') {
      setError(new Error('Cannot render in server environment'));
      setIsLoading(false);
      return;
    }

    // Dynamically import the Spline component only on client side
    const loadSpline = async () => {
      try {
        // Try to import the Spline component
        const splineModule = await import('@splinetool/react-spline');
        setSplineComponent(() => splineModule.default);
        setIsLoading(false);
      } catch (err) {
        console.error('Failed to load Spline component:', err);
        setError(err instanceof Error ? err : new Error('Failed to load Spline component'));
        setIsLoading(false);
        if (onSplineError)
          onSplineError(err instanceof Error ? err : new Error('Failed to load Spline component'));
      }
    };

    // Only load Spline on desktop
    if (isDesktop) {
      loadSpline();
    } else {
      // On mobile, we don't need to load Spline
      setIsLoading(false);
    }
  }, [onSplineError, isDesktop]);

  // Listen for specific error messages in the console
  useEffect(() => {
    if (!isDesktop) return;

    const originalConsoleError = console.error;

    // Create a patched console.error to catch buffer errors
    console.error = (...args) => {
      // Check if this is a buffer error from Spline
      if (
        args.length > 0 &&
        typeof args[0] === 'string' &&
        (args[0].includes('end of buffer not reached') ||
          args[0].includes('Failed to fetch dynamically imported module'))
      ) {
        setBufferErrors(prev => prev + 1);

        // Only retry loading after seeing multiple buffer errors
        if (bufferErrors > 2 && retryCount < MAX_RETRIES) {
          setRetryCount(prev => prev + 1);
        }
      }

      // Call the original console.error
      originalConsoleError.apply(console, args);
    };

    // Restore the original console.error when the component unmounts
    return () => {
      console.error = originalConsoleError;
    };
  }, [isDesktop, bufferErrors, retryCount, scene]);

  const handleLoad = (app: any) => {
    // Reset error counters on successful load
    setBufferErrors(0);
    setRetryCount(0);
    if (onLoad) onLoad(app);
  };

  // Handle errors that might occur during rendering
  const handleError = (err: Error) => {
    console.error('Spline rendering error for scene:', scene, err);

    // Don't set error state for buffer errors - these are often transient
    // Only set error state for serious rendering issues
    if (!err.message.includes('buffer') && !err.message.includes('dynamically imported module')) {
      setError(err);
      if (onSplineError) onSplineError(err);
    } else {
      // For buffer errors, increment the counter but don't set error state
      setBufferErrors(prev => prev + 1);
    }
  };

  // Handle fallback image errors
  const handleFallbackError = () => {
    console.error('Fallback image error:', fallbackImage);
    setFallbackError(true);
  };

  // Render fallback image (used for both mobile and when there's an error on desktop)
  const renderFallbackImage = () => {
    if (fallbackImage && !fallbackError) {
      return (
        <div className={`${className} flex items-center justify-center`} style={style}>
          <Image
            src={fallbackImage}
            alt="Asset visualization"
            width={fallbackImageWidth}
            height={fallbackImageHeight}
            priority
            className={fallbackImageClassName}
            onError={handleFallbackError}
          />
        </div>
      );
    }

    // If no fallback image is provided or there was an error, render minimal content
    return (
      <div className={`${className} flex items-center justify-center h-[180px]`} style={style}>
        {/* Only show error message when there was an actual error loading the fallback */}
        {fallbackError ? <div className="text-sm text-gray-500">Image not available</div> : null}
      </div>
    );
  };

  // Don't render anything on server
  if (!isMounted) {
    return (
      <div className={`${className}`} style={style}>
        <FontAwesomeIcon icon={faSpinnerThird} className="text-gray-600 animate-spin" />
      </div>
    );
  }

  // Don't try to render Spline if we're in a server environment
  if (typeof window === 'undefined') {
    return (
      <div className={`${className}`} style={style}>
        <div className="text-sm">3D viewer not available in server environment</div>
      </div>
    );
  }

  // On mobile, always use fallback image
  if (isMounted && !isDesktop) {
    return renderFallbackImage();
  }

  // If there's a serious error loading the 3D model (not buffer errors) and we have a fallback, use it on desktop too
  // Ignore buffer errors unless we've exceeded retry attempts
  const shouldUseFallback =
    // Only use fallback if all of these are true:
    error && // There is an error
    fallbackImage && // We have a fallback image to show
    // Either we've exceeded retries OR this is a serious non-buffer error
    ((retryCount >= MAX_RETRIES && bufferErrors > 0) ||
      (!error.message.includes('buffer') &&
        !error.message.includes('dynamically imported module')));

  if (shouldUseFallback) {
    return renderFallbackImage();
  }

  // Create a fallback UI for the error boundary that uses the fallback image if available
  const errorFallback = fallbackImage ? (
    renderFallbackImage()
  ) : (
    <div className={`${className}`} style={style}>
      <div className="text-sm text-red-600">Failed to load 3D model. Please try again later.</div>
    </div>
  );

  // Default desktop rendering with Spline
  return (
    <div className={`${className}`} style={style}>
      {isLoading && (
        <FontAwesomeIcon icon={faSpinnerThird} className="text-gray-600 animate-spin" />
      )}
      {error && !fallbackImage && (
        <div className="text-sm">Error loading 3D model: {error.message}</div>
      )}
      {!error && SplineComponent && (
        <ErrorBoundary fallback={errorFallback} onError={handleError}>
          <div>
            <SplineComponent
              scene={scene}
              onLoad={handleLoad}
              key={`spline-${scene}-retry-${retryCount}`}
              {...splineProps}
            />
          </div>
        </ErrorBoundary>
      )}
    </div>
  );
};

export default SplineViewer;
