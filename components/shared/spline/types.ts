import type { Application, SplineEvent } from '@splinetool/runtime';

export interface SplineViewerProps {
  scene: string;
  className?: string;
  style?: React.CSSProperties;
  onLoad?: (app: Application) => void;
  onSplineError?: (error: Error) => void;
  onSplineMouseDown?: (e: SplineEvent) => void;
  onSplineMouseUp?: (e: SplineEvent) => void;
  onSplineMouseHover?: (e: SplineEvent) => void;
  fallbackImage?: string; // Path to static fallback image for mobile devices
  fallbackImageWidth?: number; // Width of the fallback image (optional)
  fallbackImageHeight?: number; // Height of the fallback image (optional)
  fallbackImageClassName?: string; // Additional classes for the fallback image (optional)
}
