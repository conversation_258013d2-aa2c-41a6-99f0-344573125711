'use client';

import BaseSportCard from './BaseSportCard';
import type { SportsProps } from './types';

// Default 3D asset URL for the "Other" sport
const DEFAULT_SCENE_URL = 'https://prod.spline.design/BNJAsY6R6ACjg-di/scene.splinecode';

export default function SportsOtherCard(props: SportsProps) {
  // Remove debug logs
  // useEffect(() => {
  //   console.log('SportsOtherCard Mounted', {
  //     sceneUrl: DEFAULT_SCENE_URL,
  //     fallbackImagePath: '/images/sports/card_default_360x280.png',
  //     props,
  //   });
  // }, [props]);

  const handleSplineError = (error: Error) => {
    console.error('Error loading SportsOtherCard 3D model:', error.message);
  };

  const handleLoad = () => {
    // console.log('SportsOtherCard 3D model loaded successfully!');
  };

  return (
    <BaseSportCard
      sportName="Default"
      scene={DEFAULT_SCENE_URL}
      fallbackImage="/images/sports/card_default_360x280.png"
      onSplineError={handleSplineError}
      onLoad={handleLoad}
      {...props}
    />
  );
}
