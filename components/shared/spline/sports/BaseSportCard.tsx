'use client';

import React from 'react';
import { getSportCardImagePath } from '@/utils/sports';
import <PERSON>p<PERSON><PERSON>ie<PERSON> from '../SplineViewer';
import type { SportsProps } from './types';

interface BaseSportCardProps extends SportsProps {
  scene: string;
  sportName: string;
  fallbackImage?: string;
  onSplineError?: (error: Error) => void;
  onLoad?: (app: any) => void;
}

/**
 * Base component for all sport cards that adds fallback image support
 */
export default function BaseSportCard({
  scene,
  sportName,
  fallbackImage,
  onSplineError,
  onLoad,
  ...props
}: BaseSportCardProps) {
  // Get the fallback image path for this sport, but only if not explicitly provided
  const generatedFallbackImagePath = getSportCardImagePath(sportName);

  // Use explicitly passed fallbackImage if provided, otherwise use the generated one
  const finalFallbackImage = fallbackImage || generatedFallbackImagePath;

  // Handle Spline errors
  const handleSplineError = (error: Error) => {
    console.error(`BaseSportCard Error (${sportName}):`, error.message);
    if (onSplineError) onSplineError(error);
  };

  // Handle successful model load
  const handleLoad = (app: any) => {
    if (onLoad) onLoad(app);
  };

  return (
    <SplineViewer
      scene={scene}
      fallbackImage={finalFallbackImage}
      fallbackImageWidth={360}
      fallbackImageHeight={280}
      fallbackImageClassName="w-full h-full absolute inset-0 z-10 object-cover"
      onSplineError={handleSplineError}
      onLoad={handleLoad}
      {...props}
    />
  );
}
