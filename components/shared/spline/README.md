# SplineViewer Component

A React component wrapper for the Spline 3D viewer, designed for Next.js applications.

## Installation

Make sure you have the required dependency installed:

```bash
npm install @splinetool/react-spline
```

## Usage

Import the SplineViewer component in your component:

```tsx
import SplineViewer from '@/components/shared/spline/SplineViewer';

export default function MyComponent() {
  return (
    <div className="w-full h-96">
      <SplineViewer 
        scene="https://prod.spline.design/your-scene-id/scene.splinecode"
        className="w-full h-full"
      />
    </div>
  );
}
```

## Props

The SplineViewer component accepts the following props:

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `scene` | string | Yes | URL to the Spline scene |
| `className` | string | No | Additional CSS classes |
| `style` | React.CSSProperties | No | Inline styles |
| `onLoad` | (app: Application) => void | No | Callback when scene is loaded |
| `onSplineError` | (error: Error) => void | No | Custom error handler |
| `onSplineMouseDown` | (e: SplineEvent) => void | No | Mouse down event handler |
| `onSplineMouseUp` | (e: SplineEvent) => void | No | Mouse up event handler |
| `onSplineMouseHover` | (e: SplineEvent) => void | No | Mouse hover event handler |

## Examples

See the [SplineViewerExample.tsx](./SplineViewerExample.tsx) file for a complete example.

## Converting Spline Viewer HTML to React

If you have HTML from Spline like this:

```html
<script type="module" src="https://unpkg.com/@splinetool/viewer@1.9.68/build/spline-viewer.js"></script>
<spline-viewer url="https://prod.spline.design/8CgEnmxCFEW2MgWU/scene.splinecode"></spline-viewer>
```

You can convert it to use our SplineViewer component:

```tsx
<SplineViewer scene="https://prod.spline.design/8CgEnmxCFEW2MgWU/scene.splinecode" />
``` 