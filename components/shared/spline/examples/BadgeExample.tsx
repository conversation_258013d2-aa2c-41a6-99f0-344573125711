'use client';

import { useState } from 'react';
import {
  BadgeJVAchieved,
  BadgeJVUnachieved,
  BadgeRookieAchieved,
  BadgeRookieUnachieved,
  BadgeVarsityAchieved,
  BadgeVarsityUnachieved,
} from '../badges';

export default function BadgeExample() {
  const [badgePoints, setBadgePoints] = useState(3);

  const handlePointsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setBadgePoints(Number(e.target.value));
  };

  return (
    <div className="p-6 bg-gray-100 rounded-lg">
      <h2 className="text-2xl font-bold mb-4">Badge Example</h2>

      <div className="mb-4">
        <label htmlFor="points" className="block text-sm font-medium mb-2">
          Points: {badgePoints}
        </label>
        <input
          id="points"
          type="range"
          min="0"
          max="20"
          value={badgePoints}
          onChange={handlePointsChange}
          className="w-full"
        />
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
        <div className="flex flex-col items-center">
          <h3 className="text-lg font-semibold mb-2">Rookie Badge (1 pt)</h3>
          <div className="h-40 w-40">
            {badgePoints >= 1 ? (
              <BadgeRookieAchieved className="w-full h-full" />
            ) : (
              <BadgeRookieUnachieved className="w-full h-full" />
            )}
          </div>
          <p className="mt-2 text-sm text-center">
            {badgePoints >= 1 ? 'Achieved!' : 'Not achieved yet'}
          </p>
        </div>

        <div className="flex flex-col items-center">
          <h3 className="text-lg font-semibold mb-2">JV Badge (5 pts)</h3>
          <div className="h-40 w-40">
            {badgePoints >= 5 ? (
              <BadgeJVAchieved className="w-full h-full" />
            ) : (
              <BadgeJVUnachieved className="w-full h-full" />
            )}
          </div>
          <p className="mt-2 text-sm text-center">
            {badgePoints >= 5 ? 'Achieved!' : 'Not achieved yet'}
          </p>
        </div>

        <div className="flex flex-col items-center">
          <h3 className="text-lg font-semibold mb-2">Varsity Badge (15 pts)</h3>
          <div className="h-40 w-40">
            {badgePoints >= 15 ? (
              <BadgeVarsityAchieved className="w-full h-full" />
            ) : (
              <BadgeVarsityUnachieved className="w-full h-full" />
            )}
          </div>
          <p className="mt-2 text-sm text-center">
            {badgePoints >= 15 ? 'Achieved!' : 'Not achieved yet'}
          </p>
        </div>
      </div>
    </div>
  );
}
