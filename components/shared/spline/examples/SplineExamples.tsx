'use client';

import { useState } from 'react';
import BadgeExample from './BadgeExample';
import SportsExample from './SportsExample';

export default function SplineExamples() {
  const [activeTab, setActiveTab] = useState('badges');

  return (
    <div className="max-w-6xl mx-auto p-4">
      <h1 className="text-3xl font-bold mb-8 text-center">Spline Component Examples</h1>

      <div className="mb-8">
        <div className="flex space-x-1 rounded-xl bg-blue-900/20 p-1 mb-4">
          <button
            onClick={() => setActiveTab('badges')}
            className={`w-full rounded-lg py-2.5 text-sm font-medium leading-5 
              ${
                activeTab === 'badges'
                  ? 'bg-white shadow text-blue-700'
                  : 'text-blue-100 hover:bg-white/[0.12] hover:text-white'
              }`}
          >
            Badge Examples
          </button>
          <button
            onClick={() => setActiveTab('sports')}
            className={`w-full rounded-lg py-2.5 text-sm font-medium leading-5 
              ${
                activeTab === 'sports'
                  ? 'bg-white shadow text-blue-700'
                  : 'text-blue-100 hover:bg-white/[0.12] hover:text-white'
              }`}
          >
            Sports Examples
          </button>
        </div>

        <div className="mt-2">
          {activeTab === 'badges' && (
            <div className="rounded-xl bg-white p-3">
              <BadgeExample />
            </div>
          )}
          {activeTab === 'sports' && (
            <div className="rounded-xl bg-white p-3">
              <SportsExample />
            </div>
          )}
        </div>
      </div>

      <div className="bg-white rounded-lg p-6 shadow">
        <h2 className="text-2xl font-bold mb-4">How to Use Spline Components</h2>

        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">Badge Components</h3>
            <pre className="bg-gray-100 p-4 rounded-md mt-2 overflow-auto text-sm">
              {`import { BadgeRookieAchieved } from '@/components/shared/spline';

export default function MyComponent() {
  return (
    <div className="h-40 w-40">
      <BadgeRookieAchieved />
    </div>
  );
}`}
            </pre>
          </div>

          <div>
            <h3 className="text-lg font-semibold">Sports Components</h3>
            <pre className="bg-gray-100 p-4 rounded-md mt-2 overflow-auto text-sm">
              {`import { SportsSoccerCard } from '@/components/shared/spline';

export default function MyComponent() {
  return (
    <div className="h-60 w-60">
      <SportsSoccerCard />
    </div>
  );
}`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
