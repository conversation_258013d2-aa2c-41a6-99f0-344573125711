'use client';

import { useState } from 'react';
import SelectInput from '@/components/shared/form/SelectInput';
import {
  SportsBasketballBackground,
  SportsBasketballCard,
  SportsFootballBackground,
  SportsFootballCard,
  SportsSoccerBackground,
  SportsSoccerCard,
} from '../sports';

export default function SportsExample() {
  const [selectedSport, setSelectedSport] = useState('soccer');

  return (
    <div className="p-6 bg-gray-100 rounded-lg">
      <h2 className="text-2xl font-bold mb-4">Sports Example</h2>

      <div className="mb-6">
        <SelectInput
          label="Select Sport"
          id="sport"
          value={selectedSport}
          onChange={value => setSelectedSport(value)}
          options={[
            { value: 'soccer', label: 'Soccer' },
            { value: 'basketball', label: 'Basketball' },
            { value: 'football', label: 'Football' },
          ]}
        />
      </div>

      <div className="relative h-60 mb-8">
        <h3 className="text-lg font-semibold mb-2">Sport Background</h3>
        <div className="h-full w-full overflow-hidden rounded-lg">
          {selectedSport === 'soccer' && <SportsSoccerBackground className="w-full h-full" />}
          {selectedSport === 'basketball' && (
            <SportsBasketballBackground className="w-full h-full" />
          )}
          {selectedSport === 'football' && <SportsFootballBackground className="w-full h-full" />}
        </div>
      </div>

      <div className="mt-8">
        <h3 className="text-lg font-semibold mb-2">Sport Card</h3>
        <div className="h-60 w-full flex justify-center">
          <div className="h-full aspect-square">
            {selectedSport === 'soccer' && <SportsSoccerCard className="w-full h-full" />}
            {selectedSport === 'basketball' && <SportsBasketballCard className="w-full h-full" />}
            {selectedSport === 'football' && <SportsFootballCard className="w-full h-full" />}
          </div>
        </div>
      </div>
    </div>
  );
}
