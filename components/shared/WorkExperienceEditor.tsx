'use client';

import ItemEditor from '@/components/resume/ItemEditor';
import SiteInput from '@/components/shared/form/SiteInput';
import { Textarea } from '@/components/shared/form/Textarea';

export type WorkExperience = {
  id: string;
  title: string;
  dateRange: string;
  description: string;
};

interface WorkExperienceEditorProps {
  items: WorkExperience[];
  onAdd: () => void;
  onUpdate: (id: string, updates: Partial<Omit<WorkExperience, 'id'>>) => void;
  onRemove: (id: string) => void;
  addButtonText?: string;
}

export default function WorkExperienceEditor({
  items,
  onAdd,
  onUpdate,
  onRemove,
  addButtonText = 'Add Another Item',
}: WorkExperienceEditorProps) {
  const renderFields = (item: WorkExperience) => (
    <>
      <SiteInput
        label="Title"
        type="text"
        value={item.title}
        onChange={e => onUpdate(item.id, { title: e.target.value })}
        placeholder="e.g., Software Engineer Intern"
      />

      <SiteInput
        label="Date Range"
        type="text"
        value={item.dateRange}
        onChange={e => onUpdate(item.id, { dateRange: e.target.value })}
        placeholder="e.g., 2022-2023"
      />

      <Textarea
        label="Description"
        value={item.description}
        onChange={e => onUpdate(item.id, { description: e.target.value })}
        placeholder="Briefly describe your role and responsibilities..."
        rows={4}
      />
    </>
  );

  return (
    <ItemEditor
      items={items}
      onAdd={onAdd}
      onRemove={onRemove}
      onUpdate={onUpdate}
      getTitle={item => item.title || 'Position'}
      addButtonText={addButtonText}
      renderFields={renderFields}
    />
  );
}
