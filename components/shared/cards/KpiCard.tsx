import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { IconDefinition, IconProp } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import { useIntersectionObserver } from '@/hooks/utils/useIntersectionObserver';

export type KpiColor = 'default' | 'success' | 'error';

const colorVariants = {
  default: 'text-text-primary',
  success: 'text-utility-success',
  error: 'text-brand-red',
};

// Easing function that starts fast and then slows down (exponential)
function easeOutExpo(x: number): number {
  return x === 1 ? 1 : 1 - Math.pow(2, -10 * x);
}

export interface KpiCardProps {
  value: string | number;
  label: string;
  color?: KpiColor;
  icon?: IconProp | React.FC<React.SVGProps<SVGSVGElement>>;
  image?: string;
  iconSizeClass?: string;
  className?: string;
  colSpan?: string;
}

function AnimatedNumber({ value, shouldAnimate }: { value: number; shouldAnimate: boolean }) {
  const [current, setCurrent] = useState(shouldAnimate ? 0 : value);

  useEffect(() => {
    if (!shouldAnimate) {
      setCurrent(value);
      return;
    }

    const duration = 1000; // Increased duration slightly for better easing effect
    const steps = 60;
    const stepDuration = duration / steps;
    let step = 0;

    const timer = setInterval(() => {
      step++;
      if (step >= steps) {
        setCurrent(value);
        clearInterval(timer);
      } else {
        const progress = step / steps;
        const easedProgress = easeOutExpo(progress);
        setCurrent(easedProgress * value);
      }
    }, stepDuration);

    return () => clearInterval(timer);
  }, [value, shouldAnimate]);

  return Math.round(current);
}

function AnimatedValue({
  value,
  shouldAnimate,
}: {
  value: string | number;
  shouldAnimate: boolean;
}) {
  // If it's already a number, just animate it
  if (typeof value === 'number') {
    return <AnimatedNumber value={value} shouldAnimate={shouldAnimate} />;
  }

  // Extract number and unit from string (e.g., "11 Hrs" -> [11, "Hrs"])
  const match = value.match(/^(\d+)(.*)$/);
  if (match) {
    const [, number, unit] = match;
    return (
      <>
        <AnimatedNumber value={parseInt(number, 10)} shouldAnimate={shouldAnimate} />
        {unit}
      </>
    );
  }

  // If no number found, return original value
  return value;
}

export function KpiCard({
  value,
  label,
  color = 'default',
  icon,
  image,
  iconSizeClass = 'w-4 h-4',
  className = '',
  colSpan = '',
}: KpiCardProps) {
  const [ref, isIntersecting] = useIntersectionObserver({
    threshold: 0.1,
    freezeOnceVisible: true,
  });

  return (
    <div
      ref={ref}
      className={`bg-surface-secondary rounded-xl px-6 py-6 md:px-2 lg:px-4 xl:px-6 ${colSpan} ${className}`}
    >
      <div className="flex justify-between items-start gap-2">
        <div className="">
          <span className={`block text-3xl/none font-bold ${colorVariants[color]}`}>
            <AnimatedValue value={value} shouldAnimate={isIntersecting} />
          </span>
          <span className="block text-base/5 text-text-secondary mt-3">{label}</span>
        </div>

        {icon && !image && (
          <div className="text-text-secondary">
            {typeof icon === 'object' ? (
              <FontAwesomeIcon icon={icon as IconProp} className={iconSizeClass} />
            ) : (
              React.createElement(icon as React.FC<React.SVGProps<SVGSVGElement>>, {
                className: `${iconSizeClass} text-text-primary`,
                'aria-hidden': true,
              })
            )}
          </div>
        )}

        {image && (
          <div className={clsx('relative shrink-0', iconSizeClass)}>
            <Image src={image} alt={label} className="object-contain" fill />
          </div>
        )}
      </div>
    </div>
  );
}
