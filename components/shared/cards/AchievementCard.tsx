import React from 'react';
import Image from 'next/image';
import Card from '@/components/shared/cards/Card';
import { cn } from '@/lib/utils';

interface AchievementCardProps {
  title: string;
  currentCount: number;
  targetCount: number;
  isUnlocked?: boolean;
  className?: string;
}

export const AchievementCard = ({
  title,
  currentCount,
  targetCount,
  isUnlocked = false,
  className,
}: AchievementCardProps) => {
  const progress = Math.min((currentCount / targetCount) * 100, 100);

  return (
    <Card elevation="sm" roundedCorners="xl" className={cn('w-[300px]', className)}>
      <div className="relative w-full aspect-square mb-4">
        <Image
          src={isUnlocked ? '/achievements/badge-unlocked.png' : '/achievements/badge-locked.png'}
          alt={title}
          fill
          className={cn(
            'object-contain transition-all duration-300',
            !isUnlocked && 'grayscale opacity-50'
          )}
        />
      </div>

      <h3 className="text-xl font-bold text-center mb-2">{title}</h3>

      {/* Progress bar */}
      <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
        <div
          className={cn(
            'h-full transition-all duration-300',
            isUnlocked ? 'bg-red-500' : 'bg-gray-400'
          )}
          style={{ width: `${progress}%` }}
        />
      </div>

      {/* Progress text */}
      <p className="text-sm text-gray-600 text-center mt-2">
        Complete {targetCount} Modules
        <span className="block text-xs">
          {currentCount} / {targetCount} completed
        </span>
      </p>
    </Card>
  );
};
