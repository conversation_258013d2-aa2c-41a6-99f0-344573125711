import React from 'react';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import {
  faCircleCheck,
  faCircleExclamation,
  faCircleInfo,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';

type InfoCardType = 'info' | 'error' | 'success';

interface InfoCardProps {
  type?: InfoCardType;
  message: string;
  buttonText?: string;
  onButtonClick?: () => void;
}

const typeStyles: Record<
  InfoCardType,
  { bg: string; border: string; icon: IconDefinition; iconColor: string }
> = {
  info: {
    bg: 'bg-neutral-100',
    border: 'border-neutral-200',
    icon: faCircleInfo,
    iconColor: 'text-neutral-600',
  },
  error: {
    bg: 'bg-red-50',
    border: 'border-red-200',
    icon: faCircleExclamation,
    iconColor: 'text-red-600',
  },
  success: {
    bg: 'bg-green-50',
    border: 'border-green-200',
    icon: faCircleCheck,
    iconColor: 'text-green-600',
  },
};

export function InfoCard({ type = 'info', message, buttonText, onButtonClick }: InfoCardProps) {
  const styles = typeStyles[type];

  return (
    <div
      className={`flex items-center justify-between px-6 py-4 ${styles.bg} border ${styles.border} rounded-2xl`}
    >
      <div className="flex items-center gap-4">
        <FontAwesomeIcon icon={styles.icon} className={`text-base ${styles.iconColor}`} />
        <span className="text-neutral-900">{message}</span>
      </div>
      {buttonText && onButtonClick && (
        <Button
          color="blue"
          size="small"
          onClick={onButtonClick}
          className="h-8 px-4 py-2 text-sm font-semibold"
        >
          {buttonText}
        </Button>
      )}
    </div>
  );
}
