'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { faPlayCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';

interface PortraitModuleCardProps {
  title: string;
  subtitle: string;
  coverImage: string;
  href: string;
  className?: string;
}

export default function PortraitModuleCard({
  title,
  subtitle,
  coverImage,
  href,
  className,
}: PortraitModuleCardProps) {
  return (
    <Link
      href={href}
      className={clsx(
        'group block relative aspect-[3/4] w-60 rounded-3xl overflow-hidden',
        className
      )}
    >
      {/* Cover Image with Ken Burns effect */}
      <Image
        src={coverImage}
        alt={title}
        fill
        className="object-cover transition-transform duration-[2s] group-hover:scale-110"
      />

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent" />

      {/* Content */}
      <div className="absolute bottom-0 left-0 p-6 text-white space-y-2">
        {/* Play Button */}
        <FontAwesomeIcon icon={faPlayCircle} className="text-white text-2xl/none" />

        <h3 className="text-2xl/none font-oxanium font-bold uppercase text-white line-clamp-2">
          {title}
        </h3>

        <p className="text-base/none text-white font-semibold">{subtitle}</p>
      </div>
    </Link>
  );
}
