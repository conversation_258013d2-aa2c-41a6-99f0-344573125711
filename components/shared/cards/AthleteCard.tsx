'use client';

import React, { useMemo } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { faArrowRight, faMessage } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Avatar from '@/components/shared/Avatar';
import Card from '@/components/shared/cards/Card';
import Tag from '@/components/shared/Tag';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/lib/utils';
import { ProfileTypes } from '@/stores/auth.store';

export interface CareerInterest {
  name: string;
  count: number;
}

export interface AthleteCardProps {
  id: string;
  name: string;
  graduationYear: string;
  location?: string;
  highSchool?: string;
  avatar: string;
  careerInterests?: CareerInterest[];
  variant?: 'default' | 'simple';
  className?: string;
  onConnect?: () => void;
  canConnect?: boolean;
  hasAchievements?: boolean;
}

export default function AthleteCard({
  id,
  name,
  graduationYear,
  location,
  highSchool,
  avatar,
  careerInterests = [],
  variant = 'default',
  className,
  onConnect,
  canConnect = true,
  hasAchievements = false,
}: AthleteCardProps) {
  const isSimple = variant === 'simple';
  const { profileType } = useAuth();
  const isParentProfile = profileType === ProfileTypes.PARENT;

  const firstName = useMemo(() => {
    if (!name) return '';
    return name.split(' ')[0];
  }, [name]);

  const lastName = useMemo(() => {
    if (!name) return '';
    return name.split(' ').slice(1).join(' ');
  }, [name]);

  return (
    <Card roundedCorners="3xl" elevation="card" className={cn(className)} noPadding>
      <div className="bg-white grid grid-cols-4 gap-6 p-6 relative lg:grid-cols-12">
        <div className="col-span-4 flex gap-4 justify-between">
          <div className="flex gap-4">
            <div className="relative">
              <Avatar
                src={avatar}
                alt={name}
                size={isSimple ? 'sm' : 'md'}
                firstName={firstName}
                lastName={lastName}
              />

              {hasAchievements && (
                <div className="absolute top-8 -right-2 size-10 shrink-0 z-10 pointer-events-none">
                  <Image
                    src="/images/avatar-badge.png"
                    className="absolute inset-0 w-full h-full object-contain"
                    fill
                    aria-hidden="true"
                    alt="Achievement Badge"
                    role="presentation"
                  />
                </div>
              )}
            </div>

            <div className="flex flex-col gap-1">
              {!isSimple && graduationYear && (
                <span className="pa-eyebrow text-brand-red">CLASS OF {graduationYear}</span>
              )}
              <h3 className="font-bold text-text-primary text-xl/none">{name}</h3>
              {!isSimple && location && (
                <div className="text-sm text-text-secondary">{location}</div>
              )}
            </div>
          </div>

          {/* Mobile actions */}
          <div className="space-y-4 gap-4 lg:hidden">
            <Link href={`/pa/${id}`} className="block" aria-label="View this athlete's profile">
              <span className="sr-only">View this athlete&#39;s profile</span>
              <FontAwesomeIcon
                className="text-text-secondary"
                icon={faArrowRight}
                aria-hidden="true"
              />
            </Link>

            {canConnect && onConnect && !isParentProfile && (
              <button
                type="button"
                className="block text-text-secondary"
                onClick={onConnect}
                aria-label="Connect with this athlete"
              >
                <span className="sr-only">Connect with this athlete</span>
                <FontAwesomeIcon className="text-current" icon={faMessage} aria-hidden="true" />
              </button>
            )}
          </div>
        </div>

        <div className="col-span-4 lg:col-span-3">
          {!isSimple && careerInterests.length > 0 && (
            <>
              <span className="block text-sm text-text-secondary">Career Interests</span>
              <div className="flex flex-wrap gap-2 mt-1 pointer-events-none">
                <Tag label={careerInterests[0].name} />
                {careerInterests.length > 1 && <Tag label={`+${careerInterests.length - 1}`} />}
              </div>
            </>
          )}
        </div>
        <div className="col-span-4 lg:col-span-3">
          {!isSimple && highSchool && (
            <>
              <span className="block text-sm text-text-secondary">High School</span>
              <span className="block text-base font-bold text-text-primary">{highSchool}</span>
            </>
          )}
        </div>

        {/* Dekstop Actions */}
        <div className="hidden items-center gap-4 lg:flex lg:justify-end lg:col-span-2">
          {canConnect && onConnect && !isParentProfile && (
            <button
              type="button"
              className="text-text-secondary transition-colors hover:text-brand-blue mr-4"
              onClick={onConnect}
              aria-label="Connect with this athlete"
            >
              <span className="sr-only">Connect with this athlete</span>
              <FontAwesomeIcon icon={faMessage} aria-hidden="true" />
            </button>
          )}
          <Link
            href={`/pa/${id}`}
            className="text-text-secondary transition-colors hover:text-brand-blue"
            aria-label="View this athlete's profile"
          >
            <span className="sr-only">View this athlete&#39;s profile</span>
            <FontAwesomeIcon className="text-current" icon={faArrowRight} aria-hidden="true" />
          </Link>
        </div>
      </div>
    </Card>
  );
}
