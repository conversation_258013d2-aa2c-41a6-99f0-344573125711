import React from 'react';
import Image from 'next/image';
import { faArrowDownToBracket, faCheck } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

interface CertificateCardProps {
  id: string;
  name: string;
  courseName?: string;
  earnedAt: string | Date;
  imageUrl?: string;
  downloadUrl?: string;
}

const CertificateCard: React.FC<CertificateCardProps> = ({
  id,
  name,
  courseName,
  earnedAt,
  imageUrl,
  downloadUrl,
}) => {
  // Format the date
  const formattedDate =
    typeof earnedAt === 'string'
      ? new Date(earnedAt).toLocaleDateString()
      : earnedAt.toLocaleDateString();

  // Generate a default download URL if one isn't provided
  const certificateDownloadUrl = downloadUrl || `/api/v1/learning/certificates/${id}/download`;

  return (
    <figure className="relative rounded-3xl shadow-card min-h-[320px] px-4 pb-6 pt-0 lg:px-8 lg:py-10 text-white bg-gray-900">
      <figcaption className="flex flex-col h-full lg:flex-row gap-6 relative z-20 items-start lg:items-center">
        {/* Badge */}
        <div className="relative w-24 h-32 lg:w-36 lg:h-48 overflow-hidden -mt-6 lg:m-0">
          <Image
            src="/images/certificate-complete-badge.png"
            alt={name}
            fill
            className="object-contain"
            priority
          />
        </div>

        <div>
          {/* Earned date badge */}
          <span className="inline-flex items-center bg-white rounded px-3 py-1 gap-2 text-xs text-brand-red mb-4">
            <FontAwesomeIcon icon={faCheck} className="size-4" aria-hidden="true" />
            Earned on {formattedDate}
          </span>

          {/* Certificate title */}
          <h2 className="text-2xl lg:text-3xl font-bold mb-2">{name}</h2>

          {/* Course name if available */}
          {courseName && <p className="text-base font-medium">{courseName}</p>}

          {/* Download button */}
          <a
            href={certificateDownloadUrl}
            className="inline-flex items-center text-base gap-2 mt-6 font-medium text-white bg-brand-red hover:bg-red-700 rounded-md px-4 py-2 transition-colors"
            download
          >
            Download Certificate
            <FontAwesomeIcon icon={faArrowDownToBracket} className="size-4" aria-hidden="true" />
          </a>
        </div>
      </figcaption>

      {/* Certificate Image */}
      {imageUrl && (
        <>
          <Image
            src={imageUrl}
            alt={name}
            fill
            className="object-cover z-[5] rounded-4xl"
            sizes="(max-width: 1024px) 100vw, 33vw"
          />
          <div className="absolute inset-0 bg-gradient-to-b lg:bg-gradient-to-l from-black/10 to-gray-900/70 z-10 rounded-4xl" />
        </>
      )}
    </figure>
  );
};

export default CertificateCard;
