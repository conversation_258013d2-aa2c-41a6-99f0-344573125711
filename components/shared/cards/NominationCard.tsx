import React, { useEffect, useRef, useState } from 'react';
import { faChevronDown } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Card from '@/components/shared/cards/Card';

export interface NominationCardProps {
  nominatorName: string;
  nominatorRole?: string;
  date: string;
  content: string;
  sport?: string;
  schoolName?: string;
}

export const NominationCard: React.FC<NominationCardProps> = ({
  nominatorName,
  nominatorRole,
  date,
  content,
  sport,
  schoolName,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [needsExpansion, setNeedsExpansion] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const contentWrapperRef = useRef<HTMLDivElement>(null);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  // Function to check if content needs expansion
  const checkContentOverflow = () => {
    if (!contentRef.current || !contentWrapperRef.current) return;

    const contentElement = contentRef.current;
    const wrapperElement = contentWrapperRef.current;

    // For non-expanded state, we need to temporarily remove line-clamp to get true height
    if (!isExpanded) {
      contentElement.classList.remove('line-clamp-4');
      const scrollHeight = contentElement.scrollHeight;
      const lineHeight = parseInt(window.getComputedStyle(contentElement).lineHeight) || 24;
      const maxHeight = lineHeight * 4; // Approximate height of 4 lines

      // Check if content height exceeds 4 lines
      setNeedsExpansion(scrollHeight > maxHeight);

      // Re-add line-clamp class if not expanded
      contentElement.classList.add('line-clamp-4');
    }
  };

  useEffect(() => {
    // Initial check
    checkContentOverflow();

    // Set up resize observer to recheck on window resize
    const resizeObserver = new ResizeObserver(() => {
      checkContentOverflow();
    });

    if (contentWrapperRef.current) {
      resizeObserver.observe(contentWrapperRef.current);
    }

    // Clean up
    return () => {
      resizeObserver.disconnect();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [content, isExpanded]);

  return (
    <Card
      elevation="sm"
      className="bg-surface-secondary grid grid-cols-1 gap-4 lg:grid-cols-12"
      roundedCorners="3xl"
      noBgColor
    >
      {/* Nomination Details */}
      <div className="col-span-1 lg:col-span-4 lg:flex lg:flex-col lg:justify-end">
        <div>
          <h3 className="text-2xl font-bold text-text-primary">{nominatorName}</h3>

          {nominatorRole && <p className="pa-eyebrow text-brand-red">{nominatorRole}</p>}

          <p className="text-gray-500 font-bold text-sm mt-1">{date}</p>
        </div>
      </div>

      {/* Nomination Content */}
      <div ref={contentWrapperRef} className="flex flex-col col-span-1 lg:col-span-8">
        <div className="mb-2">
          <p
            ref={contentRef}
            className={`text-text-secondary ${!isExpanded ? 'line-clamp-4' : ''}`}
          >
            &#8220;{content}&#8221;
          </p>
        </div>

        {needsExpansion && (
          <button
            type="button"
            onClick={toggleExpand}
            className="text-brand-blue font-bold flex items-center self-start"
            aria-label={isExpanded ? 'Show Less' : 'See More'}
            aria-expanded={isExpanded}
          >
            See {isExpanded ? 'Less' : 'More'}
            <FontAwesomeIcon
              icon={faChevronDown}
              className={`ml-1 h-4 w-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              aria-hidden="true"
            />
          </button>
        )}
      </div>
    </Card>
  );
};

export default NominationCard;
