'use client';

import React, { useEffect } from 'react';
import SplineViewer from '@/components/shared/spline/SplineViewer';
import { getBadgeFallbackImagePath } from '@/utils/badges';

export interface BadgeCardProps {
  id: number;
  name: string;
  moduleRequirement: number;
  isAchieved: boolean;
  achievedAssetUrl?: string;
  unachievedAssetUrl?: string;
  currentProgress?: number;
  progressPercentage?: number;
}

const BadgeCard: React.FC<BadgeCardProps> = ({
  id,
  name,
  moduleRequirement,
  isAchieved,
  achievedAssetUrl,
  unachievedAssetUrl,
  currentProgress,
  progressPercentage,
}) => {
  // Get the fallback image path for mobile devices
  const fallbackImagePath = getBadgeFallbackImagePath(name, isAchieved);

  return (
    <figure className="flex flex-col items-center flex-shrink-0 bg-surface-secondary rounded-3xl px-8 pb-8">
      <div className="mb-4 -mt-8 flex items-center justify-center">
        <SplineViewer
          scene={isAchieved ? achievedAssetUrl || '' : unachievedAssetUrl || ''}
          className="pa-spline-badge"
          fallbackImage={fallbackImagePath}
          fallbackImageWidth={160}
          fallbackImageHeight={160}
          fallbackImageClassName="object-contain"
        />
      </div>

      <figcaption className="block space-y-4">
        {/* 
      <p className="text-sm text-center text-gray-500">
        Complete {moduleRequirement} Module{moduleRequirement !== 1 ? 's' : ''}
      </p> */}

        <h4 className="font-bold text-center text-2xl text-brand-blue">{name}</h4>

        {/* Progress bar - always shown, but filled based on progress */}
        <div className="w-full shadow-neumorphism-inset-small rounded-full p-0.5">
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className={`h-2.5 rounded-full ${isAchieved ? 'bg-brand-red' : 'bg-brand-blue'}`}
              style={{ width: `${progressPercentage || 0}%` }}
            />
          </div>
        </div>

        {/* Status text */}
        <p className="text-sm text-center font-normal text-text-secondary">
          {isAchieved ? (
            <>
              Complete <span className="font-semibold">{currentProgress || moduleRequirement}</span>{' '}
              Module
              {(currentProgress || moduleRequirement) !== 1 ? 's' : ''}
            </>
          ) : (
            `${currentProgress || 0}/${moduleRequirement} Modules`
          )}
        </p>
      </figcaption>
    </figure>
  );
};

export default BadgeCard;
