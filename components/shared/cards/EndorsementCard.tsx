'use client';

import React from 'react';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import {
  faChevronDown,
  faHandHoldingSeedling,
  faHandshake,
  faHeart,
  faPeopleGroup,
  faPuzzlePiece,
  faSmile,
  faSunBright,
} from '@fortawesome/pro-regular-svg-icons';
import { faThumbsUp } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Disclosure, DisclosureButton, DisclosurePanel, Transition } from '@headlessui/react';
import clsx from 'clsx';
import Card from '@/components/shared/cards/Card';
import { cn } from '@/lib/utils';
import { Endorser as ServiceEndorser } from '@/services/endorsement.service';

// Map endorsement IDs to icons
const getEndorsementIcon = (id: number): IconDefinition => {
  const iconMap: Record<number, IconDefinition> = {
    1: faSunBright, // Optimistic
    2: faPeopleGroup, // Puts Team First
    3: faSmile, // Encouraging
    4: faHandshake, // Respectful
    5: faPuzzlePiece, // Admits Imperfections
    6: faHeart, // True Heart for Others
    7: faHandHoldingSeedling, // Embraces Service
  };

  return iconMap[id] || faSmile; // Default to smile icon if ID not found
};

// Define the props for the EndorsmentCard component
interface EndorsmentCardProps {
  id: number;
  name: string;
  count: number;
  endorsers: ServiceEndorser[];
  className?: string;
}

export default function EndorsementCard({
  id,
  name,
  count,
  endorsers,
  className,
}: EndorsmentCardProps) {
  const icon = getEndorsementIcon(id);

  return (
    <Disclosure as="div" className={clsx('block', className)}>
      {({ open }) => (
        <>
          <DisclosureButton
            className={clsx(
              'w-full flex items-center justify-between p-6 bg-surface-secondary hover:bg-surface-tertiary transition-colors',
              open ? 'rounded-t-3xl rounded-b-none' : 'rounded-3xl'
            )}
          >
            <div className="flex flex-wrap gap-x-2 gap-y-4 items-center">
              <div className="w-full md:w-auto flex">
                <span className="bg-brand-red font-bold flex items-center gap-x-2 text-white text-sm px-2 py-0.5 rounded-full min-w-[24px] text-center mr-4">
                  {count}
                  <FontAwesomeIcon icon={faThumbsUp} className="text-white !size-3" />
                </span>
              </div>

              <div className="flex items-center gap-x-2">
                <FontAwesomeIcon icon={icon} className="text-brand-red !size-5" />
                <span className="text-xl text-left text-text-primary font-medium">{name}</span>
              </div>
            </div>

            <FontAwesomeIcon
              icon={faChevronDown}
              className={clsx(
                'size-4 text-gray-500 transition-transform duration-200',
                open && 'transform rotate-180'
              )}
            />
          </DisclosureButton>

          <DisclosurePanel className="p-6 bg-surface-tertiary rounded-b-3xl -mt-1 border-t border-transprent">
            {endorsers && endorsers.length > 0 ? (
              <ul className="space-y-2">
                {endorsers.map(endorser => (
                  <li key={endorser.id} className="flex items-center">
                    <FontAwesomeIcon
                      icon={faThumbsUp}
                      className="text-brand-red mr-2 !size-3"
                      aria-hidden="true"
                    />
                    <span className="text-sm font-bold">
                      {endorser.endorserName || endorser.name}
                    </span>
                    <span className="text-sm ml-1 font-bold">
                      - {endorser.endorserRole || endorser.relation}
                    </span>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500">No endorsers found.</p>
            )}
          </DisclosurePanel>
        </>
      )}
    </Disclosure>
  );
}
