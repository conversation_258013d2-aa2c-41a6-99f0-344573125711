import React from 'react';
import type { Icon } from '@fortawesome/fontawesome-svg-core';
import type { FontAwesomeIconProps } from '@fortawesome/react-fontawesome';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

export type CardHeaderProps = {
  title?: string;
  titleIcon?: FontAwesomeIconProps['icon'];
  viewOnlyTitle?: string;
  buttonLabel?: string;
  buttonIcon?: FontAwesomeIconProps['icon'];
  children?: React.ReactNode;
  customEdit?: React.ReactNode;
  handleClick?: () => void;
  isButtonDisabled?: boolean;
  isViewOnly?: boolean;
  className?: string;
};

const CardHeader: React.FC<CardHeaderProps> = ({
  title,
  titleIcon,
  viewOnlyTitle,
  buttonLabel,
  buttonIcon,
  children,
  customEdit,
  handleClick,
  isButtonDisabled = false,
  isViewOnly = false,
  className,
}) => {
  // Determine whether to show edit controls
  const showEditControls = !isViewOnly && (buttonLabel || customEdit);

  return (
    <div className={`flex items-center flex-wrap gap-x-2 gap-y-4 justify-between ${className}`}>
      <div className="flex items-center gap-3">
        {titleIcon && (
          <FontAwesomeIcon
            icon={titleIcon}
            className="size-4 text-text-primary"
            aria-hidden="true"
          />
        )}
        <div className="flex items-center gap-2">
          <span className="pa-eyebrow text-text-primary !leading-5">
            {isViewOnly && viewOnlyTitle ? viewOnlyTitle : title}
          </span>

          {children}
        </div>
      </div>

      {showEditControls && (
        <div className="flex items-center justify-end gap-4">
          {customEdit}

          {buttonLabel && (
            <button
              className="text-brand-red hover:text-[#B31229] transition-colors"
              onClick={handleClick}
              disabled={isButtonDisabled}
            >
              <div className="flex items-center gap-2">
                <span className="text-sm">{buttonLabel}</span>
                {buttonIcon && (
                  <FontAwesomeIcon icon={buttonIcon} className="size-4" aria-hidden="true" />
                )}
              </div>
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default CardHeader;
