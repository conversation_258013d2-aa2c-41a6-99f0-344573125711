import React from 'react';
import { clsx } from 'clsx';

export type CardProps = {
  children: React.ReactNode;
  elevation?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'card';
  roundedCorners?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
  noPadding?: boolean;
  allowOverflow?: boolean;
  noBgColor?: boolean;
  className?: string;
};

const Card: React.FC<CardProps> = ({
  children,
  className,
  elevation,
  noPadding = false,
  allowOverflow = false,
  noBgColor = false,
  roundedCorners = '4xl',
}) => {
  const shadowClasses = {
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl',
    '2xl': 'shadow-2xl',
    card: 'shadow-card',
  };

  const roundedClasses = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    '2xl': 'rounded-2xl',
    '3xl': 'rounded-3xl',
    '4xl': 'rounded-4xl',
  };

  return (
    <div
      className={clsx(
        'relative overflow-hidden',
        !noBgColor && 'bg-white',
        elevation ? shadowClasses[elevation] : 'shadow-card',
        noPadding ? 'p-0' : 'px-4 py-6 lg:p-8',
        allowOverflow ? 'overflow-visible' : 'overflow-hidden',
        roundedCorners ? roundedClasses[roundedCorners] : roundedClasses['4xl'],
        className
      )}
    >
      {children}
    </div>
  );
};

export default Card;
