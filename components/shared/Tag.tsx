'use client';

import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_MAP, IconKey } from '@/lib/fontawesome';
import { cn } from '@/lib/utils';

// Career/Industry Tag Icon Mapping - Legacy map for backward compatibility
export const CAREER_TAG_ICONS: { [key: string]: IconDefinition } = {
  // Industries & Career Fields
  'Aerospace & Aviation': ICON_MAP['fa-plane'],
  'Agriculture, Food & Natural Resources': ICON_MAP['fa-leaf'],
  Architecture: ICON_MAP['fa-ruler-triangle'],
  'Arts, Culture & Entertainment': ICON_MAP['fa-pallet'],
  'Arts & Entertainment': ICON_MAP['fa-pallet'],
  'Audio/Video Technology': ICON_MAP['fa-camera-movie'],
  'Automotive Service & Sales': ICON_MAP['fa-car'],
  'Business & Finance': ICON_MAP['fa-user-tie'],
  'Business Management & Administration': ICON_MAP['fa-chart-mixed'],
  Coaching: ICON_MAP['fa-whistle'],
  Communications: ICON_MAP['fa-messages'],
  Construction: ICON_MAP['fa-hard-hat'],
  Culinary: ICON_MAP['fa-utensils'],
  Education: ICON_MAP['fa-graduation-cap'],
  Engineering: ICON_MAP['fa-wrench'],
  'Government & Public Service': ICON_MAP['fa-building-columns'],
  'Government & Public Administration': ICON_MAP['fa-building-columns'],
  Healthcare: ICON_MAP['fa-stethoscope'],
  'Health & Medical': ICON_MAP['fa-stethoscope'],
  'Health Science': ICON_MAP['fa-heart-pulse'],
  Hospitality: ICON_MAP['fa-bell-concierge'],
  'Hospitality & Tourism': ICON_MAP['fa-bell-concierge'],
  'Human Services': ICON_MAP['fa-people'],
  'Information Technology': ICON_MAP['fa-laptop-code'],
  'Installation, Repair & Maintenance': ICON_MAP['fa-screwdriver-wrench'],
  Law: ICON_MAP['fa-gavel'],
  'Law & Legal': ICON_MAP['fa-gavel'],
  'Public Safety & Corrections': ICON_MAP['fa-handcuffs'],
  Manufacturing: ICON_MAP['fa-industry'],
  Marketing: ICON_MAP['fa-megaphone'],
  'Medical & Healthcare': ICON_MAP['fa-heart-pulse'],
  Military: ICON_MAP['fa-jet-fighter'],
  Sales: ICON_MAP['fa-message-dollar'],
  'Science & Research': ICON_MAP['fa-flask'],
  'Social Media Management': ICON_MAP['fa-mobile'],
  'Social Services': ICON_MAP['fa-heart'],
  'Sports Marketing/Management': ICON_MAP['fa-football'],
  Technology: ICON_MAP['fa-code'],
  'Technology, Engineering & Math': ICON_MAP['fa-code'],
  'Transportation & Logistics': ICON_MAP['fa-truck'],
  'Transportation, Distribution & Logistics': ICON_MAP['fa-truck'],
  'Trades & Technical': ICON_MAP['fa-toolbox'],
  "I'm Not Sure Yet": ICON_MAP['fa-question'],

  // Sports
  Sports: ICON_MAP['fa-basketball'],
  Football: ICON_MAP['fa-football'],
  Basketball: ICON_MAP['fa-basketball'],
  Baseball: ICON_MAP['fa-baseball-bat-ball'],
  Volleyball: ICON_MAP['fa-volleyball'],

  // Roles/Positions
  Teacher: ICON_MAP['fa-chalkboard-teacher'],
  Doctor: ICON_MAP['fa-user-doctor'],
  Engineer: ICON_MAP['fa-wrench'],
  Developer: ICON_MAP['fa-code'],
  Mechanic: ICON_MAP['fa-cogs'],

  // Facilities
  Energy: ICON_MAP['fa-lightbulb'],
  Finance: ICON_MAP['fa-money-check-dollar'],
  'Finance & Banking': ICON_MAP['fa-money-check-dollar'],
  Hospital: ICON_MAP['fa-hospital'],
  School: ICON_MAP['fa-graduation-cap'],
  'Office Building': ICON_MAP['fa-building'],
  'Sports Facility': ICON_MAP['fa-dumbbell'],
  'Auto Shop': ICON_MAP['fa-car'],
};

interface TagProps {
  label?: string;
  icon?: IconDefinition | IconKey | string;
  variant?: 'default' | 'reverse';
  className?: string;
}

export default function Tag({ label, icon, variant = 'default', className }: TagProps) {
  // If no icon is provided but we have a label with a matching icon in our mapping, use that
  let resolvedIcon: IconDefinition | undefined;

  if (typeof icon === 'string' && icon.startsWith('fa-')) {
    // Handle string icon format from the backend (e.g., 'fa-eye')
    resolvedIcon = ICON_MAP[icon as IconKey];
  } else if (typeof icon === 'object') {
    // Handle direct IconDefinition objects
    resolvedIcon = icon as IconDefinition;
  } else if (label && CAREER_TAG_ICONS[label]) {
    // Fallback to the legacy mapping if we have a label
    resolvedIcon = CAREER_TAG_ICONS[label];
  }

  return (
    <div
      className={cn(
        'inline-grid grid-flow-col items-center gap-2 px-3 rounded text-sm font-semibold transition-colors print:table',
        variant === 'default' && 'bg-[#E31837] text-white hover:bg-[#B61330]',
        variant === 'reverse' && 'bg-white text-[#E31837] hover:bg-[#F8F8F8]',
        className
      )}
    >
      {resolvedIcon && (
        <FontAwesomeIcon
          icon={resolvedIcon}
          className="text-sm print:table-cell print:align-middle print:leading-none"
        />
      )}

      {label && (
        <span
          className="block capitalize text-xs/6 font-semibold print:table-cell print:align-middle print:leading-none"
          style={{ wordBreak: 'break-word' }}
        >
          {label}
        </span>
      )}
    </div>
  );
}
