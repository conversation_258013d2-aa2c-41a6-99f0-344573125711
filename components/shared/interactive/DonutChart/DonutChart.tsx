'use client';

import { forwardRef } from 'react';
import dynamic from 'next/dynamic';
import type { DonutChartInnerRef } from './DonutChartInner';

export interface ChartDataItem {
  label: string;
  value: number;
  isActive: boolean;
}

export interface DonutChartProps {
  data: ChartDataItem[];
  className?: string;
  size?: number;
  onSliceHover?: (label: string | null) => void;
  showInnerContent?: boolean;
  activeLabel?: string | null;
  activeValue?: number | null;
}

const DonutChartInner = dynamic(() => import('./DonutChartInner'), { ssr: false });

const DonutChart = forwardRef<DonutChartInnerRef, DonutChartProps>((props, ref) => {
  return <DonutChartInner {...props} ref={ref} />;
});

DonutChart.displayName = 'DonutChart';

export default DonutChart;
