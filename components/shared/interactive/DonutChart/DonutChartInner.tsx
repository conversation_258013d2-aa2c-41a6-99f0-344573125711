'use client';

import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { faSpinnerThird } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { cn } from '@/lib/utils';
import type { DonutChartProps } from './DonutChart';

// Dynamically import echarts only on client-side
let echarts: any = null;

export interface DonutChartInnerRef {
  highlightSlice: (label: string | null) => void;
}

const DonutChartInner = forwardRef<DonutChartInnerRef, DonutChartProps>(
  (
    {
      data,
      className,
      size = 200,
      onSliceHover,
      showInnerContent = true,
      activeLabel: propActiveLabel,
      activeValue: propActiveValue,
    },
    ref
  ) => {
    const chartRef = useRef<HTMLDivElement>(null);
    const [chart, setChart] = useState<any | null>(null);
    const [activeSlice, setActiveSlice] = useState<string | null>(null);
    const [mounted, setMounted] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    // Use either prop values or state values for display
    const displayLabel = propActiveLabel ?? activeSlice;
    const displayValue =
      propActiveValue ?? data.find(item => item.label === displayLabel)?.value ?? null;

    // Initialize client-side only code
    useEffect(() => {
      setMounted(true);
      // Import echarts dynamically only on client-side
      const loadEcharts = async () => {
        try {
          echarts = await import('echarts');
          setIsLoading(false);
        } catch (error) {
          console.error('Failed to load echarts:', error);
        }
      };

      loadEcharts();
    }, []);

    useImperativeHandle(
      ref,
      () => ({
        highlightSlice: (label: string | null) => {
          if (!chart) return;

          // Update our internal state
          setActiveSlice(label);

          // Trigger ECharts hover action for animation
          if (label) {
            chart.dispatchAction({
              type: 'highlight',
              seriesIndex: 0,
              name: label,
            });
          } else {
            // Clear hover state
            chart.dispatchAction({
              type: 'downplay',
              seriesIndex: 0,
            });
          }

          onSliceHover?.(label);
        },
      }),
      [chart, onSliceHover]
    );

    // Initialize chart
    useEffect(() => {
      if (!mounted || !chartRef.current || !echarts || isLoading) return;

      const newChart = echarts.init(chartRef.current);
      setChart(newChart);

      return () => {
        newChart.dispose();
      };
    }, [mounted, isLoading]);

    // Update chart options
    useEffect(() => {
      if (!chart || !mounted || isLoading) return;

      const option = {
        backgroundColor: 'transparent',
        series: [
          {
            type: 'pie',
            radius: ['50%', '90%'],
            center: ['50%', '50%'],
            startAngle: 270,
            padAngle: 2,
            clockwise: true,
            avoidLabelOverlap: false,
            zlevel: 0,
            silent: false,
            animation: true,
            animationDuration: 300,
            animationEasing: 'cubicInOut',
            animationDurationUpdate: 300,
            animationEasingUpdate: 'cubicInOut',
            data: data.map(item => ({
              value: item.value,
              name: item.label,
              itemStyle: {
                color: (activeSlice ? item.label === activeSlice : item.isActive)
                  ? '#D50032'
                  : '#d8dcde',
                borderRadius: 8,
                shadowBlur: (activeSlice ? item.label === activeSlice : item.isActive) ? 10 : 0,
                shadowColor: (activeSlice ? item.label === activeSlice : item.isActive)
                  ? 'rgba(227, 24, 55, 0.3)'
                  : 'transparent',
                opacity: (activeSlice ? item.label === activeSlice : item.isActive) ? 1 : 0.8,
              },
              emphasis: {
                disabled: false,
                scale: true,
                scaleSize: 3,
                focus: 'none',
                itemStyle: {
                  color: '#D50032',
                  shadowBlur: 10,
                  shadowColor: 'rgba(227, 24, 55, 0.6)',
                },
              },
            })),
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
          },
        ],
      };

      chart.setOption(option, true);

      // Set up event handlers
      chart.off('mouseover');
      chart.off('mouseout');

      chart.on('mouseover', { seriesIndex: 0 }, (params: { name: string }) => {
        setActiveSlice(params.name as string);
        onSliceHover?.(params.name as string);
      });

      chart.on('mouseout', { seriesIndex: 0 }, () => {
        setActiveSlice(null);
        onSliceHover?.(null);
      });
    }, [chart, data, onSliceHover, activeSlice, mounted, isLoading]);

    // Handle resize
    useEffect(() => {
      if (!chart || !mounted) return;

      const handleResize = () => {
        chart.resize();
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }, [chart, mounted]);

    // Show loading state if not mounted or echarts is still loading
    if (!mounted || isLoading) {
      return (
        <div className="relative flex items-center justify-center">
          <div className={cn('relative z-[3]', className)} style={{ width: size, height: size }}>
            <div className="flex items-center justify-center h-full">
              <FontAwesomeIcon icon={faSpinnerThird} className="text-gray-600 animate-spin" />
            </div>
          </div>
          <div className="bgCircle bg-surface-secondary z-[1] absolute top-[2.5%] left-[2.5%] w-[95%] h-[95%] rounded-full"></div>
          <div className="innerCircle bg-white z-[2] absolute top-[50%] left-[50%] w-[45%] h-[45%] rounded-full translate-x-[-50%] translate-y-[-50%]"></div>
        </div>
      );
    }

    return (
      <div className="relative flex items-center justify-center">
        <div
          ref={chartRef}
          className={cn('relative z-[3]', className)}
          style={{
            width: size,
            height: size,
            visibility: chart ? 'visible' : 'hidden',
          }}
        />
        <div
          className="bgCircle bg-surface-secondary z-[1] absolute top-[2.5%] left-[2.5%] w-[95%] h-[95%] rounded-full"
          style={{
            boxShadow: `
            0 4px 8px #ffffff,
            0 24px 40px rgba(0, 0, 0, 0.06),
            0 -3px 8px rgba(0, 0, 0, 0.15),
						inset 0 24px 30px rgba(0, 0, 0, 0.07)
          `,
          }}
        />
        <div
          className="innerCircle bg-white z-[2] absolute top-[50%] left-[50%] w-[45%] h-[45%] rounded-full translate-x-[-50%] translate-y-[-50%] flex flex-col items-center justify-center"
          style={{
            boxShadow: `
             inset 0 4px 8px #ffffff,
            inset 0 24px 40px rgba(0, 0, 0, 0.06),
            inset 0 -3px 8px rgba(0, 0, 0, 0.07),
						0 24px 30px rgba(0, 0, 0, 0.07)
          `,
          }}
        >
          {showInnerContent && displayLabel && displayValue !== null && (
            <>
              <span className="text-3xl font-bold text-red-600">{displayValue}%</span>
              <span className="text-sm text-gray-600 mt-1">{displayLabel}</span>
            </>
          )}
        </div>
      </div>
    );
  }
);

DonutChartInner.displayName = 'DonutChartInner';

export default DonutChartInner;
