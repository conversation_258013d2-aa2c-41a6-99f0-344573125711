import { ReactNode, useEffect, useState } from 'react';
import { faChevronCircleLeft, faChevronCircleRight } from '@fortawesome/pro-light-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

interface SimpleCardCarousel {
  items: ReactNode[];
  className?: string;
}

// Deprecated. I'm not sure where this was ever used?...

export function SimpleCardCarousel({ items, className = '' }: SimpleCardCarousel) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [nextIndex, setNextIndex] = useState<number | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    if (nextIndex !== null) {
      requestAnimationFrame(() => {
        setIsTransitioning(true);
      });
    }
  }, [nextIndex]);

  const handlePrevious = () => {
    if (nextIndex !== null) return;
    const next = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
    setIsTransitioning(false);
    setNextIndex(next);
  };

  const handleNext = () => {
    if (nextIndex !== null) return;
    const next = currentIndex === items.length - 1 ? 0 : currentIndex + 1;
    setIsTransitioning(false);
    setNextIndex(next);
  };

  const handleDotClick = (index: number) => {
    if (nextIndex !== null || index === currentIndex) return;
    setIsTransitioning(false);
    setNextIndex(index);
  };

  const handleTransitionEnd = () => {
    if (nextIndex !== null && isTransitioning) {
      setCurrentIndex(nextIndex);
      setNextIndex(null);
      setIsTransitioning(false);
    }
  };

  if (!items?.length) return null;

  return (
    <div className={`relative ${className}`}>
      {/* Cards Container */}
      <div className="relative">
        {items.map((item, index) => {
          const isCurrent = index === currentIndex;
          const isNext = index === nextIndex;
          if (!isCurrent && !isNext) return null;

          return (
            <div
              key={index}
              className={`${!isCurrent ? 'absolute inset-0' : ''} ${
                isNext ? 'z-10 transition-opacity duration-500 ease-in-out' : ''
              } ${isNext && isTransitioning ? 'opacity-100' : isNext ? 'opacity-0' : ''}`}
              onTransitionEnd={isNext ? handleTransitionEnd : undefined}
            >
              {item}
            </div>
          );
        })}
      </div>

      {/* Navigation Buttons */}
      <button
        onClick={handlePrevious}
        className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 z-20 w-8 h-8 rounded-full bg-white flex items-center justify-center"
        aria-label="Previous item"
      >
        <FontAwesomeIcon
          icon={faChevronCircleLeft}
          className="w-[24px] h-[24px] text-[24px] font-light text-brand-red"
        />
      </button>

      <button
        onClick={handleNext}
        className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 z-20 w-8 h-8 rounded-full bg-white flex items-center justify-center"
        aria-label="Next item"
      >
        <FontAwesomeIcon
          icon={faChevronCircleRight}
          className="w-[24px] h-[24px] text-[24px] font-light text-brand-red"
        />
      </button>

      {/* Carousel Indicators */}
      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-20">
        {items.map((_, index) => (
          <button
            key={index}
            onClick={() => handleDotClick(index)}
            className={`w-2.5 h-2.5 rounded-full transition-colors ${
              index === currentIndex ? 'bg-brand-red' : 'bg-grey-3'
            }`}
            aria-label={`Go to item ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
}
