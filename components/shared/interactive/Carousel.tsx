'use client';

import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft, faChevronRight } from '@/lib/fontawesome';
import { cn } from '@/lib/utils';

interface CarouselProps {
  items: React.ReactNode[];
  itemsPerPage?: number;
  className?: string;
}

interface NavigationButtonProps {
  direction: 'left' | 'right';
  onClick: () => void;
  containerRect: DOMRect | null;
}

const NavigationButton = ({ direction, onClick, containerRect }: NavigationButtonProps) => {
  if (!containerRect) return null;

  const style: React.CSSProperties = {
    position: 'fixed',
    top: containerRect.top + containerRect.height / 2,
    transform: 'translateY(-50%)',
    ...(direction === 'left'
      ? { left: containerRect.left - 16 }
      : { left: containerRect.right - 16 }),
    zIndex: 20,
  };

  return (
    <div style={style}>
      <div className="w-8 h-8 rounded-full bg-white border-2 border-brand-red flex items-center justify-center shadow-[0_0_0_4px_white]">
        <button
          onClick={onClick}
          className={cn(
            'w-6 h-6 rounded-full bg-white flex items-center justify-center',
            'hover:opacity-90 transition-opacity'
          )}
          aria-label={`${direction === 'left' ? 'Previous' : 'Next'} page`}
        >
          <FontAwesomeIcon
            icon={direction === 'left' ? faChevronLeft : faChevronRight}
            className="w-3 h-3 text-brand-red"
          />
        </button>
      </div>
    </div>
  );
};

export const Carousel = ({ items, itemsPerPage = 4, className }: CarouselProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerRect, setContainerRect] = useState<DOMRect | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Fix total pages calculation to account for zero-based indexing
  const totalPages = Math.max(1, Math.ceil(items.length / itemsPerPage));
  const canGoNext = currentPage < totalPages - 1;
  const canGoPrev = currentPage > 0;

  useEffect(() => {
    const updateRect = () => {
      if (containerRef.current) {
        setContainerRect(containerRef.current.getBoundingClientRect());
      }
    };

    updateRect();
    window.addEventListener('resize', updateRect);
    window.addEventListener('scroll', updateRect);

    return () => {
      window.removeEventListener('resize', updateRect);
      window.removeEventListener('scroll', updateRect);
    };
  }, []);

  const handleNext = () => {
    if (canGoNext && !isAnimating) {
      setIsAnimating(true);
      setCurrentPage(prev => Math.min(prev + 1, totalPages - 1));
      setTimeout(() => setIsAnimating(false), 500);
    }
  };

  const handlePrev = () => {
    if (canGoPrev && !isAnimating) {
      setIsAnimating(true);
      setCurrentPage(prev => Math.max(prev - 1, 0));
      setTimeout(() => setIsAnimating(false), 500);
    }
  };

  // Calculate the width of each item including gap
  const itemWidth = `calc((100% - ${(itemsPerPage - 1) * 0.75}rem) / ${itemsPerPage})`;
  const gapWidth = '0.75rem';

  // Calculate total translation to move by full item widths plus gaps
  const translateX = `calc(-${currentPage} * (((100% - ${(itemsPerPage - 1) * 0.75}rem) / ${itemsPerPage}) + ${gapWidth}))`;

  return (
    <>
      <div ref={containerRef} className={cn('relative w-full', className)}>
        <div className="overflow-hidden w-full">
          <div
            className="flex gap-3 transition-transform duration-500 ease-in-out"
            style={{ transform: `translateX(${translateX})` }}
          >
            {items.map((item, idx) => (
              <div key={idx} className="flex-shrink-0" style={{ width: itemWidth }}>
                {item}
              </div>
            ))}
          </div>
        </div>
      </div>

      {typeof window !== 'undefined' && (
        <>
          {canGoPrev &&
            createPortal(
              <NavigationButton
                direction="left"
                onClick={handlePrev}
                containerRect={containerRect}
              />,
              document.body
            )}
          {canGoNext &&
            createPortal(
              <NavigationButton
                direction="right"
                onClick={handleNext}
                containerRect={containerRect}
              />,
              document.body
            )}
        </>
      )}
    </>
  );
};
