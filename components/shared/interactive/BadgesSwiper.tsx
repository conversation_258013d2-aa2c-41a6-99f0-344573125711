'use client';

import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/free-mode';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { faChevronLeft, faChevronRight, faSpinnerThird } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import type { Swiper as SwiperType } from 'swiper';
import { FreeMode, Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import BadgeCard, { BadgeCardProps } from '@/components/shared/cards/BadgeCard';

export interface BadgeSwiperProps {
  items: BadgeCardProps[];
  className?: string;
}

const BadgesSwiper: React.FC<BadgeSwiperProps> = ({ items, className = '' }) => {
  const swiperRef = useRef<SwiperType | null>(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [announcementText, setAnnouncementText] = useState('');
  const [isMounted, setIsMounted] = useState(false);

  // Only run on client-side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleNext = useCallback(() => {
    if (!swiperRef.current) return;
    swiperRef.current.slideNext();
  }, []);

  const handlePrev = useCallback(() => {
    if (!swiperRef.current) return;
    swiperRef.current.slidePrev();
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        handlePrev();
        e.preventDefault();
      } else if (e.key === 'ArrowRight') {
        handleNext();
        e.preventDefault();
      }
    },
    [handleNext, handlePrev]
  );

  // Update announcement for screen readers
  const updateAnnouncement = useCallback(
    (index: number) => {
      if (!items || !items[index]) return;

      const badge = items[index];
      const total = items.length;
      const status = badge.isAchieved ? 'Achieved' : 'Not achieved';

      setAnnouncementText(
        `Badge ${index + 1} of ${total}: ${badge.name}. ${status}. ` +
          `Requires ${badge.moduleRequirement} modules. ` +
          `Current progress: ${badge.currentProgress || 0} modules completed.`
      );
    },
    [items]
  );

  // Don't render anything on server
  if (!isMounted) {
    return (
      <div className={`flex justify-center items-center h-64 ${className}`}>
        <FontAwesomeIcon icon={faSpinnerThird} className="text-gray-600 animate-spin" />
      </div>
    );
  }

  return (
    <div
      className={`relative focus-within:outline-none group ${className}`}
      tabIndex={0}
      onKeyDown={handleKeyDown}
      role="region"
      aria-label="Badge carousel"
    >
      {/* Screen reader announcement */}
      <div className="sr-only" aria-live="polite">
        {announcementText}
      </div>

      {/* Navigation buttons - hidden by default, visible on focus */}
      <div className="absolute top-1/2 -left-4 -translate-y-1/2 z-10">
        <button
          onClick={handlePrev}
          disabled={isBeginning}
          className={`rounded-full bg-white shadow-md p-2 opacity-0 pointer-events-none group-focus-within:opacity-100 group-focus-within:pointer-events-auto transition-opacity ${
            isBeginning ? 'group-focus-within:opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'
          }`}
          aria-label="Previous badge"
        >
          <FontAwesomeIcon icon={faChevronLeft} className="text-gray-600 w-5 h-5" />
        </button>
      </div>

      <div className="absolute top-1/2 -right-4 -translate-y-1/2 z-10">
        <button
          onClick={handleNext}
          disabled={isEnd}
          className={`rounded-full bg-white shadow-md p-2 opacity-0 pointer-events-none group-focus-within:opacity-100 group-focus-within:pointer-events-auto transition-opacity ${
            isEnd ? 'group-focus-within:opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'
          }`}
          aria-label="Next badge"
        >
          <FontAwesomeIcon icon={faChevronRight} className="text-gray-600 w-5 h-5" />
        </button>
      </div>

      <Swiper
        breakpoints={{
          1: {
            spaceBetween: 16,
          },
          1024: {
            spaceBetween: 24,
          },
        }}
        slidesPerView={'auto'}
        className="!overflow-visible [&>div>.swiper-slide]:w-auto"
        onSwiper={swiper => {
          swiperRef.current = swiper;
          setIsBeginning(swiper.isBeginning);
          setIsEnd(swiper.isEnd);
          setActiveIndex(swiper.activeIndex);
          if (items && items.length > 0) {
            updateAnnouncement(swiper.activeIndex);
          }
        }}
        onSlideChange={swiper => {
          setIsBeginning(swiper.isBeginning);
          setIsEnd(swiper.isEnd);
          setActiveIndex(swiper.activeIndex);
          updateAnnouncement(swiper.activeIndex);
        }}
        onReachBeginning={() => setIsBeginning(true)}
        onReachEnd={() => setIsEnd(true)}
        onFromEdge={() => {
          setIsBeginning(false);
          setIsEnd(false);
        }}
        modules={[Navigation, FreeMode]}
        freeMode
        grabCursor
      >
        {items?.map((badge, index) => (
          <SwiperSlide key={`badge-swiper-item-${badge.id || index}`} className="w-auto">
            <BadgeCard
              id={badge.id}
              name={badge.name}
              moduleRequirement={badge.moduleRequirement}
              isAchieved={badge.isAchieved}
              achievedAssetUrl={badge.achievedAssetUrl}
              unachievedAssetUrl={badge.unachievedAssetUrl}
              currentProgress={badge.currentProgress}
              progressPercentage={badge.progressPercentage}
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default BadgesSwiper;
