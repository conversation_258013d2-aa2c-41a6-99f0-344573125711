'use client';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import React, { ReactNode, useEffect, useState } from 'react';
import { faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import type { Swiper as SwiperType } from 'swiper';
import { Autoplay, Navigation, Pagination } from 'swiper/modules';
import { Swiper } from 'swiper/react';

interface CardCarouselProps {
  children: ReactNode;
  spaceBetween?: number;
  slidesPerView?: number | 'auto';
  className?: string;
  autoplay?: boolean;
}

interface NavigationButtonProps {
  direction: 'prev' | 'next';
  onClick: () => void;
  disabled: boolean;
}

export const NavigationButton: React.FC<NavigationButtonProps> = ({
  direction,
  onClick,
  disabled,
}) => {
  const isNext = direction === 'next';

  return (
    <button
      type="button"
      onClick={onClick}
      className={clsx(
        'absolute top-1/2 -translate-y-1/2 z-20',
        'size-8 bg-white border-[3px] border-brand-red flex items-center justify-center',
        'shadow-[0_0_0_4px_white] rounded-full overflow-hidden',
        'transition-opacity duration-300 ease-in-out',
        disabled ? 'opacity-0 pointer-events-none' : 'opacity-100',
        direction === 'prev' ? '-left-4' : '-right-4'
      )}
      aria-label={isNext ? 'Next card' : 'Previous card'}
    >
      <span className="sr-only">{isNext ? 'Next card' : 'Previous card'}</span>
      <div className="w-6 h-6 rounded-full bg-white flex items-center justify-center">
        <FontAwesomeIcon
          icon={isNext ? faChevronRight : faChevronLeft}
          className="w-3 h-3 text-brand-red"
          aria-hidden="true"
        />
      </div>
    </button>
  );
};

const CardCarousel: React.FC<CardCarouselProps> = ({
  children,
  spaceBetween = 16,
  slidesPerView = 1,
  className,
  autoplay = false,
}) => {
  const [swiper, setSwiper] = useState<SwiperType | null>(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  useEffect(() => {
    if (swiper) {
      setIsBeginning(swiper.isBeginning);
      setIsEnd(swiper.isEnd);

      const updateState = () => {
        setIsBeginning(swiper.isBeginning);
        setIsEnd(swiper.isEnd);
      };

      swiper.on('slideChange', updateState);
      swiper.on('snapGridLengthChange', updateState);

      return () => {
        swiper.off('slideChange', updateState);
        swiper.off('snapGridLengthChange', updateState);
      };
    }
  }, [swiper]);

  // Don't render if no children
  if (!React.Children.count(children)) {
    return null;
  }

  return (
    <div className="overflow-hidden rounded-2xl px-4 -mx-4">
      <div className="relative">
        <Swiper
          modules={[Navigation, Pagination, Autoplay]}
          spaceBetween={spaceBetween}
          slidesPerView={slidesPerView}
          onSwiper={setSwiper}
          autoHeight
          autoplay={
            autoplay
              ? {
                  delay: 5000,
                  disableOnInteraction: true,
                  pauseOnMouseEnter: true,
                }
              : false
          }
          pagination={{
            clickable: true,
          }}
          className={clsx(
            'w-full',
            '[&_.swiper-pagination]:!bottom-6',
            '[&_.swiper-pagination]:flex [&_.swiper-pagination]:justify-center [&_.swiper-pagination]:items-center',
            '[&_.swiper-pagination]:gap-2',
            // Base bullet styles
            '[&_.swiper-pagination-bullet]:!m-0',
            '[&_.swiper-pagination-bullet]:!size-4',
            '[&_.swiper-pagination-bullet]:!border-2 [&_.swiper-pagination-bullet]:!border-white',
            '[&_.swiper-pagination-bullet]:!bg-[#D7DBDD]',
            '[&_.swiper-pagination-bullet]:!rounded-full',
            '[&_.swiper-pagination-bullet]:!opacity-100',
            '[&_.swiper-pagination-bullet]:transition-colors [&_.swiper-pagination-bullet]:duration-300',
            // Active bullet styles - using double class for higher specificity
            '[&_.swiper-pagination-bullet.swiper-pagination-bullet-active]:!bg-brand-red',
            '[&_.swiper-pagination-bullet.swiper-pagination-bullet-active]:!opacity-100',
            className
          )}
        >
          {children}
        </Swiper>

        {/* Navigation Buttons */}
        <NavigationButton
          direction="prev"
          onClick={() => swiper?.slidePrev()}
          disabled={isBeginning}
        />
        <NavigationButton direction="next" onClick={() => swiper?.slideNext()} disabled={isEnd} />
      </div>
    </div>
  );
};

export default CardCarousel;
