'use client';

import React, { useEffect, useState } from 'react';
import { faChevronCircleLeft, faChevronCircleRight } from '@fortawesome/pro-light-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import type { FontAwesomeIconProps } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import type { Swiper as SwiperType } from 'swiper';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import PortraitModuleCard from '@/components/shared/cards/PortraitModuleCard';
// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';

export interface Module {
  id: string;
  title: string;
  subtitle: string;
  coverImage: string;
  href: string;
}

interface ModuleCarouselProps {
  title?: string;
  overline?: string;
  titleIcon?: FontAwesomeIconProps['icon'];
  modules: Module[];
  className?: string;
  navigationContent?: React.ReactNode;
  showFadeGradient?: boolean;
}

export default function ModuleCarousel({
  title,
  overline,
  titleIcon,
  modules,
  className,
  navigationContent,
  showFadeGradient = false,
}: ModuleCarouselProps) {
  const [swiper, setSwiper] = useState<SwiperType | null>(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  useEffect(() => {
    if (swiper) {
      setIsBeginning(swiper.isBeginning);
      setIsEnd(swiper.isEnd);

      const updateState = () => {
        setIsBeginning(swiper.isBeginning);
        setIsEnd(swiper.isEnd);
      };

      swiper.on('slideChange', updateState);
      swiper.on('snapGridLengthChange', updateState);

      return () => {
        swiper.off('slideChange', updateState);
        swiper.off('snapGridLengthChange', updateState);
      };
    }
  }, [swiper, modules]);

  return (
    <div className={clsx('relative', className)}>
      {/* Header with Navigation */}
      <div
        className={clsx('flex items-center justify-between', {
          'mb-4': !navigationContent,
          'mb-8': navigationContent,
        })}
      >
        {(title || overline) && (
          <div className="block space-y-1 text-text-primary">
            {(overline || titleIcon) && (
              <div className="flex items-center gap-2">
                {titleIcon && (
                  <FontAwesomeIcon
                    icon={titleIcon}
                    className="!size-4 text-current"
                    aria-hidden="true"
                  />
                )}
                {overline && <span className="pa-eyebrow !leading-5">{overline}</span>}
              </div>
            )}

            {title && <h2 className="text-2xl text-text-primary">{title}</h2>}
          </div>
        )}

        <div className="flex items-center gap-2">
          <button
            type="button"
            onClick={() => swiper?.slidePrev()}
            disabled={isBeginning}
            className={clsx(
              'rounded-full flex items-center justify-center transition-opacity',
              isBeginning && 'opacity-50 cursor-not-allowed'
            )}
            aria-label="Previous module"
          >
            <span className="sr-only">Previous module</span>
            <FontAwesomeIcon
              icon={faChevronCircleLeft}
              className="!size-6 text-brand-red"
              aria-hidden="true"
            />
          </button>
          <button
            type="button"
            onClick={() => swiper?.slideNext()}
            disabled={isEnd}
            className={clsx(
              'rounded-full flex items-center justify-center transition-opacity',
              isEnd && 'opacity-50 cursor-not-allowed'
            )}
            aria-label="Next module"
          >
            <span className="sr-only">Next module</span>
            <FontAwesomeIcon
              icon={faChevronCircleRight}
              className="!size-6 text-brand-red"
              aria-hidden="true"
            />
          </button>

          {navigationContent}
        </div>
      </div>

      {/* Carousel Content */}
      <div className="relative overflow-hidden">
        {/* Fade Effect */}
        {showFadeGradient && (
          <div className="absolute right-0 top-0 bottom-0 w-24 bg-gradient-to-r from-transparent to-surface-secondary z-10 pointer-events-none" />
        )}

        <Swiper
          modules={[Navigation]}
          onSwiper={setSwiper}
          slidesPerView="auto"
          spaceBetween={24}
          className="w-full"
          watchOverflow={true}
          observer={true}
          observeParents={true}
          resizeObserver={true}
          watchSlidesProgress={true}
          onSlideChange={() => {
            if (swiper) {
              setIsBeginning(swiper.isBeginning);
              setIsEnd(swiper.isEnd);
            }
          }}
          onResize={() => {
            if (swiper) {
              setIsBeginning(swiper.isBeginning);
              setIsEnd(swiper.isEnd);
            }
          }}
        >
          {modules.map(module => (
            <SwiperSlide key={module.id} className="!w-auto">
              <PortraitModuleCard
                title={module.title}
                subtitle={module.subtitle}
                coverImage={module.coverImage}
                href={module.href}
              />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
}
