'use client';

import { faChevronDown } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Disclosure, DisclosureButton, DisclosurePanel, Transition } from '@headlessui/react';
import { cn } from '@/lib/utils';

interface AccordionProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  className?: string;
  onToggle?: () => void;
}

export default function Accordion({
  title,
  children,
  defaultOpen = false,
  className,
  onToggle,
}: AccordionProps) {
  return (
    <Disclosure as="div" defaultOpen={defaultOpen} className={className}>
      {({ open }) => (
        <>
          <DisclosureButton
            className={cn(
              'flex w-full justify-between items-center px-4 py-3',
              'text-left text-sm font-semibold text-gray-900',
              'hover:bg-gray-50 focus:outline-none focus-visible:ring focus-visible:ring-brand-blue focus-visible:ring-opacity-50'
            )}
            onClick={() => {
              if (onToggle) {
                onToggle();
              }
            }}
          >
            <span>{title}</span>
            <FontAwesomeIcon
              icon={faChevronDown}
              className={cn(
                'h-4 w-4 text-gray-500 transition-transform duration-200',
                open && 'transform rotate-180'
              )}
            />
          </DisclosureButton>
          {/* <Transition
            enter="transition duration-100 ease-out"
            enterFrom="transform scale-95 opacity-0"
            enterTo="transform scale-100 opacity-100"
            leave="transition duration-75 ease-out"
            leaveFrom="transform scale-100 opacity-100"
            leaveTo="transform scale-95 opacity-0"
          >
            <DisclosurePanel className="px-4 pb-4">{children}</DisclosurePanel>
          </Transition> */}
          <DisclosurePanel unmount={false} className="px-4 pb-4">
            {children}
          </DisclosurePanel>
        </>
      )}
    </Disclosure>
  );
}
