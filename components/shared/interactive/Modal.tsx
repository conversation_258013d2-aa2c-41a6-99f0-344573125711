'use client';

import { Dialog, DialogBackdrop, DialogPanel } from '@headlessui/react';
import { cn } from '@/lib/utils';
import { useModalStore } from '@/stores/modal.store';

// Define the mapping for maxWidth to Tailwind classes
const maxWidthClasses = {
  sm: 'sm:max-w-sm',
  md: 'sm:max-w-md',
  lg: 'sm:max-w-lg',
  xl: 'sm:max-w-xl',
  '2xl': 'sm:max-w-2xl',
};

export function Modal() {
  const { isOpen, component, close, maxWidth } = useModalStore();

  // Don't render anything on the server
  if (typeof window === 'undefined') return null;

  return (
    <Dialog open={isOpen} onClose={close} className="relative z-50">
      {/* Backdrop */}
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-black/30 backdrop-blur-sm transition-opacity data-[closed]:opacity-0 [data-enter]:duration-300 [data-enter]:ease-out data-[leave]:duration-200 data-[leave]:ease-in"
      />

      {/* Dialog Content */}
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel
          transition
          className={cn(
            'relative bg-white rounded-[32px] shadow-lg overflow-hidden',
            'w-full max-w-[95vw]',
            maxWidth ? maxWidthClasses[maxWidth] : maxWidthClasses.lg,
            'transform transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 [data-enter]:duration-300 [data-enter]:ease-out data-[leave]:duration-200 data-[leave]:ease-in data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95'
          )}
        >
          <div className="max-h-[90vh] overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">
            {component}
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
