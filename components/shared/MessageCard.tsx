'use client';

import React, { ReactNode } from 'react';
import Image from 'next/image';
import { faEllipsisH } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Menu } from '@headlessui/react';
import { cn } from '@/lib/utils';
import Avatar from './Avatar';

export type MessageType = 'user' | 'organization';

interface MenuItemProps {
  active: boolean;
}

interface MessageCardProps {
  children: ReactNode;
  orientation: 'left' | 'right';
  name: string;
  image?: string;
  type: MessageType;
  time: Date;
  ownerId: string;
  currentUserId: string;
  onMarkUnread?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

export default function MessageCard({
  children,
  orientation,
  name,
  image,
  type,
  time,
  ownerId,
  currentUserId,
  onMarkUnread,
  onEdit,
  onDelete,
}: MessageCardProps) {
  const isOwner = ownerId === currentUserId;
  const formattedTime = new Intl.DateTimeFormat('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  }).format(time);

  return (
    <div
      className={cn(
        'flex gap-3 max-w-[600px] w-full',
        orientation === 'right' && 'ml-auto flex-row-reverse'
      )}
    >
      {/* Avatar/Image */}
      <div className="flex-shrink-0">
        {type === 'user' ? (
          <Avatar
            src={image}
            firstName={name.split(' ')[0]}
            lastName={name.split(' ')[1]}
            size="sm"
          />
        ) : (
          <div className="w-8 h-8 relative rounded overflow-hidden">
            {image && <Image src={image} alt={name} fill className="object-cover" />}
          </div>
        )}
      </div>

      {/* Message Content */}
      <div className="flex-1">
        {/* Header */}
        <div className="flex items-center justify-between mb-1">
          <span className="text-sm font-medium text-text-primary">{name}</span>
          <div className="flex items-center gap-2">
            <span className="text-xs text-grey-5">{formattedTime}</span>
            <Menu as="div" className="relative">
              <Menu.Button className="p-1 hover:bg-grey-1 rounded transition-colors">
                <FontAwesomeIcon icon={faEllipsisH} className="text-grey-5 text-sm" />
              </Menu.Button>
              <Menu.Items className="absolute right-0 mt-1 w-48 bg-white rounded-lg shadow-lg py-1 z-10">
                <Menu.Item>
                  {({ active }: MenuItemProps) => (
                    <button
                      className={cn('w-full text-left px-4 py-2 text-sm', active && 'bg-grey-1')}
                      onClick={onMarkUnread}
                    >
                      Mark as Unread
                    </button>
                  )}
                </Menu.Item>
                {isOwner && (
                  <>
                    <Menu.Item>
                      {({ active }: MenuItemProps) => (
                        <button
                          className={cn(
                            'w-full text-left px-4 py-2 text-sm',
                            active && 'bg-grey-1'
                          )}
                          onClick={onEdit}
                        >
                          Edit Message
                        </button>
                      )}
                    </Menu.Item>
                    <Menu.Item>
                      {({ active }: MenuItemProps) => (
                        <button
                          className={cn(
                            'w-full text-left px-4 py-2 text-sm text-brand-red',
                            active && 'bg-grey-1'
                          )}
                          onClick={onDelete}
                        >
                          Delete Message
                        </button>
                      )}
                    </Menu.Item>
                  </>
                )}
              </Menu.Items>
            </Menu>
          </div>
        </div>

        {/* Message Body */}
        <div
          className={cn(
            'bg-white p-4 rounded-2xl shadow-sm',
            orientation === 'left' ? 'rounded-bl-none' : 'rounded-br-none'
          )}
        >
          {children}
        </div>
      </div>
    </div>
  );
}
