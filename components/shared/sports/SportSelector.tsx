import React, { useEffect, useRef, useState } from 'react';
import {
  Combobox,
  ComboboxOption,
  ComboboxOptions,
  ComboboxInput as HeadlessComboboxInput,
} from '@headlessui/react';
import debounce from 'lodash/debounce';
import { DynamicListEditor } from '@/components/shared/dynamic-list';
import type { DynamicListItem } from '@/components/shared/dynamic-list/types';
import SiteInput from '@/components/shared/form/SiteInput';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import type { Sport } from '@/services/positive-athlete-profile.service';

interface SportSearchResponse {
  platform: Sport[];
  custom: Sport[];
}

/**
 * Represents a sport item in the editor, combining both platform and custom sports
 * with consistent order handling across both types.
 */
export interface SportItem extends DynamicListItem {
  name: string;
  icon?: string;
  isCustom: boolean;
  customName?: string;
  sportId?: number;
  order: number;
}

interface SportSelectorProps {
  sports: SportItem[];
  onChange: (sports: SportItem[]) => void;
  onValidationError?: (error: string) => void;
  className?: string;
  allowCustomSportCreation?: boolean;
}

export function SportSelector({
  sports,
  onChange,
  onValidationError,
  className = '',
  allowCustomSportCreation = true,
}: SportSelectorProps) {
  const { searchSports } = usePositiveAthleteProfile();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeSearchId, setActiveSearchId] = useState<string | null>(null);
  const [inputValues, setInputValues] = useState<Record<string, string>>({});
  const [searchResults, setSearchResults] = useState<SportSearchResponse>({
    platform: [],
    custom: [],
  });
  const [isSearching, setIsSearching] = useState(false);

  // Create a debounced search function
  const debouncedSearch = useRef(
    debounce(async (query: string) => {
      if (query.length < 2) {
        setSearchResults({ platform: [], custom: [] });
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      try {
        const results = await searchSports(query);
        setSearchResults(results);
      } catch (error) {
        console.error('Search failed:', error);
        setSearchResults({ platform: [], custom: [] });
      }
      setIsSearching(false);
    }, 300)
  ).current;

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const handleAdd = () => {
    const newSport: SportItem = {
      id: crypto.randomUUID(),
      name: '',
      isCustom: false,
      order: sports.length,
    };
    onChange([...sports, newSport]);
  };

  const handleRemove = (id: string) => {
    onChange(sports.filter(item => item.id !== id));
  };

  const handleUpdate = (id: string, updates: Partial<Omit<SportItem, 'id'>>) => {
    onChange(sports.map(item => (item.id === id ? { ...item, ...updates } : item)));
  };

  const handleReorder = (reorderedItems: DynamicListItem[]) => {
    onChange(
      reorderedItems.map((item, index) => {
        const sport = sports.find(s => s.id === item.id);
        if (!sport) return sport!;
        return { ...sport, order: index };
      })
    );
  };

  const handleInputChange = (id: string, value: string) => {
    setInputValues(prev => ({ ...prev, [id]: value }));
    setActiveSearchId(id);
    debouncedSearch(value);
  };

  const handleSportSelect = (id: string, selectedSport: Sport | null) => {
    if (!selectedSport) return;

    // Check if this platform sport is already added
    if (selectedSport.id !== -1 && !selectedSport.isCustom) {
      const isDuplicate = sports.some(
        item => !item.isCustom && item.sportId === selectedSport.id && item.id !== id
      );
      if (isDuplicate) {
        onValidationError?.('You cannot add the same sport more than once.');
        return;
      }
    }

    // Check if this custom sport name is already used
    if (selectedSport.id === -1 || selectedSport.isCustom) {
      const sportName = selectedSport.name.toLowerCase();
      const isDuplicateCustom = sports.some(
        item => item.id !== id && item.isCustom && item.customName?.toLowerCase() === sportName
      );
      const isDuplicatePlatform = sports.some(
        item => !item.isCustom && item.name.toLowerCase() === sportName
      );

      if (isDuplicateCustom) {
        onValidationError?.('You cannot add the same custom sport more than once.');
        return;
      }
      if (isDuplicatePlatform) {
        onValidationError?.(
          'You cannot add a custom sport that matches an existing platform sport.'
        );
        return;
      }
    }

    if (selectedSport.id === -1) {
      // Convert to custom sport
      const query = inputValues[id] || '';
      handleUpdate(id, {
        isCustom: true,
        sportId: undefined,
        name: query,
        customName: query,
        icon: undefined,
      });
    } else if (selectedSport.isCustom) {
      // Handle existing custom sport
      handleUpdate(id, {
        isCustom: true,
        sportId: undefined,
        name: selectedSport.name,
        customName: selectedSport.name,
        icon: selectedSport.icon,
      });
    } else {
      // Handle platform sport
      handleUpdate(id, {
        name: selectedSport.name,
        icon: selectedSport.icon,
        sportId: selectedSport.id,
        isCustom: false,
        customName: undefined,
      });
    }

    // Clear input value and search state
    setInputValues(prev => ({ ...prev, [id]: '' }));
    setActiveSearchId(null);
    setSearchResults({ platform: [], custom: [] });
  };

  return (
    <div className={className}>
      <DynamicListEditor<SportItem>
        items={sports}
        onAdd={handleAdd}
        onRemove={handleRemove}
        onUpdate={handleUpdate}
        onReorder={handleReorder}
        getTitle={(item: SportItem) => `SPORT ${sports.indexOf(item) + 1}`}
        disableRemove={(item: SportItem) => sports.length === 1}
        renderFields={(item: SportItem) => (
          <div className="flex items-center gap-4">
            <Combobox
              as="div"
              className="flex-1 relative"
              value={null}
              onChange={(sport: Sport | null) => handleSportSelect(item.id, sport)}
            >
              <HeadlessComboboxInput
                label="Search for a sport..."
                as={SiteInput}
                value={inputValues[item.id] || item.name || ''}
                onChange={e => handleInputChange(item.id, e.target.value)}
                placeholder="Search for a sport..."
                className="w-full"
                hideLabel
              />
              {activeSearchId === item.id && (
                <ComboboxOptions className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black/5 overflow-auto focus:outline-none sm:text-sm">
                  {isSearching ? (
                    <div className="px-4 py-2 text-sm text-neutral-500">Searching...</div>
                  ) : searchResults.platform.length === 0 &&
                    searchResults.custom.length === 0 &&
                    !inputValues[item.id] ? (
                    <div className="px-4 py-2 text-sm text-neutral-500">
                      Start typing to search...
                    </div>
                  ) : (
                    <>
                      {/* Platform Sports */}
                      {searchResults.platform.length > 0 && (
                        <>
                          <div className="px-4 py-2 text-xs font-medium text-neutral-500">
                            PLATFORM SPORTS
                          </div>
                          {searchResults.platform.map((sport: Sport) => (
                            <ComboboxOption
                              key={sport.id}
                              value={sport}
                              className={({ active }) =>
                                `relative cursor-default select-none py-2 pl-3 pr-9 ${
                                  active ? 'bg-neutral-100 text-neutral-900' : 'text-neutral-700'
                                }`
                              }
                            >
                              {sport.name}
                            </ComboboxOption>
                          ))}
                        </>
                      )}

                      {/* Custom Sports */}
                      {searchResults.custom.length > 0 && (
                        <>
                          <div className="px-4 py-2 text-xs font-medium text-neutral-500">
                            CUSTOM SPORTS
                          </div>
                          {searchResults.custom.map((sport: Sport) => (
                            <ComboboxOption
                              key={sport.id}
                              value={{ ...sport, isCustom: true }}
                              className={({ active }) =>
                                `relative cursor-default select-none py-2 pl-3 pr-9 ${
                                  active ? 'bg-neutral-100 text-neutral-900' : 'text-neutral-700'
                                }`
                              }
                            >
                              {sport.name}
                            </ComboboxOption>
                          ))}
                        </>
                      )}

                      {/* Add Custom Sport Option - Only show if allowed */}
                      {allowCustomSportCreation && inputValues[item.id] && (
                        <>
                          {(searchResults.platform.length > 0 ||
                            searchResults.custom.length > 0) && (
                            <div className="border-t border-gray-200" />
                          )}
                          <ComboboxOption
                            value={{ id: -1, name: inputValues[item.id], isCustom: true } as Sport}
                            className={({ active }) =>
                              `relative cursor-default select-none py-2 pl-3 pr-9 ${
                                active ? 'bg-neutral-100 text-neutral-900' : 'text-neutral-700'
                              }`
                            }
                          >
                            Add &quot;{inputValues[item.id]}&quot; as custom sport
                          </ComboboxOption>
                        </>
                      )}
                    </>
                  )}
                </ComboboxOptions>
              )}
            </Combobox>
          </div>
        )}
        addButtonText="Add Another Sport"
      />
    </div>
  );
}
