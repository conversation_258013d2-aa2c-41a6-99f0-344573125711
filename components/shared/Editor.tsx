'use client';

import { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Underline from '@tiptap/extension-underline';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import {
  faBold,
  faCheck,
  faEdit,
  faItalic,
  faListUl,
  faTimes,
  faUnderline,
} from '@/lib/fontawesome';

interface EditorProps {
  content: string;
  isEditing: boolean;
  isLoading: boolean;
  error: string | null;
  isDirty: boolean;
  isEditable: boolean;
  onEdit: () => void;
  onCancel: () => void;
  onSave: () => Promise<void>;
  onChange: (content: string) => void;
  placeholder?: string;
}

export function Editor({
  content,
  isEditing,
  isLoading,
  error,
  isDirty,
  isEditable,
  onEdit,
  onCancel,
  onSave,
  onChange,
  placeholder = 'Enter content...',
}: EditorProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const editor = useEditor({
    extensions: [StarterKit, Underline],
    content,
    editable: isEditing && !isLoading,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    immediatelyRender: false,
  });

  if (!isMounted) {
    return null;
  }

  if (isEditing) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex space-x-2 items-center">
            <button
              onClick={() => editor?.chain().focus().toggleBold().run()}
              className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('bold') ? 'bg-gray-200' : ''}`}
              type="button"
              disabled={isLoading}
            >
              <FontAwesomeIcon icon={faBold} />
            </button>
            <button
              onClick={() => editor?.chain().focus().toggleItalic().run()}
              className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('italic') ? 'bg-gray-200' : ''}`}
              type="button"
              disabled={isLoading}
            >
              <FontAwesomeIcon icon={faItalic} />
            </button>
            <button
              onClick={() => editor?.chain().focus().toggleUnderline().run()}
              className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('underline') ? 'bg-gray-200' : ''}`}
              type="button"
              disabled={isLoading}
            >
              <FontAwesomeIcon icon={faUnderline} />
            </button>
            <button
              onClick={() => editor?.chain().focus().toggleBulletList().run()}
              className={`p-2 rounded hover:bg-gray-100 ${editor?.isActive('bulletList') ? 'bg-gray-200' : ''}`}
              type="button"
              disabled={isLoading}
            >
              <FontAwesomeIcon icon={faListUl} />
            </button>
          </div>
        </div>

        {error && <div className="text-red-600 text-sm">{error}</div>}

        <div className="border border-gray-300 rounded-lg">
          <EditorContent
            editor={editor}
            className="min-h-[150px] w-full px-4 py-3 [&_.ProseMirror]:min-h-[150px] [&_.ProseMirror]:outline-none [&_.ProseMirror]:prose [&_.ProseMirror]:max-w-none [&_.ProseMirror_ul]:list-disc [&_.ProseMirror_ul]:pl-[40px] [&_.ProseMirror_li]:my-0 [&_.ProseMirror_li_p]:my-0"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        {isEditable && (
          <button onClick={onEdit} className="text-[#002B5C] hover:text-[#003d80]" type="button">
            <FontAwesomeIcon icon={faEdit} className="mr-2" />
            Edit
          </button>
        )}
      </div>

      <div className="prose max-w-none">
        {content ? (
          <div dangerouslySetInnerHTML={{ __html: content }} className="text-gray-700" />
        ) : (
          <p className="text-gray-500 italic">
            {isEditable ? 'Click edit to add content.' : 'No content yet.'}
          </p>
        )}
      </div>
    </div>
  );
}
