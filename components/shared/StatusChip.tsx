'use client';

import { cn } from '@/lib/utils';

interface StatusChipProps {
  label: string;
  variant?: 'default' | 'reverse' | 'grey' | 'success' | 'alert' | 'danger';
  className?: string;
}

export default function StatusChip({ label, variant = 'default', className }: StatusChipProps) {
  return (
    <div
      className={cn(
        'inline-flex items-center justify-center p-[8px] rounded-[8px] text-[14px] leading-[1.2] font-bold',
        // Default (red)
        variant === 'default' && 'bg-brand-red text-white',
        // Reverse (white)
        variant === 'reverse' && 'bg-white text-grey-7',
        // Grey
        variant === 'grey' && 'bg-grey-2 text-grey-7',
        // Success (green)
        variant === 'success' && 'bg-utility-success text-white',
        // Alert (yellow/orange)
        variant === 'alert' && 'bg-utility-alert text-white',
        // Danger (dark red)
        variant === 'danger' && 'bg-utility-danger text-white',
        className
      )}
    >
      {label}
    </div>
  );
}
