import React, { useEffect, useRef, useState } from 'react';
import { useSchools } from '@/hooks/useSchools';
import type { School } from '@/services/school.service';
import ComboboxInput from './ComboboxInput';

interface SchoolComboboxInputProps {
  label: string;
  value?: number | null;
  onChange: (schoolId: number | null) => void;
  disabled?: boolean;
  placeholder?: string;
  isFailedValidation?: boolean;
  description?: string;
  countyId?: number;
  stateCode?: string;
  className?: string;
}

const SchoolComboboxInput: React.FC<SchoolComboboxInputProps> = ({
  label,
  value,
  onChange,
  disabled = false,
  placeholder = 'Search for a school...',
  isFailedValidation = false,
  description,
  countyId,
  stateCode,
  className,
}) => {
  const [selectedSchool, setSelectedSchool] = useState<School | null>(null);
  const isFetchingRef = useRef(false);
  const prevValueRef = useRef<number | null | undefined>(value);

  const { schools, isLoading, searchError, setSearchInput, fetchSchoolById } = useSchools({
    countyId,
    stateCode,
  });

  // Load the selected school by ID when the component mounts or value changes
  useEffect(() => {
    // Only proceed if value has changed to prevent unnecessary reruns
    if (value === prevValueRef.current) {
      return;
    }

    prevValueRef.current = value;

    const loadSelectedSchool = async () => {
      // If there's no value, reset selected school
      if (!value) {
        setSelectedSchool(null);
        return;
      }

      // If we're already fetching, don't start another fetch
      if (isFetchingRef.current) {
        return;
      }

      // First check if the school is in the current results
      const schoolInResults = schools.find(s => s.id === value);
      if (schoolInResults) {
        setSelectedSchool(schoolInResults);
        return;
      }

      // Only fetch if we can't find the school in results
      try {
        isFetchingRef.current = true;
        const school = await fetchSchoolById(value);
        if (school) {
          setSelectedSchool(school);
        }
      } finally {
        isFetchingRef.current = false;
      }
    };

    loadSelectedSchool();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, fetchSchoolById]); // Remove schools from dependency array

  // Handle selection of a school
  const handleSchoolChange = (school: School) => {
    setSelectedSchool(school);
    // Ensure we're passing the numeric ID
    if (school && typeof school.id === 'number') {
      onChange(school.id);
    }
  };

  // Handle search input changes
  const handleSearchChange = (query: string) => {
    setSearchInput(query);
  };

  // Custom display function to show school with county and state
  const displayValue = (school: School | null): string => {
    if (!school) return '';

    let display = school.name;

    if (school.county_name && school.state_name) {
      display += ` (${school.county_name}, ${school.state_name})`;
    } else if (school.state_name) {
      display += ` (${school.state_name})`;
    } else if (school.county_name) {
      display += ` (${school.county_name})`;
    }

    return display;
  };

  return (
    <ComboboxInput<School>
      label={label}
      value={selectedSchool}
      onChange={handleSchoolChange}
      options={schools}
      displayValue={displayValue}
      onSearchChange={handleSearchChange}
      isLoading={isLoading}
      disabled={disabled}
      placeholder={placeholder}
      loadingText="Searching schools..."
      emptyText={
        searchError ? 'Error loading schools' : 'No schools found, try a different search term'
      }
      isFailedValidation={isFailedValidation}
      description={description}
      className={className}
    />
  );
};

export default SchoolComboboxInput;
