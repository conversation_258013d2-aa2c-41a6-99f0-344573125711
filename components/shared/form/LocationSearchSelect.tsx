import React, { useCallback, useEffect, useState } from 'react';
import { faMapMarkerAlt } from '@fortawesome/pro-regular-svg-icons';
import { faChevronDown, faTimes } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Combobox,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
  Description,
  Field,
  Label,
} from '@headlessui/react';
import clsx from 'clsx';
import { useLocations } from '@/hooks/useLocations';
import type { LocationOption } from '@/services/location.service';

interface LocationSearchSelectProps {
  label: string;
  value: string | null; // Can be UUID, empty string, or null
  onChange: (value: string | null, displayValue: string) => void; // Always pass both values
  disabled?: boolean;
  required?: boolean;
  description?: string;
  isFailedValidation?: boolean;
  className?: string;
  placeholder?: string;
  hideLabel?: boolean;
  locationDisplay?: string;
}

const LocationSearchSelect: React.FC<LocationSearchSelectProps> = ({
  label,
  value,
  onChange,
  disabled = false,
  required = false,
  description,
  isFailedValidation = false,
  className = '',
  placeholder = 'Search for a city...',
  hideLabel = false,
  locationDisplay,
}) => {
  const [displayValue, setDisplayValue] = useState('');
  const [selectedLocationOption, setSelectedLocationOption] = useState<LocationOption | null>(null);

  const { locations, isSearching, setSearchInput } = useLocations();

  // Always use the locationDisplay prop if available
  useEffect(() => {
    // Check for invalid location display values (empty, just comma+space, or only whitespace)
    const isInvalidLocationDisplay =
      !locationDisplay || locationDisplay === ', ' || locationDisplay.trim() === '';

    if (locationDisplay && !isInvalidLocationDisplay) {
      setDisplayValue(locationDisplay);
    } else if (value === null || value === '') {
      // If value is null/empty and no display value, clear the display
      setDisplayValue('');
    }
  }, [locationDisplay, value]);

  // If we have a UUID value, try to find the matching display text
  useEffect(() => {
    if (value && locations.length > 0) {
      const matchingLocation = locations.find(location => location.id === value);
      if (matchingLocation) {
        setDisplayValue(matchingLocation.label);
        setSelectedLocationOption(matchingLocation);
      }
    } else if (
      !value &&
      (!locationDisplay || locationDisplay === ', ' || locationDisplay.trim() === '')
    ) {
      // If no value or invalid display, ensure field is cleared
      setDisplayValue('');
      setSelectedLocationOption(null);
    }
  }, [value, locations, locationDisplay]);

  // When a location is selected from dropdown, update both the UUID and display value
  const handleLocationSelect = useCallback(
    (location: LocationOption) => {
      setSelectedLocationOption(location);
      setDisplayValue(location.label);
      onChange(location.id, location.label); // Pass both UUID and display text
    },
    [onChange]
  );

  // When user types, update the search query
  const handleInputChange = useCallback(
    (newQuery: string) => {
      // If the input is cleared completely
      if (!newQuery.trim()) {
        setDisplayValue('');
        setSelectedLocationOption(null);
        onChange(null, ''); // Pass null for location and empty string for display
        return;
      }

      setDisplayValue(newQuery);
      setSearchInput(newQuery);
    },
    [setSearchInput, onChange]
  );

  // Clear the selected location
  const handleClear = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation(); // Prevent triggering the combobox
      setSelectedLocationOption(null);
      setDisplayValue('');
      onChange(null, ''); // Pass null for location and empty string for display
    },
    [onChange]
  );

  return (
    <Field disabled={disabled}>
      <Label className={clsx('pa-eyebrow text-text-primary', hideLabel ? 'sr-only' : 'mb-2')}>
        {label} {required && <span className="text-brand-red">*</span>}
      </Label>

      <Combobox value={selectedLocationOption} onChange={handleLocationSelect} disabled={disabled}>
        <div className="relative">
          <div
            className={clsx(
              'flex items-center rounded-lg bg-white border',
              isFailedValidation ? 'border-brand-red' : 'border-gray-200'
            )}
          >
            <FontAwesomeIcon
              icon={faMapMarkerAlt}
              aria-hidden="true"
              className="pointer-events-none absolute left-4 top-1/2 z-10 -translate-y-1/2 text-gray-500 size-4"
            />
            <ComboboxInput
              className={clsx(
                'block w-full py-2 pl-10 pr-8 rounded-lg text-base text-ellipsis focus-within:ring-inset focus-within:ring-1 focus-within:ring-brand-blue border-0 bg-transparent text-gray-900',
                className
              )}
              placeholder={placeholder}
              displayValue={(location: LocationOption | null) => {
                // Display empty string if displayValue is just a comma or empty
                return displayValue === ', ' ? '' : displayValue;
              }}
              onChange={e => handleInputChange(e.target.value)}
              autoComplete="off"
            />

            {displayValue && displayValue !== ', ' && (
              <button
                type="button"
                className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 z-20"
                onClick={handleClear}
                aria-label="Clear selection"
              >
                <FontAwesomeIcon icon={faTimes} className="size-3" />
                <span className="sr-only">Clear selection</span>
              </button>
            )}

            {/* <FontAwesomeIcon
              icon={faChevronDown}
              aria-hidden="true"
              className="pointer-events-none absolute top-1/2 right-4 size-3 -translate-y-1/2"
            /> */}
          </div>

          <ComboboxOptions className="absolute z-10 mt-1 w-full rounded-md bg-white shadow-lg max-h-60 overflow-auto py-1 text-base">
            {isSearching ? (
              <div className="px-4 py-2 text-sm text-gray-500">Loading...</div>
            ) : locations.length === 0 ? (
              <div className="px-4 py-2 text-sm text-gray-500">No locations found</div>
            ) : (
              locations.map(location => (
                <ComboboxOption
                  key={location.id}
                  value={location}
                  className={({ active }) =>
                    clsx(
                      'relative cursor-default select-none py-2 px-4',
                      active ? 'bg-brand-blue text-white' : 'text-gray-900'
                    )
                  }
                >
                  {({ active, selected }) => (
                    <span className={clsx('block truncate', selected && 'font-semibold')}>
                      {location.label}
                    </span>
                  )}
                </ComboboxOption>
              ))
            )}
          </ComboboxOptions>
        </div>
      </Combobox>

      {description && (
        <Description
          className={clsx(
            'mt-1 text-xs',
            isFailedValidation ? 'text-brand-red' : 'text-text-secondary'
          )}
        >
          {description}
        </Description>
      )}
    </Field>
  );
};

export default LocationSearchSelect;
