import React, { SelectHTMLAttributes } from 'react';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faChevronDown } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Description, Field, Label, Select } from '@headlessui/react';
import clsx from 'clsx';

export interface SelectOption {
  value: string;
  label: string;
}

interface SelectInputProps
  extends Omit<SelectHTMLAttributes<HTMLSelectElement>, 'className' | 'onChange'> {
  label: string;
  icon?: IconDefinition;
  disabled?: boolean;
  description?: string;
  isFailedValidation?: boolean;
  className?: string;
  options?: SelectOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  hideLabel?: boolean;
}

const SelectInput: React.FC<SelectInputProps> = ({
  label,
  icon,
  disabled = false,
  description,
  isFailedValidation = false,
  className = '',
  options = [],
  value,
  onChange,
  children,
  placeholder = 'Select an option',
  hideLabel = false,
  ...props
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(e.target.value);
  };

  return (
    <Field disabled={disabled}>
      <Label className={clsx('pa-eyebrow text-text-primary', hideLabel ? 'sr-only' : 'mb-2')}>
        {label}
      </Label>

      <div className="relative">
        <div
          className={clsx(
            'flex items-center rounded-lg bg-white border',
            isFailedValidation ? 'border-brand-red' : 'border-gray-200'
          )}
        >
          {icon && (
            <div className="shrink-0 absolute left-4 top-1/2 z-10 -translate-y-1/2 text-gray-900 select-none sm:text-sm/6">
              <FontAwesomeIcon
                icon={icon}
                aria-hidden="true"
                className="pointer-events-none size-4"
              />
            </div>
          )}
          <Select
            className={`block min-w-0 grow appearance-none py-2 rounded-lg text-base text-ellipsis focus-within:ring-inset focus-within:ring-1 focus-within:ring-brand-blue ${icon ? 'pl-10' : 'pl-4'} border-0 bg-transparent pr-8 text-gray-900 ${className}`}
            value={value}
            onChange={handleChange}
            {...props}
          >
            {options.length > 0 ? (
              <>
                <option value="" disabled={props.required}>
                  {placeholder}
                </option>
                {options.map((option, index) => {
                  const key = `select-option-${index}`;

                  return (
                    <option key={key} value={option.value}>
                      {option.label}
                    </option>
                  );
                })}
              </>
            ) : (
              children
            )}
          </Select>

          <FontAwesomeIcon
            icon={faChevronDown}
            aria-hidden="true"
            className="pointer-events-none absolute top-1/2 right-4 size-3 -translate-y-1/2"
          />
        </div>
      </div>

      {description && (
        <Description
          className={clsx(
            'mt-1 text-xs',
            isFailedValidation ? 'text-brand-red' : 'text-text-secondary'
          )}
        >
          {description}
        </Description>
      )}
    </Field>
  );
};

export default SelectInput;
