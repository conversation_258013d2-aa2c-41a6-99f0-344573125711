import React, { FC } from 'react';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faCheck, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import { InfoCard } from '@/components/shared/cards/InfoCard';

interface ModalFormContainerProps {
  title?: string;
  description?: string;
  isLoading: boolean;
  error?: string | null;
  validationError?: string | null;
  displayActions?: boolean;
  saveButtonText?: string;
  saveButtonIcon?: IconDefinition;
  saveButtonColor?: 'blue' | 'red' | 'white';
  cancelButtonText?: string;
  allowSave?: boolean;
  className?: string;
  handleSave: () => void;
  handleClose: () => void;
  children: React.ReactNode;
}

const ModalFormContainer: FC<ModalFormContainerProps> = ({
  title,
  description,
  isLoading,
  error,
  validationError,
  handleSave,
  handleClose,
  children,
  displayActions = true,
  allowSave = true,
  saveButtonText = 'Save',
  saveButtonIcon = faCheck,
  saveButtonColor = 'blue',
  cancelButtonText = 'Cancel',
  className = '',
}) => {
  return (
    <div
      className={`relative p-4 w-full max-w-[800px] bg-white rounded-4xl shadow-lg lg:p-16 ${className}`}
    >
      {/* Close Button */}
      <button
        type="button"
        className="absolute top-6 right-6 text-text-primary hover:text-neutral-600 transition-colors"
        onClick={handleClose}
        aria-label="Close Modal"
      >
        <FontAwesomeIcon icon={faXmark} className="size-4" aria-hidden="true" />
      </button>

      {/* Header */}
      {(title || description) && (
        <div className="block space-y-4 mb-10">
          {title && <h2 className="text-xl font-bold text-text-primary">{title}</h2>}
          {description && <p className="text-sm text-text-secondary">{description}</p>}
        </div>
      )}

      {/* Content */}
      <div className="block space-y-6 mb-10">
        {/* Error Messages */}
        {(error || validationError) && (
          <div>
            <InfoCard type="error" message={validationError || error || 'An error occurred'} />
          </div>
        )}

        {children}
      </div>

      {/* Actions */}
      {displayActions && (
        <div className="flex gap-6">
          <Button
            color={saveButtonColor}
            size="small"
            onClick={handleSave}
            disabled={!allowSave || isLoading}
            icon={saveButtonIcon}
            iconPosition="right"
            className="h-10 px-4 py-2 text-sm font-semibold"
          >
            {isLoading ? 'Saving...' : saveButtonText}
          </Button>

          <Button
            color="white"
            size="small"
            onClick={handleClose}
            disabled={isLoading}
            icon={faXmark}
            iconPosition="right"
            className="h-10 px-4 py-2 text-sm font-semibold text-navy-600"
          >
            {cancelButtonText}
          </Button>
        </div>
      )}
    </div>
  );
};

export default ModalFormContainer;
