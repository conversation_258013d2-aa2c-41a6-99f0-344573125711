import React, { InputHTMLAttributes, ReactNode } from 'react';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faChevronDown } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Description, Field, Input, Label, Select } from '@headlessui/react';
import clsx from 'clsx';
import type { SelectOption } from '@/components/shared/form/SelectInput';

interface TextDropdownInputProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, 'className' | 'value'> {
  label: string;
  icon?: IconDefinition | ReactNode;
  disabled?: boolean;
  description?: string;
  isFailedValidation?: boolean;
  className?: string;
  hideLabel?: boolean;
  value?: string | number | null;
  options?: SelectOption[];
  onOptionSelect?: (value: string) => void;
  selectedOption?: string;
  placeholder?: string;
}

const TextDropdownInput: React.FC<TextDropdownInputProps> = ({
  label,
  icon,
  disabled = false,
  description,
  isFailedValidation = false,
  className = '',
  hideLabel = false,
  value,
  options,
  onOptionSelect,
  selectedOption,
  placeholder,
  ...props
}) => {
  // Handle select change
  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (onOptionSelect) {
      onOptionSelect(e.target.value);
    }
  };

  return (
    <Field disabled={disabled}>
      <Label className={clsx('pa-eyebrow text-text-primary', hideLabel && 'sr-only')}>
        {label}
      </Label>

      <div className={clsx('relative', !hideLabel && 'mt-2')}>
        <div
          className={clsx(
            'flex items-center rounded-lg bg-white border',
            isFailedValidation ? 'border-brand-red' : 'border-gray-200'
          )}
        >
          {icon && (
            <div className="shrink-0 text-gray-900 z-[1] select-none sm:text-sm/6 absolute left-4 top-1/2 -translate-y-1/2">
              {typeof icon === 'object' && 'iconName' in icon ? (
                <FontAwesomeIcon
                  icon={icon as IconDefinition}
                  aria-hidden="true"
                  className="pointer-events-none size-4"
                />
              ) : (
                icon
              )}
            </div>
          )}
          <Input
            className={`block min-w-0 grow py-2 mr-0.5 truncate rounded-l-lg ${icon ? 'pl-10' : 'pl-4'} pr-4 text-gray-900 placeholder:text-gray-400 text-base focus-within:ring-inset focus-within:ring-1 focus-within:ring-brand-blue ${className}`}
            value={value ?? ''}
            placeholder={placeholder}
            {...props}
          />

          <div className="grid shrink-0 grid-cols-1 focus-within:relative">
            <Select
              className="col-start-1 row-start-1 w-full appearance-none rounded-l-none rounded-xl py-2 pl-2 pr-10 text-base focus-within:ring-inset focus-within:ring-1 focus-within:ring-brand-blue"
              onChange={handleSelectChange}
              value={selectedOption}
            >
              {options?.map((option, index) => (
                <option key={`select-option-${index}`} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Select>

            <FontAwesomeIcon
              icon={faChevronDown}
              aria-hidden="true"
              className="pointer-events-none col-start-1 row-start-1 mr-4 size-3 self-center justify-self-end text-text-primary"
            />
          </div>
        </div>
      </div>

      {description && (
        <Description
          className={clsx(
            'mt-1 text-xs',
            isFailedValidation ? 'text-brand-red' : 'text-text-secondary'
          )}
        >
          {description}
        </Description>
      )}
    </Field>
  );
};

export default TextDropdownInput;
