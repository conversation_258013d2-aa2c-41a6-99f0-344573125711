'use client';

import React, { useEffect, useState } from 'react';
import { faCheck, faPlus, faSearch, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Transition } from '@headlessui/react';
import Button from '@/components/shared/Button';
import SiteInput from '@/components/shared/form/SiteInput';
import Tag from '@/components/shared/Tag';
import { useInterests } from '@/hooks/useInterests';
import type { Interest } from '@/services/interest.service';

interface InterestsSelectorProps {
  selectedInterests: number[];
  onChange: (interests: number[]) => void;
  initialInterestData?: Interest[]; // Add prop for initial interest data
  label?: string;
  className?: string;
}

export function InterestsSelector({
  selectedInterests,
  onChange,
  initialInterestData = [],
  label = 'Interest',
  className = '',
}: InterestsSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const { interests, isLoading, setSearchInput } = useInterests();
  const [localSelectedInterests, setLocalSelectedInterests] = useState<number[]>(selectedInterests);
  const [selectedInterestNames, setSelectedInterestNames] = useState<Record<number, string>>({});

  // Initialize selected interest names from the initialInterestData
  useEffect(() => {
    if (initialInterestData.length > 0) {
      const initialNames: Record<number, string> = {};
      initialInterestData.forEach(interest => {
        if (selectedInterests.includes(interest.id)) {
          initialNames[interest.id] = interest.name;
        }
      });

      // Only update if we found some matches
      if (Object.keys(initialNames).length > 0) {
        setSelectedInterestNames(prev => ({
          ...prev,
          ...initialNames,
        }));
      }
    }
  }, [initialInterestData, selectedInterests]);

  // Load interest names for existing selections when search results come in
  useEffect(() => {
    if (interests.length > 0) {
      // Update selected interest names from loaded interests
      const newSelectedNames = { ...selectedInterestNames };

      interests.forEach(interest => {
        if (localSelectedInterests.includes(interest.id)) {
          newSelectedNames[interest.id] = interest.name;
        }
      });

      setSelectedInterestNames(newSelectedNames);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [interests, localSelectedInterests]);

  // Update local state when selectedInterests change from parent
  useEffect(() => {
    // Only update if the arrays are different to avoid loops
    if (JSON.stringify(localSelectedInterests) !== JSON.stringify(selectedInterests)) {
      setLocalSelectedInterests(selectedInterests);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedInterests]);

  const handleSelect = (interest: Interest) => {
    if (!localSelectedInterests.includes(interest.id)) {
      const newInterests = [...localSelectedInterests, interest.id];
      const newSelectedNames = { ...selectedInterestNames, [interest.id]: interest.name };

      setLocalSelectedInterests(newInterests);
      setSelectedInterestNames(newSelectedNames);
    }
  };

  const handleRemove = (interestId: number, e?: React.MouseEvent) => {
    e?.stopPropagation(); // Prevent opening the selector when removing
    e?.preventDefault(); // Prevent form submission

    const newInterests = localSelectedInterests.filter(id => id !== interestId);
    setLocalSelectedInterests(newInterests);

    // Call onChange to update the parent component
    onChange(newInterests);
  };

  const handleFieldClick = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent form submission
    setIsOpen(!isOpen);
  };

  // Handler for search input changes
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    setSearchInput(newQuery);
  };

  return (
    <div className={className}>
      {label && <h3 className="pa-eyebrow text-text-primary mb-1">{label}</h3>}

      {/* Main Display Field */}
      <div>
        <div
          className="flex items-center justify-between min-h-[48px] px-4 py-2 bg-white border border-neutral-200 rounded-lg hover:border-neutral-300 cursor-pointer transition-colors"
          onClick={handleFieldClick}
        >
          <div className="flex gap-2 flex-wrap">
            {localSelectedInterests.length === 0 ? (
              <span className="text-neutral-500">Select interests</span>
            ) : (
              localSelectedInterests.map(
                interestId =>
                  selectedInterestNames[interestId] && (
                    <div key={interestId} className="inline-flex items-center gap-1">
                      <Tag label={selectedInterestNames[interestId]} />
                      <button
                        type="button"
                        onClick={e => handleRemove(interestId, e)}
                        className="text-neutral-500 hover:text-neutral-700 ml-1"
                      >
                        <FontAwesomeIcon icon={faXmark} className="h-3 w-3" />
                      </button>
                    </div>
                  )
              )
            )}
          </div>
          <FontAwesomeIcon
            icon={isOpen ? faXmark : faPlus}
            className="text-neutral-500 h-4 w-4 flex-shrink-0"
          />
        </div>

        {/* Selector Panel */}
        <Transition
          show={isOpen}
          enter="transition-all duration-200 ease-out"
          enterFrom="opacity-0 -translate-y-2"
          enterTo="opacity-100 translate-y-0"
          leave="transition-all duration-200 ease-out"
          leaveFrom="opacity-100 translate-y-0"
          leaveTo="opacity-0 -translate-y-2"
        >
          <div className="mt-2 bg-white border border-neutral-200 rounded-lg shadow-lg p-4 z-10 relative">
            {/* Search Input */}
            <SiteInput
              label="Search"
              type="search"
              icon={faSearch}
              placeholder="Search interests..."
              value={query}
              onChange={handleSearchInputChange}
              hideLabel
            />

            {/* Results */}
            <div className="mt-2 max-h-[240px] overflow-auto">
              {isLoading ? (
                <div className="p-4 text-center text-neutral-500">Loading...</div>
              ) : interests.length === 0 ? (
                <div className="p-4 text-center text-neutral-500">
                  {query ? 'No results found' : 'Type to search interests'}
                </div>
              ) : (
                <div className="space-y-1">
                  {interests.map(interest => (
                    <button
                      type="button" // Explicitly set type to prevent form submission
                      key={interest.id}
                      onClick={() => handleSelect(interest)}
                      className={`w-full flex items-center gap-3 px-4 py-2 text-left hover:bg-neutral-50 ${
                        localSelectedInterests.includes(interest.id)
                          ? 'text-brand-blue font-medium'
                          : 'text-neutral-700'
                      }`}
                    >
                      <span
                        className={`${localSelectedInterests.includes(interest.id) ? 'text-brand-blue font-medium' : ''}`}
                      >
                        {interest.name}
                      </span>

                      {localSelectedInterests.includes(interest.id) && (
                        <FontAwesomeIcon icon={faCheck} className="h-4 w-4 ml-auto" />
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3 mt-4 pt-4 border-t border-neutral-200">
              <Button
                color="white"
                size="small"
                onClick={e => {
                  e.preventDefault(); // Prevent form submission
                  setIsOpen(false);
                }}
                type="button" // Explicitly set type to prevent form submission
              >
                Cancel
              </Button>
              <Button
                color="blue"
                size="small"
                onClick={e => {
                  e.preventDefault(); // Prevent form submission

                  // Trigger onChange with the current selected interests before closing
                  onChange(localSelectedInterests);

                  setIsOpen(false);
                }}
                type="button" // Explicitly set type to prevent form submission
              >
                Done
              </Button>
            </div>
          </div>
        </Transition>
      </div>
    </div>
  );
}
