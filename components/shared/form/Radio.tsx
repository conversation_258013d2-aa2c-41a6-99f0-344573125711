'use client';

import { Field, Radio as HeadlessRadio, Label, RadioGroup } from '@headlessui/react';
import clsx from 'clsx';

interface RadioOption {
  label: string;
  value: string;
}

interface RadioProps {
  label?: string;
  options: RadioOption[];
  value: string;
  onChange: (value: string) => void;
  name?: string;
  disabled?: boolean;
  className?: string;
}

export default function Radio({
  label,
  options,
  value,
  onChange,
  name,
  disabled = false,
  className,
}: RadioProps) {
  return (
    <Field className={className} disabled={disabled}>
      {label && (
        <Label
          className={clsx(
            'block text-sm font-medium text-[#383838] mb-2',
            disabled && 'opacity-50'
          )}
        >
          {label}
        </Label>
      )}
      <RadioGroup value={value} onChange={onChange} name={name} disabled={disabled}>
        <div className="space-y-4">
          {options.map(option => (
            <HeadlessRadio
              key={option.value}
              value={option.value}
              className={({ checked }) =>
                clsx(
                  'relative flex cursor-pointer focus:outline-none w-full transition-colors duration-200',
                  'bg-[#F7F7F7] hover:bg-[#F0F0F0] rounded-2xl p-4'
                )
              }
            >
              {({ checked }) => (
                <div className="flex items-center w-full">
                  <div className="flex items-center h-4">
                    <span
                      className={clsx(
                        'h-4 w-4 rounded-full border flex items-center justify-center transition-colors duration-200',
                        'focus:outline-none',
                        checked ? 'border-[#D50032]' : 'border-[#A5ADB0]'
                      )}
                    >
                      {checked && (
                        <span className="h-2 w-2 rounded-full bg-[#D50032] transition-all duration-200" />
                      )}
                    </span>
                  </div>
                  <span
                    className={clsx(
                      'ml-2 text-sm',
                      checked ? 'text-[#383838]' : 'text-[#777B80]',
                      disabled && 'opacity-50'
                    )}
                  >
                    {option.label}
                  </span>
                </div>
              )}
            </HeadlessRadio>
          ))}
        </div>
      </RadioGroup>
    </Field>
  );
}
