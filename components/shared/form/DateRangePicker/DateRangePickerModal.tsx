import { useState } from 'react';
import { faCalendarDay, faCalendarRange } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Field, Fieldset, Label, Legend, Radio, RadioGroup } from '@headlessui/react';
import clsx from 'clsx';
import { format, isAfter, parseISO } from 'date-fns';
import Button from '@/components/shared/Button';
import SiteInput from '@/components/shared/form/SiteInput';
import { useModalStore } from '@/stores/modal.store';

// Define DateRangeMode type
export type DateRangeMode = 'range' | 'single';

// Define DateRangeModalData interface
export interface DateRangeModalData {
  mode: DateRangeMode;
  startDate: string;
  endDate: string;
  onSave: (data: { mode: DateRangeMode; startDate: string; endDate: string }) => void;
}

const DateRangePickerModal: React.FC<DateRangeModalData> = ({
  mode: initialMode,
  startDate: initialStartDate,
  endDate: initialEndDate,
  onSave,
}) => {
  // State for the modal
  const [mode, setMode] = useState<DateRangeMode>(initialMode);
  const [startDate, setStartDate] = useState<string>(initialStartDate);
  const [endDate, setEndDate] = useState<string>(initialEndDate);
  const { close } = useModalStore();

  // Handle mode change
  const handleModeChange = (newMode: DateRangeMode) => {
    setMode(newMode);

    // If switching to single mode and we have a start date
    if (newMode === 'single' && startDate) {
      setEndDate(startDate);
    }
  };

  // Handle saving the selection
  const handleSave = () => {
    // Ensure end date matches start date in single mode
    const finalEndDate = mode === 'single' ? startDate : endDate;

    onSave({
      mode,
      startDate,
      endDate: finalEndDate,
    });

    close();
  };

  // Handle canceling
  const handleCancel = () => {
    close();
  };

  // Get today's date in ISO format for min attribute
  const getTodayISODate = (): string => {
    return format(new Date(), 'yyyy-MM-dd');
  };

  return (
    <div className="relative p-4 w-full min-w-80 sm:min-w-96 bg-white rounded-lg shadow-lg lg:p-8">
      <Fieldset className="mb-6">
        <Legend className="block font-bold text-lg mb-4">Schedule Posting</Legend>
        <RadioGroup
          value={mode}
          onChange={setMode as (value: string) => void}
          className="grid grid-cols-1 gap-2 mb-6"
        >
          <Field>
            <Label>
              <Radio
                value="range"
                className="group px-4 py-2 flex items-center gap-4 rounded-lg data-[checked]:bg-surface-secondary"
              >
                <FontAwesomeIcon
                  icon={faCalendarRange}
                  aria-hidden="true"
                  className="size-4 text-text-secondary group-data-[checked]:text-brand-blue"
                />

                <span className="text-text-secondary group-data-[checked]:text-brand-blue">
                  Date Range
                </span>
              </Radio>
            </Label>
          </Field>

          <Field>
            <Label>
              <Radio
                value="single"
                className="group px-4 py-2 flex items-center gap-4 rounded-lg data-[checked]:bg-surface-secondary"
              >
                <FontAwesomeIcon
                  icon={faCalendarDay}
                  aria-hidden="true"
                  className="size-4 text-text-secondary group-data-[checked]:text-brand-blue"
                />

                <span className="text-text-secondary group-data-[checked]:text-brand-blue">
                  No End Date
                </span>
              </Radio>
            </Label>
          </Field>
        </RadioGroup>
      </Fieldset>

      <div className="space-y-4 mb-6">
        <SiteInput
          label="Start Date"
          type="date"
          className="pa-date-input w-full py-2 px-4 border rounded-lg"
          value={startDate}
          onChange={e => setStartDate(e.target.value)}
          min={getTodayISODate()}
          onClick={e => e.currentTarget.showPicker()}
        />

        <div className={clsx('transition-opacity duration-300', mode === 'single' && 'opacity-50')}>
          <SiteInput
            label="End Date"
            type="date"
            className="pa-date-input w-full py-2 px-4 border rounded-lg"
            value={endDate}
            onChange={e => setEndDate(e.target.value)}
            min={startDate || getTodayISODate()}
            disabled={mode === 'single'}
            onClick={e => e.currentTarget.showPicker()}
          />
        </div>
      </div>

      <div className="flex gap-4 mt-6">
        <Button onClick={handleSave} disabled={!startDate}>
          Apply
        </Button>
        <Button variant="text" onClick={handleCancel}>
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default DateRangePickerModal;
