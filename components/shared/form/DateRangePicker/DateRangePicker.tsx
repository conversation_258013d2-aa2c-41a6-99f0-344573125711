import React, { InputHTMLAttributes, useEffect, useRef, useState } from 'react';
import {
  faCalendar,
  faCalendarCheck,
  faCalendarXmark,
  faCheck,
  faChevronDown,
  faDash,
  faRotateRight,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Field, Fieldset, Input, Label, Legend, Select } from '@headlessui/react';
import clsx from 'clsx';
import { format, isAfter, isBefore, isValid, parse, parseISO } from 'date-fns';
import DateRangePickerModal from '@/components/shared/form/DateRangePicker/DateRangePickerModal';
import { MEDIA_QUERY_PHONE, useMediaQuery } from '@/hooks/utils/useMediaQuery';
import { useModalStore } from '@/stores/modal.store';

type DateRangeMode = 'range' | 'single';

interface DateRangePickerProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, 'className' | 'value' | 'onChange'> {
  label: string;
  disabled?: boolean;
  description?: string;
  isFailedValidation?: boolean;
  className?: string;
  hideLabel?: boolean;
  value?: string;
  onChange?: (dateRange: { startDate: string; endDate: string }) => void;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  label,
  disabled = false,
  description,
  isFailedValidation = false,
  className = '',
  hideLabel = false,
  value = '',
  onChange,
}) => {
  const { open } = useModalStore();
  const isPhone = useMediaQuery(MEDIA_QUERY_PHONE);
  const firstDateInputRef = useRef<HTMLInputElement>(null);
  const secondDateInputRef = useRef<HTMLInputElement>(null);
  const datePickerContainerRef = useRef<HTMLDivElement>(null);

  // State to track selected dates
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');

  // State to track if in edit mode or display mode
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  // State to track date selection mode (range or single)
  const [mode, setMode] = useState<DateRangeMode>('range');

  // State to track which input is focused
  const [focusedInput, setFocusedInput] = useState<'start' | 'end' | null>(null);

  // Parse initial value if provided (format: "MM/DD/YYYY - MM/DD/YYYY")
  useEffect(() => {
    if (value && value.includes(' - ')) {
      const [start, end] = value.split(' - ');
      const formattedStart = formatToISODate(start);
      const formattedEnd = formatToISODate(end);

      setStartDate(formattedStart);
      setEndDate(formattedEnd);

      // If start and end dates are the same, set mode to single
      if (formattedStart && formattedEnd && formattedStart === formattedEnd) {
        setMode('single');
      } else {
        setMode('range');
      }
    } else if (value && !value.includes(' - ')) {
      // Handle single date case
      const formattedDate = formatToISODate(value);
      if (formattedDate) {
        setStartDate(formattedDate);
        setEndDate(formattedDate); // Copy start date to end date for single mode
        setMode('single');
      }
    }
  }, [value]);

  // Helper function to convert MM/DD/YYYY to YYYY-MM-DD
  const formatToISODate = (dateString: string): string => {
    if (!dateString) return '';

    try {
      // Parse MM/DD/YYYY format to a Date object
      const parsedDate = parse(dateString, 'MM/dd/yyyy', new Date());

      // Check if the resulting date is valid
      if (!isValid(parsedDate)) {
        return '';
      }

      // Format to ISO format (YYYY-MM-DD)
      return format(parsedDate, 'yyyy-MM-dd');
    } catch (error) {
      console.error('Error parsing date:', error);
      return '';
    }
  };

  // Helper function to convert YYYY-MM-DD to MM/DD/YYYY
  const formatToDisplayDate = (isoDate: string): string => {
    if (!isoDate) return '';

    try {
      // Parse ISO date string to Date object
      const parsedDate = parseISO(isoDate);

      // Check if the resulting date is valid
      if (!isValid(parsedDate)) {
        return '';
      }

      // Format to MM/DD/YYYY
      return format(parsedDate, 'MM/dd/yyyy');
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // Get the formatted display value for the button
  const getDisplayValue = (): string => {
    if (startDate) {
      const formattedStart = formatToDisplayDate(startDate);
      if (mode === 'range' && endDate && startDate !== endDate) {
        return `${formattedStart} - ${formatToDisplayDate(endDate)}`;
      }
      return formattedStart; // Single date display
    }
    return '';
  };

  // Handle first date selection
  const handleFirstDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const date = e.target.value;
    setStartDate(date);

    if (mode === 'single' && date) {
      // In single mode, copy start date to end date
      setEndDate(date);
    }
  };

  // Handle second date selection
  const handleSecondDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const date = e.target.value;
    setEndDate(date);
  };

  // Track input focus state
  const handleInputFocus = (input: 'start' | 'end') => {
    setFocusedInput(input);
  };

  // Handle input blur
  const handleInputBlur = (e: React.FocusEvent) => {
    // Don't finalize if we're just moving between our own inputs
    // Check if the related target is one of our date inputs
    const relatedTarget = e.relatedTarget as HTMLElement;
    const isMovingBetweenInputs =
      relatedTarget === firstDateInputRef.current || relatedTarget === secondDateInputRef.current;

    if (isMovingBetweenInputs) {
      return;
    }

    // Check if we're still within our container but clicked something else (like the mode select)
    if (datePickerContainerRef.current?.contains(relatedTarget)) {
      return;
    }

    // Otherwise, we're truly blurring away from the component
    finalizeDates();
  };

  // Finalize dates before leaving edit mode
  const finalizeDates = () => {
    // Only process if we have a start date
    if (startDate) {
      let finalEndDate = endDate;

      // Ensure end date is not before start date
      if (
        endDate &&
        isValid(parseISO(startDate)) &&
        isValid(parseISO(endDate)) &&
        isBefore(parseISO(endDate), parseISO(startDate))
      ) {
        finalEndDate = startDate;
        setEndDate(startDate);
      }

      // For single mode, ensure end date is same as start date
      if (mode === 'single') {
        finalEndDate = startDate;
        setEndDate(startDate);
      }

      finalizeDateSelection(startDate, finalEndDate || startDate);
    }

    setFocusedInput(null);
  };

  // Handle mode change
  const handleModeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newMode = e.target.value as DateRangeMode;

    // Set the new mode
    setMode(newMode);

    // If changing to single mode and we have a start date, update end date
    if (newMode === 'single' && startDate) {
      setEndDate(startDate);
    }

    // Stay in edit mode
    setIsEditMode(true);
  };

  // Update parent and exit edit mode with the provided dates
  const finalizeDateSelection = (start: string, end: string) => {
    if (start) {
      // Exit edit mode
      setIsEditMode(false);

      // Notify parent component of date selection
      if (onChange) {
        onChange({
          startDate: start,
          endDate: end || start, // Ensure we always have an end date
        });
      }
    }
  };

  // Handle clicking on the button to enter edit mode
  const handleButtonClick = () => {
    // If on mobile, open the modal instead
    if (!isPhone) {
      open(
        <DateRangePickerModal
          mode={mode}
          startDate={startDate}
          endDate={endDate}
          onSave={data => {
            // Update local state
            setMode(data.mode);
            setStartDate(data.startDate);

            // If single mode, set end date to match start date
            const finalEndDate =
              data.mode === 'single' && data.startDate ? data.startDate : data.endDate;
            setEndDate(finalEndDate);

            // Notify parent if we have dates
            if (data.startDate) {
              onChange?.({
                startDate: data.startDate,
                endDate: finalEndDate,
              });
            }
          }}
        />,
        'lg'
      );
      return;
    }

    // On desktop, clear data and show edit UI
    if (startDate || endDate) {
      handleClearDates();
    }

    setIsEditMode(true);

    // Focus the first input after switching to edit mode
    setTimeout(() => {
      firstDateInputRef.current?.focus();
    }, 0);
  };

  // Handle clearing dates
  const handleClearDates = () => {
    // Clear date selections
    setStartDate('');
    setEndDate('');

    // Exit edit mode
    setIsEditMode(false);

    // Notify parent component of cleared dates
    if (onChange) {
      onChange({
        startDate: '',
        endDate: '',
      });
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    // If Enter key is pressed, finalize the dates
    if (e.key === 'Enter') {
      finalizeDates();
    }
  };

  // Get today's date in ISO format for min attribute
  const getTodayISODate = (): string => {
    return format(new Date(), 'yyyy-MM-dd');
  };

  return (
    <Fieldset>
      <div className="flex items-center gap-2">
        <Legend className={clsx('pa-eyebrow text-text-primary', hideLabel && 'sr-only')}>
          {label}
        </Legend>

        {/* Only show clear button if there are date values */}
        {(startDate || endDate) && (
          <button
            type="button"
            className="inline-flex"
            onClick={handleClearDates}
            aria-label="Clear dates"
            title="Clear dates"
          >
            <span className="sr-only">Clear dates</span>
            <FontAwesomeIcon
              icon={faRotateRight}
              aria-hidden="true"
              className="size-3.5 text-text-secondary"
            />
          </button>
        )}

        {/* Show finalize button when we have dates to finalize */}
        {isEditMode &&
          ((mode === 'single' && startDate) || (mode === 'range' && startDate && endDate)) && (
            <button
              type="button"
              className="inline-flex ml-2"
              onClick={finalizeDates}
              aria-label="Save Edit"
              title="Save Edit"
            >
              <span className="sr-only">Save Edit</span>
              <FontAwesomeIcon
                icon={faCheck}
                aria-hidden="true"
                className="size-4 text-green-700"
              />
            </button>
          )}
      </div>

      <div className={clsx('relative', className)}>
        <div className={clsx('relative', !hideLabel && 'mt-2')}>
          {!isEditMode ? (
            // Show button with placeholder or selected dates
            <button
              type="button"
              onClick={handleButtonClick}
              disabled={disabled}
              className={clsx(
                'flex items-center w-full py-2 px-4 rounded-lg bg-white border text-left',
                isFailedValidation ? 'border-brand-red' : 'border-gray-200'
              )}
              title={!startDate ? 'Select a start date' : 'Edit dates'}
            >
              {getDisplayValue() ? (
                <span className="text-gray-900">{getDisplayValue()}</span>
              ) : (
                <span className="text-gray-400">mm/dd/yyyy - mm/dd/yyyy</span>
              )}
              <FontAwesomeIcon
                icon={!startDate ? faCalendar : faCalendarXmark}
                aria-hidden="true"
                className="ml-auto size-4"
              />
            </button>
          ) : (
            // Show date inputs when editing
            <div
              ref={datePickerContainerRef}
              className={clsx(
                'flex rounded-lg bg-white border',
                isFailedValidation ? 'border-brand-red' : 'border-gray-200'
              )}
            >
              <Field
                className={clsx('flex-1', {
                  'mr-0.5': mode === 'single',
                })}
              >
                <Label className="sr-only">Start date</Label>
                <Input
                  type="date"
                  className={clsx(
                    'block w-full py-[7px] rounded-l-lg text-gray-900 placeholder:text-gray-400 text-base focus-within:ring-inset focus-within:ring-1 focus-within:ring-brand-blue',
                    mode === 'range' && 'pl-4 pr-2',
                    mode === 'single' && 'px-4'
                  )}
                  ref={firstDateInputRef}
                  value={startDate}
                  onChange={handleFirstDateChange}
                  onFocus={() => handleInputFocus('start')}
                  onBlur={handleInputBlur}
                  onKeyDown={handleKeyPress}
                  min={getTodayISODate()} // Set min to today using date-fns
                  placeholder="Start date"
                  disabled={disabled}
                />
              </Field>

              {/* Only show the second date input in range mode */}
              {mode === 'range' && (
                <>
                  <div className="flex item-center justify-center flex-col px-1">
                    <FontAwesomeIcon icon={faDash} aria-hidden="true" />
                  </div>

                  <Field className="flex-1 mx-0.5">
                    <Label className="sr-only">End date</Label>
                    <Input
                      type="date"
                      className="block w-full pl-2 pr-4 py-[7px] rounded-sm text-gray-900 placeholder:text-gray-400 text-base focus-within:ring-inset focus-within:ring-1 focus-within:ring-brand-blue"
                      ref={secondDateInputRef}
                      value={endDate}
                      onChange={handleSecondDateChange}
                      onFocus={() => handleInputFocus('end')}
                      onBlur={handleInputBlur}
                      onKeyDown={handleKeyPress}
                      min={startDate || getTodayISODate()}
                      placeholder={disabled || !startDate ? 'Select a start date' : 'End date'}
                    />
                  </Field>
                </>
              )}

              <div className="grid shrink-0 grid-cols-1 focus-within:relative">
                <Select
                  className="col-start-1 row-start-1 w-full min-w-32 appearance-none rounded-l-none rounded-xl py-[7px] pl-2 pr-8 text-base/none focus-within:ring-inset focus-within:ring-1 focus-within:ring-brand-blue"
                  onChange={handleModeChange}
                  value={mode}
                >
                  <option value="range">Date Range</option>
                  <option value="single">No End Date</option>
                </Select>

                <FontAwesomeIcon
                  icon={faChevronDown}
                  aria-hidden="true"
                  className="pointer-events-none col-start-1 row-start-1 mr-4 size-3 self-center justify-self-end text-text-primary"
                />
              </div>
            </div>
          )}
        </div>

        {description && (
          <span
            className={clsx(
              'block mt-1 text-xs',
              isFailedValidation ? 'text-brand-red' : 'text-text-secondary'
            )}
          >
            {description}
          </span>
        )}
      </div>
    </Fieldset>
  );
};

export default DateRangePicker;
