'use client';

import React, { useEffect, useState } from 'react';
import { faCheck, faPlus, faSearch, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Transition } from '@headlessui/react';
import Button from '@/components/shared/Button';
import SiteInput from '@/components/shared/form/SiteInput';
import Tag from '@/components/shared/Tag';
import { useIndustries } from '@/hooks/useIndustries';
import type { Industry } from '@/services/industry.service';

interface IndustriesSelectorProps {
  selectedIndustries: number[];
  onChange: (industries: number[]) => void;
  initialIndustryData?: Industry[]; // Add prop for initial industry data
  label?: string;
  className?: string;
}

export function IndustriesSelector({
  selectedIndustries,
  onChange,
  initialIndustryData = [],
  label = 'Industry',
  className = '',
}: IndustriesSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const { industries, isLoading } = useIndustries(query);
  const [localSelectedIndustries, setLocalSelectedIndustries] =
    useState<number[]>(selectedIndustries);
  const [selectedIndustryNames, setSelectedIndustryNames] = useState<Record<number, string>>({});

  // Initialize selected industry names from the initialIndustryData
  useEffect(() => {
    if (initialIndustryData.length > 0) {
      const initialNames: Record<number, string> = {};
      initialIndustryData.forEach(industry => {
        if (selectedIndustries.includes(industry.id)) {
          initialNames[industry.id] = industry.name;
        }
      });

      // Only update if we found some matches
      if (Object.keys(initialNames).length > 0) {
        setSelectedIndustryNames(prev => ({
          ...prev,
          ...initialNames,
        }));
      }
    }
  }, [initialIndustryData, selectedIndustries]);

  // Load industry names for existing selections when search results come in
  useEffect(() => {
    if (industries.length > 0) {
      // Update selected industry names from loaded industries
      const newSelectedNames = { ...selectedIndustryNames };

      industries.forEach(industry => {
        if (localSelectedIndustries.includes(industry.id)) {
          newSelectedNames[industry.id] = industry.name;
        }
      });

      setSelectedIndustryNames(newSelectedNames);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [industries, localSelectedIndustries]);

  // Update local state when selectedIndustries change from parent
  useEffect(() => {
    // Only update if the arrays are different to avoid loops
    if (JSON.stringify(localSelectedIndustries) !== JSON.stringify(selectedIndustries)) {
      setLocalSelectedIndustries(selectedIndustries);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedIndustries]);

  const handleSelect = (industry: Industry) => {
    if (!localSelectedIndustries.includes(industry.id)) {
      const newIndustries = [...localSelectedIndustries, industry.id];
      const newSelectedNames = { ...selectedIndustryNames, [industry.id]: industry.name };

      setLocalSelectedIndustries(newIndustries);
      setSelectedIndustryNames(newSelectedNames);
    }
  };

  const handleRemove = (industryId: number, e?: React.MouseEvent) => {
    e?.stopPropagation(); // Prevent opening the selector when removing
    e?.preventDefault(); // Prevent form submission

    const newIndustries = localSelectedIndustries.filter(id => id !== industryId);
    setLocalSelectedIndustries(newIndustries);

    // Call onChange to update the parent component
    onChange(newIndustries);
  };

  const handleFieldClick = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent form submission
    setIsOpen(!isOpen);
  };

  return (
    <div className={className}>
      {label && <h3 className="pa-eyebrow text-text-primary mb-1">{label}</h3>}

      {/* Main Display Field */}
      <div>
        <div
          className="flex items-center justify-between min-h-[48px] px-4 py-2 bg-white border border-neutral-200 rounded-lg hover:border-neutral-300 cursor-pointer transition-colors"
          onClick={handleFieldClick}
        >
          <div className="flex gap-2 flex-wrap">
            {localSelectedIndustries.length === 0 ? (
              <span className="text-neutral-500">Select industries</span>
            ) : (
              localSelectedIndustries.map(
                industryId =>
                  selectedIndustryNames[industryId] && (
                    <div key={industryId} className="inline-flex items-center gap-1">
                      <Tag label={selectedIndustryNames[industryId]} />
                      <button
                        type="button"
                        onClick={e => handleRemove(industryId, e)}
                        className="text-neutral-500 hover:text-neutral-700 ml-1"
                      >
                        <FontAwesomeIcon icon={faXmark} className="h-3 w-3" />
                      </button>
                    </div>
                  )
              )
            )}
          </div>
          <FontAwesomeIcon
            icon={isOpen ? faXmark : faPlus}
            className="text-neutral-500 h-4 w-4 flex-shrink-0"
          />
        </div>

        {/* Selector Panel */}
        <Transition
          show={isOpen}
          enter="transition-all duration-200 ease-out"
          enterFrom="opacity-0 -translate-y-2"
          enterTo="opacity-100 translate-y-0"
          leave="transition-all duration-200 ease-out"
          leaveFrom="opacity-100 translate-y-0"
          leaveTo="opacity-0 -translate-y-2"
        >
          <div className="mt-2 bg-white border border-neutral-200 rounded-lg shadow-lg p-4 z-10 relative">
            {/* Search Input */}
            <SiteInput
              label="Search"
              type="search"
              icon={faSearch}
              placeholder="Search industries..."
              value={query}
              onChange={e => setQuery(e.target.value)}
              hideLabel
            />

            {/* Results */}
            <div className="mt-2 max-h-[240px] overflow-auto">
              {isLoading ? (
                <div className="p-4 text-center text-neutral-500">Loading...</div>
              ) : industries.length === 0 ? (
                <div className="p-4 text-center text-neutral-500">
                  {query ? 'No results found' : 'Type to search industries'}
                </div>
              ) : (
                <div className="space-y-1">
                  {industries.map(industry => (
                    <button
                      type="button" // Explicitly set type to prevent form submission
                      key={industry.id}
                      onClick={() => handleSelect(industry)}
                      className={`w-full flex items-center gap-3 px-4 py-2 text-left hover:bg-neutral-50 ${
                        localSelectedIndustries.includes(industry.id)
                          ? 'text-brand-blue font-medium'
                          : 'text-neutral-700'
                      }`}
                    >
                      <span
                        className={`${localSelectedIndustries.includes(industry.id) ? 'text-brand-blue font-medium' : ''}`}
                      >
                        {industry.name}
                      </span>

                      {localSelectedIndustries.includes(industry.id) && (
                        <FontAwesomeIcon icon={faCheck} className="h-4 w-4 ml-auto" />
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3 mt-4 pt-4 border-t border-neutral-200">
              <Button
                color="white"
                size="small"
                onClick={e => {
                  e.preventDefault(); // Prevent form submission
                  setIsOpen(false);
                }}
                type="button" // Explicitly set type to prevent form submission
              >
                Cancel
              </Button>
              <Button
                color="blue"
                size="small"
                onClick={e => {
                  e.preventDefault(); // Prevent form submission
                  // Add more detailed logging

                  // Trigger onChange with the current selected industries before closing
                  onChange(localSelectedIndustries);

                  setIsOpen(false);
                }}
                type="button" // Explicitly set type to prevent form submission
              >
                Done
              </Button>
            </div>
          </div>
        </Transition>
      </div>
    </div>
  );
}
