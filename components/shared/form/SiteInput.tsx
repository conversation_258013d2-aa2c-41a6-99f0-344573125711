import React, { InputHTMLAttributes, ReactNode } from 'react';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Description, Field, Input, Label } from '@headlessui/react';
import clsx from 'clsx';

interface SiteInputProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, 'className' | 'value'> {
  label: string;
  icon?: IconDefinition | ReactNode;
  disabled?: boolean;
  description?: string;
  isFailedValidation?: boolean;
  className?: string;
  hideLabel?: boolean;
  value?: string | number | null;
}

const SiteInput: React.FC<SiteInputProps> = ({
  label,
  icon,
  disabled = false,
  description,
  isFailedValidation = false,
  className = '',
  hideLabel = false,
  value,
  ...props
}) => {
  return (
    <Field disabled={disabled}>
      <Label className={clsx('pa-eyebrow text-text-primary', hideLabel && 'sr-only')}>
        {label}
      </Label>

      <div className={clsx('relative', !hideLabel && 'mt-2')}>
        <div
          className={clsx(
            'flex items-center rounded-lg bg-white border',
            isFailedValidation ? 'border-brand-red' : 'border-gray-200'
          )}
        >
          {icon && (
            <div className="shrink-0 text-gray-900 z-[1] select-none sm:text-sm/6 absolute left-4 top-1/2 -translate-y-1/2">
              {typeof icon === 'object' && 'iconName' in icon ? (
                <FontAwesomeIcon
                  icon={icon as IconDefinition}
                  aria-hidden="true"
                  className="pointer-events-none size-4"
                />
              ) : (
                icon
              )}
            </div>
          )}
          <Input
            className={`block min-w-0 grow py-2 rounded-lg ${icon ? 'pl-10' : 'pl-4'} pr-4 text-gray-900 placeholder:text-gray-400 text-base focus-within:ring-inset focus-within:ring-1 focus-within:ring-brand-blue ${className}`}
            value={value ?? ''}
            {...props}
          />
        </div>
      </div>

      {description && (
        <Description
          className={clsx(
            'mt-1 text-xs',
            isFailedValidation ? 'text-brand-red' : 'text-text-secondary'
          )}
        >
          {description}
        </Description>
      )}
    </Field>
  );
};

export default SiteInput;
