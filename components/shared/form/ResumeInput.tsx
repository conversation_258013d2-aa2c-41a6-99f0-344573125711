import React, { InputHTMLAttributes, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { faCloudArrowUp, faFileLines, faPlus } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Description, Field, Input, Label } from '@headlessui/react';
import clsx from 'clsx';

interface ResumeInputProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, 'className' | 'value' | 'type'> {
  label: string;
  disabled?: boolean;
  description?: string;
  className?: string;
  value?: string | number | null;
  onFileSelect?: (file: File) => void;
}

const ResumeInput: React.FC<ResumeInputProps> = ({
  label,
  disabled = false,
  description,
  className = '',
  value,
  onFileSelect,
  ...props
}) => {
  const [fileName, setFileName] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    },
    disabled,
    maxFiles: 1,
    onDrop: (acceptedFiles, rejectedFiles) => {
      if (rejectedFiles.length > 0) {
        const rejectionErrors = rejectedFiles[0].errors.map(error => error.message).join(', ');
        setError(`Invalid file: ${rejectionErrors}`);
        return;
      }

      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        setFileName(file.name);
        setError(null);
        onFileSelect?.(file);
      }
    },
  });

  return (
    <Field disabled={disabled}>
      <Label className="pa-eyebrow text-text-primary sr-only">{label}</Label>

      <div className={clsx('relative mt-2')}>
        <div
          {...getRootProps()}
          className={clsx(
            'flex flex-col items-center justify-center rounded-lg border-dashed bg-surface-secondary border-2 p-6 cursor-pointer transition-colors',
            {
              'border-brand-blue bg-blue-50': isDragActive,
              'border-gray-200 hover:bg-gray-100': !isDragActive && !error,
              'border-brand-red bg-red-50': error,
              'opacity-50 cursor-not-allowed': disabled,
            },
            className
          )}
        >
          <Input {...getInputProps()} disabled={disabled} {...props} />

          {/* <FontAwesomeIcon
            icon={fileName ? faFileLines : faCloudArrowUp}
            className={clsx('text-2xl mb-2', {
              'text-text-secondary': !fileName && !error,
              'text-brand-blue': fileName && !error,
              'text-brand-red': error,
            })}
          /> */}

          {fileName ? (
            <p className="text-gray-700 text-center break-all">{fileName}</p>
          ) : (
            <div className="text-center">
              {/* <p
                className={clsx('text-center', {
                  'text-gray-500': !isDragActive && !error,
                  'text-brand-blue font-medium': isDragActive,
                  'text-brand-red': error,
                })}
              >
                {isDragActive ? 'Drop your resume here' : 'Upload your resume'}
              </p> */}

              <span className="flex items-center justify-center gap-2 text-text-secondary">
                Upload your resume
                <FontAwesomeIcon icon={faPlus} className="size-4" aria-label="hidden" />
              </span>
            </div>
          )}
        </div>
      </div>

      {(description || error) && (
        <Description
          className={clsx('mt-1 text-xs', {
            'text-text-secondary': description && !error,
            'text-brand-red': error,
          })}
        >
          {error || description}
        </Description>
      )}
    </Field>
  );
};

export default ResumeInput;
