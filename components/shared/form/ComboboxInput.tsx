'use client';

import React, { useState } from 'react';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faCheck, faChevronDown } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Combobox,
  ComboboxButton,
  ComboboxOption,
  ComboboxOptions,
  Description,
  Field,
  ComboboxInput as HeadlessComboboxInput,
  Label,
} from '@headlessui/react';
import clsx from 'clsx';

export interface ComboboxOption {
  id: string | number;
  name: string;
  [key: string]: any; // Allow for additional properties
}

interface ComboboxInputProps<T extends ComboboxOption> {
  label: string;
  icon?: IconDefinition;
  disabled?: boolean;
  description?: string;
  isFailedValidation?: boolean;
  className?: string;
  options: T[];
  value: T | null;
  onChange: (value: T) => void;
  placeholder?: string;
  displayValue?: (option: T | null) => string;
  filterFunction?: (query: string, option: T) => boolean;
  isLoading?: boolean;
  loadingText?: string;
  emptyText?: string;
  searchQuery?: string;
  onSearchChange?: (value: string) => void;
}

function ComboboxInput<T extends ComboboxOption>({
  label,
  icon,
  disabled = false,
  description,
  isFailedValidation = false,
  className = '',
  options,
  value,
  onChange,
  placeholder = 'Select an option',
  displayValue = option => option?.name || '',
  filterFunction,
  isLoading = false,
  loadingText = 'Loading...',
  emptyText = 'No options found',
  searchQuery,
  onSearchChange,
}: ComboboxInputProps<T>) {
  const [localQuery, setLocalQuery] = useState('');

  // Use external search query if provided, otherwise use local state
  const query = searchQuery !== undefined ? searchQuery : localQuery;

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (onSearchChange) {
      onSearchChange(value);
    } else {
      setLocalQuery(value);
    }
  };

  // Filter options based on query if no external filtering is provided
  const filteredOptions = onSearchChange
    ? options // If external search is provided, don't filter locally
    : query === ''
      ? options
      : options.filter(option =>
          filterFunction
            ? filterFunction(query, option)
            : option.name.toLowerCase().includes(query.toLowerCase())
        );

  return (
    <Field disabled={disabled}>
      <Label className="pa-eyebrow text-text-primary">{label}</Label>

      <div className="relative mt-2">
        <Combobox value={value} onChange={onChange} disabled={disabled}>
          <div
            className={clsx(
              'flex items-center rounded-lg bg-white border',
              isFailedValidation ? 'border-brand-red' : 'border-gray-200'
            )}
          >
            {icon && (
              <div className="shrink-0 text-gray-900 z-10 select-none sm:text-sm/6 absolute left-4 top-1/2 -translate-y-1/2">
                <FontAwesomeIcon
                  icon={icon}
                  aria-hidden="true"
                  className="pointer-events-none size-4"
                />
              </div>
            )}
            <HeadlessComboboxInput
              className={`block min-w-0 grow py-2 rounded-lg text-base ${icon ? 'pl-10' : 'pl-4'} pr-10 text-gray-900 placeholder:text-gray-400 focus-within:ring-inset focus-within:ring-2 focus-within:ring-brand-blue ${className}`}
              displayValue={(option: T | null) => (option ? displayValue(option) : '')}
              onChange={handleSearchChange}
              placeholder={placeholder}
            />
            <ComboboxButton className="absolute inset-y-0 right-2 flex items-center pr-2">
              <FontAwesomeIcon
                icon={faChevronDown}
                aria-hidden="true"
                className="pointer-events-none size-3"
              />
            </ComboboxButton>
          </div>

          <ComboboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
            {isLoading ? (
              <div className="relative cursor-default select-none py-2 px-4 text-gray-700">
                {loadingText}
              </div>
            ) : filteredOptions.length === 0 ? (
              <div className="relative cursor-default select-none py-2 px-4 text-gray-700">
                {emptyText}
              </div>
            ) : (
              filteredOptions.map(option => (
                <ComboboxOption
                  key={option.id}
                  value={option}
                  className={({ active }) =>
                    clsx(
                      'relative cursor-default select-none py-2 pl-10 pr-4',
                      active ? 'bg-brand-blue text-white' : 'text-gray-900'
                    )
                  }
                >
                  {({ selected, active }) => (
                    <>
                      <span
                        className={clsx('block truncate', selected ? 'font-medium' : 'font-normal')}
                      >
                        {option.name}
                      </span>
                      {selected ? (
                        <span
                          className={clsx(
                            'absolute inset-y-0 left-0 flex items-center pl-3',
                            active ? 'text-white' : 'text-brand-blue'
                          )}
                        >
                          <FontAwesomeIcon icon={faCheck} aria-hidden="true" className="h-5 w-5" />
                        </span>
                      ) : null}
                    </>
                  )}
                </ComboboxOption>
              ))
            )}
          </ComboboxOptions>
        </Combobox>
      </div>

      {description && (
        <Description
          className={clsx(
            'mt-1 text-xs',
            isFailedValidation ? 'text-brand-red' : 'text-text-secondary'
          )}
        >
          {description}
        </Description>
      )}
    </Field>
  );
}

export default ComboboxInput;
