'use client';

import { Switch } from '@headlessui/react';
import { cn } from '@/lib/utils';

interface ToggleProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  size?: 'sm' | 'lg';
  className?: string;
}

export default function Toggle({
  checked,
  onChange,
  disabled = false,
  size = 'lg',
  className,
}: ToggleProps) {
  return (
    <Switch
      checked={checked}
      onChange={onChange}
      disabled={disabled}
      className={cn(
        'group relative inline-flex items-center rounded-full transition-colors duration-200 ease-in-out',
        'focus:outline-none focus-visible:ring-4 focus-visible:ring-brand-red/20',
        // Base styles
        'shadow-[inset_0_4px_6px_rgba(0,0,0,0.1),0_-3px_4px_rgba(0,0,0,0.1),0_10px_16px_rgba(0,0,0,0.05),0_4px_4px_rgba(255,255,255,1)]',
        // Size variations
        size === 'lg' ? 'h-8 w-14' : 'h-6 w-11',
        // State variations
        checked ? 'bg-brand-red' : 'bg-gray-200',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
    >
      <span className="sr-only">Toggle setting</span>
      <span
        className={cn(
          'absolute transform rounded-full bg-white transition-transform duration-200 ease-in-out',
          // Size variations
          size === 'lg' ? 'h-6 w-6 group-hover:scale-110' : 'h-4 w-4 group-hover:scale-110',
          // Position variations
          checked ? (size === 'lg' ? 'translate-x-7' : 'translate-x-6') : 'translate-x-1',
          // State variations
          disabled && 'bg-gray-300',
          'shadow-[inset_0_4px_6px_rgba(0,0,0,0.1)]'
        )}
      />
    </Switch>
  );
}
