'use client';

import { faCheck } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Field, Checkbox as HeadlessCheckbox, Label } from '@headlessui/react';
import { cn } from '@/lib/utils';

interface CheckboxProps {
  label: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  name?: string;
  disabled?: boolean;
  className?: string;
}

export default function Checkbox({
  label,
  checked,
  onChange,
  name,
  disabled = false,
  className,
}: CheckboxProps) {
  return (
    <Field className={cn('relative flex items-start', className)} disabled={disabled}>
      <div className="flex items-center h-5">
        <HeadlessCheckbox
          checked={checked}
          onChange={onChange}
          name={name}
          disabled={disabled}
          className={cn(
            'relative h-4 w-4 rounded border transition-colors',
            'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-blue',
            checked ? 'bg-brand-blue border-brand-blue' : 'border-gray-300 bg-white',
            disabled && 'opacity-50 cursor-not-allowed'
          )}
        >
          <span
            className={cn(
              'absolute inset-0 flex items-center justify-center transition-opacity',
              checked ? 'opacity-100' : 'opacity-0'
            )}
          >
            <FontAwesomeIcon icon={faCheck} className="h-3 w-3 text-white text-[16px] font-bold" />
          </span>
        </HeadlessCheckbox>
      </div>
      <div className="ml-3 text-sm">
        <Label className={cn('font-medium text-gray-700', disabled && 'opacity-50')}>{label}</Label>
      </div>
    </Field>
  );
}
