import React from 'react';
import { faChevronDown } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Description,
  Field,
  Listbox as HeadlessListbox,
  Label,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react';
import clsx from 'clsx';

export interface Option {
  value: string;
  label: string;
}

interface ListboxSelectProps {
  label?: string;
  value: string | null;
  onChange: (value: string) => void;
  options: readonly Option[] | Option[];
  disabled?: boolean;
  isFailedValidation?: boolean;
  description?: string;
  placeholder?: string;
  hideLabel?: boolean;
}

export function ListboxSelect({
  label,
  value,
  onChange,
  options,
  disabled = false,
  isFailedValidation = false,
  description,
  placeholder = 'Select...',
  hideLabel = false,
}: ListboxSelectProps) {
  // Find the selected option to display its label
  const selectedOption = options.find(option => option.value === value);
  const displayText = selectedOption?.label || placeholder;

  return (
    <Field disabled={disabled}>
      <div className="relative">
        <HeadlessListbox value={value ?? ''} onChange={onChange}>
          <div className="relative">
            <Label className={clsx('pa-eyebrow text-text-primary', hideLabel ? 'sr-only' : 'mb-2')}>
              {label}
            </Label>

            {/* TODO: Tweek the styles for the button, which is the "input" as far as the UI goes - should be 40px high */}
            <ListboxButton className="relative w-full px-4 py-[7px] bg-white border border-gray-200 rounded-lg text-base text-left text-text-primary placeholder:text-text-secondary transition-colors duration-200 ease-in-out hover:shadow focus:outline-none focus:ring-inset focus:ring-2 focus:ring-blue-700 focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed">
              <span className="block truncate pr-8">{displayText}</span>
              <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-4 text-text-primary">
                <FontAwesomeIcon icon={faChevronDown} className="size-3" aria-hidden="true" />
              </span>
            </ListboxButton>
            <ListboxOptions className="absolute z-10 w-full py-1 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto focus:outline-none">
              {options.map(option => (
                <ListboxOption
                  key={option.value}
                  value={option.value}
                  className={({ active }) =>
                    `relative cursor-pointer select-none py-2 pl-3 pr-9 ${
                      active ? 'bg-primary-50 text-primary-900' : 'text-gray-900'
                    }`
                  }
                >
                  {({ selected, active }) => (
                    <span
                      className={`block truncate ${selected ? 'font-semibold' : 'font-normal'}`}
                    >
                      {option.label}
                    </span>
                  )}
                </ListboxOption>
              ))}
            </ListboxOptions>
          </div>
        </HeadlessListbox>

        {description && (
          <Description
            className={clsx(
              'mt-1 text-xs',
              isFailedValidation ? 'text-brand-red' : 'text-text-secondary'
            )}
          >
            {description}
          </Description>
        )}
      </div>
    </Field>
  );
}
