import React, { InputHTMLAttributes } from 'react';
import { Description, Field, Input as HeadlessInput, Label } from '@headlessui/react';
import { cn } from '@/lib/utils';

interface InputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'value'> {
  label?: string;
  error?: string;
  description?: string;
  startAdornment?: React.ReactNode;
  value?: string | number | null;
}

export function Input({
  label,
  error,
  description,
  className = '',
  disabled,
  startAdornment,
  value,
  ...props
}: InputProps) {
  return (
    <Field disabled={disabled}>
      {label && (
        <Label className="block text-[14px] font-bold text-text-primary mb-[8px] data-[disabled]:opacity-50">
          {label}
        </Label>
      )}
      {description && !error && (
        <Description className="mt-1 text-sm text-text-secondary data-[disabled]:opacity-50">
          {description}
        </Description>
      )}
      <div className="relative">
        {startAdornment && (
          <div className="absolute left-4 inset-y-0 flex items-center pointer-events-none">
            {startAdornment}
          </div>
        )}
        <HeadlessInput
          invalid={Boolean(error)}
          disabled={disabled}
          value={value ?? ''}
          className={cn(
            'w-full px-4 py-1.5',
            'bg-white',
            'border border-gray-300 rounded-lg',
            'text-sm/6 text-text-primary',
            'placeholder:text-text-secondary',
            'transition-colors duration-200 ease-in-out',
            'data-[focus]:outline-none data-[focus]:ring-2 data-[focus]:ring-primary data-[focus]:border-transparent',
            'data-[hover]:shadow',
            'data-[disabled]:bg-gray-100 data-[disabled]:text-gray-500 data-[disabled]:cursor-not-allowed',
            'data-[invalid]:border-red-500 data-[invalid]:focus:ring-red-500',
            className
          )}
          {...props}
        />
      </div>
      {error && (
        <Description className="mt-1 text-sm text-red-500" role="alert">
          {error}
        </Description>
      )}
    </Field>
  );
}
