import React, { ReactNode } from 'react';

interface FormFieldProps {
  id: string;
  label: string;
  children: ReactNode;
  error?: string | string[];
  helpText?: string;
}

export function FormField({ id, label, children, error, helpText }: FormFieldProps) {
  const errorMessage = Array.isArray(error) ? error[0] : error;

  return (
    <div>
      <div className="block text-sm font-semibold text-gray-700">{label.toUpperCase()}</div>
      {children}
      {(errorMessage || helpText) && (
        <p className={`mt-2 text-sm ${errorMessage ? 'text-red-600' : 'text-gray-500'}`}>
          {errorMessage || helpText}
        </p>
      )}
    </div>
  );
}
