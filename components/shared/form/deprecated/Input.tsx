import React, { InputHTMLAttributes } from 'react';
import { cn } from '@/lib/utils';

interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  className?: string;
  error?: boolean;
}

export function Input({ className, error, ...props }: InputProps) {
  return (
    <input
      className={cn(
        'mt-1 block w-full rounded-lg border border-stroke-weak bg-surface-primary px-4 py-3 text-text-primary shadow-sm focus:border-brand-blue focus:outline-none',
        error &&
          'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500',
        className
      )}
      {...props}
    />
  );
}
