import React, { SelectHTMLAttributes } from 'react';
import { Listbox } from '@headlessui/react';
import { cn } from '@/lib/utils';

interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps extends Omit<SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {
  className?: string;
  options: readonly SelectOption[] | SelectOption[];
  onChange: (value: string) => void;
  error?: boolean;
}

export function Select({ className, options, onChange, error, ...props }: SelectProps) {
  return (
    <select
      className={cn(
        'mt-1 block w-full rounded-lg border border-stroke-weak bg-surface-primary px-4 py-3 text-text-primary shadow-sm focus:border-brand-blue focus:outline-none',
        error && 'border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500',
        className
      )}
      onChange={e => onChange(e.target.value)}
      {...props}
    >
      <option value="">Select...</option>
      {options.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
}

export function SelectListbox({ id, value, onChange, options, className }: SelectProps) {
  const selectedOption = options.find(option => option.value === value);

  return (
    <Listbox value={value} onChange={onChange}>
      <div className="relative mt-1">
        <Listbox.Button
          className={cn(
            'block w-full rounded-lg border border-stroke-weak bg-surface-primary px-4 py-3 text-left text-text-primary shadow-sm focus:border-brand-blue focus:outline-none',
            className
          )}
        >
          {selectedOption?.label}
        </Listbox.Button>
        <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          {options.map(option => (
            <Listbox.Option
              key={option.value}
              value={option.value}
              className={({ active }) =>
                cn(
                  'relative cursor-default select-none py-2 px-4',
                  active ? 'bg-brand-blue text-white' : 'text-gray-900'
                )
              }
            >
              {option.label}
            </Listbox.Option>
          ))}
        </Listbox.Options>
      </div>
    </Listbox>
  );
}
