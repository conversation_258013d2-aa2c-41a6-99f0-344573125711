import React from 'react';
import { RadioGroup } from '@headlessui/react';

interface Option {
  value: string;
  label: string;
}

interface RadioFieldProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: Option[];
}

export function RadioField({ id, label, value, onChange, options }: RadioFieldProps) {
  return (
    <RadioGroup value={value} onChange={onChange}>
      <RadioGroup.Label className="block text-sm font-semibold text-gray-700">
        {label.toUpperCase()}
      </RadioGroup.Label>
      <div className="space-y-2 mt-1">
        {options.map(option => (
          <RadioGroup.Option
            key={option.value}
            value={option.value}
            className={({ active, checked }) =>
              `${active ? 'ring-2 ring-brand-blue ring-offset-2' : ''}
              ${checked ? 'bg-brand-blue text-white' : 'bg-white'}
              relative flex cursor-pointer rounded-lg px-5 py-4 shadow-md focus:outline-none`
            }
          >
            {({ checked }) => (
              <div className="flex w-full items-center justify-between">
                <div className="flex items-center">
                  <div className="text-sm">
                    <RadioGroup.Label
                      as="p"
                      className={`font-medium ${checked ? 'text-white' : 'text-gray-900'}`}
                    >
                      {option.label}
                    </RadioGroup.Label>
                  </div>
                </div>
              </div>
            )}
          </RadioGroup.Option>
        ))}
      </div>
    </RadioGroup>
  );
}
