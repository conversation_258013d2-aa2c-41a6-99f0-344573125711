'use client';

import React, { useEffect, useState } from 'react';
import { faCheck, faPlus, faSearch, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Transition } from '@headlessui/react';
import Button from '@/components/shared/Button';
import SiteInput from '@/components/shared/form/SiteInput';
import Tag from '@/components/shared/Tag';
import { STATE_OPTIONS } from '@/utils/constants/states';

interface StatesSelectorProps {
  selectedStates: string[];
  onChange: (states: string[]) => void;
  label?: string;
  className?: string;
}

export function StatesSelector({
  selectedStates,
  onChange,
  label = 'states',
  className = '',
}: StatesSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [localSelectedStates, setLocalSelectedStates] = useState<string[]>(selectedStates);

  // Filter states based on search query
  const filteredStates =
    query.trim() === ''
      ? STATE_OPTIONS
      : STATE_OPTIONS.filter(
          state =>
            state.label.toLowerCase().includes(query.toLowerCase()) ||
            state.value.toLowerCase().includes(query.toLowerCase())
        );

  useEffect(() => {
    setLocalSelectedStates(selectedStates);
  }, [selectedStates]);

  const handleSelect = (state: { value: string; label: string }) => {
    if (!localSelectedStates.includes(state.value)) {
      const newStates = [...localSelectedStates, state.value];
      setLocalSelectedStates(newStates);
      onChange(newStates);
    }
  };

  const handleRemove = (state: string, e?: React.MouseEvent) => {
    e?.stopPropagation(); // Prevent opening the selector when removing
    e?.preventDefault(); // Prevent form submission
    const newStates = localSelectedStates.filter(s => s !== state);
    setLocalSelectedStates(newStates);
    onChange(newStates);
  };

  const handleFieldClick = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent form submission
    setIsOpen(!isOpen);
  };

  // Function to get state label from value
  const getStateLabel = (stateValue: string): string => {
    const found = STATE_OPTIONS.find(state => state.value === stateValue);
    return found ? found.label : stateValue;
  };

  return (
    <div className={className}>
      {label && <h3 className="pa-eyebrow text-text-primary mb-1">{label}</h3>}

      {/* Main Display Field */}
      <div>
        <div
          className="flex items-center justify-between min-h-[48px] px-4 py-2 bg-white border border-neutral-200 rounded-lg hover:border-neutral-300 cursor-pointer transition-colors"
          onClick={handleFieldClick}
        >
          <div className="flex gap-2 flex-wrap">
            {localSelectedStates.length === 0 ? (
              <span className="text-neutral-500">Select states</span>
            ) : (
              localSelectedStates.map((stateValue, index) => (
                <div key={index} className="inline-flex items-center gap-1">
                  <Tag label={getStateLabel(stateValue)} />
                  <button
                    type="button"
                    onClick={e => handleRemove(stateValue, e)}
                    className="text-neutral-500 hover:text-neutral-700 ml-1"
                  >
                    <FontAwesomeIcon icon={faXmark} className="h-3 w-3" />
                  </button>
                </div>
              ))
            )}
          </div>
          <FontAwesomeIcon
            icon={isOpen ? faXmark : faPlus}
            className="text-neutral-500 h-4 w-4 flex-shrink-0"
          />
        </div>

        {/* Selector Panel */}
        <Transition
          show={isOpen}
          enter="transition-all duration-200 ease-out"
          enterFrom="opacity-0 -translate-y-2"
          enterTo="opacity-100 translate-y-0"
          leave="transition-all duration-200 ease-out"
          leaveFrom="opacity-100 translate-y-0"
          leaveTo="opacity-0 -translate-y-2"
        >
          <div className="mt-2 bg-white border border-neutral-200 rounded-lg shadow-lg p-4 z-10 relative">
            {/* Search Input */}
            <SiteInput
              label="Search"
              type="search"
              icon={faSearch}
              placeholder="Search states..."
              value={query}
              onChange={e => setQuery(e.target.value)}
              hideLabel
            />

            {/* Results */}
            <div className="mt-2 max-h-[240px] overflow-auto">
              {filteredStates.length === 0 ? (
                <div className="p-4 text-center text-neutral-500">No results found</div>
              ) : (
                <div className="space-y-1">
                  {filteredStates.map(state => (
                    <button
                      type="button" // Explicitly set type to prevent form submission
                      key={state.value}
                      onClick={() => handleSelect(state)}
                      className={`w-full flex items-center gap-3 px-4 py-2 text-left hover:bg-neutral-50 ${
                        localSelectedStates.includes(state.value)
                          ? 'text-brand-blue font-medium'
                          : 'text-neutral-700'
                      }`}
                    >
                      <span className="text-brand-blue font-medium">{state.label}</span>
                      {localSelectedStates.includes(state.value) && (
                        <FontAwesomeIcon icon={faCheck} className="h-4 w-4 ml-auto" />
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3 mt-4 pt-4 border-t border-neutral-200">
              <Button
                color="white"
                size="small"
                onClick={e => {
                  e.preventDefault(); // Prevent form submission
                  setIsOpen(false);
                }}
                type="button" // Explicitly set type to prevent form submission
              >
                Cancel
              </Button>
              <Button
                color="blue"
                size="small"
                onClick={e => {
                  e.preventDefault(); // Prevent form submission
                  setIsOpen(false);
                }}
                type="button" // Explicitly set type to prevent form submission
              >
                Done
              </Button>
            </div>
          </div>
        </Transition>
      </div>
    </div>
  );
}

export default StatesSelector;
