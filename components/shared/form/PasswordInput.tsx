'use client';

import React, { useEffect, useRef, useState } from 'react';
import type { InputHTMLAttributes } from 'react';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faEye, faEyeSlash, faLock } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Description, Field, Label } from '@headlessui/react';
import clsx from 'clsx';

interface PasswordInputProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, 'className' | 'type'> {
  label: string;
  icon?: IconDefinition;
  disabled?: boolean;
  description?: string;
  isFailedValidation?: boolean;
  className?: string;
}

const PasswordInput: React.FC<PasswordInputProps> = ({
  label,
  icon,
  disabled = false,
  description,
  isFailedValidation = false,
  className = '',
  ...props
}) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Ensure the input type is synchronized with our state
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.type = isPasswordVisible ? 'text' : 'password';
    }
  }, [isPasswordVisible]);

  const togglePasswordVisibility = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsPasswordVisible(prev => !prev);
  };

  return (
    <Field disabled={disabled}>
      <Label className="pa-eyebrow text-text-primary">{label}</Label>

      <div className="relative mt-2">
        <div
          className={clsx(
            'flex items-center rounded-lg bg-white border',
            isFailedValidation ? 'border-brand-red' : 'border-gray-200'
          )}
        >
          <div className="shrink-0 absolute left-4 top-1/2 z-10 -translate-y-1/2 text-gray-900 select-none sm:text-sm/6">
            <FontAwesomeIcon
              icon={faLock}
              aria-hidden="true"
              className="pointer-events-none size-4"
            />
          </div>

          {/* Use a regular input inside Headless UI's Field context */}
          <input
            {...props}
            id={props.id || 'password-input'}
            ref={inputRef}
            // Initial type, but will be controlled by the ref
            type="password"
            className={`block w-full min-w-0 grow py-2 pl-10 rounded-lg pr-9 text-gray-900 text-base placeholder:text-gray-400 focus:ring-1 focus:ring-brand-blue focus:outline-none ${className}`}
            disabled={disabled}
          />

          <button
            type="button"
            className="absolute top-1/2 -translate-y-1/2 right-4 shrink-0"
            aria-label="Toggle Password Visibility"
            onClick={togglePasswordVisibility}
          >
            <span className="sr-only">Toggle Password Visibility</span>
            <FontAwesomeIcon
              icon={isPasswordVisible ? faEyeSlash : faEye}
              aria-hidden="true"
              className="!size-4"
            />
          </button>
        </div>
      </div>

      {description && (
        <Description
          className={clsx(
            'mt-1 text-xs',
            isFailedValidation ? 'text-brand-red' : 'text-text-secondary'
          )}
        >
          {description}
        </Description>
      )}
    </Field>
  );
};

export default PasswordInput;
