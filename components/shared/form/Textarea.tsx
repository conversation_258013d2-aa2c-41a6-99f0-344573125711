import React, { TextareaHTMLAttributes } from 'react';
import { Description, Field, Textarea as HeadlessTextarea, Label } from '@headlessui/react';
import { cn } from '@/lib/utils';

interface TextareaProps extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  error?: string;
  description?: string;
  hideLabel?: boolean;
}

export function Textarea({
  label,
  error,
  description,
  className = '',
  disabled,
  hideLabel = false,
  ...props
}: TextareaProps) {
  return (
    <Field disabled={disabled}>
      <Label
        className={cn(
          'pa-eyebrow text-text-primary data-[disabled]:opacity-50',
          hideLabel ? 'sr-only' : 'mb-2'
        )}
      >
        {label}
      </Label>
      {description && !error && (
        <Description className="mt-1 text-sm text-text-secondary data-[disabled]:opacity-50">
          {description}
        </Description>
      )}
      <HeadlessTextarea
        invalid={<PERSON>olean(error)}
        disabled={disabled}
        className={cn(
          'w-full px-4 py-3',
          'bg-white',
          'border border-gray-300 rounded-lg',
          'text-base text-text-primary',
          'placeholder:text-text-secondary',
          'transition-colors duration-200 ease-in-out',
          'data-[focus]:outline-none data-[focus]:ring-2 data-[focus]:ring-blue-700 data-[focus]:border-transparent',
          // 'data-[hover]:shadow',
          'data-[disabled]:bg-gray-100 data-[disabled]:text-gray-500 data-[disabled]:cursor-not-allowed',
          'data-[invalid]:border-red-500 data-[invalid]:focus:ring-brand-red',
          className
        )}
        {...props}
      />
      {error && (
        <Description className="mt-1 text-sm text-brand-red" role="alert">
          {error}
        </Description>
      )}
    </Field>
  );
}
