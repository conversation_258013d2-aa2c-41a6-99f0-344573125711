import React from 'react';
import type { InputHTMLAttributes } from 'react';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faCircle, faCircleDot } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Field, Label, Radio, RadioGroup } from '@headlessui/react';
import clsx from 'clsx';

export interface RadioOption {
  name: string;
  value: string;
  icon: IconDefinition;
}

export type ColumnCount = 2 | 3 | 4;

export interface IconRadioGroupProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, 'className' | 'onChange'> {
  items: RadioOption[];
  disabled?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
  columns?: ColumnCount;
}

const IconRadioGroup: React.FC<IconRadioGroupProps> = ({
  items,
  disabled = false,
  value,
  onChange,
  className = '',
  columns = 2,
  ...props
}) => {
  // Map column count to the appropriate Tailwind grid class
  const getColumnClass = (cols: ColumnCount): string => {
    switch (cols) {
      case 2:
        return 'md:grid-cols-2';
      case 3:
        return 'md:grid-cols-3';
      case 4:
        return 'md:grid-cols-4';
      default:
        return 'md:grid-cols-2';
    }
  };

  return (
    <div className={className}>
      <RadioGroup value={value ?? null} onChange={onChange} disabled={disabled} {...props}>
        <div className={clsx('grid grid-cols-1', getColumnClass(columns), 'gap-4')}>
          {items?.map((item, index) => (
            <Field key={`icon-radio-item-${index}`} className="relative">
              <Radio value={item.value} className="cursor-pointer">
                {({ checked, disabled }) => (
                  <div
                    className={clsx(
                      'h-full pointer-events-none flex gap-4 p-4 border rounded-lg cursor-pointer transition-colors',
                      checked
                        ? 'border-brand-red bg-surface-secondary'
                        : 'border-gray-200 bg-surface-secondary',
                      disabled && 'opacity-50 cursor-not-allowed'
                    )}
                  >
                    <div className="block space-y-2 flex-shrink-0 flex-1">
                      {checked ? (
                        <FontAwesomeIcon
                          icon={faCircleDot}
                          className="size-5 block text-brand-red"
                        />
                      ) : (
                        <FontAwesomeIcon icon={faCircle} className="size-5 block text-gray-400" />
                      )}

                      <Label className="text-sm block font-medium text-gray-500">{item.name}</Label>
                    </div>

                    <div className="flex justify-end">
                      {item.icon && (
                        <FontAwesomeIcon icon={item.icon} className="size-5 text-brand-red" />
                      )}
                    </div>
                  </div>
                )}
              </Radio>
            </Field>
          ))}
        </div>
      </RadioGroup>
    </div>
  );
};

export default IconRadioGroup;
