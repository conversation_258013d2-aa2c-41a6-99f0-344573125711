import { FC, useEffect } from 'react';
import { faListUl } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Placeholder from '@tiptap/extension-placeholder';
import Underline from '@tiptap/extension-underline';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { cn } from '@/lib/utils';

interface WysiwygEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  maxLength?: number;
  placeholder?: string;
  label?: string;
  hideCharacterCount?: boolean;
}

export const WysiwygEditor: FC<WysiwygEditorProps> = ({
  value,
  onChange,
  maxLength,
  placeholder = 'Start typing...',
  label,
  hideCharacterCount = false,
}) => {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          HTMLAttributes: {
            class: 'list-disc ml-4',
          },
        },
      }),
      Underline.configure(),
      Placeholder.configure({
        placeholder,
        emptyEditorClass: 'is-editor-empty',
      }),
    ],
    content: value,
    editorProps: {
      attributes: {
        class:
          'min-h-[120px] w-full p-4 text-body-md text-gray-900 focus:outline-none prose prose-neutral max-w-none',
      },
    },
    immediatelyRender: false,
    onUpdate: ({ editor }) => {
      const text = editor.state.doc.textContent;
      onChange?.(editor.getHTML());

      // If maxLength is set, enforce the limit
      if (maxLength && text.length > maxLength) {
        const truncated = text.slice(0, maxLength);
        editor.commands.setContent(truncated);
      }
    },
  });

  // Update editor content when value prop changes
  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value || '');
    }
  }, [editor, value]);

  const charCount = editor?.state.doc.textContent.length ?? 0;

  return (
    <div className="flex flex-col gap-2">
      {label && <label className="pa-eyebrow text-text-primary">{label}</label>}
      <div className="flex flex-col rounded-lg border border-gray-300 bg-white overflow-hidden">
        {/* Formatting Toolbar */}
        <div className="flex items-center gap-1 border-b border-gray-300 bg-white p-2">
          <button
            type="button"
            onClick={() => editor?.chain().focus().toggleBold().run()}
            className={cn(
              'rounded p-2 hover:bg-gray-100',
              editor?.isActive('bold') && 'bg-gray-100'
            )}
          >
            <span className="font-semibold text-gray-500">B</span>
          </button>
          <button
            type="button"
            onClick={() => editor?.chain().focus().toggleItalic().run()}
            className={cn(
              'rounded p-2 hover:bg-gray-100',
              editor?.isActive('italic') && 'bg-gray-100'
            )}
          >
            <span className="italic text-gray-500">I</span>
          </button>
          <button
            type="button"
            onClick={() => editor?.chain().focus().toggleUnderline().run()}
            className={cn(
              'rounded p-2 hover:bg-gray-100',
              editor?.isActive('underline') && 'bg-gray-100'
            )}
          >
            <span className="underline text-gray-500">U</span>
          </button>
          <button
            type="button"
            onClick={() => editor?.chain().focus().toggleBulletList().run()}
            className={cn(
              'rounded p-2 hover:bg-gray-100',
              editor?.isActive('bulletList') && 'bg-gray-100'
            )}
          >
            <FontAwesomeIcon icon={faListUl} className="h-4 w-4 text-gray-500" />
          </button>
        </div>
        {/* TipTap Editor */}
        <EditorContent
          editor={editor}
          className="[&_.is-editor-empty]:before:text-gray-400 [&_.is-editor-empty]:before:content-[attr(data-placeholder)] [&_.is-editor-empty]:before:float-left [&_.is-editor-empty]:before:pointer-events-none"
        />
      </div>
      {!hideCharacterCount && maxLength && (
        <div className="text-right text-body-sm text-gray-500">
          {maxLength - charCount} Characters Remaining
        </div>
      )}
    </div>
  );
};
