import React, { InputHTMLAttributes, useRef } from 'react';
import { faSearch, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Field, Input, Label } from '@headlessui/react';

interface SearchInputProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, 'className' | 'value'> {
  disabled?: boolean;
  description?: string;
  className?: string;
  placeholder?: string;
  value?: string | null;
}

const SearchInput: React.FC<SearchInputProps> = ({
  disabled = false,
  description,
  className = '',
  placeholder = 'Search',
  value,
  ...props
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleClear = () => {
    if (props.onChange) {
      // Create a synthetic event that matches the expected type
      const syntheticEvent = {
        target: {
          value: '',
        },
      } as React.ChangeEvent<HTMLInputElement>;

      props.onChange(syntheticEvent);

      // Focus the input after clearing
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  };

  return (
    <Field disabled={disabled}>
      <Label className="sr-only">Search</Label>

      <div className="relative flex items-center rounded-full bg-white border border-gray-20">
        <Input
          type="text"
          ref={inputRef}
          className={`block min-w-0 grow py-2 rounded-full shadow-search-input pl-4 pr-10 text-gray-900 placeholder:text-gray-400 text-ellipsis text-base focus-within:ring-inset focus-within:ring-1 focus-within:ring-brand-blue ${className}`}
          placeholder={placeholder}
          value={value ?? ''}
          {...props}
        />

        <div className="absolute inset-y-0 right-4 flex items-center">
          {value ? (
            <button
              type="button"
              className="text-current cursor-pointer"
              onClick={handleClear}
              aria-label="Clear Search"
            >
              <span className="sr-only">Clear Search</span>
              <FontAwesomeIcon icon={faXmark} aria-hidden="true" className="size-4" />
            </button>
          ) : (
            <FontAwesomeIcon
              icon={faSearch}
              className="pointer-events-none size-4 text-gray-400"
              aria-hidden="true"
            />
          )}
        </div>
      </div>
    </Field>
  );
};

export default SearchInput;
