'use client';

import React from 'react';
import type { YouTubeProps } from 'react-youtube';
import dynamic from 'next/dynamic';

const YouTube = dynamic<YouTubeProps>(() => import('react-youtube'), { ssr: false });

interface YouTubeVideoProps {
  url: string;
  className?: string;
  startTime?: number;
  endTime?: number;
  onReady?: (event: any) => void;
  onEnd?: (event: any) => void;
  onStateChange?: (event: any) => void;
}

export default function YouTubeVideo({
  url,
  className = '',
  startTime,
  endTime,
  onReady,
  onEnd,
  onStateChange,
}: YouTubeVideoProps) {
  // Extract YouTube video ID from URL
  const getYoutubeId = (url: string) => {
    const match = url.match(
      /(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))([^&?]+)/
    );
    return match ? match[1] : null;
  };

  const videoId = getYoutubeId(url);

  if (!videoId) {
    return null;
  }

  const playerVars: { [key: string]: any } = {
    controls: 1,
    disablekb: 1,
    fs: 0,
    modestbranding: 1,
    rel: 0,
    showinfo: 0,
    iv_load_policy: 3,
    playsinline: 1,
    title: 0,
    cc_load_policy: 0,
    autohide: 1,
    origin: typeof window !== 'undefined' ? window.location.origin : '',
  };

  // Add start time if provided
  if (startTime) {
    playerVars.start = startTime;
  }

  return (
    <div className={`relative aspect-video bg-gray-900 overflow-hidden rounded-lg ${className}`}>
      <YouTube
        videoId={videoId}
        iframeClassName="absolute inset-0 w-full h-full"
        onReady={onReady}
        onEnd={onEnd}
        onStateChange={onStateChange}
        opts={{
          width: '100%',
          height: '100%',
          playerVars,
        }}
      />
    </div>
  );
}
