import React from 'react';
import Link from 'next/link';
import { faArrowRight, faEyes } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';

const BadgesPlaceholder: React.FC = () => {
  return (
    <div className="block text-center py-6">
      <FontAwesomeIcon className="mb-6 !size-8" icon={faEyes} aria-hidden="true" />

      <div className="block space-y-4">
        <h2 className="text-2xl font-semibold text-text-primary">Nothing here yet!</h2>

        <p>Start earning badges by completing X Factor modules!</p>

        <Button href="/x-factor" size="small" icon={faArrowRight} iconPosition="right">
          Explore X Factor
        </Button>
      </div>
    </div>
  );
};

export default BadgesPlaceholder;
