'use client';

import React from 'react';
import clsx from 'clsx';

interface RichtextProps {
  content: string;
  className?: string;
}

export const Richtext = ({ content, className = '' }: RichtextProps) => {
  if (!content) return null;

  return (
    <div
      className={clsx('prose max-w-none', className)}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

export default Richtext;
