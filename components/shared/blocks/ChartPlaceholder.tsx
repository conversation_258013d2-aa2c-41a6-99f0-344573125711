import React from 'react';
import Image from 'next/image';
import clsx from 'clsx';

interface ChartCategory {
  name: string;
  percentage: number;
}

interface ChartPlaceholderProps {
  title?: string;
  displayTitle?: boolean;
  displayAside?: boolean;
}

const ChartPlaceholder: React.FC<ChartPlaceholderProps> = ({
  title = 'Start watching modules to see your learning investment stats',
  displayTitle = false,
  displayAside = true,
}) => {
  const chartCategories: ChartCategory[] = [
    { name: 'Leadership', percentage: 0 },
    { name: 'Integrity', percentage: 0 },
    { name: 'Balance', percentage: 0 },
    { name: 'Responsibility', percentage: 0 },
    { name: 'Focus', percentage: 0 },
    { name: 'Discipline', percentage: 0 },
    { name: 'Courage', percentage: 0 },
    { name: 'Team', percentage: 0 },
    { name: 'Compassion', percentage: 0 },
    { name: 'Purpose', percentage: 0 },
  ];

  return (
    <div
      className={clsx('flex gap-8', {
        'items-center justify-center': !displayAside,
      })}
    >
      <div className="relative size-80 mx-auto shrink-0 lg:mx-0">
        <Image
          src="/images/pinwheel.png"
          className="object-contain"
          alt="Empty Learning Chart"
          fill
        />
        {displayTitle && (
          <p className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 px-4 w-full font-semibold text-text-primary text-center">
            {title}
          </p>
        )}
      </div>

      {displayAside && (
        <ul className="hidden space-y-2 pointer-events-none lg:block">
          {chartCategories.map(category => (
            <li key={category.name} className="flex items-center gap-4">
              <div
                className={clsx(
                  'flex items-center justify-center font-bold min-w-[56px] p-1 rounded-lg text-sm',
                  'bg-gray-100 text-gray-800'
                )}
              >
                {category.percentage}%
              </div>
              <span className={clsx('text-base transition-colors', 'text-gray-60')}>
                {category.name}
              </span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default ChartPlaceholder;
