import React, { useMemo } from 'react';
import Image from 'next/image';
import { faArrowUpToLine } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import IconAward from '@/components/svgs/IconAward';
import { cn } from '@/lib/utils';

export interface AvatarProps {
  src?: string;
  firstName?: string;
  lastName?: string;
  alt?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  isUploadState?: boolean;
  placeholderContent?: React.ReactNode;
  hasAchievements?: boolean;
  onClick?: () => void;
}

const sizeMap = {
  sm: 'w-8 h-8 text-sm',
  md: 'w-12 h-12 text-base',
  lg: 'w-20 h-20 text-xl',
  xl: 'w-[150px] h-[150px] text-3xl',
};

// Frame size map adds 24px padding (12px on each side) for xl size
const frameSizeMap = {
  sm: 'w-10 h-10 shrink-0',
  md: 'w-14 h-14 shrink-0',
  lg: 'w-24 h-24 shrink-0',
  xl: 'w-[174px] h-[174px] shrink-0',
};

export default function Avatar({
  src,
  firstName,
  lastName,
  alt,
  size = 'md',
  className = '',
  isUploadState = false,
  placeholderContent,
  hasAchievements = false,
  onClick,
}: AvatarProps) {
  const initials = useMemo(() => {
    if (!firstName && !lastName) return '';
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  }, [firstName, lastName]);

  const sizeClasses = sizeMap[size];
  const frameSizeClasses = frameSizeMap[size];

  const baseClasses = `
    relative bg-transparent rounded-full
    ${sizeClasses}
    ${className}
  `.trim();

  const avatarContent = (
    <div
      className={cn(
        baseClasses,
        'border-dashed border-[#A5ABAD] border-spacing-4',
        // 'shadow-[6px_6px_10px_rgba(171,171,171,0.7),-6px_-6px_10px_rgba(255,255,255,0.8)]',
        // 'shadow-neumorphism-inset-small',
        //'shadow-avatar-outer',
        src ? 'border-none' : 'border-2',
        onClick ? 'cursor-pointer' : '',
        'relative'
      )}
      onClick={onClick}
    >
      {(!src && !initials) || isUploadState ? (
        <div className="absolute inset-0 flex flex-col items-center justify-center gap-1">
          <FontAwesomeIcon
            icon={faArrowUpToLine}
            className={cn('text-[#666666]', size === 'xl' ? 'w-5 h-5' : 'w-3 h-3')}
          />
          {size === 'xl' && (
            <span className="text-xs font-medium text-[#666666] text-center leading-tight mt-1">
              {placeholderContent ? (
                placeholderContent
              ) : (
                <>
                  Upload Profile
                  <br />
                  Photo
                </>
              )}
            </span>
          )}
        </div>
      ) : !src ? (
        <div className="absolute inset-0 overflow-hidden rounded-full flex items-center justify-center font-medium text-gray-600">
          {initials}
        </div>
      ) : (
        <>
          <div className="absolute inset-0 overflow-hidden rounded-full">
            <Image
              src={src}
              alt={alt || `${firstName} ${lastName}`.trim() || 'Avatar'}
              fill
              unoptimized
              className="object-cover"
              sizes={size === 'xl' ? '150px' : '80px'}
            />
          </div>
          {/* Inner shadow overlay */}
          <div className="absolute inset-0 rounded-full overflow-hidden">
            <div className="absolute inset-0 shadow-[inset_0px_2px_4px_rgba(0,0,0,0.08)]" />
            <div className="absolute inset-0 shadow-[0px_-2px_3px_rgba(0,0,0,0.07)]" />
            <div className="absolute inset-0 shadow-[0px_6px_8px_rgba(0,0,0,0.03)]" />
            <div className="absolute inset-0 shadow-[0px_2px_3px_rgba(255,255,255,0.8)]" />
          </div>
          {/* Inward neumorphic overlay */}
          <div
            className="absolute inset-0 rounded-full"
            style={{
              background: 'rgba(255, 255, 255, 0.15)',
              boxShadow: `
                inset 0px -14px 20px -6px rgba(50, 50, 93, 0.3),
                inset 0px -8px 16px -8px rgba(0, 0, 0, 0.35),
                inset 0px 14px 20px -6px rgba(255, 255, 255, 0.45),
                inset 0px 8px 16px -8px rgba(255, 255, 255, 0.55)
              `,
            }}
          />
        </>
      )}
    </div>
  );

  // Add the outer frame for the avatar
  return (
    <div
      className={cn(
        'relative rounded-full bg-surface-secondary',
        frameSizeClasses,
        'flex items-center justify-center',
        // 'shadow-[0px_0px_15px_rgba(0,0,0,0.05)]',
        'shadow-avatar-outer'
      )}
    >
      {avatarContent}

      {hasAchievements && (
        <div className="absolute -bottom-2 right-0 size-16 shrink-0 z-10 pointer-events-none">
          <Image
            src="/images/avatar-badge.png"
            className="absolute inset-0 w-full h-full object-contain"
            fill
            aria-hidden="true"
            alt="Achievement Badge"
            role="presentation"
          />
        </div>
      )}
    </div>
  );
}
