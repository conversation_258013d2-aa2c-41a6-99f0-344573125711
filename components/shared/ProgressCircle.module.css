.progress {
  position: relative;
}

.circle-progress {
  transform: rotate(-90deg);
  overflow: visible;
}

.circle-progress-circle {
  stroke-width: 12px;
  fill: none;
}

.circle-progress-value {
  stroke-width: 8px;
  fill: none;
  stroke-linecap: round;
  stroke-dasharray: var(--circumference) var(--circumference);
  animation: progress 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

@keyframes progress {
  from {
    stroke-dashoffset: var(--circumference);
  }
  to {
    stroke-dashoffset: var(--offset);
  }
}
