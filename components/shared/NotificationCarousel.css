/* Custom styles for NotificationCarousel */

.notification-carousel .swiper-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1rem;
}

.notification-carousel .swiper-pagination-fraction {
  position: relative;
  bottom: auto;
  left: auto;
  width: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4b5563; /* text-gray-600 */
  font-size: 0.875rem; /* text-sm */
  font-weight: 500; /* font-medium */
  margin-right: 0.75rem;
}

.notification-carousel .swiper-pagination-bullets {
  position: relative;
  bottom: auto;
  left: auto;
  width: auto;
  display: flex;
  align-items: center;
}

.notification-carousel .swiper-pagination-bullet {
  width: 0.625rem;
  height: 0.625rem;
  margin: 0 0.25rem;
  background-color: #d7dcde; /* bg-grey-3 */
  opacity: 1;
  transition: background-color 0.3s ease;
}

.notification-carousel .swiper-pagination-bullet-active {
  background-color: #d50032; /* bg-brand-red */
}
