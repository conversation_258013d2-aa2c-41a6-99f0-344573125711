'use client';

import Image from 'next/image';
import { cn } from '@/lib/utils';
import { PhotoMetadata } from '@/types/profile';

interface PhotoGridProps {
  photos: PhotoMetadata[];
  height?: number;
  className?: string;
}

interface PhotoLayout {
  photo: PhotoMetadata;
  width: string; // percentage width
}

export default function PhotoGrid({ photos, height = 280, className }: PhotoGridProps) {
  if (!photos?.length) return null;

  // Calculate aspect ratio for a photo
  const getAspectRatio = (photo: PhotoMetadata) => photo.width / photo.height;

  // Calculate optimal layout based on aspect ratios
  const calculateLayout = (photos: PhotoMetadata[]): PhotoLayout[] => {
    const displayPhotos = photos.slice(0, 4);
    const ratios = displayPhotos.map(getAspectRatio);

    // The goal is to make all photos the same height while filling the width
    // If we make all photos the same height (h), then each photo's width will be: ratio * h
    // The sum of all widths should equal the container width
    const totalRatios = ratios.reduce((sum, ratio) => sum + ratio, 0);

    // Each photo's width percentage is its ratio divided by total ratios
    return displayPhotos.map((photo, i) => ({
      photo,
      width: `${(ratios[i] / totalRatios) * 100}%`,
    }));
  };

  const layout = calculateLayout(photos);
  const remainingCount = Math.max(0, photos.length - 4);

  return (
    <div className={cn('flex gap-1', className)} style={{ height }}>
      {layout.map(({ photo, width }, index) => (
        <div key={photo.url} className="relative overflow-hidden" style={{ width }}>
          <Image
            src={photo.url}
            alt=""
            fill
            className="object-cover"
            sizes={`(max-width: 768px) 100vw, ${parseFloat(width)}vw`}
          />

          {/* Show remaining count overlay on last visible photo if there are more */}
          {remainingCount > 0 && index === layout.length - 1 && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <span className="text-white text-2xl font-bold">+{remainingCount}</span>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
