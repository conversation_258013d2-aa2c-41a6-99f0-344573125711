import React, { forwardRef, useCallback, useImperativeHandle, useRef, useState } from 'react';
import { FileRejection, useDropzone } from 'react-dropzone';
import Cropper from 'react-easy-crop';
import { faCloudArrowUp, faSpinner } from '@fortawesome/pro-light-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useToastNotify } from '@/stores/toastNotify.store';

interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface CroppedArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface UploadResult {
  success: boolean;
  data?: any;
  error?: string;
  validationErrors?: Record<string, string[]>;
}

export interface AvatarEditorProps {
  onSave?: (file: File) => void;
  onUpload?: (file: File) => Promise<UploadResult>;
  onReadyToSave?: (isReady: boolean) => void;
  className?: string;
  dropzoneText?: {
    default: string;
    active: string;
    subText: string;
  };
  showZoomControl?: boolean;
  currentImageUrl?: string;
  maxFileSizeMB?: number;
  isUploading?: boolean;
  uploadError?: string | null;
}

export interface AvatarEditorRef {
  handleSave: () => Promise<void>;
  getFile: () => Promise<File | null>;
  uploadFile: () => Promise<UploadResult>;
  reset: () => void;
  setError: (error: string | null) => void;
}

export const AvatarEditor = forwardRef<AvatarEditorRef, AvatarEditorProps>(function AvatarEditor(
  {
    onSave,
    onUpload,
    onReadyToSave,
    className = '',
    dropzoneText = {
      default: 'Drag & drop your photo here',
      active: 'Drop your photo here',
      subText: 'or click to browse',
    },
    showZoomControl = true,
    currentImageUrl,
    maxFileSizeMB,
    isUploading: externalIsUploading = false,
    uploadError: externalUploadError = null,
  },
  ref
) {
  const [selectedImage, setSelectedImage] = React.useState<string | null>(currentImageUrl || null);
  const [pendingFile, setPendingFile] = React.useState<File | null>(null);
  const [crop, setCrop] = React.useState({ x: 0, y: 0 });
  const [zoom, setZoom] = React.useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = React.useState<CropArea | null>(null);
  const [error, setError] = React.useState<string | null>(null);
  const [internalIsUploading, setInternalIsUploading] = React.useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toastNotify } = useToastNotify();

  const isUploading = externalIsUploading || internalIsUploading;
  const currentError = externalUploadError || error;

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles && acceptedFiles.length > 0) {
        const file = acceptedFiles[0];

        if (maxFileSizeMB && file.size > maxFileSizeMB * 1024 * 1024) {
          toastNotify(`File Too Large: Image must be smaller than ${maxFileSizeMB}MB.`, 'error');
          return;
        }

        const url = URL.createObjectURL(file);
        setSelectedImage(url);
        setPendingFile(file);
        setError(null);
        if (onReadyToSave) {
          onReadyToSave(true);
        }
      }
    },
    [onReadyToSave, maxFileSizeMB, toastNotify]
  );

  const handleClick = () => {
    if (fileInputRef.current && !isUploading) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (maxFileSizeMB && file.size > maxFileSizeMB * 1024 * 1024) {
        toastNotify(`File Too Large: Image must be smaller than ${maxFileSizeMB}MB.`, 'error');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }

      const url = URL.createObjectURL(file);
      setSelectedImage(url);
      setPendingFile(file);
      setError(null);
      if (onReadyToSave) {
        onReadyToSave(true);
      }
    }
  };

  const onDropRejected = useCallback(
    (fileRejections: FileRejection[]) => {
      fileRejections.forEach(({ file, errors }) => {
        errors.forEach(err => {
          if (err.code === 'file-invalid-type') {
            toastNotify(
              `Invalid File Type: "${file.name}" is not a supported image type.`,
              'error'
            );
          } else {
            toastNotify(`Upload Error: ${err.message}`, 'error');
          }
        });
      });
    },
    [toastNotify]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    onDropRejected,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif'],
    },
    maxSize: maxFileSizeMB ? maxFileSizeMB * 1024 * 1024 : undefined,
    maxFiles: 1,
    noClick: true,
    disabled: isUploading,
  });

  const onCropComplete = React.useCallback(
    (croppedArea: CroppedArea, croppedAreaPixels: CropArea) => {
      setCroppedAreaPixels(croppedAreaPixels);
    },
    []
  );

  const createImage = (url: string): Promise<HTMLImageElement> =>
    new Promise((resolve, reject) => {
      const image = new Image();
      image.addEventListener('load', () => resolve(image));
      image.addEventListener('error', error => reject(error));
      image.src = url;
    });

  const getCroppedImg = async (imageSrc: string, pixelCrop: CropArea): Promise<Blob> => {
    const image = await createImage(imageSrc);
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('No 2d context');
    }

    canvas.width = pixelCrop.width;
    canvas.height = pixelCrop.height;

    ctx.drawImage(
      image,
      pixelCrop.x,
      pixelCrop.y,
      pixelCrop.width,
      pixelCrop.height,
      0,
      0,
      pixelCrop.width,
      pixelCrop.height
    );

    return new Promise(resolve => {
      canvas.toBlob(
        blob => {
          if (!blob) {
            throw new Error('Canvas is empty');
          }
          resolve(blob);
        },
        'image/jpeg',
        0.95
      );
    });
  };

  const getFile = async (): Promise<File | null> => {
    try {
      if (!selectedImage || !croppedAreaPixels || !pendingFile) return null;

      const croppedImage = await getCroppedImg(selectedImage, croppedAreaPixels);
      const file = new File([croppedImage], pendingFile.name, { type: 'image/jpeg' });

      return file;
    } catch (err) {
      console.error('Error processing image:', err);
      return null;
    }
  };

  const uploadFile = async (): Promise<UploadResult> => {
    if (!onUpload) {
      return {
        success: false,
        error: 'No upload handler provided',
      };
    }

    try {
      setInternalIsUploading(true);
      setError(null);

      const file = await getFile();
      if (!file) {
        return {
          success: false,
          error: 'Failed to process image',
        };
      }

      const result = await onUpload(file);

      // Check if the result indicates success/failure
      if (result && typeof result === 'object' && 'success' in result) {
        if (!result.success) {
          setError(result.error || 'Upload failed');
        }
        return result;
      }

      // If no result object or success property, assume success
      return {
        success: true,
        data: result,
      };
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to upload image. Please try again.';
      setError(errorMessage);
      console.error('Error uploading image:', err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setInternalIsUploading(false);
    }
  };

  const handleSave = async (): Promise<void> => {
    try {
      if (!selectedImage || !croppedAreaPixels || !pendingFile) return;
      setError(null);

      const file = await getFile();
      if (!file) return;

      if (onSave) {
        onSave(file);
      }
    } catch (err) {
      setError('Failed to process image. Please try again.');
      console.error('Error processing image:', err);
    }
  };

  const resetEditor = () => {
    setSelectedImage(null);
    setPendingFile(null);
    setError(null);
    setInternalIsUploading(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    if (onReadyToSave) {
      onReadyToSave(false);
    }
  };

  const setErrorState = (newError: string | null) => {
    setError(newError);
  };

  useImperativeHandle(ref, () => ({
    handleSave,
    getFile,
    uploadFile,
    reset: resetEditor,
    setError: setErrorState,
  }));

  React.useEffect(() => {
    return () => {
      if (selectedImage && selectedImage !== currentImageUrl) {
        URL.revokeObjectURL(selectedImage);
      }
    };
  }, [selectedImage, currentImageUrl]);

  return (
    <div className={className}>
      {currentError && (
        <div className="mb-4 p-4 bg-red-50 text-red-700 rounded-lg">{currentError}</div>
      )}

      <div className="relative w-full aspect-square rounded-2xl border border-neutral-300 overflow-hidden">
        {!selectedImage ? (
          <div
            {...getRootProps()}
            onClick={handleClick}
            className={`flex flex-col items-center justify-center w-full h-full cursor-pointer bg-neutral-100 hover:bg-neutral-50 transition-colors ${
              isDragActive ? 'border-2 border-dashed border-blue-500 bg-blue-50' : ''
            } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              ref={fileInputRef}
              className="hidden"
              disabled={isUploading}
            />
            <div className="text-neutral-500 text-center">
              {isUploading ? (
                <FontAwesomeIcon icon={faSpinner} className="h-8 w-8 mb-2 animate-spin" />
              ) : (
                <FontAwesomeIcon icon={faCloudArrowUp} className="h-8 w-8 mb-2" />
              )}
              <div className="mb-2">
                {isUploading
                  ? 'Uploading...'
                  : isDragActive
                    ? dropzoneText.active
                    : dropzoneText.default}
              </div>
              {!isUploading && <div className="text-sm">{dropzoneText.subText}</div>}
            </div>
          </div>
        ) : (
          <div className="relative w-full h-full">
            <Cropper
              image={selectedImage}
              crop={crop}
              zoom={zoom}
              aspect={1}
              cropShape="round"
              showGrid={false}
              onCropChange={setCrop}
              onZoomChange={setZoom}
              onCropComplete={onCropComplete}
              disableAutomaticStylesInjection={isUploading}
            />
            {isUploading && (
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-10">
                <div className="text-white text-center">
                  <FontAwesomeIcon icon={faSpinner} className="h-8 w-8 mb-2 animate-spin" />
                  <div>Uploading...</div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {selectedImage && showZoomControl && !isUploading && (
        <div className="mt-4">
          <label className="block text-sm font-medium text-neutral-700 mb-2">Zoom</label>
          <input
            type="range"
            min={1}
            max={3}
            step={0.1}
            value={zoom}
            onChange={e => setZoom(Number(e.target.value))}
            className="w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer
              [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4
              [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-brand-red
              [&::-webkit-slider-thumb]:cursor-pointer [&::-webkit-slider-thumb]:border-0"
          />
        </div>
      )}
    </div>
  );
});
