import styles from './ProgressCircle.module.css';

interface ProgressCircleProps {
  progress: number;
  size?: number;
  primaryColor?: string;
  secondaryColor?: string;
  className?: string;
}

export function ProgressCircle({
  progress,
  size = 120,
  primaryColor = '#D50032',
  secondaryColor = '#FFFFFF',
  className = '',
}: ProgressCircleProps) {
  // Ensure progress is between 0 and 100
  const normalizedProgress = Math.min(100, Math.max(0, progress));

  // Calculate dimensions
  const normalizedSize = size - 16;
  const viewBoxSize = normalizedSize;
  const strokeWidth = 8;
  const radius = normalizedSize / 2 - strokeWidth / 2;
  const center = viewBoxSize / 2;

  // Calculate the circumference
  const circumference = 2 * Math.PI * radius;
  const offset = circumference - (normalizedProgress / 100) * circumference;

  return (
    <div
      className={`${styles.progress} ${className}`}
      style={{
        width: size,
        height: size,
        ['--circumference' as string]: circumference,
        ['--offset' as string]: offset,
      }}
      role="progressbar"
      aria-valuemin={0}
      aria-valuemax={100}
      aria-valuenow={normalizedProgress}
    >
      <svg
        width="100%"
        height="100%"
        viewBox={`0 0 ${viewBoxSize} ${viewBoxSize}`}
        className={styles['circle-progress']}
      >
        {/* Background circle */}
        <circle
          className={styles['circle-progress-circle']}
          cx={center}
          cy={center}
          r={radius}
          fill="none"
          stroke={secondaryColor}
          strokeWidth={strokeWidth + 4}
        />

        {/* Progress circle */}
        <circle
          className={styles['circle-progress-value']}
          cx={center}
          cy={center}
          r={radius}
          fill="none"
          stroke={primaryColor}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
        />
      </svg>

      {/* Center Content */}
      <div className="absolute inset-0 flex items-center justify-center">
        <span className="text-2xl font-bold text-white">{Math.round(normalizedProgress)}%</span>
      </div>
    </div>
  );
}
