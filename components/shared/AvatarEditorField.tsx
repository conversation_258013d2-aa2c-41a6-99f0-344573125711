import React, { forwardRef, useImperativeHandle, useState } from 'react';
import Image from 'next/image';
import { AvatarEditor, AvatarEditorRef, UploadResult } from '@/components/shared/AvatarEditor';
import { useAvatarUpload } from '@/hooks/useAvatarUpload';

interface AvatarEditorFieldProps {
  onSave?: (file: File) => void; // Legacy sync support
  onUpload?: (file: File) => Promise<any>; // New async upload
  onSuccess?: (result: any) => void; // Success callback
  onError?: (error: string) => void; // Error callback
  label?: string;
  description?: string;
  error?: string; // External error prop
  currentImageUrl?: string;
  onReadyToSave?: (isReady: boolean) => void;
  maxFileSizeMB?: number; // Optional client-side size hint
  uploadOnSave?: boolean; // Control when upload happens (default: true for immediate upload)
}

export interface AvatarEditorFieldRef {
  save: () => Promise<void>;
  upload: () => Promise<UploadResult>;
  reset: () => void;
  isEditing: boolean;
  isUploading: boolean;
}

export const AvatarEditorField = forwardRef<AvatarEditorFieldRef, AvatarEditorFieldProps>(
  function AvatarEditorField(
    {
      onSave,
      onUpload,
      onSuccess,
      onError,
      label = 'Profile Photo',
      description = 'Upload a professional photo of yourself.',
      error: externalError,
      currentImageUrl,
      onReadyToSave,
      maxFileSizeMB,
      uploadOnSave = true,
    },
    ref
  ) {
    const [isEditing, setIsEditing] = useState(false);
    const editorRef = React.useRef<AvatarEditorRef>(null);

    // Only set up avatar upload if we have an upload function
    const avatarUpload = useAvatarUpload({
      uploadFn: async (file: File) => {
        if (onUpload) {
          return await onUpload(file);
        }
        throw new Error('No upload function provided');
      },
      onSuccess: result => {
        if (onSuccess) {
          onSuccess(result);
        }
        setIsEditing(false);
      },
      onError: error => {
        if (onError) {
          onError(typeof error === 'string' ? error : error.message);
        }
      },
    });

    const handleImageClick = () => {
      if (!avatarUpload.isUploading) {
        setIsEditing(true);
      }
    };

    const handleSave = async () => {
      if (editorRef.current) {
        if (uploadOnSave && onUpload) {
          // Use async upload
          await editorRef.current.uploadFile();
        } else {
          // Use legacy sync save
          const file = await editorRef.current.getFile();
          if (file && onSave) {
            onSave(file);
            setIsEditing(false);
          }
        }
      }
    };

    const handleUpload = async (): Promise<UploadResult> => {
      if (editorRef.current) {
        return await editorRef.current.uploadFile();
      }
      return { success: false, error: 'Editor not ready' };
    };

    const handleReset = () => {
      if (editorRef.current) {
        editorRef.current.reset();
      }
      setIsEditing(false);
      avatarUpload.clearError();
    };

    const handleEditorUpload = async (file: File): Promise<UploadResult> => {
      if (onUpload) {
        return await avatarUpload.upload(file);
      }
      return { success: false, error: 'No upload handler provided' };
    };

    const handleEditorSave = (file: File) => {
      if (onSave) {
        onSave(file);
        setIsEditing(false);
      }
    };

    useImperativeHandle(ref, () => ({
      save: handleSave,
      upload: handleUpload,
      reset: handleReset,
      isEditing,
      isUploading: avatarUpload.isUploading,
    }));

    // Determine which error to show
    const displayError = externalError || avatarUpload.error;

    return (
      <div className="space-y-2">
        {label && (
          <label className="block text-label-lg font-semibold text-gray-900">{label}</label>
        )}
        {description && <p className="text-body-sm text-gray-500">{description}</p>}

        {currentImageUrl && !isEditing ? (
          <div
            onClick={handleImageClick}
            className={`max-w-[320px] transition-opacity ${
              avatarUpload.isUploading
                ? 'opacity-50 cursor-not-allowed'
                : 'cursor-pointer hover:opacity-80'
            }`}
          >
            <div className="relative w-24 h-24 rounded-full overflow-hidden">
              <Image
                src={currentImageUrl}
                alt="Current avatar"
                fill
                className="object-cover"
                sizes="96px"
                unoptimized
              />
            </div>
            <div className="mt-2 text-sm text-center text-gray-500">
              {avatarUpload.isUploading ? 'Uploading...' : 'Click to change photo'}
            </div>
          </div>
        ) : (
          <div>
            <AvatarEditor
              ref={editorRef}
              onReadyToSave={onReadyToSave}
              onUpload={onUpload ? handleEditorUpload : undefined}
              onSave={onSave ? handleEditorSave : undefined}
              className="max-w-[320px]"
              dropzoneText={{
                default: 'Upload your photo',
                active: 'Drop to upload',
                subText: 'or click to browse',
              }}
              currentImageUrl={isEditing ? undefined : currentImageUrl}
              maxFileSizeMB={maxFileSizeMB}
              isUploading={avatarUpload.isUploading}
              uploadError={avatarUpload.error}
            />
          </div>
        )}
        {displayError && <p className="text-sm text-red-600">{displayError}</p>}
      </div>
    );
  }
);
