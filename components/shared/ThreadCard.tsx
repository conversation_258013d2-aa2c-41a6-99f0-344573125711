'use client';

import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronRight, faChevronRightThin, faPinRegular, faPinSolid } from '@/lib/fontawesome';
import { cn } from '@/lib/utils';
import Avatar from './Avatar';

interface Participant {
  id: string;
  name: string;
  image?: string;
}

interface ThreadCardProps {
  title: string;
  datetime: Date;
  content: string;
  participants: Participant[];
  unreadCount?: number;
  isPinned?: boolean;
  isSelected?: boolean;
  onPin?: () => void;
  onClick?: () => void;
  className?: string;
}

export default function ThreadCard({
  title,
  datetime,
  content,
  participants,
  unreadCount = 0,
  isPinned = false,
  isSelected = false,
  onPin,
  onClick,
  className,
}: ThreadCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const formattedDate = new Intl.DateTimeFormat('en-US', {
    month: 'numeric',
    day: 'numeric',
    year: '2-digit',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  }).format(datetime);

  return (
    <div
      className={cn(
        'relative flex items-start gap-4 p-4 rounded-lg cursor-pointer transition-colors',
        isSelected ? 'bg-surface-secondary' : 'hover:bg-surface-secondary',
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
    >
      {/* Unread Indicator */}
      {unreadCount > 0 && (
        <div className="absolute left-2 top-1/2 -translate-y-1/2 w-2 h-2 rounded-full bg-brand-red" />
      )}

      {/* Stacked Avatars */}
      <div className="relative flex-shrink-0" style={{ width: '32px' }}>
        {participants.slice(0, 3).map((participant, index) => (
          <div
            key={participant.id}
            className="absolute"
            style={{
              left: `${index * 4}px`,
              zIndex: participants.length - index,
            }}
          >
            <Avatar
              src={participant.image}
              firstName={participant.name.split(' ')[0]}
              lastName={participant.name.split(' ')[1]}
              size="sm"
              className="border-2 border-white"
            />
          </div>
        ))}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between gap-2 mb-1">
          <h3 className="text-sm font-medium text-text-primary truncate">{title}</h3>
          <div className="flex items-center gap-2 flex-shrink-0">
            <span className="text-xs text-grey-5">{formattedDate}</span>
            {/* Pin Icon */}
            {(isPinned || isHovered) && (
              <button
                onClick={e => {
                  e.stopPropagation();
                  onPin?.();
                }}
                className="p-1 hover:bg-grey-1 rounded transition-colors"
              >
                <FontAwesomeIcon
                  icon={isPinned ? faPinSolid : faPinRegular}
                  className={cn('text-sm', isPinned ? 'text-brand-blue' : 'text-grey-5')}
                />
              </button>
            )}
            {/* Chevron */}
            {(isHovered || isSelected) && (
              <FontAwesomeIcon
                icon={isSelected ? faChevronRightThin : faChevronRight}
                className="text-grey-5 text-sm"
              />
            )}
          </div>
        </div>
        <p className="text-sm text-grey-7 line-clamp-1">{content}</p>
      </div>
    </div>
  );
}
