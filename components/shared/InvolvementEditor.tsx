'use client';

import ItemEditor from '@/components/resume/ItemEditor';
import SiteInput from '@/components/shared/form/SiteInput';
import { Textarea } from '@/components/shared/form/Textarea';

export type Involvement = {
  id: string;
  title: string;
  dateRange: string;
  description: string;
};

interface InvolvementEditorProps {
  items: Involvement[];
  onAdd: () => void;
  onUpdate: (id: string, updates: Partial<Omit<Involvement, 'id'>>) => void;
  onRemove: (id: string) => void;
  addButtonText?: string;
}

export default function InvolvementEditor({
  items,
  onAdd,
  onUpdate,
  onRemove,
  addButtonText = 'Add Another Item',
}: InvolvementEditorProps) {
  const renderFields = (item: Involvement) => (
    <>
      <SiteInput
        label="Title"
        type="text"
        value={item.title}
        onChange={e => onUpdate(item.id, { title: e.target.value })}
        placeholder="e.g., Student Government Treasurer"
      />

      <SiteInput
        label="Date Range"
        type="text"
        value={item.dateRange}
        onChange={e => onUpdate(item.id, { dateRange: e.target.value })}
        placeholder="e.g., 2022-2023"
      />

      <Textarea
        label="Description"
        value={item.description}
        onChange={e => onUpdate(item.id, { description: e.target.value })}
        placeholder="Briefly describe this activity"
        rows={4}
      />
    </>
  );

  return (
    <ItemEditor
      items={items}
      onAdd={onAdd}
      onRemove={onRemove}
      onUpdate={onUpdate}
      getTitle={item => item.title || 'Activity'}
      addButtonText={addButtonText}
      renderFields={renderFields}
    />
  );
}
