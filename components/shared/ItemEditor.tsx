'use client';

interface Item {
  id: string;
  title: string;
  dateRange: string;
  description: string;
}

interface ItemEditorProps {
  items: Item[];
  isEditing: boolean;
  isLoading: boolean;
  error: string | null;
  isDirty: boolean;
  isEditable: boolean;
  onEdit: () => void;
  onCancel: () => void;
  onSave: () => Promise<void>;
  onChange: (items: Item[]) => void;
  onAdd?: () => void;
  onRemove?: (id: string) => void;
  onUpdateItem?: (id: string, updates: Partial<Item>) => void;
  emptyMessage: string;
  addButtonText: string;
}

export function ItemEditor({ items, isEditing, onAdd, onRemove, onUpdateItem }: ItemEditorProps) {
  const handleRemove = (id: string) => {
    if (onRemove) {
      onRemove(id);
    }
  };

  const handleUpdate = (id: string, updates: Partial<Item>) => {
    if (onUpdateItem) {
      onUpdateItem(id, updates);
    }
  };

  const handleAdd = () => {
    if (onAdd) {
      onAdd();
    }
  };

  return (
    <div>
      {isEditing ? (
        <div>
          {items.map(item => (
            <div key={item.id}>
              {/* Item editing UI */}
              <button onClick={() => handleRemove(item.id)}>Remove</button>
              <button onClick={() => handleUpdate(item.id, {})}>Update</button>
            </div>
          ))}
          <button onClick={handleAdd}>Add</button>
        </div>
      ) : (
        <div>
          {items.map(item => (
            <div key={item.id}>{/* Item display UI */}</div>
          ))}
        </div>
      )}
    </div>
  );
}
