'use client';

import React, { useState } from 'react';
import {
  closestCenter,
  DndContext,
  DragCancelEvent,
  DragEndEvent,
  DragStartEvent,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { faPlus } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { default as DynamicListItemComponent } from './DynamicListItem';
import type { DynamicListEditorProps, DynamicListItem } from './types';

export default function DynamicListEditor<T extends DynamicListItem>({
  items,
  onAdd,
  onRemove,
  onUpdate,
  onReorder,
  getTitle,
  renderFields,
  addButtonText = 'Add Another Item',
  disableRemove,
}: DynamicListEditorProps<T>) {
  const [activeId, setActiveId] = useState<string | null>(null);

  // Using multiple sensors for better cross-device compatibility
  const sensors = useSensors(
    // MouseSensor with minimal activation distance
    useSensor(MouseSensor, {
      // Lower value helps to distinguish clicks from drags
      activationConstraint: {
        distance: 10, // 10px
      },
    }),
    // TouchSensor with delay to prevent accidental activations on scroll
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    }),
    // Keyboard sensor for accessibility
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (over && active.id !== over.id && onReorder) {
      const oldIndex = items.findIndex(item => item.id === active.id);
      const newIndex = items.findIndex(item => item.id === over.id);

      onReorder(arrayMove(items, oldIndex, newIndex));
    }
  };

  const handleDragCancel = (event: DragCancelEvent) => {
    setActiveId(null);
  };

  return (
    <div className="space-y-4">
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        <SortableContext items={items.map(item => item.id)} strategy={verticalListSortingStrategy}>
          <div className="space-y-4">
            {items.map((item, index) => (
              <DynamicListItemComponent
                key={item.id}
                item={item}
                index={index}
                onRemove={() => onRemove(item.id)}
                onUpdate={updates => onUpdate(item.id, updates)}
                getTitle={getTitle}
                renderFields={renderFields}
                disableRemove={disableRemove?.(item)}
                isActive={activeId === item.id}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>

      <button
        type="button"
        onClick={onAdd}
        className="flex items-center gap-2 text-brand-red hover:text-[#B31229] transition-colors"
      >
        <FontAwesomeIcon icon={faPlus} className="text-sm" />
        <span className="text-sm">{addButtonText}</span>
      </button>
    </div>
  );
}
