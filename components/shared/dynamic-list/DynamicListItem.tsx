'use client';

import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { faCircleMinus, faGrip } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import type { DynamicListItem, DynamicListItemProps } from './types';

export default function DynamicListItem<T extends DynamicListItem>({
  item,
  index,
  onRemove,
  onUpdate,
  getTitle,
  renderFields,
  disableRemove,
  isActive,
}: DynamicListItemProps<T>) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging, isSorting } =
    useSortable({
      id: item.id,
      transition: {
        duration: 150, // milliseconds
        easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
      },
    });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : undefined,
    zIndex: isDragging ? 10 : undefined,
    position: isDragging ? ('relative' as const) : undefined,
    cursor: isDragging ? 'grabbing' : undefined,
  };

  // Combined state that considers both the local isDragging and the parent-provided isActive
  const isBeingDragged = isDragging || isActive;

  return (
    <div ref={setNodeRef} style={style} className="pb-4">
      <div className="flex gap-4">
        {/* Left Column */}
        <div className="flex flex-col justify-start items-center gap-4 pt-1">
          {/* Remove Circle */}
          <button
            type="button"
            disabled={disableRemove}
            onClick={disableRemove ? undefined : onRemove}
            className="flex items-center justify-center focus:outline-none"
            aria-label="Remove item"
          >
            <FontAwesomeIcon
              icon={faCircleMinus}
              className={`size-6 ${disableRemove ? 'text-gray-300 cursor-not-allowed' : 'text-red-500 hover:text-red-600'}`}
            />
          </button>

          {/* Drag Handle */}
          <button
            type="button"
            {...attributes}
            {...listeners}
            className="flex items-center justify-center cursor-grab rotate-90 active:cursor-grabbing text-gray-400 hover:text-gray-600 focus:outline-none"
            aria-label="Drag to reorder"
            style={{ touchAction: 'none' }}
          >
            <span className="sr-only">Drag to reorder</span>
            <FontAwesomeIcon icon={faGrip} aria-hidden="true" />
          </button>
        </div>

        {/* Right Column */}
        <div className="grow">
          {/* Title Row */}
          <div className="mb-2">
            <span className="pa-eyebrow text-text-primary">Item {index + 1}</span>
          </div>

          {/* Form Fields */}
          <div className="space-y-4">{renderFields(item)}</div>
        </div>
      </div>
    </div>
  );
}
