import { ReactNode } from 'react';

export interface DynamicListItem {
  id: string;
}

export interface DynamicListEditorProps<T extends DynamicListItem> {
  items: T[];
  onAdd: () => void;
  onRemove: (id: string) => void;
  onUpdate: (id: string, updates: Partial<Omit<T, 'id'>>) => void;
  onReorder?: (items: T[]) => void;
  getTitle: (item: T) => string;
  renderFields: (item: T) => ReactNode;
  addButtonText?: string;
  disableRemove?: (item: T) => boolean;
}

export interface DynamicListItemProps<T extends DynamicListItem> {
  item: T;
  index: number;
  onRemove: () => void;
  onUpdate: (updates: Partial<Omit<T, 'id'>>) => void;
  getTitle: (item: T) => string;
  renderFields: (item: T) => ReactNode;
  disableRemove?: boolean;
  isActive?: boolean;
}
