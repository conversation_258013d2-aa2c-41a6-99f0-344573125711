'use client';

import { AnchorHTMLAttributes, ButtonHTMLAttributes, ReactNode } from 'react';
import Link, { LinkProps } from 'next/link';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';

type ButtonBaseProps = {
  children?: ReactNode;
  color?: 'blue' | 'red' | 'white';
  size?: 'small' | 'large';
  variant?: 'filled' | 'text';
  icon?: IconDefinition;
  iconPosition?: 'left' | 'right';
  className?: string;
  disabled?: boolean;
};

type ButtonAsButtonProps = ButtonBaseProps &
  ButtonHTMLAttributes<HTMLButtonElement> & {
    href?: undefined;
  };

type ButtonAsLinkProps = ButtonBaseProps &
  Omit<LinkProps, 'href'> & {
    href: string;
  } & Omit<AnchorHTMLAttributes<HTMLAnchorElement>, 'href' | keyof LinkProps>;

type ButtonAsExternalLinkProps = ButtonBaseProps &
  AnchorHTMLAttributes<HTMLAnchorElement> & {
    href: string;
  };

type ButtonProps = ButtonAsButtonProps | ButtonAsLinkProps | ButtonAsExternalLinkProps;

export default function Button({
  children,
  color = 'blue',
  size = 'large',
  variant = 'filled',
  icon,
  iconPosition = 'right',
  className,
  disabled,
  href,
  ...props
}: ButtonProps) {
  const iconClasses = 'text-[14px] leading-[1] font-normal';

  const buttonStyles = clsx(
    'relative inline-flex items-center justify-center transition-colors text-[14px] leading-[1.2]',
    // Base styles for filled buttons
    variant === 'filled' && [
      'rounded-[8px] font-semibold',
      // Size variants
      size === 'large' && 'px-[16px] py-[16px]',
      size === 'small' && 'px-[12px] py-[12px]',
      // Colors for filled buttons
      color === 'blue' && [
        'bg-brand-blue text-white disabled:bg-grey-3 disabled:text-grey-5',
        'before:absolute before:inset-0 before:rounded-[8px] before:transition-colors before:bg-black/0 hover:before:bg-black/20',
      ],
      color === 'red' && [
        'bg-brand-red text-white disabled:bg-grey-3 disabled:text-grey-5',
        'before:absolute before:inset-0 before:rounded-[8px] before:transition-colors before:bg-black/0 hover:before:bg-black/20',
      ],
      color === 'white' && [
        'bg-white text-text-primary disabled:bg-grey-3 disabled:text-grey-5',
        'before:absolute before:inset-0 before:rounded-[8px] before:transition-colors before:bg-black/0 hover:before:bg-black/20',
      ],
    ],
    // Text-only styles
    variant === 'text' && [
      'font-normal',
      // Colors for text buttons
      color === 'blue' && 'text-brand-blue hover:text-[#002044] disabled:text-grey-5',
      color === 'red' && 'text-brand-red hover:text-[#aa0028] disabled:text-grey-5',
      color === 'white' && 'text-text-primary hover:text-[#a5abad] disabled:text-grey-5',
    ],
    disabled && href && 'opacity-50 pointer-events-none',
    className
  );

  const contentMarkup = (
    <span className={clsx('relative inline-flex items-center', children && icon && 'gap-[8px]')}>
      {icon && iconPosition === 'left' && <FontAwesomeIcon icon={icon} className={iconClasses} />}
      {children}
      {icon && iconPosition === 'right' && <FontAwesomeIcon icon={icon} className={iconClasses} />}
    </span>
  );

  // Check if href contains http:// or https:// to determine if external link
  const isExternalLink = href && (href.startsWith('http://') || href.startsWith('https://'));

  // If href is provided, render as Link
  if (href) {
    if (isExternalLink) {
      return (
        <a
          href={href}
          className={buttonStyles}
          target="_blank"
          rel="noopener noreferrer"
          {...(props as AnchorHTMLAttributes<HTMLAnchorElement>)}
        >
          {contentMarkup}
        </a>
      );
    }

    return (
      <Link
        href={href}
        className={buttonStyles}
        {...(props as Omit<LinkProps, 'href'> &
          Omit<AnchorHTMLAttributes<HTMLAnchorElement>, 'href' | keyof LinkProps>)}
      >
        {contentMarkup}
      </Link>
    );
  }

  // Otherwise render as button
  return (
    <button
      disabled={disabled}
      className={buttonStyles}
      {...(props as ButtonHTMLAttributes<HTMLButtonElement>)}
    >
      {contentMarkup}
    </button>
  );
}
