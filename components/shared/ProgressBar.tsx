interface ProgressBarProps {
  progress: number;
  primaryColor?: string;
  secondaryColor?: string;
  className?: string;
}

export function ProgressBar({
  progress,
  primaryColor = '#D50032',
  secondaryColor = '#FFFFFF',
  className = '',
}: ProgressBarProps) {
  // Ensure progress is between 0 and 100
  const normalizedProgress = Math.min(100, Math.max(0, progress));

  return (
    <div
      className={`relative h-4 w-full rounded-full ${className}`}
      style={{ backgroundColor: secondaryColor, padding: '2px' }}
      role="progressbar"
      aria-valuemin={0}
      aria-valuemax={100}
      aria-valuenow={normalizedProgress}
    >
      <div
        className="h-full rounded-full transition-all duration-300 ease-in-out"
        style={{
          width: `${normalizedProgress}%`,
          backgroundColor: primaryColor,
        }}
      />
    </div>
  );
}
