import React, { ChangeEvent, Dispatch, FormEvent, SetStateAction } from 'react';
import { faEnvelope, faLock, faMobile, faRotateRight } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import SelectInput from '@/components/shared/form/SelectInput';
import SiteInput from '@/components/shared/form/SiteInput';
import { ProfileData, UpdateProfileRequest } from '@/services/account.service';
import { ProfileType, ProfileTypes } from '@/stores/auth.store';

interface ParentProfileFormProps {
  profileData: UpdateProfileRequest;
  profileErrors: Record<string, string>;
  handleProfileChange: (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  setProfileData: Dispatch<SetStateAction<UpdateProfileRequest & ProfileData>>;
  openResetPasswordModal: () => void;
  profileEmail?: string;
  profileType?: ProfileType;
}

export default function ParentProfileForm({
  profileData,
  profileErrors,
  handleProfileChange,
  setProfileData,
  openResetPasswordModal,
  profileEmail,
}: ParentProfileFormProps) {
  return (
    <Card elevation="sm">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <SiteInput
            label="FIRST NAME"
            name="first_name"
            value={profileData.first_name}
            onChange={handleProfileChange}
          />
          {profileErrors.first_name && (
            <p className="text-red-500 text-sm">{profileErrors.first_name}</p>
          )}
        </div>
        <div>
          <SiteInput
            label="LAST NAME"
            name="last_name"
            value={profileData.last_name}
            onChange={handleProfileChange}
          />
          {profileErrors.last_name && (
            <p className="text-red-500 text-sm">{profileErrors.last_name}</p>
          )}
        </div>
        <div>
          <SiteInput
            label="EMAIL"
            type="email"
            name="email"
            value={profileData.email || profileEmail || ''}
            onChange={handleProfileChange}
            icon={faEnvelope}
          />
          {profileErrors.email && <p className="text-red-500 text-sm">{profileErrors.email}</p>}
        </div>
        <div>
          <SiteInput
            label="PHONE"
            type="tel"
            name="phone"
            value={profileData.phone || ''}
            onChange={handleProfileChange}
            icon={faMobile}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {/* Commenting out for Parent Profile for now */}
        {/* <SelectInput
          label="LIFE STAGE"
          value={profileData.life_stage || ''}
          onChange={value => setProfileData(prev => ({ ...prev, life_stage: value }))}
          options={[
            { value: 'high_school_student', label: "I'm still in high school" },
            { value: 'high_school_graduate', label: "I'm a high school graduate" },
            { value: 'college_student', label: "I'm in college now" },
            { value: 'college_graduate', label: "I'm a college graduate" },
            { value: 'gap_year', label: "I'm not sure yet, or I'm taking a gap year" },
            { value: 'professional', label: "I'm working now" },
          ]}
        /> */}

        <div className="space-y-2">
          <label className="pa-eyebrow text-text-primary">PASSWORD</label>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FontAwesomeIcon icon={faLock} className="h-4 w-4 text-gray-500 mr-2" />
              <span className="text-lg tracking-widest">••••••</span>
            </div>
            <Button
              color="blue"
              size="small"
              icon={faRotateRight}
              iconPosition="right"
              onClick={openResetPasswordModal}
            >
              Reset Password
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
}
