import React, { useEffect, useState } from 'react';
import {
  faBuilding,
  faEnvelope,
  faLock,
  faMobile,
  faRotateRight,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { UploadImageDropzone } from '@/components/profile/shared/UploadImageDropzone';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import SiteInput from '@/components/shared/form/SiteInput';
import { useOrganization } from '@/hooks/useOrganization';
import { useToastNotify } from '@/stores/toastNotify.store';

interface SponsorProfileFormProps {
  profileData: {
    first_name?: string;
    last_name?: string;
    email?: string;
    phone?: string;
  };
  profileErrors: Record<string, string>;
  profileEmail?: string;
  handleProfileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  openResetPasswordModal: () => void;
  handleWysiwygChange: (name: string, value: string) => void;
  onDeleteLogo?: () => void;
  logoFile?: File | null;
  setLogoFile?: (file: File | null) => void;
  logoPreviewUrl?: string | null;
  setLogoPreviewUrl?: (url: string | null) => void;
  clearError?: (field: string) => void;
  fieldErrors?: Record<string, string>;
}

export default function SponsorProfileForm({
  profileData,
  profileErrors,
  profileEmail,
  handleProfileChange,
  openResetPasswordModal,
  handleWysiwygChange,
  clearError,
  fieldErrors = {},
}: SponsorProfileFormProps) {
  const { toastNotify } = useToastNotify();

  // Get organization data using the useOrganization hook
  const { organization, isLoadingOrganization, updateOrganization, isUpdatingOrganization } =
    useOrganization({
      onSuccess: data => {
        console.log('Organization updated successfully', data);
        toastNotify('Organization information updated successfully', 'success');
        // Any additional success handling
      },
      onError: error => {
        console.error('Error with organization data', error);
        toastNotify('Failed to update organization information', 'error');
        // Any additional error handling
      },
    });

  // Local state for form data
  const [organizationName, setOrganizationName] = useState('');
  const [organizationWebsite, setOrganizationWebsite] = useState('');
  const [organizationAbout, setOrganizationAbout] = useState('');
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreviewUrl, setLogoPreviewUrl] = useState<string | null>(null);

  // Update local state when organization data is loaded
  useEffect(() => {
    if (organization) {
      setOrganizationName(organization.name || '');
      setOrganizationWebsite(organization.website || '');
      setOrganizationAbout(organization.about || '');
      setLogoPreviewUrl(organization.logo_url);
    }
  }, [organization]);

  // Handle logo file selection
  const handleLogoSelect = (file: File | null) => {
    setLogoFile(file);

    if (file) {
      const url = URL.createObjectURL(file);
      setLogoPreviewUrl(url);
    } else {
      setLogoPreviewUrl(null);
    }

    if (clearError) clearError('organization_logo');
  };

  // Handle organization form submission
  const handleSaveOrganizationChanges = () => {
    const organizationData: any = {};

    if (organizationName) organizationData.organization_name = organizationName;
    if (organizationWebsite !== undefined)
      organizationData.organization_website = organizationWebsite;
    if (organizationAbout !== undefined) organizationData.organization_about = organizationAbout;

    // Always include logo state in the submission
    if (logoFile) {
      organizationData.organization_logo = logoFile;
    } else if (logoPreviewUrl === null) {
      // If logoPreviewUrl is explicitly null (user removed it), set logo to null
      organizationData.organization_logo = null;
    }

    updateOrganization(organizationData);
  };

  return (
    <>
      {/* Personal Information Card */}
      <Card elevation="sm">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <SiteInput
              label="FIRST NAME"
              name="first_name"
              value={profileData.first_name || ''}
              onChange={handleProfileChange}
            />
            {profileErrors.first_name && (
              <p className="text-red-500 text-sm">{profileErrors.first_name}</p>
            )}
          </div>
          <div>
            <SiteInput
              label="LAST NAME"
              name="last_name"
              value={profileData.last_name || ''}
              onChange={handleProfileChange}
            />
            {profileErrors.last_name && (
              <p className="text-red-500 text-sm">{profileErrors.last_name}</p>
            )}
          </div>
          <div>
            <SiteInput
              label="EMAIL"
              type="email"
              name="email"
              value={profileData.email || profileEmail || ''}
              onChange={handleProfileChange}
              icon={faEnvelope}
            />
            {profileErrors.email && <p className="text-red-500 text-sm">{profileErrors.email}</p>}
          </div>
          <div>
            <SiteInput
              label="PHONE"
              type="tel"
              name="phone"
              value={profileData.phone || ''}
              onChange={handleProfileChange}
              icon={faMobile}
            />
          </div>
        </div>

        <div className="space-y-2 mt-6">
          <label className="pa-eyebrow text-text-primary">PASSWORD</label>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FontAwesomeIcon icon={faLock} className="h-4 w-4 text-gray-500 mr-2" />
              <span className="text-lg tracking-widest">••••••</span>
            </div>
            <Button
              color="blue"
              size="small"
              icon={faRotateRight}
              iconPosition="right"
              onClick={openResetPasswordModal}
            >
              Reset Password
            </Button>
          </div>
        </div>
      </Card>

      {/* Organization Information Card */}
      <Card elevation="sm" className="mt-10">
        <CardHeader title="YOUR ORGANIZATION" titleIcon={faBuilding} className="mb-8" />

        <p className="text-sm text-text-secondary mb-8">Update your organization&#39;s logo.</p>

        {isLoadingOrganization ? (
          <div className="flex justify-center py-4">
            <p className="text-sm text-gray-500">Loading organization information...</p>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="w-full">
              <div className="w-full max-w-full">
                <label className="pa-eyebrow text-text-primary mb-2">Organization Logo</label>
                <UploadImageDropzone
                  onFileSelect={handleLogoSelect}
                  currentImageUrl={logoPreviewUrl}
                  dropzoneText={{
                    default: 'Drag & drop your logo here',
                    active: 'Drop your logo here',
                    subText: 'or click to browse',
                  }}
                />
              </div>

              {fieldErrors.organization_logo && (
                <p className="text-sm text-red-500 mt-1" id="logo-error">
                  {fieldErrors.organization_logo}
                </p>
              )}
            </div>

            <div className="flex justify-end pt-4">
              <Button
                color="blue"
                size="small"
                onClick={handleSaveOrganizationChanges}
                disabled={isUpdatingOrganization}
                className="ml-auto"
              >
                {isUpdatingOrganization ? 'Saving...' : 'Save Organization'}
              </Button>
            </div>
          </div>
        )}
      </Card>
    </>
  );
}
