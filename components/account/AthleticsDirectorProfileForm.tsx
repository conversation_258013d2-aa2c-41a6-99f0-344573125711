import React, { ChangeEvent, Dispatch, SetStateAction } from 'react';
import { faEnvelope, faLock, faMobile, faRotateRight } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import SiteInput from '@/components/shared/form/SiteInput';
import { ProfileData, UpdateProfileRequest } from '@/services/account.service';

interface AthleticsDirectorProfileFormProps {
  profileData: UpdateProfileRequest & ProfileData;
  profileErrors: Record<string, string>;
  handleProfileChange: (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  setProfileData: Dispatch<SetStateAction<UpdateProfileRequest & ProfileData>>;
  openResetPasswordModal: () => void;
  profileEmail?: string;
}

export default function AthleticsDirectorProfileForm({
  profileData,
  profileErrors,
  handleProfileChange,
  setProfileData,
  openResetPasswordModal,
  profileEmail,
}: AthleticsDirectorProfileFormProps) {
  return (
    <Card elevation="sm">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <SiteInput
            label="FIRST NAME"
            name="first_name"
            value={profileData.first_name}
            onChange={handleProfileChange}
          />
          {profileErrors.first_name && (
            <p className="text-red-500 text-sm">{profileErrors.first_name}</p>
          )}
        </div>
        <div>
          <SiteInput
            label="LAST NAME"
            name="last_name"
            value={profileData.last_name}
            onChange={handleProfileChange}
          />
          {profileErrors.last_name && (
            <p className="text-red-500 text-sm">{profileErrors.last_name}</p>
          )}
        </div>
        <div className="col-span-1 md:col-span-2">
          <SiteInput
            label="ACCOUNT EMAIL"
            type="email"
            name="email"
            value={profileData.email || profileEmail || ''}
            onChange={handleProfileChange}
            icon={faEnvelope}
          />
          {profileErrors.email && <p className="text-red-500 text-sm">{profileErrors.email}</p>}
          <p className="text-gray-600 text-xs mt-1">Should be a personal email address</p>
        </div>
        {/* <div>
          <SiteInput
            label="NOTIFICATION EMAIL"
            type="email"
            name="notification_email"
            value={profileData.notification_email || ''}
            onChange={handleProfileChange}
            icon={faEnvelope}
          />
          {profileErrors.notification_email && (
            <p className="text-red-500 text-sm">{profileErrors.notification_email}</p>
          )}
          <p className="text-gray-600 text-xs mt-1">
            System notifications will send to this email. Can be a school email address.
          </p>
        </div> */}
        <div>
          <SiteInput
            label="PHONE"
            type="tel"
            name="phone"
            value={profileData.phone || ''}
            onChange={handleProfileChange}
            icon={faMobile}
          />
        </div>
        <div>
          <div className="space-y-2">
            <label className="pa-eyebrow text-text-primary">PASSWORD</label>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FontAwesomeIcon icon={faLock} className="h-4 w-4 text-gray-500 mr-2" />
                <span className="text-lg tracking-widest">••••••</span>
              </div>
              <Button
                color="blue"
                size="small"
                icon={faRotateRight}
                iconPosition="right"
                onClick={openResetPasswordModal}
              >
                Reset Password
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
