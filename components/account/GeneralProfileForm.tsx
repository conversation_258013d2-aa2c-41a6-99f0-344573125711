import React, { ChangeEvent, Dispatch, FormEvent, SetStateAction } from 'react';
import { faEnvelope, faLock, faMobile, faRotateRight } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import SelectInput from '@/components/shared/form/SelectInput';
import SiteInput from '@/components/shared/form/SiteInput';
import {
  ProfileData,
  UpdateAddressRequest,
  UpdateProfileRequest,
} from '@/services/account.service';
import { ProfileType, ProfileTypes } from '@/stores/auth.store';
import { STATE_OPTIONS } from '@/utils/constants/states';

interface GeneralProfileFormProps {
  profileData: UpdateProfileRequest;
  profileErrors: Record<string, string>;
  addressData: UpdateAddressRequest;
  addressErrors: Record<string, string>;
  handleProfileChange: (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  handleAddressChange: (e: ChangeEvent<HTMLInputElement>) => void;
  setProfileData: Dispatch<SetStateAction<UpdateProfileRequest & ProfileData>>;
  setAddressData: Dispatch<SetStateAction<UpdateAddressRequest>>;
  openResetPasswordModal: () => void;
  profileEmail?: string;
  profileType?: ProfileType;
}

export default function GeneralProfileForm({
  profileData,
  profileErrors,
  addressData,
  addressErrors,
  handleProfileChange,
  handleAddressChange,
  setProfileData,
  setAddressData,
  openResetPasswordModal,
  profileEmail,
  profileType,
}: GeneralProfileFormProps) {
  // Show life stage only for positive athletes, college athletes, or professionals
  const shouldShowLifeStage = [
    ProfileTypes.POSITIVE_ATHLETE,
    ProfileTypes.COLLEGE_ATHLETE,
    ProfileTypes.PROFESSIONAL,
  ].includes(profileType as any);

  const isAthleticsDirector = profileType === ProfileTypes.ATHLETICS_DIRECTOR;
  const isPositiveCoach = profileType === ProfileTypes.POSITIVE_COACH;

  return (
    <Card elevation="sm">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <SiteInput
            label="FIRST NAME"
            name="first_name"
            value={profileData.first_name}
            onChange={handleProfileChange}
          />
          {profileErrors.first_name && (
            <p className="text-red-500 text-sm">{profileErrors.first_name}</p>
          )}
        </div>
        <div>
          <SiteInput
            label="LAST NAME"
            name="last_name"
            value={profileData.last_name}
            onChange={handleProfileChange}
          />
          {profileErrors.last_name && (
            <p className="text-red-500 text-sm">{profileErrors.last_name}</p>
          )}
        </div>
        <div>
          <SiteInput
            label={isAthleticsDirector ? 'Account Email' : 'Email'}
            type="email"
            name="email"
            value={profileData.email || profileEmail || ''}
            onChange={handleProfileChange}
            icon={faEnvelope}
          />
          {profileErrors.email && <p className="text-red-500 text-sm">{profileErrors.email}</p>}
        </div>

        {/* {isAthleticsDirector && (
          <div>
            <SiteInput
              label="Notification Email"
              type="email"
              name="notification_email"
              value={profileData.notification_email || ''}
              onChange={handleProfileChange}
              icon={faEnvelope}
            />
            {profileErrors.notification_email && (
              <p className="text-red-500 text-sm">{profileErrors.notification_email}</p>
            )}
          </div>
        )} */}
        <div>
          <SiteInput
            label="PHONE"
            type="tel"
            name="phone"
            value={profileData.phone || ''}
            onChange={handleProfileChange}
            icon={faMobile}
          />
        </div>
        {shouldShowLifeStage && (
          <div>
            <SelectInput
              label="LIFE STAGE"
              value={profileData.life_stage || ''}
              onChange={value => setProfileData(prev => ({ ...prev, life_stage: value }))}
              options={[
                { value: 'high_school_student', label: "I'm still in high school" },
                { value: 'high_school_graduate', label: "I'm a high school graduate" },
                { value: 'college_student', label: "I'm in college now" },
                { value: 'college_graduate', label: "I'm a college graduate" },
                { value: 'gap_year', label: "I'm not sure yet, or I'm taking a gap year" },
                { value: 'professional', label: "I'm working now" },
              ]}
            />
          </div>
        )}
        <div className={`${isPositiveCoach ? 'col-span-1 md:col-span-2' : ''}`}>
          <div className="space-y-2">
            <label className="pa-eyebrow text-text-primary">PASSWORD</label>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FontAwesomeIcon icon={faLock} className="h-4 w-4 text-gray-500 mr-2" />
                <span className="text-lg tracking-widest">••••••</span>
              </div>
              <Button
                color="blue"
                size="small"
                icon={faRotateRight}
                iconPosition="right"
                onClick={openResetPasswordModal}
              >
                Reset Password
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 border-t border-gray-200 pt-8">
        <h2 className="text-sm font-bold text-gray-800 uppercase mb-6">ADDRESS</h2>
        <div className="grid grid-cols-1 gap-6">
          <SiteInput
            label="STREET ADDRESS"
            name="street_address_1"
            value={addressData.street_address_1 || ''}
            onChange={handleAddressChange}
          />
          <SiteInput
            label="UNIT OR APARTMENT"
            name="street_address_2"
            value={addressData.street_address_2 || ''}
            onChange={handleAddressChange}
          />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <SiteInput
                label="CITY"
                name="city"
                value={addressData.city || ''}
                onChange={handleAddressChange}
              />
            </div>
            <div>
              <SelectInput
                label="STATE"
                value={addressData.state_code || ''}
                onChange={value => setAddressData(prev => ({ ...prev, state_code: value }))}
                options={[...STATE_OPTIONS]}
              />
            </div>
            <div>
              <SiteInput
                label="ZIP CODE"
                name="zip"
                value={addressData.zip || ''}
                onChange={handleAddressChange}
              />
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
