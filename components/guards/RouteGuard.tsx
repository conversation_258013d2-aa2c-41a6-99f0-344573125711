'use client';

import { useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import LoadingScreen from '@/components/loading/LoadingScreen';
import { useAuth } from '@/hooks/useAuth';
import {
  getDefaultRoute,
  hasRoutePermission,
  matchesRoute,
  onboardingRoutes,
  protectedRoutes,
  publicOnlyRoutes,
} from '@/utils/route-rules';

export default function RouteGuard({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, isLoggedIn, user, profileType } = useAuth();
  const [isRouteReady, setIsRouteReady] = useState(false);

  useEffect(() => {
    if (!pathname) return;

    // Handle routing using an immediately executed async function
    // to ensure redirections happen as quickly as possible
    (async () => {
      try {
        // Allow login page for non-authenticated users
        if (!isLoggedIn && pathname === '/login') {
          setIsRouteReady(true);
          return;
        }

        // Root route handling
        if (pathname === '/') {
          if (isLoggedIn && user && profileType) {
            await router.replace(getDefaultRoute(profileType));
          } else {
            await router.replace('/login');
          }
          return; // Exit early without rendering content
        }

        // For authenticated users
        if (isLoggedIn && user && profileType) {
          // If trying to access public-only routes, redirect to default
          if (
            matchesRoute(pathname, publicOnlyRoutes) ||
            matchesRoute(pathname, onboardingRoutes)
          ) {
            await router.replace(getDefaultRoute(profileType));
            return; // Exit early without rendering content
          }

          // For protected routes, check permissions
          if (matchesRoute(pathname, protectedRoutes)) {
            // Immediately check permissions - no content shown before redirect
            if (!hasRoutePermission(profileType, pathname)) {
              const fallbackRoute = getDefaultRoute(profileType);

              await router.replace(fallbackRoute);
              return; // Exit early without rendering content
            }
          }
        } else {
          // For unauthenticated users
          if (matchesRoute(pathname, protectedRoutes)) {
            const callbackUrl = encodeURIComponent(pathname);
            await router.replace(`/login?callback=${callbackUrl}`);
            return; // Exit early without rendering content
          }

          // Allow access to public routes for unauthenticated users
          if (matchesRoute(pathname, publicOnlyRoutes)) {
            setIsRouteReady(true);
            return;
          }
        }

        // If we get here, user can access this route
        setIsRouteReady(true);
      } catch (error) {
        console.error('Error in route guard:', error);
        setIsRouteReady(true); // Set to true to avoid hanging
      }
    })();
  }, [pathname, isLoggedIn, user, profileType, router]);

  // Only render children if route is ready and no redirects are pending
  if (!isRouteReady) {
    return <LoadingScreen />;
  }

  return <>{children}</>;
}
