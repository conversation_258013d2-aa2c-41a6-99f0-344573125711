import React, { SVGProps } from 'react';

export function HudlIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M11.7965 23.0351C9.96778 23.0351 8.38525 21.7925 7.95152 20.0107C7.95152 19.9872 7.92807 19.9638 7.90463 19.9638C7.38884 19.7528 6.88477 19.5066 6.40415 19.2136C6.39243 19.2018 6.38071 19.1901 6.36899 19.1784C5.66564 17.3614 5.4898 15.392 5.8532 13.4813C5.8532 13.4578 5.87664 13.4344 5.90009 13.4227H5.91181C5.93525 13.4227 5.9587 13.4227 5.97042 13.4461C6.93166 14.4894 8.10391 15.3217 9.40511 15.8961C9.41683 15.8961 9.42855 15.9078 9.44027 15.8961C9.452 15.8961 9.47544 15.8961 9.48716 15.8844C11.1986 14.6535 13.59 14.9935 14.8912 16.6581C14.9029 16.6815 14.9264 16.6815 14.9498 16.6815H14.9616C15.325 16.6346 15.6884 16.5643 16.0517 16.4822C18.197 15.9547 20.1312 14.7707 21.5613 13.0944C21.573 13.0827 21.5965 13.071 21.6199 13.071C21.6316 13.071 21.6434 13.071 21.6551 13.0827C21.6903 13.0944 21.702 13.1296 21.6903 13.1648C20.8228 16.2243 18.6072 18.7212 15.6649 19.9404C15.6415 19.9521 15.6297 19.9638 15.618 19.9872C15.2077 21.7808 13.6252 23.0351 11.7965 23.0351ZM4.42306 17.631C4.39961 17.631 4.38789 17.6193 4.37617 17.6076C2.1489 15.3451 1.09387 12.1684 1.5276 9.01501C1.5276 8.99157 1.5276 8.96812 1.50416 8.9564C0.167798 7.7021 -0.125264 5.70928 0.777366 4.12674C1.68 2.54421 3.55559 1.78225 5.31396 2.29804C5.33741 2.30976 5.36085 2.29804 5.3843 2.28631C5.82975 1.93464 6.29865 1.61813 6.791 1.34852C6.80272 1.33679 6.81444 1.33679 6.82616 1.33679C8.76037 1.62985 10.5656 2.47387 12.0427 3.7399C12.0661 3.76334 12.0778 3.78679 12.0661 3.81024C12.0544 3.83368 12.0427 3.85712 12.0075 3.86885C10.6125 4.18535 9.31133 4.7832 8.16252 5.61549C8.13908 5.62722 8.12736 5.65066 8.12736 5.68583C8.13908 5.81478 8.1508 5.95545 8.1508 6.08439C8.13908 8.04205 6.69722 9.69492 4.75128 9.97626C4.72784 9.97626 4.70439 9.9997 4.69267 10.0231C4.552 10.3631 4.43478 10.7148 4.32928 11.0664C3.70798 13.1882 3.7666 15.4506 4.50511 17.5255C4.51684 17.549 4.50511 17.5724 4.49339 17.5959C4.46995 17.6193 4.4465 17.631 4.42306 17.631ZM17.3529 13.9502C17.3295 13.9502 17.3061 13.9384 17.2943 13.9267C17.2826 13.9033 17.2709 13.8798 17.2826 13.8564C17.6929 12.52 17.8336 11.1016 17.6929 9.70664C17.6929 9.68319 17.6695 9.65975 17.646 9.64803C16.2628 9.00329 15.3718 7.62004 15.3601 6.08439C15.3601 5.5686 15.4539 5.05282 15.6532 4.58392C15.6649 4.56047 15.6649 4.53703 15.6415 4.51358C15.407 4.2088 15.1491 3.90401 14.8912 3.62267C13.3673 2.0167 11.3745 0.93823 9.20582 0.539666C9.17066 0.527943 9.14721 0.504498 9.14721 0.46933C9.14721 0.434163 9.17066 0.398995 9.20582 0.398995C12.3123 -0.398133 15.618 0.293493 18.1383 2.28631C18.1501 2.29804 18.1618 2.29804 18.1852 2.29804C18.197 2.29804 18.197 2.29804 18.2087 2.29804C18.5604 2.19253 18.9355 2.13392 19.3106 2.13392C20.9283 2.13392 22.3819 3.11861 22.9797 4.61908C23.5776 6.11956 23.1907 7.84277 22.0185 8.94468C22.0068 8.9564 21.995 8.97985 21.995 9.00329C22.0537 9.46047 22.0888 9.91764 22.0888 10.3865C22.0888 10.4334 22.0888 10.4686 22.0888 10.5155V10.5975C22.0888 10.6093 22.0771 10.6327 22.0771 10.6444C20.858 12.1566 19.2403 13.3054 17.3998 13.9502H17.3529Z" />
    </svg>
  );
}
