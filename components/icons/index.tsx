import React from 'react';
import {
  faGauge as faDashboard,
  faMessage as faMessages,
  faNetworkWired as faNetwork,
  faBriefcase as faOpportunities,
  faUser as faProfile,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import type { FontAwesomeIconProps } from '@fortawesome/react-fontawesome';

type IconProps = Omit<FontAwesomeIconProps, 'icon'>;

export const IconDashboard = (props: IconProps) => (
  <FontAwesomeIcon icon={faDashboard} {...props} />
);

export const IconProfile = (props: IconProps) => <FontAwesomeIcon icon={faProfile} {...props} />;

export const IconNetwork = (props: IconProps) => <FontAwesomeIcon icon={faNetwork} {...props} />;

export const IconOpportunities = (props: IconProps) => (
  <FontAwesomeIcon icon={faOpportunities} {...props} />
);

export const IconMessages = (props: IconProps) => <FontAwesomeIcon icon={faMessages} {...props} />;
