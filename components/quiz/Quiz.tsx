'use client';

import { useEffect } from 'react';
import {
  faArrowLeft,
  faArrowRight,
  faCheck,
  faCircleCheck,
  faLock,
  faRotateRight,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import Radio from '@/components/shared/form/Radio';
import { useXFactorQuiz } from '@/hooks/x-factor/useXFactorQuiz';
import { useQuizStore } from '@/stores/quizStore';
import type { TestAttempt } from '@/types/quiz';

interface QuizSubmissionResponse {
  score: number;
  status: string;
}

interface QuizProps {
  moduleId: number;
  isUnlocked: boolean;
  hasVideo: boolean;
  onComplete?: (score: number) => void;
}

export const Quiz = ({ moduleId, isUnlocked, hasVideo, onComplete }: QuizProps) => {
  const {
    quiz,
    isLoading,
    error,
    startAttempt,
    isStartingAttempt,
    submitResponses,
    isSubmittingResponse,
    currentAttempt,
  } = useXFactorQuiz(moduleId);

  const {
    test,
    testAttempt,
    currentQuestionIndex,
    responses,
    setTest,
    selectAnswer,
    nextQuestion,
    previousQuestion,
    completeQuiz,
  } = useQuizStore();

  // Handle quiz completion state
  useEffect(() => {
    if (quiz?.lastCompletedAttempt && onComplete && quiz.lastCompletedAttempt.score === 100) {
      onComplete(quiz.lastCompletedAttempt.score);
    }
  }, [quiz?.lastCompletedAttempt, onComplete]);

  useEffect(() => {
    if (quiz) {
      setTest({
        id: quiz.id,
        moduleId: quiz.moduleId,
        type: 'quiz',
        questions: quiz.questions.map(q => ({
          id: q.id,
          testId: quiz.id,
          type: 'multiple_choice',
          question: q.question,
          answers: q.answers.map(a => ({
            id: a.id,
            questionId: q.id,
            answer: a.answer,
            isCorrect: a.isCorrect,
          })),
        })),
      });

      // Only log answers in development
      // if (process.env.NODE_ENV === 'development') {
      //   console.log(
      //     'RIGHT ANSWERS',
      //     quiz.questions.map(q => q.answers.findIndex(a => a.isCorrect) + 1)
      //   );
      // }
    }
    return () => {
      useQuizStore.getState().reset();
    };
  }, [quiz, setTest]);

  useEffect(() => {
    if (currentAttempt && !testAttempt) {
      useQuizStore.getState().startAttempt(currentAttempt);
    }
  }, [currentAttempt, testAttempt]);

  const handleStartQuiz = async () => {
    if (test) {
      await startAttempt(test.id);
    }
  };

  // Show loading state
  if (isLoading || isStartingAttempt) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    const axiosError = error as { response?: { data?: { message?: string } } };
    const errorMessage =
      axiosError.response?.data?.message || error.message || 'An error occurred. Please try again.';

    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-red-600 mb-4">Error</h2>
        <p className="text-gray-600 mb-4">{errorMessage}</p>
        <Button onClick={() => window.location.reload()} color="blue">
          Retry
        </Button>
      </div>
    );
  }

  // Show locked state if quiz is not unlocked
  if (!isUnlocked) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex flex-col items-center justify-center text-center">
          <div className="flex items-center justify-center gap-4">
            <FontAwesomeIcon icon={faLock} className="text-2xl text-primary mb-4" />
            <h2 className="text-2xl font-bold mb-4">Quiz</h2>
          </div>
          <p className="text-gray-600">
            {hasVideo
              ? 'Watch the video to unlock this quiz. This module will not be marked as complete until the quiz has been completed.'
              : 'This quiz is currently locked. Please complete the previous requirements to unlock it.'}
          </p>
        </div>
      </div>
    );
  }

  // Show completion state if we have a completed attempt and no active attempt
  if (quiz?.lastCompletedAttempt && !testAttempt && currentAttempt?.status !== 'in_progress') {
    const score = quiz.lastCompletedAttempt.score || 0;
    const totalQuestions = quiz.questions.length;
    const correctAnswers = Math.round((score / 100) * totalQuestions);
    const isHighScore = score >= 90;

    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex flex-col items-center justify-center text-center">
          <div className="flex items-center justify-center gap-4 mb-4">
            <FontAwesomeIcon icon={faCircleCheck} className="text-2xl text-primary" />
            <h2 className="text-2xl font-bold">Quiz</h2>
          </div>

          <div className="bg-gray-50 rounded-lg p-8 w-full max-w-md mb-8">
            <h3 className="text-2xl font-bold mb-6">
              {isHighScore ? 'NICE JOB!' : 'ROOM FOR IMPROVEMENT'}
            </h3>
            <div
              className={`text-[40px] font-bold border-2 rounded-2xl bg-white inline-block px-6 py-2 mb-6 ${
                isHighScore ? 'text-green-600 border-green-600' : 'text-[#B98900] border-[#B98900]'
              }`}
            >
              {score}%
            </div>
            <p className="text-gray-600">
              You correctly answered {correctAnswers}/{totalQuestions} questions!
            </p>
          </div>

          <Button
            onClick={handleStartQuiz}
            color="blue"
            icon={faRotateRight}
            disabled={isStartingAttempt}
          >
            Retake Quiz
          </Button>
        </div>
      </div>
    );
  }

  // Show start quiz UI if no attempt exists
  if (!currentAttempt && !testAttempt && test) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex flex-col items-start">
          <h2 className="text-2xl font-bold mb-4">Ready to start the quiz?</h2>
          <p className="text-gray-600 mb-6">
            This quiz contains {test.questions.length} multiple choice questions.
          </p>
          <Button onClick={handleStartQuiz} disabled={isStartingAttempt} color="blue">
            {isStartingAttempt ? 'Starting Quiz...' : 'Start Quiz'}
          </Button>
        </div>
      </div>
    );
  }

  // Ensure we have a test loaded
  if (!test || !quiz) {
    return null;
  }

  const currentQuestion = test.questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === test.questions.length - 1;
  const isFirstQuestion = currentQuestionIndex === 0;
  const selectedAnswerId = responses[currentQuestion.id];
  const hasAnsweredAllQuestions = Object.keys(responses).length === test.questions.length;

  const handleCompleteQuiz = async () => {
    const submissionData = completeQuiz();
    if (submissionData) {
      try {
        // Submit the responses using the mutation function
        submitResponses(submissionData.responses, {
          onSuccess: (result: TestAttempt) => {
            if (result.score === 100) {
              onComplete?.(result.score);
            }
          },
          onError: error => {
            console.error('Error submitting quiz:', error);
          },
        });
      } catch (error) {
        console.error('Error submitting quiz:', error);
      }
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      {/* Header */}
      <div className="flex items-center mb-6">
        <div className="flex items-center gap-2">
          <FontAwesomeIcon icon={faCircleCheck} className="text-primary" />
          <h2 className="text-xl font-bold">Quiz</h2>
          <span className="text-white bg-brand-red px-4 rounded-full font-bold">
            {currentQuestionIndex + 1}/{test.questions.length}
          </span>
        </div>
      </div>

      {/* Question */}
      <h2 className="text-xl font-bold mb-6">{currentQuestion.question}</h2>

      {/* Answer Options */}
      <div className="space-y-4 mb-6">
        <Radio
          options={currentQuestion.answers.map(answer => ({
            label: answer.answer,
            value: answer.id.toString(),
          }))}
          value={(selectedAnswerId || '').toString()}
          onChange={value => selectAnswer(currentQuestion.id, parseInt(value))}
          name={`question-${currentQuestion.id}`}
        />
      </div>

      {/* Navigation */}
      <div className="flex justify-between">
        {!isFirstQuestion && (
          <Button
            onClick={previousQuestion}
            color="blue"
            variant="filled"
            icon={faArrowLeft}
            iconPosition="left"
            disabled={isSubmittingResponse}
          >
            Previous
          </Button>
        )}

        <div className="flex gap-2 ml-auto">
          {!isLastQuestion ? (
            <Button
              onClick={nextQuestion}
              color="blue"
              icon={faArrowRight}
              disabled={isSubmittingResponse}
            >
              Next
            </Button>
          ) : hasAnsweredAllQuestions ? (
            <Button
              onClick={handleCompleteQuiz}
              color="blue"
              icon={faCheck}
              disabled={isSubmittingResponse}
            >
              {isSubmittingResponse ? 'Submitting...' : 'Finish Quiz'}
            </Button>
          ) : null}
        </div>
      </div>
    </div>
  );
};
