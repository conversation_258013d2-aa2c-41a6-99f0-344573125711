'use client';

import { faChevronDown, faPlus, faTrash } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Disclosure, Transition } from '@headlessui/react';

interface ItemEditorProps<T> {
  items: Array<T & { id: string }>;
  onAdd: () => void;
  onRemove: (id: string) => void;
  onUpdate: (id: string, updates: Partial<T>) => void;
  getTitle: (item: T) => string;
  renderFields: (item: T & { id: string }) => React.ReactNode;
  addButtonText?: string;
}

export default function ItemEditor<T>({
  items,
  onAdd,
  onRemove,
  onUpdate,
  getTitle,
  renderFields,
  addButtonText = 'Add Another Item',
}: ItemEditorProps<T>) {
  return (
    <div className="space-y-4">
      {items.map((item, index) => (
        <Disclosure key={item.id} defaultOpen>
          {({ open }) => (
            <div className="border rounded-lg">
              <Disclosure.Button className="w-full flex justify-between items-center p-4 text-left">
                <div className="flex items-center gap-2">
                  <span className="text-gray-500">ITEM {index + 1}</span>
                  <span className="font-medium">{getTitle(item)}</span>
                </div>
                <div className="flex items-center gap-4">
                  <div
                    role="button"
                    tabIndex={0}
                    onClick={e => {
                      e.preventDefault();
                      e.stopPropagation();
                      onRemove(item.id);
                    }}
                    onKeyDown={e => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        onRemove(item.id);
                      }
                    }}
                    className="text-red-600 hover:text-red-800 cursor-pointer"
                  >
                    <FontAwesomeIcon icon={faTrash} />
                  </div>
                  <FontAwesomeIcon
                    icon={faChevronDown}
                    className={`transform transition-transform ${open ? 'rotate-180' : ''}`}
                  />
                </div>
              </Disclosure.Button>

              <Transition
                show={open}
                enter="transition duration-100 ease-out"
                enterFrom="transform scale-95 opacity-0"
                enterTo="transform scale-100 opacity-100"
                leave="transition duration-75 ease-out"
                leaveFrom="transform scale-100 opacity-100"
                leaveTo="transform scale-95 opacity-0"
              >
                <Disclosure.Panel className="p-4 pt-0 space-y-4">
                  {renderFields(item)}
                </Disclosure.Panel>
              </Transition>
            </div>
          )}
        </Disclosure>
      ))}

      <button
        onClick={onAdd}
        className="w-full py-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-500 hover:text-gray-700 hover:border-gray-400 flex items-center justify-center"
      >
        <FontAwesomeIcon icon={faPlus} className="mr-2" />
        {addButtonText}
      </button>
    </div>
  );
}
