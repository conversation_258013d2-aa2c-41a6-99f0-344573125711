'use client';

import SiteInput from '@/components/shared/form/SiteInput';
import ItemEditor from './ItemEditor';

export type Education = {
  id: string;
  schoolName: string;
  years: string;
  gpa: string;
  classRank: string;
};

interface EducationEditorProps {
  items: Education[];
  onAdd: () => void;
  onUpdate: (id: string, updates: Partial<Omit<Education, 'id'>>) => void;
  onRemove: (id: string) => void;
}

export default function EducationEditor({
  items,
  onAdd,
  onUpdate,
  onRemove,
}: EducationEditorProps) {
  const renderFields = (item: Education) => (
    <>
      <SiteInput
        label="School Name"
        type="text"
        value={item.schoolName}
        onChange={e => onUpdate(item.id, { schoolName: e.target.value })}
        placeholder="e.g., Central Dupage High"
      />

      <SiteInput
        label="Date or Range"
        type="text"
        value={item.years}
        onChange={e => onUpdate(item.id, { years: e.target.value })}
        placeholder="e.g., 2022 - Present"
      />

      <SiteInput
        label="Current GPA"
        type="text"
        value={item.gpa}
        onChange={e => onUpdate(item.id, { gpa: e.target.value })}
        placeholder="e.g., 4.0"
      />

      <SiteInput
        label="Current Class Rank"
        type="text"
        value={item.classRank}
        onChange={e => onUpdate(item.id, { classRank: e.target.value })}
        placeholder="e.g., 16/402"
        className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
      />
    </>
  );

  return (
    <ItemEditor
      items={items}
      onAdd={onAdd}
      onRemove={onRemove}
      onUpdate={onUpdate}
      getTitle={item => item.schoolName || 'School Name'}
      renderFields={renderFields}
    />
  );
}
