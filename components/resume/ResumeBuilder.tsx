'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { faChevronDown } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { InvolvementForm } from '@/components/profile/InvolvementForm';
import { SportsForm } from '@/components/profile/SportsForm';
import { StoryForm } from '@/components/profile/StoryForm';
import { WorkExperienceForm } from '@/components/profile/WorkExperienceForm';
import EducationEditor from '@/components/resume/EducationEditor';
import SiteInput from '@/components/shared/form/SiteInput';
import Toggle from '@/components/shared/form/Toggle';
import { useResumeStore } from '@/stores/resumeStore';
import { Profile, SportEntry } from '@/types/profile';

interface ResumeBuilderProps {
  profile: Profile;
}

export default function ResumeBuilder({ profile }: ResumeBuilderProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>(['profile']);
  const {
    bio,
    contact,
    education,
    involvement,
    workExperience,
    sports,
    enabledSections,
    setBio,
    setContact,
    addEducation,
    updateEducation,
    removeEducation,
    updateInvolvement,
    updateWorkExperience,
    updateSports,
    toggleSection,
    resetAll,
  } = useResumeStore();

  // Initialize sports from profile
  useEffect(() => {
    if (profile.sports) {
      updateSports(profile.sports);
    }
  }, [profile.sports, updateSports]);

  const toggleExpanded = (section: string) => {
    setExpandedSections(prev =>
      prev.includes(section) ? prev.filter(s => s !== section) : [...prev, section]
    );
  };

  const handleAddEducation = () => {
    addEducation({
      schoolName: '',
      years: '',
      gpa: '',
      classRank: '',
    });
  };

  const handleEducationToggle = () => {
    toggleSection('education');
  };

  const handleInvolvementToggle = () => {
    toggleSection('involvement');
  };

  const handleWorkExperienceToggle = () => {
    toggleSection('workExperience');
  };

  const handleSportsToggle = () => {
    toggleSection('sports');
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Link href="/profile" className="text-gray-600 hover:text-gray-800">
          ← Back
        </Link>
        <button onClick={resetAll} className="text-red-600 hover:text-red-700">
          Reset All
        </button>
      </div>

      <h1 className="text-2xl font-bold text-gray-900">Resume Builder</h1>

      {/* Profile Section */}
      <div className="border border-gray-200 rounded-lg bg-white">
        <button
          onClick={() => toggleExpanded('profile')}
          className="w-full flex justify-between items-center p-4 text-left"
        >
          <span className="font-medium">Your Profile</span>
          <FontAwesomeIcon
            icon={faChevronDown}
            className={`transform transition-transform ${
              expandedSections.includes('profile') ? 'rotate-180' : ''
            }`}
          />
        </button>
        {expandedSections.includes('profile') && (
          <div className="p-4 pt-0 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-4">
              Quickly give us a sense for who you are with a short bio that speaks to your
              interests, background, qualities, and skills.
            </p>
            <StoryForm
              content={bio}
              onSave={async () => {
                // No need to save in resume builder
              }}
              onCancel={() => {
                // No need to cancel in resume builder
              }}
              onChange={setBio}
              error={null}
              showActions={false}
            />
          </div>
        )}
      </div>

      {/* Contact Info Section */}
      <div className="border border-gray-200 rounded-lg bg-white">
        <button
          onClick={() => toggleExpanded('contact')}
          className="w-full flex justify-between items-center p-4 text-left"
        >
          <span className="font-medium">Contact Info</span>
          <FontAwesomeIcon
            icon={faChevronDown}
            className={`transform transition-transform ${
              expandedSections.includes('contact') ? 'rotate-180' : ''
            }`}
          />
        </button>
        {expandedSections.includes('contact') && (
          <div className="p-4 pt-0 border-t border-gray-200 space-y-4">
            <p className="text-sm text-gray-600">
              Including your contact information in your resume allows potential employers or
              recruiters to get in touch.
            </p>

            <SiteInput
              label="Email"
              type="email"
              value={contact.email}
              onChange={e => setContact({ email: e.target.value })}
              placeholder="<EMAIL>"
            />

            <SiteInput
              label="Phone"
              type="tel"
              value={contact.phone}
              onChange={e => setContact({ phone: e.target.value })}
              placeholder="(*************"
            />

            <SiteInput
              label="Location"
              type="text"
              value={contact.location}
              onChange={e => setContact({ location: e.target.value })}
              placeholder="City, State"
            />
          </div>
        )}
      </div>

      {/* Education Section */}
      <div className="border border-gray-200 rounded-lg bg-white">
        <div className="flex items-center justify-between p-4">
          <button
            onClick={() => toggleExpanded('education')}
            className="flex-1 flex justify-between items-center text-left"
          >
            <span className="font-medium">Education</span>
            <FontAwesomeIcon
              icon={faChevronDown}
              className={`transform transition-transform ${
                expandedSections.includes('education') ? 'rotate-180' : ''
              }`}
            />
          </button>
          <Toggle
            checked={enabledSections.education}
            onChange={handleEducationToggle}
            className="ml-auto"
          />
        </div>
        {expandedSections.includes('education') && (
          <div className="p-4 pt-0 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-4">
              Tell us where you went to school, and if you have a solid GPA and class rank, include
              those as well!
            </p>
            <EducationEditor
              items={education}
              onAdd={handleAddEducation}
              onUpdate={updateEducation}
              onRemove={removeEducation}
            />
          </div>
        )}
      </div>

      {/* School/Community Involvement Section */}
      <div className="border border-gray-200 rounded-lg bg-white">
        <div className="flex items-center justify-between p-4">
          <button
            onClick={() => toggleExpanded('involvement')}
            className="flex-1 flex justify-between items-center text-left"
          >
            <span className="font-medium">School / Community Involvement</span>
            <FontAwesomeIcon
              icon={faChevronDown}
              className={`transform transition-transform ${
                expandedSections.includes('involvement') ? 'rotate-180' : ''
              }`}
            />
          </button>
          <Toggle
            checked={enabledSections.involvement}
            onChange={handleInvolvementToggle}
            className="ml-auto"
          />
        </div>
        {expandedSections.includes('involvement') && (
          <div className="p-4 pt-0 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-4">
              Highlight your school achievements and ways you&apos;ve positively engaged with your
              community.
            </p>
            <InvolvementForm
              items={involvement}
              onSave={async () => {
                // No need to save in resume builder
              }}
              onCancel={() => {
                // No need to cancel in resume builder
              }}
              onChange={updateInvolvement}
              error={null}
              showActions={false}
            />
          </div>
        )}
      </div>

      {/* Work Experience Section */}
      <div className="border border-gray-200 rounded-lg bg-white">
        <div className="flex items-center justify-between p-4">
          <button
            onClick={() => toggleExpanded('workExperience')}
            className="flex-1 flex justify-between items-center text-left"
          >
            <span className="font-medium">Work Experience</span>
            <FontAwesomeIcon
              icon={faChevronDown}
              className={`transform transition-transform ${
                expandedSections.includes('workExperience') ? 'rotate-180' : ''
              }`}
            />
          </button>
          <Toggle
            checked={enabledSections.workExperience}
            onChange={handleWorkExperienceToggle}
            className="ml-auto"
          />
        </div>
        {expandedSections.includes('workExperience') && (
          <div className="p-4 pt-0 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-4">
              Share your work history and the valuable skills you&apos;ve gained from each
              experience.
            </p>
            <WorkExperienceForm
              items={workExperience}
              onSave={async () => {
                // No need to save in resume builder
              }}
              onCancel={() => {
                // No need to cancel in resume builder
              }}
              onChange={updateWorkExperience}
              error={null}
              showActions={false}
            />
          </div>
        )}
      </div>

      {/* Sports Section */}
      <div className="border border-gray-200 rounded-lg bg-white">
        <div className="flex items-center justify-between p-4">
          <button
            onClick={() => toggleExpanded('sports')}
            className="flex-1 flex justify-between items-center text-left"
          >
            <span className="font-medium">Sports</span>
            <FontAwesomeIcon
              icon={faChevronDown}
              className={`transform transition-transform ${
                expandedSections.includes('sports') ? 'rotate-180' : ''
              }`}
            />
          </button>
          <Toggle
            checked={enabledSections.sports}
            onChange={handleSportsToggle}
            className="ml-auto"
          />
        </div>
        {expandedSections.includes('sports') && (
          <div className="p-4 pt-0 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-4">
              Show the sports that you play to round out your resume and highlight your athleticism.
            </p>
            <SportsForm
              sports={sports}
              onSave={async () => {
                // No need to save in resume builder
              }}
              onCancel={() => {
                // No need to cancel in resume builder
              }}
              onChange={updateSports}
              error={null}
              showActions={false}
            />
          </div>
        )}
      </div>
    </div>
  );
}
