'use client';

import { useState } from 'react';
import { faDownload, faSave } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import { useResumeStore } from '@/stores/resumeStore';
import { useSportsStore } from '@/stores/sportsStore';
import { Profile } from '@/types/profile';
import Richtext from '../shared/blocks/Richtext';

interface ResumePreviewProps {
  profile: Profile;
}

export default function ResumePreview({ profile }: ResumePreviewProps) {
  const [isSaving, setIsSaving] = useState(false);
  const { bio, contact, education, involvement, workExperience, sports, enabledSections } =
    useResumeStore();
  const availableSports = useSportsStore(state => state.sports);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // TODO: Implement save functionality
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
    } catch (error) {
      console.error('Failed to save resume:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="p-8">
      {/* Header Actions */}
      <div className="flex justify-end gap-4 mb-8">
        <Button variant="filled" onClick={handleSave}>
          Save Resume
        </Button>
        <Button variant="filled" icon={faDownload}>
          Download PDF
        </Button>
      </div>

      {/* Resume Content */}
      <div className="max-w-[800px] mx-auto bg-white shadow-lg rounded-lg p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">RESUMÉ</h1>
          <h2 className="text-3xl font-bold text-gray-800">
            {profile.firstName} {profile.lastName}
          </h2>
        </div>

        {/* Contact Info */}
        <div className="text-center mb-8 text-gray-600">
          {contact.email && <p>E: {contact.email}</p>}
          {contact.phone && <p>P: {contact.phone}</p>}
          {contact.location && <p>{contact.location}</p>}
        </div>

        {/* Bio */}
        {bio && (
          <div className="mb-8">
            <Richtext content={bio || ''} className="text-text-primary" />
          </div>
        )}

        {/* Education */}
        {enabledSections.education && education.length > 0 && (
          <div className="mb-8">
            <h3 className="text-xl font-bold text-[#D50032] mb-4">EDUCATION</h3>
            <div className="space-y-4">
              {education.map(item => (
                <div key={item.id}>
                  <div className="flex justify-between items-baseline mb-1">
                    <h4 className="font-bold">{item.schoolName}</h4>
                    <span className="text-gray-600">{item.years}</span>
                  </div>
                  <div className="text-gray-700">
                    {item.gpa && <p>GPA: {item.gpa}</p>}
                    {item.classRank && <p>Class Rank: {item.classRank}</p>}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* School/Community Involvement */}
        {enabledSections.involvement && involvement.length > 0 && (
          <div className="mb-8">
            <h3 className="text-xl font-bold text-[#D50032] mb-4">
              SCHOOL / COMMUNITY INVOLVEMENT
            </h3>
            <div className="space-y-4">
              {involvement.map(item => (
                <div key={item.id}>
                  <div className="flex justify-between items-baseline mb-1">
                    <h4 className="font-bold">{item.title}</h4>
                    <span className="text-gray-600">{item.dateRange}</span>
                  </div>
                  <p className="text-gray-700">{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Work Experience */}
        {enabledSections.workExperience && workExperience.length > 0 && (
          <div className="mb-8">
            <h3 className="text-xl font-bold text-[#D50032] mb-4">WORK EXPERIENCE</h3>
            <div className="space-y-4">
              {workExperience.map(item => (
                <div key={item.id}>
                  <div className="flex justify-between items-baseline mb-1">
                    <h4 className="font-bold">{item.title}</h4>
                    <span className="text-gray-600">{item.dateRange}</span>
                  </div>
                  <p className="text-gray-700">{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Sports */}
        {enabledSections.sports && sports.length > 0 && (
          <div>
            <h3 className="text-xl font-bold text-[#D50032] mb-4">SPORTS</h3>
            <div className="flex flex-wrap gap-2">
              {sports.map(sport => {
                const sportInfo = availableSports.find(s => s.slug === sport.sport);
                return (
                  <span key={sport.id} className="bg-gray-100 text-gray-700 px-4 py-1 rounded-full">
                    {sport.type === 'custom' ? sport.customName : sportInfo?.label || sport.sport}
                  </span>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
