'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  faArrowRight,
  faCheck,
  faEllipsis,
  faPencil,
  faThumbsDown,
  faThumbsUp,
  faTrash,
  faTriangleExclamation,
  faUser,
  faXmark,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';
import { format, isSameYear, isToday, isYesterday } from 'date-fns';
import YouTubeVideo from '@/components/shared/blocks/YouTubeVideo';
import Button from '@/components/shared/Button';
import { Textarea } from '@/components/shared/form/Textarea';
import IconPositiveAthlete from '@/components/svgs/IconPositiveAthlete';
import { useAuth } from '@/hooks/useAuth';
import { Message, PlatformMessage } from '@/services/messages.service';
import { formatDateTimeLocal } from '@/utils/date-helpers';

interface PlatformMessageCardProps {
  message: PlatformMessage;
  onMarkAsRead: (messageId: number) => void;
  onDeleteMessage: (messageId: number) => void;
}

export default function PlatformMessageCard({
  message,
  onMarkAsRead,
  onDeleteMessage,
}: PlatformMessageCardProps) {
  const { user } = useAuth();

  // Check if this is Read Only - when the current user is neither the sender nor recipient
  const isReadOnly = user && user.id !== message.senderId && user.id !== message.recipientId;

  return (
    <div
      className={`w-full max-w-fit p-4 flex gap-x-4 gap-y-2 flex-wrap rounded-4xl shadow-card bg-white rounded-bl-none lg:p-6`}
    >
      <div className="flex gap-x-4 gap-y-2 w-full">
        <div className="flex items-center justify-center overflow-hidden shrink-0 relative">
          <IconPositiveAthlete className="text-brand-blue" aria-hidden="true" />
        </div>

        <div className="w-full flex flex-wrap items-center justify-between gap-4">
          <h4 className="text-sm font-bold">{message.title}</h4>

          <div className="flex flex-wrap items-center gap-4">
            <span className="text-xs text-text-secondary">
              {formatDateTimeLocal(message.createdAt)}˝
            </span>

            {/* Show menu for both sent and received messages */}
            {!isReadOnly && (
              <Menu as="div" className="relative inline-block text-left">
                <div>
                  <MenuButton className="inline-flex text-gray-900">
                    <FontAwesomeIcon
                      icon={faEllipsis}
                      aria-hidden="true"
                      className="-mr-1 size-5 text-gray-400"
                    />
                  </MenuButton>
                </div>

                <MenuItems
                  transition
                  className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                >
                  <div className="py-1 space-y-2">
                    {/* Delete option - available for both sent and received messages */}
                    <MenuItem
                      as="button"
                      className="inline-flex items-center gap-2 w-full text-xs text-text-primary p-2 text-left hover:bg-gray-100"
                      onClick={() => onDeleteMessage(message.id)}
                      aria-label="Delete Message"
                    >
                      Delete
                      <FontAwesomeIcon icon={faTrash} className="size-4" />
                    </MenuItem>

                    {/* Mark as read option (only for received messages that aren't read) */}
                    {!message.readAt && (
                      <MenuItem
                        as="button"
                        className="inline-flex items-center gap-2 w-full text-xs text-text-primary p-2 text-left hover:bg-gray-100"
                        onClick={() => onMarkAsRead(message.id)}
                        aria-label="Mark as Read"
                      >
                        Mark as Read
                        <FontAwesomeIcon icon={faCheck} className="size-4" />
                      </MenuItem>
                    )}
                  </div>
                </MenuItems>
              </Menu>
            )}
          </div>
        </div>
      </div>

      <div className="relative block space-y-2 lg:pl-14 w-full">
        <p className="text-sm">{message.content}</p>

        {/* YouTube Video Player */}
        {message.youtubeUrl && <YouTubeVideo url={message.youtubeUrl} />}

        {/* Athletics Director Verify Nominee Section */}
        <div className="space-y-4">
          {/* User Profile Row */}
          <Link
            href="/pa/4"
            className="group flex items-center justify-between rounded-3xl border p-4 transition-colors hover:border-brand-blue lg:px-6"
          >
            <div className="flex items-center gap-3">
              {/* Avatar placeholder - gray circle */}
              <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                <FontAwesomeIcon icon={faUser} className="text-gray-500 text-lg" />
              </div>

              {/* User Name */}
              <span className="font-semibold text-gray-900">Toby Robins</span>
            </div>

            {/* Arrow Link */}
            <Link
              href="/profile/123"
              className="text-gray-400 group-hover:text-brand-blue transition-colors"
            >
              <FontAwesomeIcon icon={faArrowRight} className="text-current text-lg" />
            </Link>
          </Link>

          {/* Description Text */}
          <p className="text-sm text-gray-700 leading-relaxed">
            Can you attest to this student&apos;s character and recommend them for this award? If
            no, please send a message indicating why.
          </p>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button variant="filled" size="small" color="blue" className="flex items-center gap-2">
              <FontAwesomeIcon icon={faThumbsUp} className="text-sm" />
              Yes!
            </Button>

            <Button variant="filled" size="small" color="red" className="flex items-center gap-2">
              <FontAwesomeIcon icon={faThumbsDown} className="text-sm" />
              No
            </Button>
          </div>
        </div>

        <div className={`text-xs mt-2 flex justify-between text-text-primary`}>
          <span className="block">{message.readAt ? 'Read' : 'Delivered'}</span>
        </div>
      </div>
    </div>
  );
}
