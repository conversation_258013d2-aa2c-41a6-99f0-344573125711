'use client';

import React from 'react';
import Image from 'next/image';
import { faCheck, faXmark } from '@fortawesome/free-solid-svg-icons';
import Button from '@/components/shared/Button';
import { Message } from '@/services/messages.service';
import MessageCard from './MessageCard';

interface ConnectionRequestProps {
  user: {
    id: number;
    firstName: string;
    lastName: string;
    profileImageUrl: string | null;
  };
  connectionId: number;
  message: Message | null;
  disableActions?: boolean;
  onAccept: (connectionId: number) => void;
  onBlock: (userId: number) => void;
}

export default function ConnectionRequest({
  user,
  connectionId,
  message,
  disableActions = false,
  onAccept,
  onBlock,
}: ConnectionRequestProps) {
  return (
    <div className="w-full mx-auto mt-32">
      <div className="block space-y-4 text-center mb-24">
        <p className="text-2xl font-semibold text-text-primary text-center mb-4">
          {user.firstName} would like to connect with you.
        </p>

        <div className="flex items-center justify-center gap-4 w-full">
          <Button
            size="small"
            icon={faXmark}
            iconPosition="right"
            onClick={() => onBlock(user.id)}
            disabled={disableActions}
          >
            Block
          </Button>

          <Button
            size="small"
            icon={faCheck}
            iconPosition="right"
            onClick={() => onAccept(connectionId)}
            disabled={disableActions}
          >
            Accept
          </Button>
        </div>
      </div>

      {message && (
        <div className="w-full flex items-center justify-center mt-8">
          <MessageCard
            message={message}
            isFromMe={false}
            senderName={`${message.sender?.firstName || user.firstName} ${message.sender?.lastName || user.lastName}`}
            recipientName={`${user.firstName} ${user.lastName}`}
            onMarkAsRead={() => {}}
            onDeleteMessage={() => {}}
          />
        </div>
      )}
    </div>
  );
}
