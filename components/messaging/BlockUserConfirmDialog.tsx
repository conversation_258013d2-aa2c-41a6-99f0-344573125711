import React from 'react';
import { faCheck, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';

interface BlockUserConfirmDialogProps {
  handleBlockUser: () => void;
  handleClose: () => void;
  className?: string;
}

export function BlockUserConfirmDialog({
  handleBlockUser,
  handleClose,
  className = '',
}: BlockUserConfirmDialogProps) {
  return (
    <div
      className={`relative p-4 w-full max-w-[500px] bg-white rounded-4xl shadow-lg lg:p-16 ${className}`}
    >
      {/* Close Button */}
      <button
        type="button"
        className="absolute top-6 right-6 text-text-primary hover:text-neutral-600 transition-colors"
        onClick={handleClose}
        aria-label="Close Modal"
      >
        <FontAwesomeIcon icon={faXmark} className="size-4" aria-hidden="true" />
      </button>

      {/* Header */}
      <div className="block space-y-4 mb-10">
        <h2 className="text-xl font-bold text-text-primary">Block User</h2>
        <p className="text-sm text-text-secondary">
          Are you sure you want to block this user? You will no longer receive messages from them.
        </p>
      </div>

      <div className="flex gap-4 items-center">
        <Button color="red" size="small" onClick={handleBlockUser} icon={faCheck}>
          Block
        </Button>
        <Button size="small" color="white" onClick={handleClose} icon={faXmark}>
          Cancel
        </Button>
      </div>
    </div>
  );
}

export default BlockUserConfirmDialog;
