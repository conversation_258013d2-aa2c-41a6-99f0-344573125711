'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import {
  faCheck,
  faEllipsis,
  faPencil,
  faTrash,
  faTriangleExclamation,
  faUser,
  faXmark,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';
import { format, isSameYear, isToday, isYesterday } from 'date-fns';
import Button from '@/components/shared/Button';
import { Textarea } from '@/components/shared/form/Textarea';
import { useAuth } from '@/hooks/useAuth';
import { Message } from '@/services/messages.service';
import { formatDateTimeLocal } from '@/utils/date-helpers';

interface MessageCardProps {
  message: Message;
  isFromMe: boolean;
  senderName: string;
  recipientName: string;
  onMarkAsRead: (messageId: number) => void;
  onDeleteMessage: (messageId: number) => void;
  onEditMessage?: (messageId: number, content: string) => void;
}

export default function MessageCard({
  message,
  isFromMe,
  senderName,
  recipientName,
  onMarkAsRead,
  onDeleteMessage,
  onEditMessage,
}: MessageCardProps) {
  const { user } = useAuth();
  // State to track if flagged content should be shown
  const [showFlaggedContent, setShowFlaggedContent] = useState(false);
  // State to track if message is being edited
  const [isEditing, setIsEditing] = useState(false);
  // State to track the edited message content
  const [editedContent, setEditedContent] = useState(message.content || '');

  // Update editedContent when message prop changes
  useEffect(() => {
    setEditedContent(message.content || '');
  }, [message.content]);

  // Check if this message is flagged and received by the current user
  // We only apply the blur to messages not sent by the current user
  const isFlaggedForRecipient = message.isFlagged === true && !isFromMe;
  // Check if this is a flagged message from the current user
  const isFlaggedFromMe = message.isFlagged === true && isFromMe;

  // Check if this is Read Only - when the current user is neither the sender nor recipient
  const isReadOnly = user && user.id !== message.senderId && user.id !== message.recipientId;

  // Format the message timestamp with appropriate context
  // const formatMessageTime = (dateString: string) => {
  //   const date = new Date(dateString);
  //
  //   if (isToday(date)) {
  //     // Just show time for today's messages
  //     return format(date, 'h:mm a');
  //   } else if (isYesterday(date)) {
  //     // Show "Yesterday" for yesterday's messages
  //     return `Yesterday, ${format(date, 'h:mm a')}`;
  //   } else if (isSameYear(date, new Date())) {
  //     // For messages from this year, show Month Day, Time
  //     return format(date, 'MMM d, h:mm a');
  //   } else {
  //     // For older messages, include the year
  //     return format(date, 'MMM d, yyyy, h:mm a');
  //   }
  // }; // Removed old formatting function

  // Handle save of edited message
  const handleSaveEdit = () => {
    if (onEditMessage && editedContent.trim() !== '') {
      onEditMessage(message.id, editedContent);
      setIsEditing(false);
    }
  };

  // Handle cancel of editing
  const handleCancelEdit = () => {
    setEditedContent(message.content || '');
    setIsEditing(false);
  };

  return (
    <figure
      className={`w-full max-w-fit p-4 flex gap-x-4 gap-y-2 flex-wrap rounded-4xl shadow-card bg-white ${
        isFromMe ? 'text-text-secondary rounded-br-none' : ' rounded-bl-none'
      } lg:p-6`}
    >
      <div className="flex gap-x-4 gap-y-2 w-full">
        <div className="flex items-center justify-center size-10 overflow-hidden rounded-full shrink-0 bg-gray-400 relative">
          {/* Show organization logo if available */}
          {message.sender?.organizationLogoUrl ? (
            <Image
              src={message.sender.organizationLogoUrl}
              alt={`${message.sender?.organizationName || 'Organization'} logo`}
              className="object-cover rounded-full"
              fill
              sizes="40px"
            />
          ) : message.sender?.profileImageUrl ? (
            <Image
              src={message.sender.profileImageUrl}
              alt={`${message.sender?.firstName || ''}'s profile`}
              className="object-cover rounded-full"
              fill
              sizes="40px"
            />
          ) : message.sender?.firstName ? (
            <span className="text-base font-medium text-white">
              {message.sender.firstName.charAt(0)}
              {message.sender.lastName ? message.sender.lastName.charAt(0) : ''}
            </span>
          ) : (
            <FontAwesomeIcon icon={faUser} className="size-4 text-gray-300" />
          )}
        </div>

        <div className="w-full flex flex-wrap items-center justify-between gap-4">
          <h4 className="text-sm font-bold">{senderName}</h4>

          <div className="flex flex-wrap items-center gap-4">
            <span className="text-xs text-text-secondary">
              {formatDateTimeLocal(message.createdAt)}
              {message.editedAt && ' (edited)'}
            </span>

            {/* Show menu for both sent and received messages */}
            {!isEditing && !isReadOnly && (
              <Menu as="div" className="relative inline-block text-left">
                <div>
                  <MenuButton className="inline-flex text-gray-900">
                    <FontAwesomeIcon
                      icon={faEllipsis}
                      aria-hidden="true"
                      className="-mr-1 size-5 text-gray-400"
                    />
                  </MenuButton>
                </div>

                <MenuItems
                  transition
                  className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                >
                  <div className="py-1 space-y-2">
                    {/* Edit option (only for own messages that are not flagged) */}
                    {isFromMe && !message.isFlagged && (
                      <MenuItem
                        as="button"
                        className="inline-flex items-center gap-2 w-full text-xs text-text-primary p-2 text-left hover:bg-gray-100"
                        onClick={() => setIsEditing(true)}
                        aria-label="Edit Message"
                      >
                        Edit
                        <FontAwesomeIcon icon={faPencil} className="size-4" />
                      </MenuItem>
                    )}

                    {/* Delete option - available for both sent and received messages */}
                    <MenuItem
                      as="button"
                      className="inline-flex items-center gap-2 w-full text-xs text-text-primary p-2 text-left hover:bg-gray-100"
                      onClick={() => onDeleteMessage(message.id)}
                      aria-label="Delete Message"
                    >
                      Delete
                      <FontAwesomeIcon icon={faTrash} className="size-4" />
                    </MenuItem>

                    {/* Mark as read option (only for received messages that aren't read) */}
                    {!isFromMe && !message.readAt && (
                      <MenuItem
                        as="button"
                        className="inline-flex items-center gap-2 w-full text-xs text-text-primary p-2 text-left hover:bg-gray-100"
                        onClick={() => onMarkAsRead(message.id)}
                        aria-label="Mark as Read"
                      >
                        Mark as Read
                        <FontAwesomeIcon icon={faCheck} className="size-4" />
                      </MenuItem>
                    )}
                  </div>
                </MenuItems>
              </Menu>
            )}
          </div>
        </div>
      </div>

      <figcaption className="relative block space-y-2 lg:pl-14 w-full">
        {isEditing ? (
          <div className="space-y-3">
            <Textarea
              label="Edit message"
              hideLabel
              value={editedContent}
              onChange={e => setEditedContent(e.target.value)}
              placeholder="Edit your message..."
              rows={3}
              className="w-full"
            />
            <div className="flex gap-2 justify-end">
              <Button size="small" variant="text" onClick={handleCancelEdit}>
                <FontAwesomeIcon icon={faXmark} className="mr-1" />
                Cancel
              </Button>
              <Button size="small" onClick={handleSaveEdit} disabled={editedContent.trim() === ''}>
                <FontAwesomeIcon icon={faCheck} className="mr-1" />
                Save
              </Button>
            </div>
          </div>
        ) : isFlaggedForRecipient && !showFlaggedContent ? (
          <button
            type="button"
            onClick={() => setShowFlaggedContent(true)}
            className="group relative w-full text-left cursor-pointer bg-gradient-to-r from-white/20 to-white/40 backdrop-blur-xl rounded p-4"
            aria-label="View flagged content"
          >
            <div className="text-center">
              <p className="text-sm font-medium text-yellow-600">
                This message contains potentially inappropriate content
              </p>
              <p className="text-xs text-text-secondary mt-1 transition-colors group-hover:text-text-primary">
                Click to view
              </p>
            </div>
          </button>
        ) : (
          <p className="text-sm">{message.content}</p>
        )}

        <div
          className={`text-xs mt-2 flex justify-between ${
            isFromMe ? 'text-text-secondary' : 'text-text-primary'
          }`}
        >
          {isFromMe && !message.isFlagged && <span>{message.readAt ? 'Read' : 'Delivered'}</span>}

          {isFlaggedForRecipient && showFlaggedContent && (
            <div className="flex mt-2 gap-2 text-brand-red">
              <FontAwesomeIcon
                icon={faTriangleExclamation}
                className="text-current size-4"
                aria-hidden="true"
              />
              <span className="text-sm">Flagged as inappropriate</span>
            </div>
          )}
          {isFlaggedFromMe && (
            <div className="flex gap-2 text-brand-red">
              <FontAwesomeIcon
                icon={faTriangleExclamation}
                className="text-current size-4"
                aria-hidden="true"
              />
              <span className="text-sm">
                Message was flagged for inappropriate content and was not delivered
              </span>
            </div>
          )}
        </div>
      </figcaption>
    </figure>
  );
}
