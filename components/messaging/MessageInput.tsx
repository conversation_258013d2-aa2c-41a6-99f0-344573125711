'use client';

import React, { TextareaHTMLAttributes, useEffect, useRef } from 'react';
import { faPaperPlane as faPaperPlaneRegular, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { faPaperPlane as faPaperPlaneSolid } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Field, Label, Textarea } from '@headlessui/react';

interface MessageInputProps
  extends Omit<TextareaHTMLAttributes<HTMLTextAreaElement>, 'className' | 'value'> {
  disabled?: boolean;
  description?: string;
  className?: string;
  placeholder?: string;
  value?: string | null;
  onSend: (message: string) => void;
}

const MessageInput: React.FC<MessageInputProps> = ({
  disabled = false,
  description,
  className = '',
  placeholder = 'Your message here',
  value,
  onSend,
  ...props
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize the textarea based on content
  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // Reset height to calculate the new height based on content
    textarea.style.height = 'auto';

    // Calculate new height (with a max height of 150px)
    const newHeight = Math.min(textarea.scrollHeight, 150);
    textarea.style.height = `${newHeight}px`;
  }, [value]);

  const handleClear = () => {
    if (props.onChange) {
      // Create a synthetic event that matches the expected type
      const syntheticEvent = {
        target: {
          value: '',
        },
      } as React.ChangeEvent<HTMLTextAreaElement>;

      props.onChange(syntheticEvent);

      // Focus the textarea after clearing
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    }
  };

  const handleSendClick = () => {
    if (value && value.trim()) {
      onSend(value);
      handleClear();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Send message on Enter, but allow Shift+Enter for new lines
    if (e.key === 'Enter' && !e.shiftKey && value && value.trim()) {
      e.preventDefault(); // Prevent default to avoid new line
      onSend(value);
      handleClear();
    }

    // Call the original onKeyDown if it exists
    if (props.onKeyDown) {
      props.onKeyDown(e);
    }
  };

  return (
    <Field disabled={disabled}>
      <Label className="sr-only">Your message</Label>

      <div className="relative flex items-center rounded-4xl bg-white border border-gray-20">
        <Textarea
          ref={textareaRef}
          className={`block min-w-0 grow py-2 rounded-4xl shadow-search-input pl-4 pr-10 text-gray-900 placeholder:text-gray-400 text-ellipsis text-base focus-within:ring-inset focus-within:ring-1 focus-within:ring-brand-blue resize-none overflow-hidden ${className}`}
          placeholder={placeholder}
          value={value ?? ''}
          onKeyDown={handleKeyDown}
          rows={1}
          style={{ minHeight: '40px' }}
          {...props}
        />

        <div className="absolute top-3 right-4 flex items-center">
          {value ? (
            <button
              type="button"
              className="size-4 text-current cursor-pointer p-0 m-0"
              onClick={handleSendClick}
              disabled={!value || !value.trim() || disabled}
              aria-label="Send Message"
            >
              <span className="sr-only">Send Message</span>
              <FontAwesomeIcon
                icon={faPaperPlaneSolid}
                className="size-4 text-blue-600 -translate-y-0.5"
                aria-hidden="true"
              />
            </button>
          ) : (
            <FontAwesomeIcon
              icon={faPaperPlaneRegular}
              aria-hidden="true"
              className="size-4 pointer-events-none"
            />
          )}
        </div>
      </div>
    </Field>
  );
};

export default MessageInput;
