'use client';

import Image from 'next/image';
import {
  faThumbTack,
  faThumbTackSlash,
  faUser,
  faUserSlash,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Radio } from '@headlessui/react';
import { format } from 'date-fns';
import { useAuth } from '@/hooks/useAuth';
import { Conversation } from '@/services/messages.service';
import { ProfileTypes } from '@/stores/auth.store';
import { useModalStore } from '@/stores/modal.store';
import { formatDateTimeSimple } from '@/utils/date-helpers';
import { BlockUserConfirmDialog } from './BlockUserConfirmDialog';

interface ConversationCardProps {
  conversation: Conversation;
  onSelectConversation: ((userId: number) => void) | ((userId: number, sponsorId?: number) => void);
  onPinConversation: (userId: number) => void;
  onUnpinConversation: (userId: number) => void;
  onBlockUser: (userId: number) => void;
  isReadOnly?: boolean;
}

export default function ConversationCard({
  conversation,
  onSelectConversation,
  onPinConversation,
  onUnpinConversation,
  onBlockUser,
  isReadOnly = false,
}: ConversationCardProps) {
  const { open, close } = useModalStore();
  const { profileType } = useAuth();

  const isParentProfile = profileType === ProfileTypes.PARENT;

  const handleOpenDialog = () => {
    const handleBlockUser = () => {
      onBlockUser(conversation.otherUserId);
      close();
    };

    open(<BlockUserConfirmDialog handleBlockUser={handleBlockUser} handleClose={close} />);
  };

  return (
    <>
      <Radio
        value={conversation.otherUserId.toString()}
        className="flex w-full p-4 items-center text-left cursor-pointer"
      >
        <div className="size-10 flex items-center justify-center shrink-0 rounded-full bg-white mr-4 relative">
          {/* Show organization logo if available */}
          {conversation.otherUser?.organizationLogoUrl ? (
            <Image
              src={conversation.otherUser.organizationLogoUrl}
              alt={`${conversation.otherUser.organizationName || 'Organization'} logo`}
              className="object-cover rounded-full"
              fill
              sizes="40px"
            />
          ) : conversation.otherUser?.profileImageUrl ? (
            <Image
              src={conversation.otherUser.profileImageUrl}
              alt={`${conversation.otherUser.firstName}'s profile`}
              className="object-cover rounded-full"
              fill
              sizes="40px"
            />
          ) : conversation.otherUser?.firstName ? (
            <span className="text-base font-medium text-gray-600">
              {conversation.otherUser.firstName.charAt(0)}
              {conversation.otherUser.lastName ? conversation.otherUser.lastName.charAt(0) : ''}
            </span>
          ) : (
            <FontAwesomeIcon icon={faUser} className="size-4 text-gray-300" aria-hidden="true" />
          )}
        </div>

        <div className="flex-1">
          <div className="flex flex-wrap justify-between gap-4">
            <h3 className="text-base font-bold text-text-primary">
              {conversation.otherUser?.firstName} {conversation.otherUser?.lastName}
              {conversation.otherUser?.organizationName && (
                <span className="font-normal text-gray-600">
                  {' '}
                  @ {conversation.otherUser.organizationName}
                </span>
              )}
            </h3>

            <span className="inline-block text-xs text-text-secondary">
              {conversation.lastMessageAt && formatDateTimeSimple(conversation.lastMessageAt)}

              {conversation.isReadonly && conversation.sponsorUser && (
                <div className="inline-block ml-2 text-xs text-text-secondary">
                  Via {conversation.sponsorUser.firstName} {conversation.sponsorUser.lastName}
                </div>
              )}
            </span>
          </div>
          <p className="text-sm text-text-secondary line-clamp-1 mt-1">
            {conversation.lastMessage || 'No messages yet'}
          </p>
        </div>
      </Radio>

      <div className="self-start shrink-0 pl-2 pr-4 py-4">
        {!isReadOnly && (
          <>
            {!isParentProfile && (
              <button
                type="button"
                className="block text-text-primary transition-colors hover:text-text-secondary"
                aria-label={conversation.isPinned ? 'Unpin' : 'Pin'}
                title={conversation.isPinned ? 'Unpin' : 'Pin'}
                onClick={e => {
                  e.stopPropagation();
                  if (conversation.isPinned) {
                    onUnpinConversation(conversation.otherUserId);
                  } else {
                    onPinConversation(conversation.otherUserId);
                  }
                }}
              >
                <span className="sr-only">
                  {conversation.isPinned ? 'Unpin' : 'Pin'} conversation
                </span>
                {conversation.isPinned ? (
                  <FontAwesomeIcon
                    icon={faThumbTackSlash}
                    className="text-current size-4"
                    aria-hidden="true"
                  />
                ) : (
                  <FontAwesomeIcon
                    icon={faThumbTack}
                    className="text-current size-4"
                    aria-hidden="true"
                  />
                )}
              </button>
            )}

            {!isParentProfile && (
              <button
                type="button"
                className="block text-brand-red"
                aria-label="Block user"
                title="Block user"
                onClick={e => {
                  e.stopPropagation();
                  handleOpenDialog();
                }}
              >
                <span className="sr-only">Block user</span>
                <FontAwesomeIcon
                  icon={faUserSlash}
                  className="text-current size-4"
                  aria-hidden="true"
                />
              </button>
            )}
          </>
        )}
      </div>
    </>
  );
}
