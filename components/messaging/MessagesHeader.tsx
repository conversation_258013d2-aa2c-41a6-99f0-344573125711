'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { faArrowRight, faSpinnerThird, faUser } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import { Message } from '@/services/messages.service';

interface MessagesHeaderProps {
  isLoading: boolean;
  error: string | null;
  messages: Message[] | undefined;
  selectedUserId: number | null;
}

export default function MessagesHeader({
  isLoading,
  error,
  messages,
  selectedUserId,
}: MessagesHeaderProps) {
  // Find the other user's information from the messages
  const otherUserInfo =
    messages && messages.length > 0
      ? messages[0].sender?.id === selectedUserId
        ? {
            id: messages[0].sender?.id,
            firstName: messages[0].sender?.firstName,
            lastName: messages[0].sender?.lastName,
            profileImageUrl: messages[0].sender?.profileImageUrl,
            organizationName: messages[0].sender?.organizationName,
            organizationLogoUrl: messages[0].sender?.organizationLogoUrl,
          }
        : {
            id: messages[0].recipient?.id,
            firstName: messages[0].recipient?.firstName,
            lastName: messages[0].recipient?.lastName,
            profileImageUrl: messages[0].recipient?.profileImageUrl,
            organizationName: messages[0].recipient?.organizationName,
            organizationLogoUrl: messages[0].recipient?.organizationLogoUrl,
          }
      : null;

  return (
    <>
      {isLoading ? (
        <FontAwesomeIcon icon={faSpinnerThird} className="animate-spin size-6 text-gray-500" />
      ) : error ? (
        <div className="text-red-500">{error}</div>
      ) : messages && messages.length > 0 ? (
        <figure className="flex gap-4">
          <div className="flex items-center justify-center size-10 overflow-hidden rounded-full shrink-0 bg-gray-400 lg:size-16 relative">
            {/* Show organization logo if available */}
            {otherUserInfo?.organizationLogoUrl ? (
              <Image
                src={otherUserInfo.organizationLogoUrl}
                alt={`${otherUserInfo.organizationName || 'Organization'} logo`}
                className="object-cover rounded-full"
                fill
                sizes="(max-width: 1024px) 40px, 64px"
              />
            ) : otherUserInfo?.profileImageUrl ? (
              <Image
                src={otherUserInfo.profileImageUrl}
                alt={`${otherUserInfo.firstName}'s profile`}
                className="object-cover rounded-full"
                fill
                sizes="(max-width: 1024px) 40px, 64px"
              />
            ) : otherUserInfo?.firstName ? (
              <span className="text-base font-medium text-white lg:text-xl">
                {otherUserInfo.firstName.charAt(0)}
                {otherUserInfo.lastName ? otherUserInfo.lastName.charAt(0) : ''}
              </span>
            ) : (
              <FontAwesomeIcon icon={faUser} className="size-4 text-gray-300 lg:size-6" />
            )}
          </div>

          <figcaption className="w-full flex flex-wrap items-center justify-between gap-4">
            <div className="block space-y-1">
              <h3 className="text-xl font-bold text-text-primary lg:text-2xl">
                {otherUserInfo
                  ? `${otherUserInfo.firstName} ${otherUserInfo.lastName}${otherUserInfo.organizationName ? ` @ ${otherUserInfo.organizationName}` : ''}`
                  : 'Unknown User'}
              </h3>
              {/* <p className="text-sm text-gray-500">Status not available</p> */}
            </div>

            <div>
              <Button href={`/pa/${otherUserInfo?.id}`} size="small" icon={faArrowRight}>
                Profile
              </Button>
            </div>
          </figcaption>
        </figure>
      ) : (
        <p>No user information available</p>
      )}
    </>
  );
}
