'use client';

// DEPRECATED: This is old boilerplate code that is no longer used.
import { faXmark } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Connection } from '@/services/networking.service';

interface ConnectionsDialogProps {
  pendingConnections: Connection[];
  acceptedConnections: Connection[];
  isLoadingPendingConnections: boolean;
  isLoadingAcceptedConnections: boolean;
  selectedUserId: number | null;
  onClose: () => void;
  onAcceptConnection: (connectionId: number) => void;
  onRejectConnection: (connectionId: number) => void;
  onBlockUser: (userId: number) => void;
  onSelectConversation: (userId: number) => void;
  onCreateConnectionRequest?: (userId: number) => void;
}

export default function ConnectionsDialog({
  pendingConnections,
  acceptedConnections,
  isLoadingPendingConnections,
  isLoadingAcceptedConnections,
  selectedUserId,
  onClose,
  onAcceptConnection,
  onRejectConnection,
  onBlockUser,
  onSelectConversation,
  onCreateConnectionRequest,
}: ConnectionsDialogProps) {
  return (
    <div className="w-full min-w-[600px] p-6">
      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <h2 className="text-xl font-semibold">Connections</h2>

        <button type="button" onClick={onClose} aria-label="Close">
          <span className="sr-only">Close</span>
          <FontAwesomeIcon icon={faXmark} className="size-4" aria-hidden="true" />
        </button>
      </div>

      <div className="mb-6">
        <h3 className="font-medium mb-2">Pending Requests</h3>
        {isLoadingPendingConnections ? (
          <p>Loading pending requests...</p>
        ) : pendingConnections.length === 0 ? (
          <p className="text-sm text-gray-500">No pending requests</p>
        ) : (
          <ul className="space-y-2">
            {pendingConnections.map((connection, index) => (
              <li key={connection.id || index} className="p-2 border rounded">
                <p>
                  {connection.requester?.firstName || 'Unknown'}{' '}
                  {connection.requester?.lastName || 'User'}
                </p>
                <div className="flex space-x-2 mt-1">
                  <button
                    className="text-xs bg-green-500 text-white px-2 py-1 rounded"
                    onClick={() => onAcceptConnection(connection.id)}
                  >
                    Accept
                  </button>
                  <button
                    className="text-xs bg-red-500 text-white px-2 py-1 rounded"
                    onClick={() => onRejectConnection(connection.id)}
                  >
                    Reject
                  </button>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      <h3 className="font-medium mb-2">Connected Users</h3>
      {isLoadingAcceptedConnections ? (
        <p>Loading connections...</p>
      ) : acceptedConnections.length === 0 ? (
        <p className="text-sm text-gray-500">No connections yet</p>
      ) : (
        <ul className="space-y-2">
          {acceptedConnections.map((connection, index) => {
            const otherUser =
              connection.requesterId === selectedUserId
                ? connection.requester
                : connection.recipient;

            return (
              <li
                key={connection.id || index}
                className="p-2 border rounded cursor-pointer hover:bg-gray-100"
                onClick={() => onSelectConversation(otherUser?.id || 0)}
              >
                <p>
                  {otherUser?.firstName || 'Unknown'} {otherUser?.lastName || 'User'}
                </p>
                <div className="flex justify-between items-center mt-1">
                  <span className="text-xs text-gray-500">
                    Connected since{' '}
                    {connection.createdAt
                      ? new Date(connection.createdAt).toLocaleDateString()
                      : 'Unknown'}
                  </span>
                  <button
                    className="text-xs text-red-500"
                    onClick={e => {
                      e.stopPropagation();
                      onBlockUser(otherUser?.id || 0);
                    }}
                  >
                    Block
                  </button>
                </div>
              </li>
            );
          })}
        </ul>
      )}
    </div>
  );
}
