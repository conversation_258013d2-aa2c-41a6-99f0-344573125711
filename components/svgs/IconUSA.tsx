import React, { SVGProps } from 'react';

type IconUSAProps = SVGProps<SVGSVGElement>;

const IconUSA: React.FC<IconUSAProps> = props => {
  return (
    <svg
      width="26"
      height="16"
      viewBox="0 0 26 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M3.50991 1.00001C4.25687 1.06788 4.99902 1.31861 5.75735 1.44022C7.32442 1.72405 8.86331 1.93744 10.4545 2.07337C11.1715 2.12855 11.9199 2.14809 12.64 2.15401C13.1139 2.14778 13.5126 2.09532 13.9541 2.23424C14.1486 2.29034 14.3978 2.39504 14.6176 2.45541C14.8269 2.51464 15.0433 2.53355 15.2403 2.59176C15.5366 2.66674 15.5126 2.8158 15.3713 3.01935C15.1366 3.33125 15.3561 3.38212 15.6434 3.31102C16.6166 2.98224 16.5732 3.27374 17.4789 3.32086C17.6391 3.32713 18.2704 3.3677 17.8885 3.5684C17.6136 3.6735 17.2648 3.75635 17.0548 3.98078C16.8144 4.20327 16.938 4.41796 16.9157 4.72014C16.9133 4.81791 16.9022 4.95346 16.8985 5.06285C16.8905 5.26059 16.9044 5.47916 16.9943 5.65478C17.1232 5.91903 17.3412 5.84324 17.404 5.60045C17.5284 5.19074 17.4495 4.68452 17.6613 4.308C17.7919 4.07933 18.0816 3.90197 18.3125 4.04659C18.4577 4.14862 18.5499 4.35097 18.6432 4.5029C18.7303 4.66567 18.8524 4.80066 18.9334 4.96694C19.0434 5.19131 18.9798 5.4852 19.0807 5.70898C19.2278 5.99399 19.631 5.82062 19.8558 5.70456C20.1006 5.57628 20.2897 5.3704 20.4678 5.18493C20.7698 4.85528 21.1331 4.66454 21.4661 4.37489C22.185 3.55941 22.0781 3.61748 23.1434 3.18649C23.7392 2.96012 23.721 2.33842 24.0067 1.89795C24.3465 1.47195 24.8507 2.16706 24.9587 2.468C25.2319 3.23408 24.0535 3.54883 24.2275 4.36835C24.2531 4.5115 24.3274 4.62488 24.336 4.75247C24.3535 5.00765 23.6465 5.24097 23.4892 5.43255C23.3494 5.60885 23.3523 5.80226 23.2178 5.99295C23.0803 6.23223 22.8807 6.44195 22.7981 6.69579C22.7454 6.84626 22.7334 7.00407 22.7371 7.17487C22.7443 7.2633 22.7553 7.51055 22.6108 7.42993C22.5043 7.33883 22.454 7.18491 22.3766 7.07583C22.2 6.85254 22.2557 7.24154 22.2704 7.33981C22.3402 7.788 22.6384 8.19932 22.6896 8.64004C22.6857 9.10123 22.4107 9.41941 22.0884 9.77009C21.725 10.1736 21.326 10.561 21.0413 11.0213C20.2016 12.2741 21.7442 13.194 21.6963 14.389C21.6 14.992 20.9704 14.5228 20.7638 14.234C20.3226 13.7851 20.2906 13.0121 19.656 12.7424C19.2827 12.6081 18.8799 12.5946 18.4872 12.5519C18.0127 12.4619 17.5507 12.457 17.0779 12.5533C16.9179 12.5957 16.6204 12.5983 16.5732 12.7582C16.5802 12.8426 16.7447 12.9574 16.7263 13.0599C16.5482 13.5375 14.983 12.68 13.9296 13.2765C13.5642 13.4651 13.2178 13.696 12.9281 13.979C12.6332 14.2192 12.7019 15.0051 12.3308 15C11.4835 14.8042 11.3271 13.7769 10.7316 13.2807C10.2684 12.8105 9.59497 13.3919 9.14789 12.8907C8.62101 12.3393 8.29865 11.5755 7.36423 11.6737C6.02388 11.8354 5.29028 11.2788 4.23193 10.6496C3.81196 10.4772 3.3213 10.4194 2.99972 10.0717C2.84854 9.92762 2.72869 9.75507 2.59036 9.59946C2.34886 9.33621 2.03731 9.13486 1.83377 8.8416C1.33574 8.16815 1.39702 7.29278 1.13615 6.53408C0.942455 5.98391 0.97543 5.4279 1.11031 4.86471C1.23153 4.25405 1.42734 3.66799 1.69079 3.09709C1.89034 2.66392 2.05593 2.19056 2.11997 1.72851C2.15102 1.54438 2.21203 1.23637 2.45062 1.24439C2.57652 1.25674 2.73754 1.36716 2.83793 1.37169C3.02344 1.34014 3.11431 0.999301 3.50732 1.00008L3.50991 1.00001Z"
        stroke="currentColor"
        strokeMiterlimit="10"
      />
    </svg>
  );
};

export default IconUSA;
