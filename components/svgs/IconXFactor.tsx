import React, { SVGProps } from 'react';

type IconXFactorProps = SVGProps<SVGSVGElement>;

const IconXFactor: React.FC<IconXFactorProps> = props => {
  return (
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath="url(#clip0_4225_28418)">
        <path
          d="M5.02573 6.55594C5.427 6.18026 5.78978 5.80806 6.18646 5.47642C6.88494 4.89249 7.72037 4.76173 8.58705 4.89376C9.19564 4.98648 9.71874 5.30571 10.1623 5.73724C10.6014 6.16444 11.0359 6.59645 11.4696 7.02912C12.3953 7.95253 13.3192 8.87778 14.244 9.80218C14.7966 10.3547 15.3451 10.9115 15.9054 11.4561C16.0433 11.5901 16.0216 11.6708 15.8996 11.7913C15.2428 12.4397 14.5915 13.0935 13.9387 13.7458C13.2233 14.4606 12.5062 15.1738 11.7953 15.893C11.6756 16.0141 11.5965 16.0201 11.4735 15.8952C10.7807 15.1918 10.0835 14.4925 9.38176 13.798C9.00023 13.4205 8.52899 13.2899 7.99681 13.2782C7.27084 13.2622 6.77217 13.6381 6.31049 14.1326C5.94597 14.523 5.55065 14.8846 5.17084 15.2609C4.96482 15.465 4.75599 15.6669 4.56119 15.8814C4.44532 16.009 4.36349 16.0149 4.24038 15.8885C3.85408 15.4919 3.4571 15.1057 3.06537 14.7144C2.59816 14.2476 2.13168 13.7801 1.66566 13.3122C1.15027 12.7946 0.63833 12.2737 0.118985 11.7602C-0.00544987 11.6371 0.0895083 11.5712 0.162216 11.4986C0.792378 10.8691 1.42483 10.2419 2.05593 9.61333C2.31824 9.35209 2.58398 9.09399 2.83795 8.82483C2.94141 8.71516 3.02426 8.71212 3.12233 8.81459C3.28742 8.9871 3.44871 9.16336 3.61748 9.33216C3.71018 9.42488 3.68109 9.48535 3.59858 9.56685C2.99303 10.1651 2.39215 10.7681 1.78882 11.3687C1.52697 11.6293 1.53192 11.6696 1.78933 11.9255C2.51878 12.6506 3.23745 13.3866 3.97145 14.1071C4.35743 14.4859 4.32874 14.5675 4.76357 14.1237C5.18622 13.6923 5.60837 13.2582 6.05932 12.8575C6.49018 12.4746 7.00565 12.2585 7.5847 12.162C8.57508 11.997 9.39472 12.3094 10.0999 12.9811C10.5504 13.4102 10.9858 13.8552 11.4261 14.2949C11.5938 14.4624 11.711 14.4727 11.8791 14.3052C12.5257 13.6606 13.1685 13.0121 13.8133 12.3657C13.9734 12.2052 14.138 12.0491 14.2963 11.8869C14.4979 11.6804 14.4958 11.6088 14.2883 11.4021C13.2805 10.3982 12.2712 9.39578 11.2642 8.39093C10.6401 7.76809 10.0233 7.13792 9.396 6.51827C8.77188 5.90175 7.71488 5.76452 6.98041 6.28273C6.62972 6.53016 6.32894 6.84893 6.00894 7.13913C5.81513 7.31488 5.81611 7.32012 5.62287 7.13676C5.42832 6.95214 5.23815 6.76294 5.02573 6.55594Z"
          fill="currentColor"
        />
        <path
          d="M7.59909e-06 4.42706C1.49089 2.93202 2.9582 1.46062 4.40113 0.0136719C4.88945 0.486084 5.37823 0.957897 5.86579 1.43096C6.18512 1.7408 6.49125 2.06547 6.82491 2.35891C7.06268 2.56801 7.35791 2.6613 7.67616 2.73002C8.1866 2.84023 8.6455 2.7352 9.0562 2.46847C9.35136 2.27678 9.59295 2.00038 9.8506 1.75357C10.2218 1.39798 10.5878 1.03689 10.9522 0.67423C11.1212 0.506032 11.2841 0.331397 11.4439 0.154407C11.5557 0.0306558 11.647 0.0194548 11.7734 0.146808C12.9171 1.29934 14.0654 2.44726 15.2128 3.59617C15.4261 3.80985 15.6398 4.02325 15.8561 4.23406C15.9513 4.32696 15.9588 4.40474 15.8574 4.50428C15.2592 5.09154 14.664 5.68179 14.0704 6.27378C13.807 6.53649 13.5459 6.80179 13.2914 7.07307C13.089 7.28879 13.0577 7.29584 12.8478 7.09253C12.7163 6.96519 12.5902 6.83214 12.4551 6.70882C12.3495 6.61246 12.3386 6.53199 12.4466 6.42593C12.9092 5.97155 13.3669 5.51209 13.8267 5.05479C13.983 4.89934 14.1414 4.74599 14.2967 4.58953C14.4532 4.43187 14.4588 4.31183 14.3023 4.1525C14.0115 3.85639 13.7134 3.56741 13.4193 3.27444C13.0449 2.90144 12.672 2.52686 12.2973 2.15413C12.1455 2.00309 11.9941 1.85132 11.8361 1.70682C11.6795 1.56353 11.5962 1.56621 11.4482 1.71116C10.998 2.15199 10.5529 2.59811 10.0997 3.0358C9.34246 3.76703 8.4451 4.01964 7.41142 3.82421C6.73428 3.69618 6.20014 3.34176 5.73413 2.85046C5.35304 2.4487 4.95508 2.06263 4.55685 1.67761C4.40251 1.52838 4.32429 1.53843 4.17248 1.68926C3.39087 2.46581 2.60923 3.24233 1.83034 4.02161C1.50802 4.34407 1.51023 4.38327 1.82901 4.70248C2.81814 5.69294 3.80746 6.68323 4.79775 7.67253C5.39735 8.27153 5.99577 8.87177 6.60051 9.46555C7.12324 9.9788 7.76187 10.1456 8.46558 10.0022C8.79257 9.93559 9.103 9.78513 9.34631 9.53169C9.58623 9.28178 9.83527 9.0406 10.0742 8.78974C10.1599 8.69972 10.2345 8.70014 10.3168 8.78034C10.5153 8.97367 10.7146 9.16636 10.9069 9.36582C10.9328 9.39273 10.9426 9.48045 10.9218 9.50065C10.4163 9.99025 9.97587 10.5612 9.32582 10.8767C8.83564 11.1146 8.31537 11.2104 7.76205 11.1536C7.22187 11.0982 6.72081 10.9469 6.28796 10.6243C6.00273 10.4118 5.74299 10.1618 5.48871 9.91147C4.85764 9.2901 4.23675 8.65839 3.61078 8.03182C3.18942 7.61005 2.76551 7.19083 2.34425 6.76896C1.67919 6.10292 1.01565 5.43536 0.350804 4.7691C0.232335 4.65038 0.110696 4.53483 7.59909e-06 4.42706Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_4225_28418">
          <rect
            width="16"
            height="15.9726"
            fill="rgba(255, 255, 255, 0)"
            transform="translate(0 0.0136719)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconXFactor;
