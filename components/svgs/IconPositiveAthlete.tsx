import React, { SVGProps } from 'react';

type IconPositiveAthleteProps = SVGProps<SVGSVGElement>;

const IconPositiveAthlete: React.FC<IconPositiveAthleteProps> = props => {
  return (
    <svg
      width="40"
      height="22"
      viewBox="0 0 40 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_6267_63207)">
        <path
          d="M4.35884 22H9.10284V17.5044H11.5974L14.2528 12.6114H9.1029V8.10742H4.3589V12.6114H0V17.5044H4.35884V22Z"
          fill="currentColor"
        />
        <path
          d="M40 21.9999L27.7639 0H22.9686L10.8979 21.9999H15.8586L18.3388 17.182L20.447 13.217L25.201 4.26339L29.4789 12.6113H22.6137L19.9582 17.5043H32.0277L34.4194 21.9999H40Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_6267_63207">
          <rect width="40" height="22" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconPositiveAthlete;
