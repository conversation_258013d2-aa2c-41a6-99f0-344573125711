import React, { SVGProps } from 'react';

type IconXFactorHeavyProps = SVGProps<SVGSVGElement>;

const IconXFactorHeavy: React.FC<IconXFactorHeavyProps> = props => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M6.02573 7.55594C6.427 7.18026 6.78978 6.80806 7.18646 6.47642C7.88494 5.89249 8.72037 5.76173 9.58705 5.89376C10.1956 5.98648 10.7187 6.30571 11.1623 6.73724C11.6014 7.16444 12.0359 7.59645 12.4696 8.02912C13.3953 8.95253 14.3192 9.87778 15.244 10.8022C15.7966 11.3547 16.3451 11.9115 16.9054 12.4561C17.0433 12.5901 17.0216 12.6708 16.8996 12.7913C16.2428 13.4397 15.5915 14.0935 14.9387 14.7458C14.2233 15.4606 13.5062 16.1738 12.7953 16.893C12.6756 17.0141 12.5965 17.0201 12.4735 16.8952C11.7807 16.1918 11.0835 15.4925 10.3818 14.798C10.0002 14.4205 9.52899 14.2899 8.99681 14.2782C8.27084 14.2622 7.77217 14.6381 7.31049 15.1326C6.94597 15.523 6.55065 15.8846 6.17084 16.2609C5.96482 16.465 5.75599 16.6669 5.56119 16.8814C5.44532 17.009 5.36349 17.0149 5.24038 16.8885C4.85408 16.4919 4.4571 16.1057 4.06537 15.7144C3.59816 15.2476 3.13168 14.7801 2.66566 14.3122C2.15027 13.7946 1.63833 13.2737 1.11899 12.7602C0.99455 12.6371 1.08951 12.5712 1.16222 12.4986C1.79238 11.8691 2.42483 11.2419 3.05593 10.6133C3.31824 10.3521 3.58398 10.094 3.83795 9.82483C3.94141 9.71516 4.02426 9.71212 4.12233 9.81459C4.28742 9.9871 4.44871 10.1634 4.61748 10.3322C4.71018 10.4249 4.68109 10.4853 4.59858 10.5669C3.99303 11.1651 3.39215 11.7681 2.78882 12.3687C2.52697 12.6293 2.53192 12.6696 2.78933 12.9255C3.51878 13.6506 4.23745 14.3866 4.97145 15.1071C5.35743 15.4859 5.32874 15.5675 5.76357 15.1237C6.18622 14.6923 6.60837 14.2582 7.05932 13.8575C7.49018 13.4746 8.00565 13.2585 8.5847 13.162C9.57508 12.997 10.3947 13.3094 11.0999 13.9811C11.5504 14.4102 11.9858 14.8552 12.4261 15.2949C12.5938 15.4624 12.711 15.4727 12.8791 15.3052C13.5257 14.6606 14.1685 14.0121 14.8133 13.3657C14.9734 13.2052 15.138 13.0491 15.2963 12.8869C15.4979 12.6804 15.4958 12.6088 15.2883 12.4021C14.2805 11.3982 13.2712 10.3958 12.2642 9.39093C11.6401 8.76809 11.0233 8.13792 10.396 7.51827C9.77188 6.90175 8.71488 6.76452 7.98041 7.28273C7.62972 7.53016 7.32894 7.84893 7.00894 8.13913C6.81513 8.31488 6.81611 8.32012 6.62287 8.13676C6.42832 7.95214 6.23815 7.76294 6.02573 7.55594Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="0.5"
      />
      <path
        d="M1.00001 5.42706C2.49089 3.93202 3.9582 2.46062 5.40113 1.01367C5.88945 1.48608 6.37823 1.9579 6.86579 2.43096C7.18512 2.7408 7.49125 3.06547 7.82491 3.35891C8.06268 3.56801 8.35791 3.6613 8.67616 3.73002C9.1866 3.84023 9.6455 3.7352 10.0562 3.46847C10.3514 3.27678 10.593 3.00038 10.8506 2.75357C11.2218 2.39798 11.5878 2.03689 11.9522 1.67423C12.1212 1.50603 12.2841 1.3314 12.4439 1.15441C12.5557 1.03066 12.647 1.01945 12.7734 1.14681C13.9171 2.29934 15.0654 3.44726 16.2128 4.59617C16.4261 4.80985 16.6398 5.02325 16.8561 5.23406C16.9513 5.32696 16.9588 5.40474 16.8574 5.50428C16.2592 6.09154 15.664 6.68179 15.0704 7.27378C14.807 7.53649 14.5459 7.80179 14.2914 8.07307C14.089 8.28879 14.0577 8.29584 13.8478 8.09253C13.7163 7.96519 13.5902 7.83214 13.4551 7.70882C13.3495 7.61246 13.3386 7.53199 13.4466 7.42593C13.9092 6.97155 14.3669 6.51209 14.8267 6.05479C14.983 5.89934 15.1414 5.74599 15.2967 5.58953C15.4532 5.43187 15.4588 5.31183 15.3023 5.1525C15.0115 4.85639 14.7134 4.56741 14.4193 4.27444C14.0449 3.90144 13.672 3.52686 13.2973 3.15413C13.1455 3.00309 12.9941 2.85132 12.8361 2.70682C12.6795 2.56353 12.5962 2.56621 12.4482 2.71116C11.998 3.15199 11.5529 3.59811 11.0997 4.0358C10.3425 4.76703 9.4451 5.01964 8.41142 4.82421C7.73428 4.69618 7.20014 4.34176 6.73413 3.85046C6.35304 3.4487 5.95508 3.06263 5.55685 2.67761C5.40251 2.52838 5.32429 2.53843 5.17248 2.68926C4.39087 3.46581 3.60923 4.24233 2.83034 5.02161C2.50802 5.34407 2.51023 5.38327 2.82901 5.70248C3.81814 6.69294 4.80746 7.68323 5.79775 8.67253C6.39735 9.27153 6.99577 9.87177 7.60051 10.4655C8.12324 10.9788 8.76187 11.1456 9.46558 11.0022C9.79257 10.9356 10.103 10.7851 10.3463 10.5317C10.5862 10.2818 10.8353 10.0406 11.0742 9.78974C11.1599 9.69972 11.2345 9.70014 11.3168 9.78034C11.5153 9.97367 11.7146 10.1664 11.9069 10.3658C11.9328 10.3927 11.9426 10.4804 11.9218 10.5006C11.4163 10.9902 10.9759 11.5612 10.3258 11.8767C9.83564 12.1146 9.31537 12.2104 8.76205 12.1536C8.22187 12.0982 7.72081 11.9469 7.28796 11.6243C7.00273 11.4118 6.74299 11.1618 6.48871 10.9115C5.85764 10.2901 5.23675 9.65839 4.61078 9.03182C4.18942 8.61005 3.76551 8.19083 3.34425 7.76896C2.67919 7.10292 2.01565 6.43536 1.3508 5.7691C1.23233 5.65038 1.1107 5.53483 1.00001 5.42706Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="0.5"
      />
    </svg>
  );
};

export default IconXFactorHeavy;
