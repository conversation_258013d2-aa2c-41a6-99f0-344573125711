import React, { SVGProps } from 'react';

type IconAwardProps = SVGProps<SVGSVGElement>;

const IconAward: React.FC<IconAwardProps> = props => {
  return (
    <svg
      width="68"
      height="69"
      viewBox="0 0 68 69"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g filter="url(#filter0_d_2345_55879)">
        <rect
          x="14"
          y="10.1094"
          width="40"
          height="40"
          rx="20"
          fill="url(#paint0_linear_2345_55879)"
          shapeRendering="crispEdges"
        />
        <rect
          x="13"
          y="9.10938"
          width="42"
          height="42"
          rx="21"
          stroke="url(#paint1_linear_2345_55879)"
          strokeWidth="2"
          shapeRendering="crispEdges"
        />
        <g filter="url(#filter1_d_2345_55879)">
          <path
            d="M25.75 23.3594C25.75 20.5 28.0938 18.1094 31 18.1094H31.75C32.125 18.1094 32.5 18.4844 32.5 18.8594V19.6094C32.5 22.4688 30.25 24.7656 27.4844 24.8594C26.3594 26.4062 25.75 28.2344 25.75 30.1094C25.75 34.7031 29.4062 38.3594 34 38.3594H34.5156C36.5781 38.3594 38.5469 38.875 40.375 39.7656L40.8438 40C41.4062 40.2812 41.6406 40.9375 41.3594 41.5C41.0781 42.0625 40.4219 42.2969 39.8594 42.0156L39.3906 41.7812C37.8906 41.0312 36.2031 40.6094 34.5156 40.6094H34H33.4375C31.75 40.6094 30.0625 41.0312 28.5625 41.7812L28.0938 42.0156C27.5312 42.2969 26.875 42.0625 26.5938 41.5C26.3125 40.9375 26.5469 40.2812 27.1094 40L27.5781 39.7656C27.9531 39.5781 28.3281 39.3906 28.75 39.25C28.4688 39.1094 28.2344 38.9219 28 38.7812C25.1875 40.0938 21.8125 39.0625 20.2188 36.3438L19.8438 35.6875C19.6562 35.3125 19.7969 34.8438 20.125 34.6562C20.7812 34.2812 21.4844 34.0469 22.1875 33.9062C20.2656 32.9219 19 30.9062 19 28.6094V27.8594C19 27.4844 19.3281 27.1094 19.75 27.1094C20.4531 27.1094 21.2031 27.25 21.8594 27.5312C20.6875 25.7031 20.5938 23.3125 21.7188 21.3438L22.0938 20.6875C22.3281 20.3125 22.7969 20.2188 23.125 20.4062C24.3906 21.1094 25.2344 22.1875 25.75 23.4062V23.3594ZM40.5156 24.8594C37.7031 24.7656 35.5 22.4688 35.5 19.6094V18.8594C35.5 18.4844 35.8281 18.1094 36.25 18.1094H37C39.8594 18.1094 42.25 20.5 42.25 23.3594V23.4062C42.7188 22.1875 43.6094 21.1094 44.8281 20.4062C45.2031 20.2188 45.625 20.3125 45.8594 20.6875L46.2344 21.3438C47.3594 23.3125 47.2656 25.7031 46.0938 27.5312C46.7969 27.25 47.5 27.1562 48.25 27.1562C48.625 27.1562 49 27.4844 49 27.9062V28.6562C49 30.9531 47.6875 32.9219 45.8125 33.9531C46.4688 34.0469 47.1719 34.2812 47.8281 34.6562C48.2031 34.8906 48.2969 35.3125 48.1094 35.6875L47.7344 36.3438C46.6094 38.3125 44.5 39.3906 42.3906 39.3438C42.1562 39.0625 41.875 38.8281 41.5469 38.6875L41.0781 38.4531C40.9375 38.3594 40.7969 38.3125 40.6562 38.2188C40.1875 38.0312 39.6719 37.7969 39.1562 37.6562C38.8281 37.5156 38.4531 37.4219 38.0312 37.3281C40.5625 35.875 42.25 33.2031 42.25 30.1094C42.25 28.2344 41.5938 26.4062 40.5156 24.8594Z"
            fill="white"
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_d_2345_55879"
          x="0"
          y="0.109375"
          width="68"
          height="68"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="6" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2345_55879" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_2345_55879"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_d_2345_55879"
          x="14"
          y="13.1094"
          width="40"
          height="34.1875"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="2.5" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.711247 0 0 0 0 0.551912 0 0 0 0 0.028385 0 0 0 1 0"
          />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2345_55879" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_2345_55879"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_2345_55879"
          x1="19.5"
          y1="15.6094"
          x2="48"
          y2="44.1094"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFEFB8" />
          <stop offset="1" stopColor="#D2A100" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_2345_55879"
          x1="19"
          y1="11.6094"
          x2="57.5147"
          y2="32.1221"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#D7DCDE" />
          <stop offset="0.385" stopColor="white" />
          <stop offset="1" stopColor="#D7DCDE" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default IconAward;
