'use client';

import React, { Fragment, useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import {
  faArrowRightFromBracket,
  faBars,
  faGear as faGearRegular,
  faUserCircle,
  faXmark,
} from '@fortawesome/pro-regular-svg-icons';
import { faGear as faGearSolid } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  Transition,
  TransitionChild,
} from '@headlessui/react';
import { useScrollStore } from '@/stores/useScrollStore';
import DrawerNavItem from './DrawerNavItem';
import HeaderNavItem from './HeaderNavItem';
import { NavItemProps } from './NavItem';

interface HeaderProps {
  navigationItems: NavItemProps[];
  ctaLabel?: string;
  ctaHref?: string;
  displayNavigationDesktop?: boolean;
  displayAdminAndLogout?: boolean;
  displayLogin?: boolean;
}

export default function Header({
  navigationItems,
  ctaLabel,
  ctaHref,
  displayNavigationDesktop = false,
  displayAdminAndLogout = false,
  displayLogin = false,
}: HeaderProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { isAboveFold, isScrollUp, initializeScroll, updateScroll } = useScrollStore();
  const pathname = usePathname() || '';

  // Check if CTA href is an external link
  const isCtaExternal = ctaHref
    ? ctaHref.startsWith('http://') || ctaHref.startsWith('https://')
    : false;

  useEffect(() => {
    // Initialize scroll values
    initializeScroll();

    // Add scroll event listener
    const handleScroll = () => {
      updateScroll(window.scrollY);
    };

    // Add resize event listener
    const handleResize = () => {
      initializeScroll();
    };

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, [initializeScroll, updateScroll]);

  return (
    <>
      {/* Mobile Header */}
      <header
        className={`fixed top-0 left-0 right-0 h-20 bg-white border-b border-gray-50 shadow-header z-50 
          transition-transform duration-300 ease-in-out
          ${!isAboveFold && !isScrollUp && !mobileMenuOpen ? '-translate-y-full' : 'translate-y-0'}`}
      >
        <div className="pa-container flex items-center gap-x-4 justify-between px-4 h-full">
          <Link href="/dashboard" aria-label="Dashboard">
            <Image
              src="/images/positive-athlete.svg"
              alt="Positive Athlete"
              width={100}
              height={28}
              className="h-7 w-auto"
            />
          </Link>

          <nav className="flex items-center gap-4 justify-end">
            {displayNavigationDesktop && (
              <ul className="hidden lg:flex items-center gap-x-6">
                {navigationItems.map(item => {
                  const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`);

                  return (
                    <li key={item.href}>
                      <HeaderNavItem
                        href={item.href}
                        label={item.label}
                        isActive={isActive}
                        iconActive={item.iconActive}
                        iconInactive={item.iconInactive}
                        badge={item.badge}
                        showIcon={false}
                      />
                    </li>
                  );
                })}

                {/* {displayAdminAndLogout && (
                  <>
                    <li>
                      <HeaderNavItem
                        href="/admin"
                        label="Settings"
                        isActive={false}
                        iconActive={faGear}
                        iconInactive={faGear}
                      />
                    </li>

                    <li>
                      <HeaderNavItem
                        href="/logout"
                        label="Logout"
                        isActive={false}
                        iconActive={faArrowRightFromBracket}
                        iconInactive={faArrowRightFromBracket}
                      />
                    </li>
                  </>
                )} */}

                {displayLogin && (
                  <li>
                    <Link
                      href="/login"
                      className="flex items-center gap-2 text-sm text-brand-blue font-semibold px-4 py-2 rounded-md bg-surface-secondary transition-colors hover:bg-neutral-200"
                      aria-label="Log In"
                    >
                      Log In
                    </Link>
                  </li>
                )}

                {ctaLabel && ctaHref && (
                  <li>
                    <Link
                      href={ctaHref}
                      className="bg-brand-blue p-4 text-sm text-white font-semibold rounded-md transition-colors hover:bg-slate-900"
                      aria-label={ctaLabel}
                      target={isCtaExternal ? '_blank' : undefined}
                      rel={isCtaExternal ? 'noopener noreferrer' : undefined}
                    >
                      {ctaLabel}
                    </Link>
                  </li>
                )}
              </ul>
            )}

            <button
              type="button"
              className="text-gray-700 lg:hidden"
              onClick={() => setMobileMenuOpen(true)}
              aria-label="Open menu"
            >
              <span className="sr-only">Open menu</span>
              <FontAwesomeIcon icon={faBars} className="size-4" aria-label="hidden" />
            </button>
          </nav>
        </div>
      </header>

      {/* Mobile Navigation Drawer */}
      <Dialog open={mobileMenuOpen} onClose={setMobileMenuOpen} className="relative z-50">
        <DialogBackdrop
          transition
          className="fixed inset-0 bg-gray-500/75 transition-opacity duration-500 ease-in-out data-[closed]:opacity-0"
        />

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <DialogPanel
                transition
                className="pointer-events-auto relative w-screen max-w-sm transform transition duration-500 ease-in-out data-[closed]:translate-x-full"
              >
                <div className="flex h-svh flex-col bg-white shadow-xl">
                  <div className="flex items-center justify-between px-4 py-4 border-b border-gray-100">
                    <Link href="/dashboard">
                      <Image
                        src="/images/positive-athlete.svg"
                        alt="Positive Athlete"
                        width={100}
                        height={28}
                        className="h-7 w-auto"
                      />
                    </Link>
                    <button
                      type="button"
                      className="p-2 text-gray-700"
                      onClick={() => setMobileMenuOpen(false)}
                      aria-label="Close menu"
                    >
                      <span className="sr-only">Close menu</span>
                      <FontAwesomeIcon icon={faXmark} className="size-4" aria-hidden="true" />
                    </button>
                  </div>

                  <nav className="flex-1 flex flex-col justify-between p-4">
                    <div className="space-y-1">
                      {navigationItems.map(item => {
                        const isActive =
                          pathname === item.href || pathname.startsWith(`${item.href}/`);

                        return (
                          <DrawerNavItem
                            key={item.href}
                            href={item.href}
                            label={item.label}
                            isActive={isActive}
                            iconActive={item.iconActive}
                            iconInactive={item.iconInactive}
                            badge={item.badge}
                            onClick={() => setMobileMenuOpen(false)}
                          />
                        );
                      })}
                    </div>

                    {(displayAdminAndLogout || (ctaLabel && ctaHref) || displayLogin) && (
                      <div className="space-y-3 pt-4 border-t border-gray-100">
                        {displayAdminAndLogout && (
                          <>
                            <DrawerNavItem
                              href="/account"
                              label="Account"
                              isActive={pathname === '/account' || pathname.startsWith('/account/')}
                              iconActive={faGearSolid}
                              iconInactive={faGearRegular}
                              onClick={() => setMobileMenuOpen(false)}
                            />

                            <DrawerNavItem
                              href="/logout"
                              label="Logout"
                              isActive={false}
                              iconActive={faArrowRightFromBracket}
                              iconInactive={faArrowRightFromBracket}
                              onClick={() => setMobileMenuOpen(false)}
                            />
                          </>
                        )}

                        {displayLogin && (
                          <Link
                            href="/login"
                            className="block w-full p-4 rounded-md text-center text-brand-blue text-sm bg-surface-secondary font-semibold"
                            aria-label="Log In"
                            onClick={() => setMobileMenuOpen(false)}
                          >
                            Log In
                          </Link>
                        )}

                        {ctaLabel && ctaHref && (
                          <Link
                            href={ctaHref}
                            className="block w-full p-4 rounded-md text-center text-white text-sm bg-brand-blue font-semibold"
                            aria-label={ctaLabel}
                            onClick={() => setMobileMenuOpen(false)}
                            target={isCtaExternal ? '_blank' : undefined}
                            rel={isCtaExternal ? 'noopener noreferrer' : undefined}
                          >
                            {ctaLabel}
                          </Link>
                        )}
                      </div>
                    )}
                  </nav>
                </div>
              </DialogPanel>
            </div>
          </div>
        </div>
      </Dialog>
    </>
  );
}
