import {
  faRectanglesMixed as DashboardRegularIcon,
  faMessage as MessagesRegularIcon,
  faGraduationCap as MySchoolRegularIcon,
  faShareNodes as NetworkRegularIcon,
  faBriefcase as OpportunitiesRegularIcon,
  faUserCircle as ProfileRegularIcon,
} from '@fortawesome/pro-regular-svg-icons';
import {
  faRectanglesMixed as DashboardSolidIcon,
  faMessage as MessagesSolidIcon,
  faGraduationCap as MySchoolSolidIcon,
  faShareNodes as NetworkSolidIcon,
  faBriefcase as OpportunitiesSolidIcon,
  faUserCircle as ProfileSolidIcon,
} from '@fortawesome/pro-solid-svg-icons';
import type { NavItemProps } from '@/components/navigation/NavItem';
import IconXFactor from '@/components/svgs/IconXFactor';
import IconXFactorHeavy from '@/components/svgs/IconXFactorHeavy';

type NavItem = NavItemProps;

export const authenticatedNavigationItems: NavItem[] = [
  {
    href: '/dashboard',
    iconActive: DashboardSolidIcon,
    iconInactive: DashboardRegularIcon,
    label: 'Dashboard',
  },
  {
    href: '/profile',
    iconActive: ProfileSolidIcon,
    iconInactive: ProfileRegularIcon,
    label: 'My Profile',
  },
  {
    href: '/x-factor',
    iconActive: IconXFactorHeavy,
    iconInactive: IconXFactor,
    label: 'X Factor',
  },
  {
    href: '/network',
    iconActive: NetworkSolidIcon,
    iconInactive: NetworkRegularIcon,
    label: 'Network',
  },
  {
    href: '/opportunities',
    iconActive: OpportunitiesSolidIcon,
    iconInactive: OpportunitiesRegularIcon,
    label: 'Opportunities',
  },
  {
    href: '/messages',
    iconActive: MessagesSolidIcon,
    iconInactive: MessagesRegularIcon,
    label: 'Messages',
    // badge: 0,
  },
];

export const authenticatedCoachNavigationItems: NavItem[] = [
  {
    href: '/profile',
    iconActive: ProfileSolidIcon,
    iconInactive: ProfileRegularIcon,
    label: 'My Profile',
  },
  {
    href: '/network',
    iconActive: NetworkSolidIcon,
    iconInactive: NetworkRegularIcon,
    label: 'Network',
  },
  {
    href: '/messages',
    iconActive: MessagesSolidIcon,
    iconInactive: MessagesRegularIcon,
    label: 'Messages',
    // badge: 0,
  },
];

/**
 * Navigation items for parent users
 * Only includes Profile, Network, Opportunities, and Messages routes
 * Excludes Dashboard and X Factor routes intentionally
 */
export const authenticatedParentNavigationItems: NavItem[] = [
  {
    href: '/profile',
    iconActive: ProfileSolidIcon,
    iconInactive: ProfileRegularIcon,
    label: 'My Profile',
  },
  {
    href: '/network',
    iconActive: NetworkSolidIcon,
    iconInactive: NetworkRegularIcon,
    label: 'Network',
  },
  {
    href: '/opportunities',
    iconActive: OpportunitiesSolidIcon,
    iconInactive: OpportunitiesRegularIcon,
    label: 'Opportunities',
  },
  {
    href: '/messages',
    iconActive: MessagesSolidIcon,
    iconInactive: MessagesRegularIcon,
    label: 'Messages',
  },
];

export const authenticatedSponsorNavigationItems: NavItem[] = [
  {
    href: '/network',
    iconActive: NetworkSolidIcon,
    iconInactive: NetworkRegularIcon,
    label: 'Athletes',
  },
  {
    href: '/postings',
    iconActive: OpportunitiesSolidIcon,
    iconInactive: OpportunitiesRegularIcon,
    label: 'Postings',
  },
  {
    href: '/messages',
    iconActive: MessagesSolidIcon,
    iconInactive: MessagesRegularIcon,
    label: 'Messages',
  },
];

export const authenticatedADNavigationItems: NavItem[] = [
  {
    href: '/my-school',
    iconActive: MySchoolSolidIcon,
    iconInactive: MySchoolRegularIcon,
    label: 'My School',
  },
  {
    href: '/messages',
    iconActive: MessagesSolidIcon,
    iconInactive: MessagesRegularIcon,
    label: 'Messages',
  },
  {
    href: '/profile',
    iconActive: ProfileSolidIcon,
    iconInactive: ProfileRegularIcon,
    label: 'My Profile',
  },
];

export const publicNavigationItems: NavItem[] = [
  {
    href: 'https://www.positiveathlete.org/',
    label: 'About',
  },
  // {
  //   href: 'https://www.positiveathlete.org/positiveathleteapp',
  //   label: 'Athletes',
  // },
  {
    href: 'https://www.positiveathlete.org/regions',
    label: 'Markets',
  },
  {
    href: 'https://www.positiveathlete.org/video',
    label: 'Videos',
  },
  // {
  //   href: '/store',
  //   label: 'Store',
  // },
];
