'use client';

import React from 'react';
import Link from 'next/link';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { NavItemProps } from './NavItem';

interface SidebarNavItemProps extends NavItemProps {
  isActive: boolean;
  isExpanded: boolean;
}

const SidebarNavItem: React.FC<SidebarNavItemProps> = ({
  href,
  label,
  isActive,
  isExpanded,
  iconActive,
  iconInactive,
  badge,
}) => {
  const Icon = isActive ? iconActive : iconInactive;

  return (
    <Link
      href={href}
      className={`flex items-center justify-around p-2 gap-x-2 text-sm text-brand-blue rounded-lg group 
        ${isActive ? 'bg-surface-secondary font-bold' : 'font-normal hover:bg-surface-secondary'}`}
      aria-label={label}
      title={label}
    >
      {Icon &&
        (typeof Icon === 'function' ? (
          <Icon className="!size-4 shrink-0 text-current" aria-hidden="true" />
        ) : (
          <FontAwesomeIcon
            icon={Icon}
            className="!size-4 shrink-0 text-current"
            aria-hidden="true"
          />
        ))}

      {isExpanded && (
        <>
          <span className="flex-1 transition-opacity duration-300 whitespace-nowrap">{label}</span>
          {badge && (
            <span className="bg-brand-red text-white text-xs font-medium flex items-center justify-center size-4 rounded-full transition-opacity duration-300">
              {badge}
            </span>
          )}
        </>
      )}
    </Link>
  );
};

export default SidebarNavItem;
