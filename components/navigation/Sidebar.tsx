'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import {
  faArrowRightFromBracket,
  faChevronLeft,
  faChevronRight,
  faGear as faGearRegular,
} from '@fortawesome/pro-regular-svg-icons';
import { faGear as faGearSolid } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useSidebarStore } from '@/stores/useSidebarStore';
import { NavItemProps } from './NavItem';
import SidebarNavItem from './SidebarNavItem';

interface SidebarProps {
  navigationItems: NavItemProps[];
}

export default function Sidebar({ navigationItems }: SidebarProps) {
  const { isExpanded, toggle } = useSidebarStore();
  const pathname = usePathname() || '';

  return (
    <aside
      className={`fixed left-0 top-0 bottom-0 flex-col justify-between gap-y-4 h-screen shadow-sidebar bg-white border-r border-gray-50 z-50 overflow-visible hidden lg:flex
        transition-all duration-300 ease-in-out ${isExpanded ? 'w-40' : 'w-20'}`}
    >
      <nav className="px-2 pb-6 pt-12">
        <div className="flex items-center justify-around relative">
          <Link href="/dashboard" className="block mb-16" aria-label="Dashboard">
            {isExpanded ? (
              <Image
                src="/images/positive-athlete.svg"
                alt="Positive Athlete"
                width={120}
                height={32}
                className="transition-opacity duration-300"
              />
            ) : (
              <Image
                src="/images/positive-athlete-icon.svg"
                alt="Positive Athlete"
                width={32}
                height={32}
                className="transition-opacity duration-300 !size-8"
              />
            )}
          </Link>

          <button
            type="button"
            className="text-text-secondary absolute -right-5 top-12 size-6 flex items-center justify-center rounded-full border border-gray-300 bg-white hover:text-[#002B5C] transition-colors"
            onClick={toggle}
            aria-label="Toggle menu"
          >
            <span className="sr-only">Toggle menu</span>
            <FontAwesomeIcon
              icon={isExpanded ? faChevronLeft : faChevronRight}
              className="size-4 pointer-events-none"
              aria-hidden="true"
            />
          </button>
        </div>

        <div className="space-y-1 relaive overflow-hidden">
          {navigationItems.map(item => {
            const isActive = pathname === item.href || pathname.startsWith(`${item.href}/`);

            return (
              <SidebarNavItem
                key={item.href}
                href={item.href}
                label={item.label}
                isActive={isActive}
                isExpanded={isExpanded}
                iconActive={item.iconActive}
                iconInactive={item.iconInactive}
                badge={item.badge}
              />
            );
          })}
        </div>
      </nav>

      <nav className="space-y-2 px-2 py-6">
        <SidebarNavItem
          href="/account"
          label="Account"
          isActive={pathname === '/account' || pathname.startsWith('/account/')}
          isExpanded={isExpanded}
          iconActive={faGearSolid}
          iconInactive={faGearRegular}
        />

        <SidebarNavItem
          href="/logout"
          label="Logout"
          isActive={false}
          isExpanded={isExpanded}
          iconActive={faArrowRightFromBracket}
          iconInactive={faArrowRightFromBracket}
        />
      </nav>
    </aside>
  );
}
