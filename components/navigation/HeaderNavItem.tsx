'use client';

import React from 'react';
import Link from 'next/link';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { NavItemProps } from './NavItem';

interface HeaderNavItemProps extends NavItemProps {
  isActive: boolean;
  showIcon?: boolean;
}

const HeaderNavItem: React.FC<HeaderNavItemProps> = ({
  href,
  label,
  isActive,
  iconActive,
  iconInactive,
  badge,
  showIcon = false,
}) => {
  const Icon = isActive ? iconActive : iconInactive;
  const isExternalLink = href.startsWith('http://') || href.startsWith('https://');

  return (
    <Link
      href={href}
      className={`flex items-center gap-2 text-sm text-brand-blue hover:text-black ${isActive ? 'font-bold' : 'font-regular'}`}
      aria-label={label}
      target={isExternalLink ? '_blank' : undefined}
      rel={isExternalLink ? 'noopener noreferrer' : undefined}
    >
      {showIcon &&
        Icon &&
        (typeof Icon === 'function' ? (
          <Icon className="size-4 text-current" aria-hidden="true" />
        ) : (
          <FontAwesomeIcon icon={Icon} className="size-4 text-current" aria-hidden="true" />
        ))}

      <span>{label}</span>

      {badge && (
        <span className="bg-brand-red text-white text-xs font-medium flex items-center justify-center size-4 rounded-full">
          {badge}
        </span>
      )}
    </Link>
  );
};

export default HeaderNavItem;
