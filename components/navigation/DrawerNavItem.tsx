'use client';

import React from 'react';
import Link from 'next/link';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { NavItemProps } from './NavItem';

interface DrawerNavItemProps extends NavItemProps {
  isActive: boolean;
  onClick?: () => void;
}

const DrawerNavItem: React.FC<DrawerNavItemProps> = ({
  href,
  label,
  isActive,
  iconActive,
  iconInactive,
  badge,
  onClick,
}) => {
  const Icon = isActive ? iconActive : iconInactive;
  const isExternalLink = href.startsWith('http://') || href.startsWith('https://');

  return (
    <Link
      href={href}
      className={`flex items-center px-4 py-2 rounded-lg group text-brand-blue
        ${isActive ? 'bg-surface-secondary font-bold' : 'font-normal hover:bg-gray-50'}`}
      onClick={onClick}
      target={isExternalLink ? '_blank' : undefined}
      rel={isExternalLink ? 'noopener noreferrer' : undefined}
    >
      {Icon &&
        (typeof Icon === 'function' ? (
          <Icon className="size-4 mr-2 text-current" />
        ) : (
          <FontAwesomeIcon icon={Icon} className="size-4 mr-2 text-current" aria-hidden="true" />
        ))}

      <span className="flex-1">{label}</span>
      {badge && (
        <span className="bg-brand-red text-white text-xs font-medium flex items-center justify-center size-4 rounded-full">
          {badge}
        </span>
      )}
    </Link>
  );
};

export default DrawerNavItem;
