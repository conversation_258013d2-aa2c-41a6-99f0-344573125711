'use client';

import { Fragment, useEffect } from 'react';
import {
  faCheckCircle,
  faCircleInfo,
  faCircleXmark,
  faTriangleExclamation,
  faXmark,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Transition } from '@headlessui/react';
import clsx from 'clsx';
import { ToastType, useToastNotify } from '@/stores/toastNotify.store';

export default function ToastNotify() {
  const { isVisible, message, type, closeToastNotify } = useToastNotify();

  // Map toast types to icons and styles
  const toastConfig: Record<
    ToastType,
    {
      icon: typeof faCheckCircle;
      bgColor: string;
      textColor: string;
      importance: 'polite' | 'assertive';
    }
  > = {
    success: {
      icon: faCheckCircle,
      bgColor: 'bg-green-50',
      textColor: 'text-green-600',
      importance: 'polite',
    },
    info: {
      icon: faCircleInfo,
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600',
      importance: 'polite',
    },
    warning: {
      icon: faTriangleExclamation,
      bgColor: 'bg-amber-50',
      textColor: 'text-amber-600',
      importance: 'polite',
    },
    error: {
      icon: faCircleXmark,
      bgColor: 'bg-red-50',
      textColor: 'text-red-600',
      importance: 'assertive',
    },
  };

  const { icon, bgColor, textColor, importance } = toastConfig[type];

  // Handle Escape key press to dismiss the toast
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isVisible) {
        closeToastNotify();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isVisible, closeToastNotify]);

  return (
    <Transition
      show={isVisible}
      as={Fragment}
      enter="transform transition ease-out duration-300"
      enterFrom="translate-x-full opacity-0"
      enterTo="translate-x-0 opacity-100"
      leave="transform transition ease-in duration-200"
      leaveFrom="translate-x-0 opacity-100"
      leaveTo="translate-x-full opacity-0"
    >
      <div
        role="alert"
        aria-live={importance}
        aria-atomic="true"
        className="fixed bottom-4 right-4 z-[60] max-w-md shadow-md ring-1 ring-gray-100 rounded-lg overflow-hidden"
      >
        <div className={clsx('flex items-start gap-3 p-4 pr-8', bgColor, textColor)}>
          <div className="flex-shrink-0" aria-hidden="true">
            <FontAwesomeIcon icon={icon} className="h-5 w-5" aria-hidden="true" />
          </div>
          <div className="flex-1 pt-0.5">
            <p id="toast-message" className="text-sm font-medium">
              {message}
            </p>
          </div>
          <button
            type="button"
            className="flex-shrink-0 ml-auto"
            onClick={closeToastNotify}
            aria-label="Close notification"
            aria-describedby="toast-message"
          >
            <FontAwesomeIcon icon={faXmark} className="h-4 w-4 text-gray-500" aria-hidden="true" />
          </button>
        </div>
      </div>
    </Transition>
  );
}
