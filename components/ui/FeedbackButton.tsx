import { faXmark } from '@fortawesome/pro-light-svg-icons';
import { faMessageLines } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

interface FeedbackButtonProps {
  isFeedbackVisible: boolean;
  handleRemoveFeedback: () => void;
}

const CLICKUP_FEEDBACK_FORM_URL =
  'https://forms.clickup.com/2293136/f/25zcg-48317/NVEYEKK1V1UJ9VPSSW?Device%20Info=xxxxx';

const FeedbackButton: React.FC<FeedbackButtonProps> = ({
  isFeedbackVisible,
  handleRemoveFeedback,
}) => {
  if (!isFeedbackVisible) {
    return null;
  }

  return (
    <div className="fixed right-4 bottom-4 z-50 flex flex-nowrap items-center gap-x-2">
      <a
        href={CLICKUP_FEEDBACK_FORM_URL}
        className="flex shrink-0 items-center gap-x-2 whitespace-nowrap rounded bg-brand-red px-4 py-2 text-xs font-bold uppercase leading-none tracking-wide text-white shadow-lg hover:bg-red-700 hover:text-white"
        target="_blank"
        rel="noopener noreferrer"
        aria-label="Send Feedback"
      >
        <FontAwesomeIcon icon={faMessageLines} className="size-4 text-current" aria-hidden="true" />
        Send Feedback
      </a>

      <button
        className="flex size-4 items-center justify-center rounded-full bg-white text-black"
        onClick={handleRemoveFeedback}
        aria-label="Remove Feedback Button"
      >
        <span className="sr-only">Remove Feedback Button</span>

        <FontAwesomeIcon icon={faXmark} className="size-3 text-current" aria-hidden="true" />
      </button>
    </div>
  );
};

export default FeedbackButton;
