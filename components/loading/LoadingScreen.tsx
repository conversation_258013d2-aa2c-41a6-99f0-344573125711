'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';

interface LoadingScreenProps {
  text?: string;
  content?: React.ReactNode;
}

export default function LoadingScreen({
  text = 'Loading Positive Athlete...',
  content,
}: LoadingScreenProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Only show loading screen if we're mounted (prevents flash on hydration)
  if (!mounted) return null;

  return (
    <div className="fixed inset-0 bg-white z-[9999] flex items-center justify-center">
      <div className="flex flex-col items-center gap-6">
        <Image
          src="/images/positive-athlete.svg"
          alt="Positive Athlete"
          width={120}
          height={32}
          priority
          className="mb-2"
        />
        <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin" />

        {content ? content : <p className="text-gray-600 font-medium">{text}</p>}
      </div>
    </div>
  );
}
