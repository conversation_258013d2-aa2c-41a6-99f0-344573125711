'use client';

import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useDraggable,
  useDroppable,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { faGripVertical, faPlus, faTrash } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import SiteInput from '@/components/shared/form/SiteInput';
import { Textarea } from '@/components/shared/form/Textarea';
import { FormActions } from '@/components/shared/FormActions';

export interface WorkExperience {
  id: string;
  title: string;
  dateRange: string;
  description: string;
}

interface WorkExperienceFormProps {
  items: WorkExperience[];
  isLoading?: boolean;
  error?: string | null;
  onChange: (items: WorkExperience[]) => void;
  onSave?: () => Promise<void>;
  onCancel?: () => void;
  showActions?: boolean;
}

interface DroppableContainerProps {
  id: string;
  children: React.ReactNode;
}

function DroppableContainer({ id, children }: DroppableContainerProps) {
  const { setNodeRef } = useDroppable({ id });
  return <div ref={setNodeRef}>{children}</div>;
}

interface DraggableWorkExperienceItemProps {
  item: WorkExperience;
  onUpdateItem: (updates: Partial<WorkExperience>) => void;
  onRemove: () => void;
}

function DraggableWorkExperienceItem({
  item,
  onUpdateItem,
  onRemove,
}: DraggableWorkExperienceItemProps) {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({
    id: item.id,
  });

  const style = transform
    ? {
        transform: CSS.Transform.toString(transform),
      }
    : undefined;

  return (
    <div ref={setNodeRef} style={style} className="flex gap-4 items-start touch-none mb-4">
      <div {...attributes} {...listeners} className="pt-3 cursor-grab text-gray-400">
        <FontAwesomeIcon icon={faGripVertical} />
      </div>
      <div className="flex-1 space-y-4">
        <SiteInput
          label="Job Title"
          value={item.title}
          onChange={e => onUpdateItem({ title: e.target.value })}
          placeholder="Job Title"
          hideLabel
        />
        <SiteInput
          label="Date Range"
          type="text"
          value={item.dateRange}
          onChange={e => onUpdateItem({ dateRange: e.target.value })}
          placeholder="Date Range (e.g., 2020-2022)"
          hideLabel
        />
        <Textarea
          label="Description"
          value={item.description}
          onChange={e => onUpdateItem({ description: e.target.value })}
          placeholder="Describe your responsibilities and achievements"
          rows={3}
          hideLabel
        />
      </div>
      <button onClick={onRemove} className="pt-3 text-gray-400 hover:text-red-600">
        <FontAwesomeIcon icon={faTrash} />
      </button>
    </div>
  );
}

export function WorkExperienceForm({
  items,
  isLoading,
  error,
  onChange,
  onSave,
  onCancel,
  showActions = true,
}: WorkExperienceFormProps) {
  const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor));

  const handleAddItem = () => {
    const newItem: WorkExperience = {
      id: crypto.randomUUID(),
      title: '',
      dateRange: '',
      description: '',
    };
    onChange([...items, newItem]);
  };

  const handleRemoveItem = (id: string) => {
    onChange(items.filter(item => item.id !== id));
  };

  const handleUpdateItem = (id: string, updates: Partial<WorkExperience>) => {
    onChange(items.map(item => (item.id === id ? { ...item, ...updates } : item)));
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = items.findIndex(item => item.id === active.id);
      const newIndex = items.findIndex(item => item.id === over.id);

      // Create new array with reordered items
      const newItems = [...items];
      const [movedItem] = newItems.splice(oldIndex, 1);
      newItems.splice(newIndex, 0, movedItem);

      onChange(newItems);
    }
  };

  return (
    <div className="space-y-6">
      {error && <p className="text-red-600">{error}</p>}

      <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <div className="space-y-4">
          {items.map(item => (
            <DroppableContainer key={item.id} id={item.id}>
              <DraggableWorkExperienceItem
                item={item}
                onUpdateItem={updates => handleUpdateItem(item.id, updates)}
                onRemove={() => handleRemoveItem(item.id)}
              />
            </DroppableContainer>
          ))}
        </div>
      </DndContext>

      <Button
        color="white"
        variant="text"
        onClick={handleAddItem}
        className="w-full"
        icon={faPlus}
        iconPosition="left"
      >
        Add Work Experience
      </Button>

      {showActions && onSave && onCancel && (
        <FormActions onSave={onSave} onCancel={onCancel} isLoading={isLoading} className="mt-6" />
      )}
    </div>
  );
}
