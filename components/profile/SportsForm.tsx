'use client';

import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useDraggable,
  useDroppable,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { faGripVertical, faTrash } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import SelectInput from '@/components/shared/form/SelectInput';
import SiteInput from '@/components/shared/form/SiteInput';
import { FormActions } from '@/components/shared/FormActions';
import { useSportsStore } from '@/stores/sportsStore';
import { SportEntry } from '@/types/profile';

interface SportsFormProps {
  sports: SportEntry[];
  isLoading?: boolean;
  error?: string | null;
  onChange: (sports: SportEntry[]) => void;
  onSave?: () => Promise<void>;
  onCancel?: () => void;
  showActions?: boolean;
}

interface DroppableContainerProps {
  id: string;
  children: React.ReactNode;
}

function DroppableContainer({ id, children }: DroppableContainerProps) {
  const { setNodeRef } = useDroppable({ id });
  return <div ref={setNodeRef}>{children}</div>;
}

interface DraggableSportItemProps {
  sport: SportEntry;
  sportOptions: { label: string; value: string }[];
  onUpdateSport: (value: string) => void;
  onUpdateCustomName: (value: string) => void;
  onRemove: () => void;
}

function DraggableSportItem({
  sport,
  sportOptions,
  onUpdateSport,
  onUpdateCustomName,
  onRemove,
}: DraggableSportItemProps) {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({
    id: sport.id,
  });

  const style = transform
    ? {
        transform: CSS.Transform.toString(transform),
      }
    : undefined;

  return (
    <div ref={setNodeRef} style={style} className="flex gap-4 items-start touch-none mb-4">
      <div {...attributes} {...listeners} className="pt-3 cursor-grab text-gray-400">
        <FontAwesomeIcon icon={faGripVertical} />
      </div>
      <div className="flex-1 space-y-2">
        <SelectInput
          label="Sport"
          value={sport.sport}
          onChange={onUpdateSport}
          options={sportOptions}
          placeholder="Select a sport"
          hideLabel
        />
        {sport.type === 'custom' && (
          <SiteInput
            label="Sport Name"
            value={sport.customName || ''}
            onChange={e => onUpdateCustomName(e.target.value)}
            placeholder="Enter sport name"
            hideLabel
          />
        )}
      </div>
      <button onClick={onRemove} className="pt-3 text-gray-400 hover:text-red-600">
        <FontAwesomeIcon icon={faTrash} />
      </button>
    </div>
  );
}

export function SportsForm({
  sports,
  isLoading,
  error,
  onChange,
  onSave,
  onCancel,
  showActions = true,
}: SportsFormProps) {
  const availableSports = useSportsStore(state => state.sports);

  const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor));

  const handleAddSport = () => {
    onChange([...sports, { id: crypto.randomUUID(), type: 'predefined', sport: '' }]);
  };

  const handleRemoveSport = (index: number) => {
    onChange(sports.filter((_, i) => i !== index));
  };

  const handleUpdateSport = (index: number, value: string) => {
    const newSports = [...sports];
    const sport = availableSports.find(s => s.slug === value);

    newSports[index] = {
      ...newSports[index],
      sport: value,
      type: value === 'custom' ? 'custom' : 'predefined',
      customName: value === 'custom' ? '' : undefined,
    };
    onChange(newSports);
  };

  const handleUpdateCustomName = (index: number, value: string) => {
    const newSports = [...sports];
    newSports[index] = {
      ...newSports[index],
      customName: value,
    };
    onChange(newSports);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = sports.findIndex(sport => sport.id === active.id);
      const newIndex = sports.findIndex(sport => sport.id === over.id);

      // Create new array with reordered items
      const newSports = [...sports];
      const [movedItem] = newSports.splice(oldIndex, 1);
      newSports.splice(newIndex, 0, movedItem);

      onChange(newSports);
    }
  };

  const sportOptions = [
    ...availableSports.map(sport => ({
      label: sport.label,
      value: sport.slug,
    })),
    { label: 'Other', value: 'custom' },
  ];

  return (
    <div className="space-y-6">
      {error && <p className="text-red-600">{error}</p>}

      <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <div className="space-y-4">
          {sports.map((sport, index) => (
            <DroppableContainer key={sport.id} id={sport.id}>
              <DraggableSportItem
                sport={sport}
                sportOptions={sportOptions}
                onUpdateSport={value => handleUpdateSport(index, value)}
                onUpdateCustomName={value => handleUpdateCustomName(index, value)}
                onRemove={() => handleRemoveSport(index)}
              />
            </DroppableContainer>
          ))}
        </div>
      </DndContext>

      <Button color="white" variant="text" onClick={handleAddSport} className="w-full">
        Add Sport
      </Button>

      {showActions && onSave && onCancel && (
        <FormActions onSave={onSave} onCancel={onCancel} isLoading={isLoading} className="mt-6" />
      )}
    </div>
  );
}
