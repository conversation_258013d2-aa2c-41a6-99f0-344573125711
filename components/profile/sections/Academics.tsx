import React from 'react';
import { faGraduationCap } from '@fortawesome/pro-regular-svg-icons';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import { EmptyState } from './EmptyState';

interface AcademicsProps {
  userId: number;
}

export function Academics({ userId }: AcademicsProps) {
  return (
    <Card elevation="card">
      <CardHeader title="Academics" titleIcon={faGraduationCap} />
      <EmptyState message="Add academics" />
    </Card>
  );
}
