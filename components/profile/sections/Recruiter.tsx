import React from 'react';
import { faBuilding } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import Toggle from '@/components/shared/form/Toggle';

interface RecruiterProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
}

export const Recruiter: React.FC<RecruiterProps> = ({ checked, onChange, disabled }) => {
  return (
    <Card>
      <CardHeader
        title="Recruiter Database"
        titleIcon={faBuilding}
        viewOnlyTitle="Recruiter Database"
        className="mb-8"
      />

      <div className="flex items-center gap-4">
        <Toggle checked={checked} onChange={onChange} disabled={disabled} />
        <span className="text-sm text-gray-500">Include me in the recruiter database</span>
      </div>
    </Card>
  );
};

export default Recruiter;
