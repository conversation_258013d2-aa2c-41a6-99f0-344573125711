import React, { FC, useEffect, useState } from 'react';
import {
  faDownload,
  faFileLines,
  faFilePdf,
  faPencil,
  faTrash,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import ResumeInput from '@/components/shared/form/ResumeInput';
import { useResume } from '@/hooks/useResume';

interface ResumeProps {
  isViewOnly?: boolean;
  onEdit: () => void;
  className?: string;
}

export const Resume: FC<ResumeProps> = ({ onEdit, className = '', isViewOnly = false }) => {
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const {
    resume,
    uploadResumePdf,
    isUploadingPdf,
    resumePdfUrl,
    isLoadingPdf,
    deleteResumePdf,
    isDeletingPdf,
  } = useResume();

  // Check if there's a PDF already uploaded
  useEffect(() => {
    if (resumePdfUrl) {
      // If there's a PDF URL available, set a placeholder resumeFile
      // so the UI shows the PDF icon
      if (!resumeFile) {
        const placeholderFile = new File([], 'resume.pdf', { type: 'application/pdf' });
        setResumeFile(placeholderFile);
      }
    } else {
      // If there's no PDF URL, clear the resumeFile
      setResumeFile(null);
    }
  }, [resumePdfUrl, resumeFile]);

  const handleDelete = async () => {
    try {
      await deleteResumePdf();
      setResumeFile(null);
      setUploadError(null);
    } catch (error) {
      setUploadError('Failed to delete resume. Please try again.');
    }
  };

  const handleDownload = () => {
    if (resumePdfUrl) {
      // Create a temporary link element to trigger the download
      const link = document.createElement('a');
      link.href = resumePdfUrl;
      link.target = '_blank';
      link.download = 'resume.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleFileSelect = async (file: File) => {
    setResumeFile(file);
    setUploadError(null);

    try {
      await uploadResumePdf({
        pdfFile: file,
        fileName: file.name, // Pass the original file name
      });
    } catch (error) {
      setUploadError('Failed to upload resume. Please try again.');
      setResumeFile(null);
    }
  };

  if (isViewOnly && !resumeFile) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader
        title="Resume"
        titleIcon={faFileLines}
        viewOnlyTitle="Resume"
        buttonLabel="Resume Builder"
        buttonIcon={faPencil}
        handleClick={onEdit}
        isViewOnly={isViewOnly}
        className="mb-8"
      >
        {/* <div className="bg-brand-red text-white text-sm/none font-bold px-3 py-0.5 rounded-full min-w-9 text-center">
          1/2
        </div> */}
      </CardHeader>

      {resumeFile ? (
        <div className="flex flex-col items-center justify-center gap-4">
          <div className="flex flex-col items-center justify-center gap-2">
            <FontAwesomeIcon icon={faFilePdf} className="!size-16 text-text-secondary" />
            <p className="text-sm text-text-secondary">
              {resumeFile.name !== '' ? resumeFile.name : 'Resume.pdf'}
            </p>
          </div>

          {resumePdfUrl && (
            <div className="flex gap-2">
              <Button
                color="blue"
                size="small"
                icon={faDownload}
                iconPosition="right"
                onClick={handleDownload}
              >
                Download PDF
              </Button>
              <Button
                color="red"
                size="small"
                icon={faTrash}
                iconPosition="right"
                onClick={handleDelete}
                disabled={isDeletingPdf}
                aria-label="Delete resume"
              >
                Delete
              </Button>
            </div>
          )}
        </div>
      ) : (
        <ResumeInput
          label="Resume"
          description={uploadError || 'Upload your resume in PDF format'}
          onFileSelect={handleFileSelect}
          disabled={isUploadingPdf || isLoadingPdf}
        />
      )}
    </Card>
  );
};
