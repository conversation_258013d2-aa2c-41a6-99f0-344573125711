'use client';

import React from 'react';
import { faUserCircle } from '@fortawesome/pro-regular-svg-icons';
import { faPencil, faSpinnerThird } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { EditDetailsModal } from '@/components/profile/modals/EditDetailsModal';
import { CardDetails } from '@/components/profile/shared/CardDetails';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import { useAuth } from '@/hooks/useAuth';
import { useUnifiedProfile } from '@/hooks/useUnifiedProfile';
import { ProfileTypes, useAuthStore } from '@/stores/auth.store';
import { useModalStore } from '@/stores/modal.store';

interface DetailsProps {
  isViewOnly?: boolean;
}

export function Details({ isViewOnly = false }: DetailsProps) {
  const { open } = useModalStore();
  const { details, isLoadingDetails } = useUnifiedProfile();
  const { profileType } = useAuth();

  const isPositiveCoach = profileType === ProfileTypes.POSITIVE_COACH;
  const isAthleticsDirector = profileType === ProfileTypes.ATHLETICS_DIRECTOR;

  const showInterests = !isAthleticsDirector && !isPositiveCoach;

  const handleEditClick = () => {
    open(<EditDetailsModal />, '2xl');
  };

  if (isLoadingDetails) {
    return (
      <div className="flex items-center justify-center h-full">
        <FontAwesomeIcon icon={faSpinnerThird} className="animate-spin size-5" />
      </div>
    );
  }

  const positiveAthleteDetails = [
    {
      label: 'County & State',
      value:
        details?.state || details?.county_name
          ? [details?.county_name, details?.state].filter(Boolean).join(', ')
          : null,
    },
    {
      label: 'High School',
      value: details?.school_name,
    },
    {
      label: 'Current GPA',
      value: details?.gpa,
      formatter: (value: number) => value.toFixed(1),
    },
    {
      label: 'Current Class Rank',
      value: details?.class_rank,
    },
    {
      label: 'Gender',
      value: details?.gender,
      formatter: (value: string) => value.charAt(0).toUpperCase() + value.slice(1),
    },
  ];

  const positiveCoachDetails = [
    {
      label: 'County & State',
      value:
        details?.state || details?.county_name
          ? [details?.county_name, details?.state].filter(Boolean).join(', ')
          : null,
    },
    {
      label: 'High School',
      value: details?.school_name,
    },
  ];

  const collegeAthleteDetails = [
    {
      label: 'State',
      value: details?.state ? details?.state : null,
    },
    {
      label: 'College',
      value: details?.college ? details?.college : null,
    },
    {
      label: 'Current GPA',
      value: details?.gpa,
      formatter: (value: number) => value.toFixed(1),
    },
    {
      label: 'Gender',
      value: details?.gender,
      formatter: (value: string) => value.charAt(0).toUpperCase() + value.slice(1),
    },
    {
      label: 'Height',
      value: details?.height_in_inches ? details?.height_in_inches : null,
      formatter: (value: number) => {
        const feet = Math.floor(value / 12);
        const inches = value % 12;
        return `${feet}' ${inches}''`;
      },
    },
    {
      label: 'Weight',
      value: details?.weight ? details?.weight : null,
    },
  ];

  const professionalDetails = [
    {
      label: 'State',
      value: details?.state ? details?.state : null,
    },
    {
      label: 'Employer',
      value: details?.employer ? details?.employer : null,
    },
  ];

  const athleticsDirectorDetails = [
    {
      label: 'County & State',
      value:
        details?.state || details?.county_name
          ? [details?.county_name, details?.state].filter(Boolean).join(', ')
          : null,
    },
    {
      label: 'High School',
      value: details?.school_name,
    },
  ];

  // Prepare detail items for the CardDetails component
  const detailItems = (() => {
    switch (profileType) {
      case ProfileTypes.POSITIVE_ATHLETE:
        return positiveAthleteDetails;
      case ProfileTypes.POSITIVE_COACH:
        return positiveCoachDetails;
      case ProfileTypes.COLLEGE_ATHLETE:
        return collegeAthleteDetails;
      case ProfileTypes.PROFESSIONAL:
        return professionalDetails;
      case ProfileTypes.ATHLETICS_DIRECTOR:
        return athleticsDirectorDetails;
      default:
        return positiveAthleteDetails;
    }
  })();

  // Prepare social links
  const socialLinks = {
    instagram: details?.instagram || undefined,
    facebook: details?.facebook || undefined,
    twitter: details?.twitter || undefined,
    hudl: details?.hudl || undefined,
    custom_link: details?.custom_link || undefined,
  };

  // Transform interests data if needed
  const interests = details?.interests?.map(interest => ({
    id: interest.id,
    name: interest.name,
    icon: interest.icon || undefined,
  }));

  return (
    <Card elevation="card">
      <CardHeader
        title="Details"
        titleIcon={faUserCircle}
        viewOnlyTitle="Details"
        buttonLabel="Edit"
        buttonIcon={faPencil}
        handleClick={handleEditClick}
        isViewOnly={isViewOnly}
        className="mb-8"
      />

      <CardDetails
        detailItems={detailItems}
        socialLinks={socialLinks}
        interests={showInterests ? interests : undefined}
      />
    </Card>
  );
}
