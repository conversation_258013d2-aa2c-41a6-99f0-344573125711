import React from 'react';
import { faPencil, IconDefinition } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

interface ProfileSectionProps {
  title: string;
  icon: IconDefinition;
  onEdit?: () => void;
  children: React.ReactNode;
}

export function ProfileSection({ title, icon, onEdit, children }: ProfileSectionProps) {
  return (
    <div className="bg-white rounded-[32px] p-[32px] shadow-card">
      <div className="flex items-center justify-between mb-[32px]">
        <div className="flex items-center gap-3">
          <FontAwesomeIcon icon={icon} className="h-5 w-5 text-gray-500" />
          <h2 className="text-lg font-semibold">{title}</h2>
        </div>
        {onEdit && (
          <button
            onClick={onEdit}
            className="text-gray-500 hover:text-[#E31837] transition-colors p-2"
          >
            <FontAwesomeIcon icon={faPencil} className="h-4 w-4" />
          </button>
        )}
      </div>
      {children}
    </div>
  );
}
