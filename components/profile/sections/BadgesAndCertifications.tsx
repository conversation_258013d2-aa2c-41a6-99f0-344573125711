import React, { useMemo, useState } from 'react';
import dynamic from 'next/dynamic';
import { faB<PERSON><PERSON><PERSON><PERSON><PERSON>, fa<PERSON><PERSON><PERSON><PERSON>, fa<PERSON>ie<PERSON><PERSON> } from '@fortawesome/pro-regular-svg-icons';
import BadgesPlaceholder from '@/components/shared/blocks/BadgesPlaceholder';
import ChartPlaceholder from '@/components/shared/blocks/ChartPlaceholder';
import type { BadgeCardProps } from '@/components/shared/cards/BadgeCard';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import CertificateCard from '@/components/shared/cards/CertificateCard';
import { Spinner } from '@/components/shared/Spinner';
import { useLearningProgress, UserIdParam } from '@/hooks/useLearningProgress';
import { LearningInvestmentChart } from './LearningInvestmentChart';
import LearningStatsGrid from './LearningStatsGrid';

const BadgesSwiper = dynamic(() => import('@/components/shared/interactive/BadgesSwiper'), {
  ssr: false,
  loading: () => (
    <div className="flex overflow-x-auto pb-6 space-x-4 no-scrollbar h-64 items-center justify-center">
      <Spinner className="text-2xl" />
    </div>
  ),
});

/**
 * Props for the BadgesAndCertifications component
 * @param userId - Optional user ID to fetch data for a specific user
 * @param isPublicView - Optional flag to indicate if this is being viewed in public mode
 */
export interface BadgesAndCertificationsProps {
  userId?: UserIdParam; // Optional userId parameter
  isPublicView?: boolean; // Flag to indicate if this is being viewed in public mode
}

/**
 * Displays badges and certifications for a user
 * Can be used in both profile edit mode and public profile view
 */
export function BadgesAndCertifications({
  userId = null,
  isPublicView = false,
}: BadgesAndCertificationsProps) {
  const [showAllCertificates, setShowAllCertificates] = useState(false);

  const {
    stats,
    isLoadingStats,
    statsError,
    certificates,
    isLoadingCertificates,
    certificatesError,
    badges,
    isLoadingBadges,
    badgesError,
    investment,
    isLoadingInvestment,
    investmentError,
    userId: resolvedUserId, // Get the resolved user ID from the hook
  } = useLearningProgress(userId); // Pass the userId to the hook

  // Ensure certificates is always an array using useMemo
  const certificatesArray = useMemo(() => {
    return Array.isArray(certificates) ? certificates : [];
  }, [certificates]);

  // Ensure badges is always an array using useMemo
  const badgesArray = useMemo(() => {
    return Array.isArray(badges) ? badges : [];
  }, [badges]);

  // Only log in development mode
  // if (process.env.NODE_ENV === 'development') {
  //   React.useEffect(() => {
  //     console.log('Resolved User ID:', resolvedUserId);
  //     console.log('Learning Stats (raw):', stats);
  //     console.log('Certificates (raw):', certificates);
  //     console.log('Badges (raw):', badges);
  //     console.log('Learning Investment (raw):', investment);
  //     console.log('Certificates Array:', certificatesArray);
  //     console.log('Badges Array:', badgesArray);
  //   }, [resolvedUserId, stats, certificates, badges, investment, certificatesArray, badgesArray]);
  // }

  const isLoading =
    isLoadingStats || isLoadingCertificates || isLoadingBadges || isLoadingInvestment;

  const hasError = statsError || certificatesError || badgesError || investmentError;

  // Determine which certificates to display based on showAllCertificates state
  const displayedCertificates = showAllCertificates
    ? certificatesArray
    : certificatesArray.slice(0, 2);

  // Check if we need to show the "View More" button
  const hasMoreCertificates = certificatesArray.length > 2;

  const toggleCertificatesView = () => {
    setShowAllCertificates(prev => !prev);
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-center items-center h-40">
          <Spinner />
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center text-red-500">
          <p>There was an error loading your learning progress data.</p>
          <p className="text-sm mt-2">Please try again later.</p>
        </div>
      </div>
    );
  }

  // Default investment data if undefined
  const investmentData = investment || {
    hoursSpent: 0,
    modulesCompleted: 0,
    lastActive: null,
    topics: [],
    primaryTopic: undefined,
    primaryTopicPercentage: undefined,
  };

  // Default stats data if undefined
  const statsData = stats || {
    coursesCompleted: 0,
    coursesInProgress: 0,
    certificatesEarned: 0,
    badgesEarned: 0,
    totalModulesCompleted: 0,
  };

  // Get the primary topic information if available
  const primaryTopic = investmentData.primaryTopic;
  const primaryTopicPercentage = investmentData.primaryTopicPercentage;

  return (
    <>
      <Card elevation="card" className="mb-10">
        <CardHeader
          title={isPublicView ? 'Certificates' : 'Certificates'}
          titleIcon={faBadgeCheck}
          className="mb-8"
          isViewOnly={isPublicView}
        >
          <span className="bg-brand-red text-white text-sm/none font-bold px-3 py-0.5 rounded-full min-w-9 text-center">
            {statsData.coursesCompleted || 0}
          </span>
        </CardHeader>

        {/* Certificates Section */}
        {certificatesArray.length === 0 ? (
          <p className="text-gray-500 italic">No certificates earned yet.</p>
        ) : (
          <>
            <div className="grid grid-cols-1 gap-6">
              {displayedCertificates.map(certificate => (
                <CertificateCard
                  key={certificate.id}
                  id={certificate.id.toString()}
                  name={certificate.name}
                  courseName={certificate.courseName}
                  earnedAt={certificate.earnedAt}
                  imageUrl={certificate.imageUrl}
                />
              ))}
            </div>

            {hasMoreCertificates && (
              <div className="mt-6 text-center">
                <button
                  onClick={toggleCertificatesView}
                  className="px-4 py-2 bg-brand-blue text-white rounded-md hover:bg-brand-blue-dark transition-colors"
                >
                  {showAllCertificates ? 'Show Less' : `View All (${certificatesArray.length})`}
                </button>
              </div>
            )}
          </>
        )}
      </Card>

      <div className="grid grid-cols-1 gap-6 mb-10 md:grid-cols-6 xl:grid-cols-5">
        {/* Learning Stats Summary */}
        <Card elevation="card" className="col-span-1 md:col-span-3 xl:col-span-2">
          <CardHeader
            title="Learning Stats"
            titleIcon={faLineChart}
            className="mb-8"
            isViewOnly={isPublicView}
          />

          <LearningStatsGrid
            averageModuleScore={statsData.averageModuleScore}
            hoursSpent={statsData.hoursSpent}
            modulesCompleted={statsData.modulesCompleted}
            stateLeaderboardRank={statsData.stateLeaderboardRank}
            stateName={statsData.stateName}
            nationalLeaderboardRank={statsData.nationalLeaderboardRank}
            academicYear={statsData.academicYear}
          />
        </Card>

        {/* Learning Investment */}
        <Card elevation="card" className="col-span-1 md:col-span-3">
          <CardHeader
            title="Learning Investment"
            titleIcon={faPieChart}
            className="mb-8"
            isViewOnly={isPublicView}
          />

          {investmentData.topics && investmentData.topics.length > 0 ? (
            <LearningInvestmentChart
              topics={investmentData.topics}
              primaryTopic={investmentData.primaryTopic}
              primaryTopicPercentage={investmentData.primaryTopicPercentage}
            />
          ) : (
            <ChartPlaceholder
              title={
                isPublicView
                  ? 'No learning investment data available'
                  : 'Start watching modules to see your learning investment stats'
              }
              displayTitle
            />
          )}
        </Card>
      </div>

      {/* Badges Section */}
      <Card elevation="card" className="mb-10">
        <CardHeader
          title="X Factor Badges"
          titleIcon={faBadgeCheck}
          className="mb-10"
          isViewOnly={isPublicView}
        >
          <div className="bg-brand-red text-white text-sm/none font-bold px-3 py-0.5 rounded-full min-w-9 text-center">
            {badgesArray.length || 0}
          </div>
        </CardHeader>

        <div className="mb-0">
          {badgesArray.length === 0 ? (
            <>
              {isPublicView ? (
                <p className="text-gray-500 italic">No badges earned yet.</p>
              ) : (
                <>
                  {isPublicView ? (
                    <p className="text-gray-500 italic">No badges earned yet.</p>
                  ) : (
                    <BadgesPlaceholder />
                  )}
                </>
              )}
            </>
          ) : (
            <BadgesSwiper items={badgesArray as BadgeCardProps[]} />
          )}
        </div>
      </Card>
    </>
  );
}
