import React from 'react';
import { faPlus } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

interface EmptyStateProps {
  message: string;
  onClick?: () => void;
}

export function EmptyState({ message, onClick }: EmptyStateProps) {
  return (
    <div
      onClick={onClick}
      className="bg-surface-secondary text-text-secondary border-2 border-dashed border-gray-200 rounded-lg p-6 text-center cursor-pointer hover:bg-gray-100 transition-colors"
    >
      <div className="flex items-center justify-center gap-2">
        {message}
        <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
      </div>
    </div>
  );
}
