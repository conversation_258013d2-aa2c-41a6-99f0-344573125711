import React from 'react';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faFeatherAlt, faPencil } from '@fortawesome/pro-regular-svg-icons';
import { EditStoryModal } from '@/components/profile/modals/EditStoryModal';
import { Richtext } from '@/components/shared/blocks/Richtext';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import { useModalStore } from '@/stores/modal.store';
import { EmptyState } from './EmptyState';

interface StoryProps {
  cardHeaderTitle?: string;
  cardHeaderViewOnlyTitle?: string;
  cardHeaderTitleIcon?: IconDefinition;
  isViewOnly?: boolean;
}

export function Story({
  cardHeaderTitle = 'Story',
  cardHeaderViewOnlyTitle = 'Story',
  cardHeaderTitleIcon = faFeatherAlt,
  isViewOnly = false,
}: StoryProps) {
  const { open } = useModalStore();
  const { story, isLoadingStory, updateStory, isUpdatingStory } = usePositiveAthleteProfile();

  const handleStoryUpdate = async (content: string) => {
    try {
      await updateStory({ content });
    } catch (error) {
      console.error('Failed to update story:', error);
    }
  };

  const openEditModal = () => {
    open(<EditStoryModal initialContent={story?.content || ''} onSave={handleStoryUpdate} />, 'lg');
  };

  if (isViewOnly && !story?.content) {
    return null;
  }

  return (
    <Card>
      <CardHeader
        title={cardHeaderTitle}
        titleIcon={cardHeaderTitleIcon}
        viewOnlyTitle={cardHeaderViewOnlyTitle}
        buttonLabel="Edit"
        buttonIcon={faPencil}
        handleClick={openEditModal}
        isButtonDisabled={isUpdatingStory}
        isViewOnly={isViewOnly}
        className="mb-8"
      >
        {/* <div className="bg-brand-red text-white text-sm/none font-bold px-3 py-0.5 rounded-full min-w-9 text-center">
          1/2
        </div> */}
      </CardHeader>

      {isLoadingStory ? (
        <div className="animate-pulse h-32 bg-gray-100 rounded-lg" />
      ) : story?.content ? (
        <Richtext content={story.content} className="text-text-secondary" />
      ) : (
        <EmptyState message="Share your story" onClick={openEditModal} />
      )}
    </Card>
  );
}
