import React from 'react';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faCheckCircle, faClock, faPenSwirl } from '@fortawesome/pro-regular-svg-icons';
import { KpiCard, KpiCardProps } from '@/components/shared/cards/KpiCard';
import IconUSA from '@/components/svgs/IconUSA';

export interface LearningStatsGridProps {
  averageModuleScore?: number;
  hoursSpent?: number;
  modulesCompleted?: number;
  stateLeaderboardRank?: number;
  stateName?: string;
  nationalLeaderboardRank?: number;
  academicYear?: string;
}

type StatItemType = {
  id: string;
  value: number | string;
  label: string;
  icon?: IconDefinition | React.FC<React.SVGProps<SVGSVGElement>>;
  image?: string;
  iconSizeClass: string;
  color?: KpiCardProps['color'];
  colSpan?: string;
};

// Helper to format state name for SVG path
const formatStateNameForPath = (stateName: string): string => {
  return stateName.trim().replace(/\s+/g, '-').toLowerCase();
};

const LearningStatsGrid: React.FC<LearningStatsGridProps> = ({
  averageModuleScore = 0,
  hoursSpent = 0,
  modulesCompleted = 0,
  stateLeaderboardRank = 0,
  stateName = 'State',
  nationalLeaderboardRank = 0,
  academicYear = '',
}) => {
  // Format state name for SVG path
  const stateImagePath = `/images/states/${formatStateNameForPath(stateName)}.svg`;

  // Create an array of stat items with their specific configurations
  const statItems: StatItemType[] = [
    {
      id: 'averageModuleScore',
      value: `${averageModuleScore}%`,
      label: 'Average Module Score',
      icon: faPenSwirl,
      iconSizeClass: 'size-4',
      color: 'success',
      colSpan: 'col-span-1 md:col-span-2',
    },
    {
      id: 'hoursSpent',
      value: `${hoursSpent} Hrs`,
      label: 'Spent Learning',
      icon: faClock,
      iconSizeClass: 'size-4',
    },
    {
      id: 'modulesCompleted',
      value: modulesCompleted,
      label: 'Modules Completed',
      icon: faCheckCircle,
      iconSizeClass: 'size-4',
    },
    {
      id: 'stateLeaderboardRank',
      value: stateLeaderboardRank,
      label: `${stateName} Leaderboard Rank, ${academicYear}`,
      image: stateImagePath,
      iconSizeClass: 'size-5',
    },
    {
      id: 'nationalLeaderboardRank',
      value: nationalLeaderboardRank,
      label: `National Leaderboard Rank, ${academicYear}`,
      icon: IconUSA,
      iconSizeClass: 'size-5',
    },
  ];

  return (
    <div className="flex flex-col gap-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {statItems.map(item => (
          <KpiCard
            key={item.id}
            value={item.value}
            label={item.label}
            icon={item.icon}
            image={item.image}
            iconSizeClass={item.iconSizeClass}
            color={item.color}
            colSpan={item.colSpan}
          />
        ))}
      </div>
    </div>
  );
};

export default LearningStatsGrid;
