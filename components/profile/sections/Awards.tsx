import React from 'react';
import { faArrowUpRightFromSquare, faDiploma } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';

export function Awards() {
  return (
    <Card>
      <CardHeader
        title="Awards & Scholarships"
        titleIcon={faDiploma}
        viewOnlyTitle="Awards & Scholarships"
        className="mb-8"
      />

      <Button
        color="blue"
        icon={faArrowUpRightFromSquare}
        iconPosition="right"
        className="rounded-[16px]"
      >
        Browse Available Scholarships
      </Button>
    </Card>
  );
}
