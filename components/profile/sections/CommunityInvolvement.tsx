'use client';

import React from 'react';
import { faPencil, faSchool, faSpinnerThird } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { EditInvolvementModal } from '@/components/profile/modals/EditInvolvementModal';
import { CardInvolvment, InvolvementItem } from '@/components/profile/shared/CardInvolvment';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import type { CommunityInvolvement as Involvement } from '@/services/positive-athlete-profile.service';
import { useModalStore } from '@/stores/modal.store';
import { EmptyState } from './EmptyState';

interface CommunityInvolvementProps {
  cardHeaderTitle?: string;
  isViewOnly?: boolean;
}

export function CommunityInvolvement({
  cardHeaderTitle = 'School / Community Involvement',
  isViewOnly = false,
}: CommunityInvolvementProps) {
  const { open } = useModalStore();
  const {
    involvements,
    isLoadingInvolvements,
    updateInvolvements,
    isUpdatingInvolvements,
    involvementsUpdateError,
  } = usePositiveAthleteProfile();

  const handleOpenEdit = () => {
    open(
      <EditInvolvementModal
        initialInvolvements={involvements}
        onSave={handleSave}
        isSaving={isUpdatingInvolvements}
        error={involvementsUpdateError?.message}
      />
    );
  };

  const handleSave = async (updatedInvolvements: Involvement[]) => {
    try {
      await updateInvolvements(
        { involvements: updatedInvolvements },
        {
          onSuccess: () => {
            useModalStore.getState().close();
          },
        }
      );
    } catch (error) {
      // Error will be handled by the error state in the modal
      console.error('Failed to save involvements:', error);
    }
  };

  if (isLoadingInvolvements) {
    return (
      <div className="flex items-center justify-center h-full">
        <FontAwesomeIcon icon={faSpinnerThird} className="animate-spin size-5" />
      </div>
    );
  }

  // Map the involvement data to match the InvolvementItem interface
  const mappedInvolvements: InvolvementItem[] = involvements.map(inv => ({
    id: inv.id || Math.random().toString(),
    title: inv.title,
    date_range: inv.date_range,
    description: inv.description,
  }));

  if (isViewOnly && !mappedInvolvements.length) {
    return null;
  }

  return (
    <Card elevation="card">
      <CardHeader
        title={cardHeaderTitle}
        titleIcon={faSchool}
        viewOnlyTitle="Community Involvement"
        buttonLabel="Edit"
        buttonIcon={faPencil}
        handleClick={handleOpenEdit}
        isButtonDisabled={isUpdatingInvolvements}
        isViewOnly={isViewOnly}
        className="mb-8"
      />

      <CardInvolvment
        involvements={mappedInvolvements}
        emptyComponent={<EmptyState message="Add community involvement" />}
        onEmptyClick={handleOpenEdit}
      />
    </Card>
  );
}
