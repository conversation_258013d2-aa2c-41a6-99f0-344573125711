'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import dynamic from 'next/dynamic';
import { faArrowRight, faSpinnerThird } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Transition } from '@headlessui/react';
import clsx from 'clsx';
import type { ChartDataItem } from '@/components/shared/interactive/DonutChart/DonutChart';
import type { DonutChartInnerRef } from '@/components/shared/interactive/DonutChart/DonutChartInner';
import { useIntersectionObserver } from '@/hooks/utils/useIntersectionObserver';
import { MEDIA_QUERY_TABLET, useMediaQuery } from '@/hooks/utils/useMediaQuery';

const DonutChart = dynamic(() => import('@/components/shared/interactive/DonutChart/DonutChart'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full">
      <FontAwesomeIcon icon={faSpinnerThird} className="text-gray-600 animate-spin" />
    </div>
  ),
});

interface LearningInvestmentChartProps {
  topics: Array<{
    name: string;
    percentage: number;
    minutesSpent: number;
    modulesCompleted: number;
  }>;
  primaryTopic?: string;
  primaryTopicPercentage?: number;
  displayAside?: boolean;
}

export function LearningInvestmentChart({
  topics,
  primaryTopic,
  primaryTopicPercentage,
  displayAside = true,
}: LearningInvestmentChartProps) {
  const isDesktop = useMediaQuery(MEDIA_QUERY_TABLET);
  const chartRef = useRef<DonutChartInnerRef>(null);
  const [hoveredTopic, setHoveredTopic] = useState<string | null>(null);
  const [lastActiveTopic, setLastActiveTopic] = useState<string | null>(primaryTopic || null);
  const [isMounted, setIsMounted] = useState(false);

  // Only run on client-side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Add intersection observer to detect when chart is in view
  const [containerRef, isInView] = useIntersectionObserver({
    threshold: 0.2,
    freezeOnceVisible: true,
  });

  // Sort topics by percentage in descending order
  const sortedTopics = [...topics].sort((a, b) => b.percentage - a.percentage);

  // Filter out "Uncategorized" topic and limit to top 10 if needed
  const displayTopics = sortedTopics.filter(topic => topic.name !== 'Uncategorized').slice(0, 10);

  // Transform topics into chart data format
  const chartData: ChartDataItem[] = displayTopics.map(topic => ({
    label: topic.name,
    value: topic.percentage,
    isActive: hoveredTopic ? topic.name === hoveredTopic : topic.name === lastActiveTopic,
  }));

  const activeTopic = hoveredTopic || lastActiveTopic;
  const activeTopicData = displayTopics.find(t => t.name === activeTopic);
  const activePercentage = activeTopicData?.percentage;

  const handleMouseEnter = useCallback((topicName: string) => {
    setHoveredTopic(topicName);
    setLastActiveTopic(topicName);
    chartRef.current?.highlightSlice(topicName);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setHoveredTopic(null);
    chartRef.current?.highlightSlice(null);
  }, []);

  // Don't render anything on server
  if (!isMounted) {
    return (
      <div className="flex items-center justify-center h-64">
        <FontAwesomeIcon icon={faSpinnerThird} className="text-gray-600 animate-spin" />
      </div>
    );
  }

  // Render the active topic item (used for both mobile and as part of the desktop list)
  const renderTopicItem = (topic: (typeof displayTopics)[0], isActive: boolean) => (
    <li
      key={topic.name}
      className="flex items-center cursor-pointer group gap-4"
      onMouseEnter={() => handleMouseEnter(topic.name)}
      onMouseLeave={handleMouseLeave}
    >
      <div
        className={clsx(
          'flex items-center justify-center font-bold min-w-[56px] px-2 py-1 rounded-lg text-sm',
          isActive ? 'bg-brand-red text-white' : 'bg-gray-100 text-gray-800'
        )}
      >
        {topic.percentage}%
      </div>
      <span
        className={clsx(
          'text-base transition-colors',
          isActive ? 'text-brand-red font-bold' : 'text-gray-600 group-hover:text-gray-700'
        )}
      >
        {topic.name}
      </span>
      <div
        className={clsx(
          'transition-all duration-300 ease-in-out hidden 2xl:block',
          isActive ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4'
        )}
      >
        <FontAwesomeIcon icon={faArrowRight} className="text-brand-red text-base" />
      </div>
    </li>
  );

  return (
    <div
      ref={containerRef}
      className={clsx('flex flex-col 2xl:flex-row items-center 2xl:items-start p-4', {
        'justify-center': !displayAside,
      })}
    >
      <div className="relative w-[320px] h-[320px] mb-6 2xl:mb-0">
        {isInView && (
          <DonutChart
            ref={chartRef}
            data={chartData}
            size={320}
            activeLabel={activeTopic}
            activeValue={activePercentage}
            onSliceHover={label => {
              if (label) {
                setHoveredTopic(label);
                setLastActiveTopic(label);
              } else {
                setHoveredTopic(null);
              }
            }}
          />
        )}
      </div>

      <div
        className={clsx('flex-1 2xl:ml-8', {
          hidden: !displayAside,
        })}
      >
        {/* Mobile view - only show active topic */}
        {/* {!isDesktop && activeTopicData && (
          <Transition
            show={isInView}
            enter="transition-opacity duration-500"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="flex justify-center mt-4 2xl:hidden">
              {renderTopicItem(activeTopicData, true)}
            </div>
          </Transition>
        )} */}
        <div
          className={clsx(
            'flex justify-center mt-4 pointer-events-none transition-opacity duration-500 opacity-0 2xl:hidden',
            isInView && 'opacity-100'
          )}
        >
          {activeTopicData && renderTopicItem(activeTopicData, true)}
        </div>

        {/* Desktop view - show full list */}
        {/* {isDesktop && (
          <Transition
            show={isInView}
            enter="transition-opacity duration-500"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="hidden flex-col gap-2 2xl:flex">
              {displayTopics.map(topic => {
                const isActive =
                  hoveredTopic === topic.name || (!hoveredTopic && topic.name === lastActiveTopic);
                return renderTopicItem(topic, isActive);
              })}
            </div>
          </Transition>
        )} */}
        <ul
          className={clsx(
            'hidden flex-col gap-2 2xl:flex transition-opacity duration-500 opacity-0',
            isInView && 'opacity-100'
          )}
        >
          {displayTopics?.map(topic => {
            const isActive =
              hoveredTopic === topic?.name || (!hoveredTopic && topic?.name === lastActiveTopic);
            return renderTopicItem(topic, isActive);
          })}
        </ul>
      </div>
    </div>
  );
}
