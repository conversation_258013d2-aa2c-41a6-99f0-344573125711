'use client';

import React from 'react';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faBriefcase, faPencil, faSpinnerThird } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { EditWorkExperienceModal } from '@/components/profile/modals/EditWorkExperienceModal';
import {
  CardWorkExperience,
  WorkExperienceItem,
} from '@/components/profile/shared/CardWorkExperience';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import type { WorkExperience as Experience } from '@/services/positive-athlete-profile.service';
import { useModalStore } from '@/stores/modal.store';
import { EmptyState } from './EmptyState';

interface WorkExperienceProps {
  cardHeaderTitle?: string;
  cardHeaderViewOnlyTitle?: string;
  cardHeaderTitleIcon?: IconDefinition;
  editDialogTitle?: string;
  editDialogDescription?: string;
  isViewOnly?: boolean;
}

export function WorkExperience({
  cardHeaderTitle = 'Work Experience',
  cardHeaderViewOnlyTitle = 'Experience',
  cardHeaderTitleIcon = faBriefcase,
  editDialogTitle,
  editDialogDescription,
  isViewOnly = false,
}: WorkExperienceProps) {
  const { open } = useModalStore();
  const {
    workExperiences,
    isLoadingWorkExperiences,
    updateWorkExperiences,
    isUpdatingWorkExperiences,
    workExperiencesUpdateError,
  } = usePositiveAthleteProfile();

  const handleOpenEdit = () => {
    open(
      <EditWorkExperienceModal
        editDialogTitle={editDialogTitle}
        editDialogDescription={editDialogDescription}
        initialExperiences={workExperiences}
        titleContext={cardHeaderTitle}
        onSave={handleSave}
        isSaving={isUpdatingWorkExperiences}
        error={workExperiencesUpdateError?.message}
      />
    );
  };

  const handleSave = async (updatedExperiences: Experience[]) => {
    try {
      await updateWorkExperiences(
        { experiences: updatedExperiences },
        {
          onSuccess: () => {
            useModalStore.getState().close();
          },
        }
      );
    } catch (error) {
      // Error will be handled by the error state in the modal
      console.error('Failed to save work experiences:', error);
    }
  };

  if (isLoadingWorkExperiences) {
    return (
      <div className="flex items-center justify-center h-full">
        <FontAwesomeIcon icon={faSpinnerThird} className="animate-spin size-5" />
      </div>
    );
  }

  // Map the work experience data to match the WorkExperienceItem interface
  const mappedExperiences: WorkExperienceItem[] = workExperiences.map(exp => ({
    id: exp.id || Math.random().toString(),
    name: exp.name,
    date: exp.date,
    description: exp.description,
  }));

  if (isViewOnly && !mappedExperiences.length) {
    return null;
  }

  return (
    <Card elevation="card">
      <CardHeader
        title={cardHeaderTitle}
        titleIcon={cardHeaderTitleIcon}
        viewOnlyTitle={cardHeaderViewOnlyTitle}
        buttonLabel="Edit"
        buttonIcon={faPencil}
        handleClick={handleOpenEdit}
        isViewOnly={isViewOnly}
        className="mb-8"
      />

      <CardWorkExperience
        workExperiences={mappedExperiences}
        emptyComponent={<EmptyState message="Add work experience" />}
        onEmptyClick={handleOpenEdit}
      />
    </Card>
  );
}
