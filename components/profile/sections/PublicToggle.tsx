import React from 'react';
import { faEye } from '@fortawesome/pro-regular-svg-icons';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import Toggle from '@/components/shared/form/Toggle';

interface PublicToggleProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
}

export const PublicToggle: React.FC<PublicToggleProps> = ({ checked, onChange, disabled }) => {
  return (
    <Card>
      <CardHeader
        title="Profile Visibility"
        titleIcon={faEye}
        viewOnlyTitle="Profile Visibility"
        className="mb-8"
      />

      <div className="flex items-center gap-4 mb-4">
        <Toggle checked={checked} onChange={onChange} disabled={disabled} />
        <span className="inline-block min-w-28 text-gray-700">
          {checked ? 'Public Profile' : 'Private Profile'}
        </span>
      </div>

      <p className="text-sm text-gray-500">
        {checked
          ? 'Your profile is visible to everyone and can be found in search results.'
          : 'Your profile is only visible to you and cannot be found in search results.'}
      </p>
    </Card>
  );
};

export default PublicToggle;
