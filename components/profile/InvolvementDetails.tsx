'use client';

import React from 'react';
import Richtext from '@/components/shared/blocks/Richtext';
import type { Involvement } from './InvolvementForm';

interface InvolvementDetailsProps {
  items: Involvement[];
}

export function InvolvementDetails({ items }: InvolvementDetailsProps) {
  if (items.length === 0) {
    return <p className="text-gray-500 italic">No involvement listed yet.</p>;
  }

  return (
    <div className="space-y-4">
      {items.map(item => (
        <div key={item.id} className="bg-gray-50 p-4 rounded-lg">
          <div className="space-y-2">
            <div className="flex justify-between">
              <h3 className="font-medium text-gray-900">{item.title}</h3>
              <span className="text-sm text-gray-500">{item.dateRange}</span>
            </div>

            <Richtext content={item.description} className="prose-sm text-text-secondary" />
          </div>
        </div>
      ))}
    </div>
  );
}
