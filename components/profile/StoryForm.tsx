'use client';

import React, { useEffect, useState } from 'react';
import Button from '../shared/Button';
import { Editor } from '../shared/Editor';

interface StoryFormProps {
  content: string;
  isLoading?: boolean;
  error: string | null;
  onSave: () => Promise<void>;
  onCancel: () => void;
  onChange: (content: string) => void;
  showActions?: boolean;
}

export function StoryForm({
  content,
  isLoading,
  error,
  onSave,
  onCancel,
  onChange,
  showActions = true,
}: StoryFormProps) {
  const [isEditing, setIsEditing] = useState(true);
  const [isDirty, setIsDirty] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleChange = (newContent: string) => {
    onChange(newContent);
    setIsDirty(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSave();
  };

  if (!isMounted) {
    return null;
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <Editor
        content={content}
        isEditing={isEditing}
        isLoading={isLoading || false}
        error={error}
        isDirty={isDirty}
        isEditable={true}
        onEdit={() => setIsEditing(true)}
        onCancel={() => {}}
        onSave={() => Promise.resolve()}
        onChange={handleChange}
        placeholder="Share your story..."
      />
      {showActions && (
        <div className="flex justify-end gap-4 mt-4">
          <Button
            color="white"
            variant="text"
            onClick={() => {
              setIsEditing(false);
              onCancel();
            }}
          >
            Cancel
          </Button>
          <Button color="blue" onClick={onSave} disabled={isLoading || !isDirty}>
            Save
          </Button>
        </div>
      )}
    </form>
  );
}
