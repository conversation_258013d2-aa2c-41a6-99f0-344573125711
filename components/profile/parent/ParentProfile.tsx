'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { TabGroup, TabPanel, TabPanels } from '@headlessui/react';
import { BadgesAndCertifications } from '@/components/profile/sections/BadgesAndCertifications';
import Recruiter from '@/components/profile/sections/Recruiter';
import { PublicDetails } from '@/components/public-profile/sections/PublicDetails';
import { PublicInvolvement } from '@/components/public-profile/sections/PublicInvolvement';
import { PublicNominationsAndEndorsements } from '@/components/public-profile/sections/PublicNominationsAndEndorsements';
import { PublicProfileHeader } from '@/components/public-profile/sections/PublicProfileHeader';
import { PublicSports } from '@/components/public-profile/sections/PublicSports';
import { PublicStory } from '@/components/public-profile/sections/PublicStory';
import { PublicWorkExperience } from '@/components/public-profile/sections/PublicWorkExperience';
import { useAuth } from '@/hooks/useAuth';
import { useNetworking } from '@/hooks/useNetworking';
import { usePublicProfile } from '@/hooks/usePublicProfile';
import type { ProfilePhoto } from '@/services/positive-athlete-profile.service';
import { ProfileTypes } from '@/stores/auth.store';
import { useModalStore } from '@/stores/modal.store';
import { getTabListItems } from '@/utils/profile-utils';

// Simple Skeleton component since we don't have access to the UI library
const Skeleton = ({ className }: { className: string }) => (
  <div className={`animate-pulse bg-gray-200 rounded ${className}`} />
);

export function ParentProfile() {
  const { isLoggedIn, user: currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState('about');
  const athleteId = currentUser?.athlete?.id ? String(currentUser.athlete.id) : undefined;
  const id = athleteId ?? '';

  const {
    profile,
    details,
    sports,
    story,
    involvements,
    avatar,
    photos,
    careerInterests,
    workExperiences,
    isLoading,
    hasError,
  } = usePublicProfile(id);

  // Don't try to get tab list items until we have a valid profile type
  const profileTabsList = getTabListItems(ProfileTypes.POSITIVE_ATHLETE);

  // Convert activeTab string to index for Headless UI
  const getTabIndex = (tabId: string) => {
    return profileTabsList.findIndex(tab => tab.id === tabId);
  };

  // Handle tab change from Headless UI (index) to our component (id)
  const handleTabChange = (index: number) => {
    if (profileTabsList[index]) {
      setActiveTab(profileTabsList[index].id);
    }
  };

  const selectedIndex = getTabIndex(activeTab);

  // Extract graduation year from details and convert to string if needed
  const graduationYear = details?.graduation_year ? String(details.graduation_year) : undefined;

  // Map public photos to ProfilePhoto type
  const mappedPhotos: ProfilePhoto[] =
    photos?.map(photo => ({
      id: String(photo.id), // Convert ID to string
      url: photo.url,
      thumbnail_url: photo.url, // Use the same URL for thumbnail if not available
      width: photo.width || 0,
      height: photo.height || 0,
      order: 0, // Default order
      focal_point: { x: 0.5, y: 0.5 }, // Default focal point
    })) || [];

  if (isLoading) {
    return (
      <div className="pa-container py-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-48 w-full" />
        </div>
      </div>
    );
  }

  if (!id) {
    return (
      <section className="pa-container py-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          <p className="font-medium">No Athlete Linked</p>
          <p>
            A parent account must be linked to a Positive Athlete account to view profile details.
            Please ensure an athlete account is linked.
          </p>

          <Link
            href="/account"
            className="inline-block text-yellow-800 transition-colors mt-4 hover:text-yellow-900"
            aria-label="Go To Account"
          >
            Go To Account
          </Link>
        </div>
      </section>
    );
  }

  if (hasError) {
    return (
      <div className="pa-container py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p className="font-medium">Error loading profile</p>
          <p>There was an error loading this profile. Please try again later.</p>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="pa-container py-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          <p className="font-medium">Profile not found</p>
          <p>The requested profile could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <section className="min-h-screen bg-surface-secondary">
      <TabGroup selectedIndex={selectedIndex} onChange={handleTabChange}>
        <PublicProfileHeader
          profileId={Number(id)}
          firstName={profile.first_name}
          lastName={profile.last_name}
          avatarUrl={avatar?.url}
          photos={mappedPhotos}
          graduationYear={graduationYear}
          isLoading={false}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          profileType={ProfileTypes.POSITIVE_ATHLETE}
          showMessageButton={false}
          onMessageClick={() => {}}
          hasAchievements={profile?.has_achievements ?? false}
          isParentProfile
        />

        <TabPanels>
          {/* About Tab Panel */}
          <TabPanel>
            <div className="pa-container pb-20 bg-surface-secondary">
              <div className="pa-profile-grid">
                {/* Left Column */}
                <div className="pa-profile-grid-left">
                  <PublicDetails
                    details={details}
                    profile={profile}
                    careerInterests={careerInterests}
                  />

                  <PublicSports sports={sports} />

                  <div className="pointer-events-none">
                    <Recruiter
                      checked={currentUser?.athlete?.recruiter_enabled || false}
                      onChange={() => {}}
                    />
                  </div>
                </div>

                {/* Main Content - Center and Right */}
                <div className="pa-profile-grid-right">
                  {story ? <PublicStory story={story} /> : <p>Check back to see their story.</p>}

                  {/* Public Involvement */}
                  <PublicInvolvement involvements={involvements || []} />

                  {/* Public Work experience */}
                  <PublicWorkExperience experiences={workExperiences || []} />
                </div>
              </div>
            </div>
          </TabPanel>

          {/* Badges & Certifications Tab Panel */}
          <TabPanel>
            <div className="pa-container pb-20 bg-surface-secondary">
              <BadgesAndCertifications userId={Number(id)} isPublicView={true} />
            </div>
          </TabPanel>

          {/* Nominations & Endorsements Tab Panel */}
          <TabPanel>
            <div className="pa-container pb-20 bg-surface-secondary">
              <PublicNominationsAndEndorsements
                userId={Number(id)}
                displayEndorsementButton={false}
              />
            </div>
          </TabPanel>
        </TabPanels>
      </TabGroup>
    </section>
  );
}
