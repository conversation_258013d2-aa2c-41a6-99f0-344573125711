import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { faArrowRotateRight } from '@fortawesome/pro-light-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { DynamicListEditor } from '@/components/shared/dynamic-list';
import SiteInput from '@/components/shared/form/SiteInput';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import { useResume } from '@/hooks/useResume';
import type { Education, ListContent } from '@/types/resume';

interface EducationFormProps {
  isEnabled?: boolean;
}

export interface EducationFormHandle {
  handleReset: () => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
}

export const EducationForm = forwardRef<EducationFormHandle, EducationFormProps>(
  ({ isEnabled = false }, ref) => {
    const { updateSection, resume, isLoading: isLoadingResume } = useResume();
    const { details, isLoadingDetails } = usePositiveAthleteProfile();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [items, setItems] = useState<Education[]>([]);
    const [serverState, setServerState] = useState<Education[] | null>(null);
    const [initialEnabled, setInitialEnabled] = useState(isEnabled);

    // Initialize form state from resume data
    useEffect(() => {
      if (!isLoadingResume && resume?.sections?.length) {
        const educationSection = resume.sections.find(
          section => section.section_type === 'education'
        );
        if (educationSection) {
          const content = educationSection.content as ListContent<Education>;
          const educationItems = content.items || [];
          setItems(educationItems);
          setServerState(educationItems);
          setInitialEnabled(educationSection.is_enabled);
        } else {
          // Initialize with empty state if section doesn't exist
          setItems([]);
          setServerState([]);
          setInitialEnabled(false);
        }
      }
    }, [resume, isLoadingResume]);

    const hasContent = items.length > 0;

    const hasChanges = React.useMemo(() => {
      // If we have no items and no server state, and enabled state hasn't changed, no changes
      if (
        items.length === 0 &&
        (!serverState || serverState.length === 0) &&
        isEnabled === initialEnabled
      ) {
        return false;
      }

      // Check if lengths are different
      if (!serverState || items.length !== serverState.length) {
        return true;
      }

      // Check if order or content has changed by comparing each item in sequence
      const itemsChanged = items.some((item, index) => {
        const serverItem = serverState[index];
        return (
          item.schoolName !== serverItem.schoolName ||
          item.dateRange !== serverItem.dateRange ||
          item.gpa !== serverItem.gpa ||
          item.classRank !== serverItem.classRank ||
          item.achievements !== serverItem.achievements ||
          item.id !== serverItem.id
        );
      });

      const enabledChanged = isEnabled !== initialEnabled;

      return (itemsChanged && items.length > 0) || enabledChanged;
    }, [items, serverState, isEnabled, initialEnabled]);

    const handleReset = () => {
      if (!details) return;

      const currentYear = new Date().getFullYear();
      const graduationYear = details.graduation_year;
      let dateRange = '';

      if (graduationYear) {
        if (graduationYear <= currentYear) {
          // Already graduated - show full range
          dateRange = `${graduationYear - 4}-${graduationYear}`;
        } else {
          // Still in school - show current range
          dateRange = `${currentYear}-${graduationYear}`;
        }
      }

      const newItem: Education = {
        id: crypto.randomUUID(),
        schoolName: details.school_name || '',
        dateRange,
        gpa: details.gpa?.toString() || '',
        classRank: details.class_rank || '',
        achievements: '',
      };

      setItems([newItem]);
      // Don't update server state yet - wait for user to save
      // This matches the behavior of other reset implementations
    };

    const handleAdd = () => {
      const newItem: Education = {
        id: crypto.randomUUID(),
        schoolName: '',
        dateRange: '',
        gpa: '',
        classRank: '',
        achievements: '',
      };
      setItems(prev => [...prev, newItem]);
    };

    const handleRemove = (id: string) => {
      setItems(prev => prev.filter(item => item.id !== id));
    };

    const handleUpdate = (id: string, updates: Partial<Omit<Education, 'id'>>) => {
      setItems(prev =>
        prev.map(item => {
          if (item.id === id) {
            return { ...item, ...updates };
          }
          return item;
        })
      );
    };

    const handleReorder = (reorderedItems: Education[]) => {
      setItems(reorderedItems);
    };

    const handleSave = async () => {
      if (!resume || !hasChanges) return;

      try {
        setIsSubmitting(true);

        await updateSection(
          {
            type: 'education',
            content: { items },
            is_enabled: isEnabled,
          },
          {
            onSuccess: () => {
              setServerState(items);
              setInitialEnabled(isEnabled);
            },
            onError: error => {
              console.error('Failed to update resume section:', error);
            },
            onSettled: () => {
              setIsSubmitting(false);
            },
          }
        );
      } catch (error) {
        console.error('Failed to update resume section:', error);
        setIsSubmitting(false);
      }
    };

    useImperativeHandle(ref, () => ({
      handleReset,
      handleSubmit: handleSave,
    }));

    if (isLoadingResume) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="text-gray-500">Loading resume data...</div>
        </div>
      );
    }

    return (
      <div className="flex flex-col gap-6">
        <div className="flex justify-end">
          <button
            type="button"
            onClick={handleReset}
            disabled={isLoadingDetails || isSubmitting}
            className="flex items-center gap-2 text-body-sm font-medium text-brand-red hover:text-[#B31229] transition-colors disabled:opacity-50"
          >
            <FontAwesomeIcon icon={faArrowRotateRight} className="h-4 w-4" />
            <span>Reset to Profile</span>
          </button>
        </div>

        <DynamicListEditor
          items={items}
          onAdd={handleAdd}
          onRemove={handleRemove}
          onUpdate={handleUpdate}
          onReorder={handleReorder}
          addButtonText="Add Another School"
          getTitle={item => `ITEM ${items.indexOf(item) + 1}`}
          renderFields={item => (
            <div className="flex flex-col gap-4">
              <SiteInput
                label="School Name"
                value={item.schoolName}
                onChange={e => handleUpdate(item.id, { schoolName: e.target.value })}
                placeholder="School Name"
                required
                hideLabel
              />

              <SiteInput
                label="DATE OR RANGE"
                value={item.dateRange}
                onChange={e => handleUpdate(item.id, { dateRange: e.target.value })}
                placeholder="e.g., 2022-Present"
                required
              />

              <SiteInput
                label="CURRENT GPA"
                value={item.gpa}
                onChange={e => handleUpdate(item.id, { gpa: e.target.value })}
                placeholder="e.g., 4.0"
              />

              <SiteInput
                label="CURRENT CLASS RANK"
                value={item.classRank}
                onChange={e => handleUpdate(item.id, { classRank: e.target.value })}
                placeholder="e.g., 16/402"
              />
            </div>
          )}
        />

        <div className="flex justify-end">
          <button
            onClick={handleSave}
            disabled={isSubmitting || isLoadingResume || !resume || !hasChanges}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              hasChanges
                ? 'bg-brand-red text-white hover:bg-[#B31229]'
                : 'bg-gray-100 text-gray-400'
            }`}
          >
            {isSubmitting ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    );
  }
);

EducationForm.displayName = 'EducationForm';
