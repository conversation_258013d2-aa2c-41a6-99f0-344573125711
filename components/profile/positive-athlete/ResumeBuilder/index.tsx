import React, { FC, useEffect, useRef, useState } from 'react';
import {
  ResumeForm,
  ResumeFormHandle,
} from '@/components/profile/positive-athlete/ResumeBuilder/ResumeForm';
import {
  ResumePreview,
  ResumePreviewHandle,
} from '@/components/profile/positive-athlete/ResumeBuilder/ResumePreview';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import { useResume } from '@/hooks/useResume';
import { useAuthStore } from '@/stores/auth.store';
import type {
  ContactContent,
  Education,
  Experience,
  Involvement,
  ListContent,
  ProfileContent,
  Sport,
} from '@/types/resume';

// Create a global variable to store the syncPdf function reference
let globalSyncPdfFn: (() => Promise<void>) | null = null;

// Export a utility function that can be called from anywhere to trigger PDF sync
export const triggerResumePdfSync = () => {
  if (globalSyncPdfFn) {
    globalSyncPdfFn();
  } else {
    // If the function isn't set yet, dispatch an event that will be caught later
    const event = new CustomEvent('resumeUpdateNeeded');
    document.dispatchEvent(event);
  }
};

interface ResumeBuilderProps {
  onBack: () => void;
}

export const ResumeBuilder: FC<ResumeBuilderProps> = ({ onBack }) => {
  const { user } = useAuthStore();
  const { resume, isLoading, createResume, isCreating, avatarUrl, isLoadingAvatar } = useResume();
  const { avatarUrl: profileAvatarUrl } = usePositiveAthleteProfile();
  const resumeFormRef = useRef<ResumeFormHandle>(null);
  const resumePreviewRef = useRef<ResumePreviewHandle>(null);

  // Track if this is the initial creation
  const initialCreationRef = useRef(true);
  const lastSyncTimeRef = useRef<number>(0);
  const syncCooldownMs = 1000; // 1 second cooldown between syncs

  // Function to reset scroll position to top
  const resetScrollPosition = () => {
    if (typeof window !== 'undefined') {
      // Only reset the main window scroll, not the scrollable containers
      // This preserves the sticky behavior but fixes the initial position
      window.scrollTo(0, 0);

      // Reset the left form panel scroll only
      const formPanel = document.querySelector('.w-\\[500px\\].overflow-y-auto');
      if (formPanel) {
        (formPanel as HTMLElement).scrollTop = 0;
      }
    }
  };

  // Create a universal PDF sync function that can be used for all sync scenarios
  const syncPdfWithCooldown = async (source: string = 'unknown') => {
    const now = Date.now();
    // Check if we're within cooldown period
    if (now - lastSyncTimeRef.current < syncCooldownMs) {
      return;
    }

    // Update last sync time
    lastSyncTimeRef.current = now;

    if (resumePreviewRef.current) {
      try {
        await resumePreviewRef.current.syncPdf();
      } catch (error) {
        // Error handling managed by syncPdf
      }
    }
  };

  // Set the global sync function when component mounts
  useEffect(() => {
    globalSyncPdfFn = syncPdfWithCooldown;

    // Reset scroll position when component mounts with a small delay
    // to ensure the layout is fully calculated
    setTimeout(resetScrollPosition, 100);

    return () => {
      globalSyncPdfFn = null;
    };
  }, []);

  // Initialize resume if it doesn't exist
  useEffect(() => {
    if (!isLoading && !resume && !isCreating && user) {
      createResume({
        name: 'My Resume',
        sections: [
          {
            section_type: 'profile',
            is_enabled: true,
            content: {
              name: `${user.first_name} ${user.last_name}`,
              description: user.content,
              showPhoto: false,
            },
          },
          {
            section_type: 'contact',
            is_enabled: true,
            content: {
              email: user.email,
              phone: '',
              location: '',
            },
          },
          {
            section_type: 'education',
            is_enabled: true,
            content: {
              items: [],
            },
          },
          {
            section_type: 'involvement',
            is_enabled: true,
            content: {
              items: [],
            },
          },
          {
            section_type: 'experience',
            is_enabled: true,
            content: {
              items: [],
            },
          },
          {
            section_type: 'sports',
            is_enabled: true,
            content: {
              items: [],
            },
          },
        ],
      });
    }
  }, [resume, isLoading, createResume, isCreating, user]);

  // Event listeners for various PDF sync triggers
  useEffect(() => {
    const handleResetComplete = () => {
      syncPdfWithCooldown('reset-complete-event');
    };

    // Listen for form submission events - common ancestor for all form submissions
    const handleFormSubmit = (event: Event) => {
      // Only handle form submits from our resume sections
      const form = event.target as HTMLFormElement;
      const isResumeForm = form.closest('.pa-resume-section') !== null;

      if (isResumeForm) {
        // Use a longer delay to ensure the form submission and server operations complete
        setTimeout(() => {
          syncPdfWithCooldown('form-submit');
        }, 500);
      }
    };

    // Add listener for manually triggered updates from section components
    const handleResumeUpdateNeeded = () => {
      // Add a delay to ensure server operations complete
      setTimeout(() => {
        syncPdfWithCooldown('manual-update-trigger');
      }, 500);
    };

    // Add event listeners
    document.addEventListener('resumeResetComplete', handleResetComplete);
    document.addEventListener('submit', handleFormSubmit, true); // Capture phase to catch events
    document.addEventListener('resumeUpdateNeeded', handleResumeUpdateNeeded);

    // Clean up
    return () => {
      document.removeEventListener('resumeResetComplete', handleResetComplete);
      document.removeEventListener('submit', handleFormSubmit, true);
      document.removeEventListener('resumeUpdateNeeded', handleResumeUpdateNeeded);
    };
  }, []);

  // Update our reset timeout to use the same safe trigger function
  useEffect(() => {
    // Only run this effect when we have a resume and we're not loading/creating anymore
    if (resume && !isLoading && !isCreating && initialCreationRef.current) {
      // Check if this resume has populated data already and should not be reset
      const hasExistingData = resume.sections.some(section => {
        if (
          section.section_type === 'education' ||
          section.section_type === 'involvement' ||
          section.section_type === 'experience' ||
          section.section_type === 'sports'
        ) {
          const content = section.content as any;
          return content?.items && content.items.length > 0;
        }
        return false;
      });

      // If we have existing data, just set initialCreation to false and skip reset
      if (hasExistingData) {
        initialCreationRef.current = false;
        // Just trigger a PDF sync without reset to ensure PDF is generated
        syncPdfWithCooldown('existing-resume-sync');
        return;
      }

      // Set initialCreation to false to ensure this only runs once
      initialCreationRef.current = false;
      // Reset the last sync time for a fresh start
      lastSyncTimeRef.current = 0;

      // Use a shorter timeout to start the process faster
      const timeoutId = setTimeout(async () => {
        try {
          if (!resumeFormRef.current) {
            return;
          }

          // Reset scroll position before we start modifying content
          resetScrollPosition();

          // First expand all sections
          resumeFormRef.current.expandAllSections();

          // Reduced wait time
          await new Promise(resolve => setTimeout(resolve, 100));

          // Check if sections are visible before proceeding
          const allSections = document.querySelectorAll('.pa-resume-section');
          const visibleSections = Array.from(allSections).filter(
            el => window.getComputedStyle(el).display !== 'none'
          );

          if (visibleSections.length < 6) {
            // Wait a bit longer if not all sections are visible yet, but shorter than before
            await new Promise(resolve => setTimeout(resolve, 100));
          }

          // Then perform the reset
          await resumeFormRef.current.resetAll();

          // Manually trigger PDF sync after initial reset
          syncPdfWithCooldown('initial-reset');

          // Final scroll reset with a delay to ensure all layouts have settled
          setTimeout(resetScrollPosition, 300);
        } catch (error) {
          // Error handling is managed by component itself
        }
      }, 200); // Reduced initial timeout from 1000ms to 200ms

      return () => clearTimeout(timeoutId);
    }
  }, [resume, isLoading, isCreating]);

  if (isLoading || isCreating) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center bg-white">
        <div className="text-gray-500">
          {isCreating ? 'Creating resume...' : 'Loading resume...'}
        </div>
      </div>
    );
  }

  if (!resume) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center bg-white">
        <div className="text-gray-500">No resume found</div>
      </div>
    );
  }

  // Get profile section data
  const profileSection = resume?.sections.find(section => section.section_type === 'profile');
  const contactSection = resume.sections.find(section => section.section_type === 'contact');
  const educationSection = resume.sections.find(section => section.section_type === 'education');
  const involvementSection = resume.sections.find(
    section => section.section_type === 'involvement'
  );
  const experienceSection = resume.sections.find(section => section.section_type === 'experience');
  const sportsSection = resume.sections.find(section => section.section_type === 'sports');

  const profileContent = profileSection?.content as ProfileContent | undefined;
  const contactContent = (contactSection?.content || {}) as ContactContent;
  const educationContent = (educationSection?.content || { items: [] }) as ListContent<Education>;
  const involvementContent = (involvementSection?.content || {
    items: [],
  }) as ListContent<Involvement>;
  const experienceContent = (experienceSection?.content || {
    items: [],
  }) as ListContent<Experience>;
  const sportsContent = (sportsSection?.content || { items: [] }) as ListContent<Sport>;

  // Check if we should show the profile avatar
  const shouldShowProfileAvatar =
    profileContent?.pendingAvatarFile === 'USE_PROFILE_AVATAR' && profileContent?.showPhoto;

  // Map resume data to preview props
  const previewProps = {
    name: profileContent?.name || `${user?.first_name} ${user?.last_name}`,
    email: contactContent.email || user?.email || '',
    phone: contactContent.phone || '',
    location: contactContent.location || '',
    summary: profileContent?.description || '',
    education: educationSection?.is_enabled
      ? educationContent.items.map(item => ({
          school: item.schoolName,
          period: item.dateRange,
          details: `${item.gpa ? `GPA: ${item.gpa}, ` : ''}${
            item.classRank ? `Class Rank: ${item.classRank}, ` : ''
          }${item.achievements}`.trim(),
        }))
      : [],
    involvement: involvementSection?.is_enabled
      ? involvementContent.items.map(item => ({
          title: `${item.name}${item.role ? `, ${item.role}` : ''}`,
          period: item.dateRange,
          description: item.description,
        }))
      : [],
    experience: experienceSection?.is_enabled
      ? experienceContent.items.map(item => ({
          title: `${item.companyName}${item.role ? `, ${item.role}` : ''}`,
          period: item.dateRange,
          description: item.description,
        }))
      : [],
    sports: sportsSection?.is_enabled
      ? sportsContent.items.map(item => ({
          name: item.name,
          position: item.position,
          level: item.level,
          period: item.dateRange,
          achievements: item.achievements,
        }))
      : [],
    currentPage: 1,
    totalPages: 1,
    avatarUrl: shouldShowProfileAvatar || !profileContent?.showPhoto ? undefined : avatarUrl,
    pendingProfileAvatarUrl: shouldShowProfileAvatar ? profileAvatarUrl : undefined,
  };

  return (
    <div className="flex min-h-screen w-full bg-white">
      <div className="w-[500px] flex-shrink-0 border-r border-gray-200 overflow-y-auto">
        <ResumeForm ref={resumeFormRef} onBack={onBack} />
      </div>
      <div className="grow px-6 py-24 flex justify-center bg-surface-secondary">
        <ResumePreview ref={resumePreviewRef} {...previewProps} />
      </div>
    </div>
  );
};
