import { FC, forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { faArrowLeft, faArrowRotateRight } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useResume } from '@/hooks/useResume';
import { ContactInfoForm, ContactInfoFormHandle } from './ContactInfoForm';
import { EducationForm, EducationFormHandle } from './EducationForm';
import { ExperienceForm, ExperienceFormHandle } from './ExperienceForm';
import { InvolvementForm, InvolvementFormHandle } from './InvolvementForm';
import { ProfileForm, ProfileFormHandle } from './ProfileForm';
import { ResumeSection } from './ResumeSection';
import { SportsForm, SportsFormHandle } from './SportsForm';

// Add interface for section content
interface SectionContent {
  items?: Array<{
    id?: string;
    [key: string]: unknown;
  }>;
}

interface ResumeFormProps {
  onBack: () => void;
}

export interface ResumeFormHandle {
  resetAll: () => Promise<void>;
  expandAllSections: () => void;
}

export const ResumeForm = forwardRef<ResumeFormHandle, ResumeFormProps>(({ onBack }, ref) => {
  const { resume } = useResume();
  const [profileExpanded, setProfileExpanded] = useState(false);
  const [contactExpanded, setContactExpanded] = useState(false);

  // Create refs with proper types
  const profileFormRef = useRef<ProfileFormHandle>(null);
  const contactFormRef = useRef<ContactInfoFormHandle>(null);
  const educationFormRef = useRef<EducationFormHandle>(null);
  const involvementFormRef = useRef<InvolvementFormHandle>(null);
  const experienceFormRef = useRef<ExperienceFormHandle>(null);
  const sportsFormRef = useRef<SportsFormHandle>(null);

  // Function to expand all sections
  const expandAllSections = () => {
    setProfileExpanded(true);
    setContactExpanded(true);
  };

  const handleResetAll = async () => {
    try {
      // Ensure sections are expanded first
      expandAllSections();

      // Give React a chance to render the expanded sections, but with shorter delay
      await new Promise(resolve => setTimeout(resolve, 50));

      // Wait for all resets to complete first
      await Promise.all(
        [
          profileFormRef.current?.handleReset(),
          contactFormRef.current?.handleReset(),
          educationFormRef.current?.handleReset(),
          involvementFormRef.current?.handleReset(),
          experienceFormRef.current?.handleReset(),
          sportsFormRef.current?.handleReset(),
        ].filter(Boolean)
      );

      // Create a synthetic form event that matches React's expectations
      const syntheticEvent = {
        preventDefault: () => {},
        stopPropagation: () => {},
        isPropagationStopped: () => false,
        isDefaultPrevented: () => false,
        persist: () => {},
        nativeEvent: new Event('submit'),
        bubbles: true,
        cancelable: true,
        target: document.createElement('form'),
        currentTarget: document.createElement('form'),
        type: 'submit',
        timeStamp: Date.now(),
        defaultPrevented: false,
        isTrusted: true,
      } as unknown as React.FormEvent;

      // Now wait for all saves to complete
      await Promise.all(
        [
          profileFormRef.current
            ?.handleSubmit(syntheticEvent)
            .then(() => 'profile:success')
            .catch(() => 'profile:error'),
          contactFormRef.current
            ?.handleSubmit(syntheticEvent)
            .then(() => 'contact:success')
            .catch(() => 'contact:error'),
          educationFormRef.current
            ?.handleSubmit(syntheticEvent)
            .then(() => 'education:success')
            .catch(() => 'education:error'),
          involvementFormRef.current
            ?.handleSubmit(syntheticEvent)
            .then(() => 'involvement:success')
            .catch(() => 'involvement:error'),
          experienceFormRef.current
            ?.handleSubmit(syntheticEvent)
            .then(() => 'experience:success')
            .catch(() => 'experience:error'),
          sportsFormRef.current
            ?.handleSubmit(syntheticEvent)
            .then(() => 'sports:success')
            .catch(() => 'sports:error'),
        ].filter(Boolean)
      );

      // Longer delay before dispatching event to ensure all server operations are complete
      await new Promise(resolve => setTimeout(resolve, 300));

      // Dispatch a custom event to notify that reset is complete
      const resetCompleteEvent = new CustomEvent('resumeResetComplete', {
        bubbles: true,
        detail: { timestamp: Date.now() },
      });
      document.dispatchEvent(resetCompleteEvent);

      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  // Expose the reset functionality to parent components
  useImperativeHandle(ref, () => ({
    resetAll: handleResetAll,
    expandAllSections,
  }));

  const getSectionEnabled = (type: string) => {
    const section = resume?.sections?.find(s => s.section_type === type);
    return section?.is_enabled ?? false;
  };

  const getSectionHasContent = (type: string) => {
    const section = resume?.sections?.find(s => s.section_type === type);
    if (!section || !section.content) return false;

    const content = section.content as SectionContent;
    return Boolean(content.items?.length);
  };

  return (
    <div className="flex flex-col px-8 py-10 gap-6">
      {/* Header */}
      <div className="flex w-full items-center">
        <button
          onClick={onBack}
          className="flex items-center gap-2 text-body-sm text-brand-blue hover:text-brand-blue-600"
        >
          <FontAwesomeIcon icon={faArrowLeft} className="h-4 w-4" />
          <span>Back</span>
        </button>
      </div>
      <div className="flex items-center justify-between gap-2">
        <h1 className="text-heading-xl font-semibold text-gray-900">Resume Builder</h1>
        <button
          onClick={handleResetAll}
          className="flex items-center gap-2 text-body-sm font-medium text-brand-red hover:text-[#B31229] transition-colors"
        >
          <span>Reset All</span>
          <FontAwesomeIcon icon={faArrowRotateRight} className="h-4 w-4" />
        </button>
      </div>

      {/* Form Sections */}
      <div className="flex flex-col gap-4">
        <ResumeSection
          title="Your Profile"
          description="Quickly give us a sense for who you are with a short bio that speaks to your interests, background, qualities, and skills."
          defaultExpanded={profileExpanded}
        >
          <ProfileForm ref={profileFormRef} />
        </ResumeSection>

        <ResumeSection
          title="Contact Info"
          description="Including your contact information in your resume allows potential employers or recruiters to get in touch."
          defaultExpanded={contactExpanded}
        >
          <ContactInfoForm ref={contactFormRef} />
        </ResumeSection>

        <ResumeSection
          title="Education"
          description="Tell us where you went to school, and if you have a solid GPA and class rank, include those as well!"
          hasToggle
          defaultExpanded
          initialEnabled={getSectionEnabled('education')}
          canEnable={getSectionHasContent('education')}
        >
          {isEnabled => <EducationForm ref={educationFormRef} isEnabled={isEnabled} />}
        </ResumeSection>

        <ResumeSection
          title="School / Community Involvement"
          description="Highlight your school achievements and ways you've positively engaged with your community."
          hasToggle
          defaultExpanded
          initialEnabled={getSectionEnabled('involvement')}
          canEnable={getSectionHasContent('involvement')}
        >
          {isEnabled => <InvolvementForm ref={involvementFormRef} isEnabled={isEnabled} />}
        </ResumeSection>

        <ResumeSection
          title="Work Experience"
          description="Highlight any work experience you've done like internships, part-time jobs, or summer gigs."
          hasToggle
          defaultExpanded
          initialEnabled={getSectionEnabled('experience')}
          canEnable={getSectionHasContent('experience')}
        >
          {isEnabled => <ExperienceForm ref={experienceFormRef} isEnabled={isEnabled} />}
        </ResumeSection>

        <ResumeSection
          title="Sports"
          description="Show the sports that you play to round out your resume and highlight your athleticism."
          hasToggle
          defaultExpanded
          initialEnabled={getSectionEnabled('sports')}
          canEnable={getSectionHasContent('sports')}
        >
          {isEnabled => <SportsForm ref={sportsFormRef} isEnabled={isEnabled} />}
        </ResumeSection>
      </div>
    </div>
  );
});

ResumeForm.displayName = 'ResumeForm';
