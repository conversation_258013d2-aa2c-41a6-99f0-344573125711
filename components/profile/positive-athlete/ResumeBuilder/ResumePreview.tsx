import * as React from 'react';
import Image from 'next/image';
import { faArrowDownToLine, faCheck, faSave, faTrophy } from '@fortawesome/pro-regular-svg-icons';
import {
  faBaseball,
  faBasketball,
  faBowlingBall,
  faBullseye,
  faDice,
  faFootball,
  faFutbol,
  faGolfBallTee,
  faHockeyPuck,
  faHockeySticks,
  faLacrosseStick,
  faMedal,
  faPersonBiking,
  faPersonBikingMountain,
  faPersonRunning,
  faPersonSkiing,
  faPersonSnowboarding,
  faPersonSwimming,
  faShuttlecock,
  faSwords,
  faTableTennisPaddleBall,
  faUserGroup,
  faVolleyball,
  faWater,
} from '@fortawesome/pro-solid-svg-icons';
import html2pdf, { Html2PdfOptions } from 'html2pdf.js';
import debounce from 'lodash/debounce';
import Richtext from '@/components/shared/blocks/Richtext';
import Button from '@/components/shared/Button';
import Tag from '@/components/shared/Tag';
import { useResume } from '@/hooks/useResume';

// Sport icon mapping
const SPORT_ICONS = {
  football: faFootball,
  basketball: faBasketball,
  baseball: faBaseball,
  softball: faBaseball,
  soccer: faFutbol,
  'track-field': faPersonRunning,
  'cross-country': faPersonRunning,
  volleyball: faVolleyball,
  tennis: faTableTennisPaddleBall,
  golf: faGolfBallTee,
  wrestling: faUserGroup,
  'swimming-diving': faPersonSwimming,
  lacrosse: faLacrosseStick,
  'field-hockey': faHockeySticks,
  'ice-hockey': faHockeyPuck,
  cheerleading: faMedal,
  dance: faPersonRunning,
  gymnastics: faMedal,
  'water-polo': faWater,
  rowing: faPersonBiking,
  bowling: faBowlingBall,
  'ultimate-frisbee': faDice,
  rugby: faFootball,
  skiing: faPersonSkiing,
  snowboarding: faPersonSnowboarding,
  'mountain-biking': faPersonBikingMountain,
  cycling: faPersonBiking,
  archery: faBullseye,
  badminton: faShuttlecock,
  fencing: faSwords,
} as const;

interface Education {
  school: string;
  period: string;
  details: string;
}

interface Involvement {
  title: string;
  period: string;
  description: string;
}

interface Experience {
  title: string;
  period: string;
  description: string;
}

interface Sport {
  name: string;
  position?: string;
  level: string;
  period: string;
  achievements: string;
  slug?: string;
}

interface ResumePreviewProps {
  name: string;
  email: string;
  phone: string;
  location: string;
  summary: string;
  education: Education[];
  involvement: Involvement[];
  experience: Experience[];
  sports: Sport[];
  currentPage: number;
  totalPages: number;
  avatarUrl?: string;
  pendingProfileAvatarUrl?: string | null;
}

// Add ResumePreviewHandle interface
export interface ResumePreviewHandle {
  syncPdf: () => Promise<void>;
}

const fetchImageAsDataURL = async (url: string): Promise<string> => {
  try {
    // Use the Next.js API route proxy
    const proxyUrl = `/api/image-proxy?imageUrl=${encodeURIComponent(url)}`;
    const response = await fetch(proxyUrl); // No mode: 'cors' needed as it's a same-origin request

    if (!response.ok) {
      // Attempt to get error message from proxy if it sent one
      let errorMessage = `Failed to fetch image via proxy: ${response.status} ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.error) {
          errorMessage += ` - Proxy error: ${errorData.error}`;
        }
      } catch (e) {
        // Ignore if response is not JSON
      }
      throw new Error(errorMessage);
    }
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    // Error handling
    throw error; // Re-throw the error to be caught by the caller
  }
};

// Convert from FC to forwardRef
export const ResumePreview = React.forwardRef<ResumePreviewHandle, ResumePreviewProps>(
  (
    {
      name,
      email,
      phone,
      location,
      summary,
      education,
      involvement,
      experience,
      sports,
      currentPage,
      totalPages,
      avatarUrl: avatarUrlProp,
      pendingProfileAvatarUrl,
    },
    ref
  ) => {
    const resumeRef = React.useRef<HTMLDivElement>(null);
    const [isGeneratingForDownload, setIsGeneratingForDownload] = React.useState(false);
    const [actualDisplaySrc, setActualDisplaySrc] = React.useState<string | undefined>(undefined);
    const [saveState, setSaveState] = React.useState<'idle' | 'saving' | 'success'>('idle');

    const { resume, uploadResumePdf, isUploadingPdf, avatarUrl: avatarUrlFromHook } = useResume();

    const getInitials = React.useCallback((fullName: string): string => {
      if (!fullName) return '?';
      const parts = fullName.split(' ');
      if (parts.length === 1) return parts[0].charAt(0).toUpperCase();
      return (parts[0].charAt(0) + (parts[parts.length - 1] || '').charAt(0)).toUpperCase();
    }, []);

    React.useEffect(() => {
      let newSrc: string;
      const effectiveAvatarUrl = avatarUrlFromHook || avatarUrlProp;
      if (
        typeof effectiveAvatarUrl === 'string' &&
        effectiveAvatarUrl.trim() !== '' &&
        !effectiveAvatarUrl.endsWith('/img/avatar-placeholder.svg')
      ) {
        newSrc = effectiveAvatarUrl;
      } else {
        const initials = getInitials(name);
        newSrc = `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=D50032&color=fff&size=96`;
      }
      setActualDisplaySrc(newSrc);
    }, [avatarUrlFromHook, avatarUrlProp, name, getInitials]);

    const generatePdfBlob = React.useCallback(async (): Promise<Blob | null> => {
      if (!resumeRef.current) {
        return null;
      }
      const elementToRender = resumeRef.current;
      const opt: Html2PdfOptions = {
        margin: [0.2, 0.2],
        filename: `${name.toLowerCase().replace(/\s+/g, '-')}-resume.pdf`,
        image: { type: 'jpeg', quality: 1.0 },
        html2canvas: { scale: 2, useCORS: true, letterRendering: true },
        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' as const },
      };

      const avatarImg = elementToRender.querySelector(
        'img[alt="Profile photo"]'
      ) as HTMLImageElement | null;
      let originalAvatarSrc: string | null = null;

      try {
        if (avatarImg && avatarImg.src && !avatarImg.src.startsWith('data:')) {
          originalAvatarSrc = avatarImg.src;
          let imageUrlToFetch = avatarImg.src;
          if (imageUrlToFetch.startsWith('http:') || imageUrlToFetch.startsWith('https:')) {
            const dataUrl = await fetchImageAsDataURL(imageUrlToFetch);
            await new Promise<void>((resolve, reject) => {
              avatarImg.onload = () => {
                resolve();
              };
              avatarImg.onerror = () => {
                reject(new Error('Failed to load avatar Data URL into live image'));
              };
              avatarImg.src = dataUrl;
            });
          }
        }

        const images = elementToRender.querySelectorAll('img');
        await Promise.all(
          Array.from(images).map(img => {
            if (!img.complete || img.naturalHeight === 0) {
              return new Promise<void>(resolve => {
                img.onload = () => resolve();
                img.onerror = () => resolve();
              });
            }
            return Promise.resolve();
          })
        );
        await new Promise(resolve => setTimeout(resolve, 500));

        // @ts-ignore // Add ts-ignore back for the html2pdf().outputPdf call
        const pdfBlob = await html2pdf().set(opt).from(elementToRender).outputPdf('blob');

        return pdfBlob;
      } catch (error) {
        // Error handling
        return null;
      } finally {
        if (avatarImg && originalAvatarSrc && avatarImg.src !== originalAvatarSrc) {
          avatarImg.src = originalAvatarSrc;
        }
      }
    }, [name]);

    const uploadPdfLogic = React.useCallback(async () => {
      // Set the save state to saving
      setSaveState('saving');

      // Increase buffer time to ensure all server calls have resolved
      const AVATAR_SAVE_BUFFER_MS = 1000; // Set to 1 second for better reliability in production
      await new Promise(resolve => setTimeout(resolve, AVATAR_SAVE_BUFFER_MS));

      if (!resumeRef.current) {
        setSaveState('idle');
        return;
      }

      const blob = await generatePdfBlob();
      if (blob) {
        try {
          await uploadResumePdf({
            pdfFile: blob,
            fileName: 'resume.pdf', // Always use standard name for system-generated resumes
          });
          // Show success state
          setSaveState('success');
          // Reset to idle state after a delay
          setTimeout(() => {
            setSaveState('idle');
          }, 2000);
        } catch (uploadError) {
          // Error handling for upload failures
          setSaveState('idle');
        }
      } else {
        setSaveState('idle');
      }
    }, [uploadResumePdf, generatePdfBlob]);

    // Expose the syncPdf method via the ref
    React.useImperativeHandle(ref, () => ({
      syncPdf: async () => {
        await uploadPdfLogic();
      },
    }));

    const handleDownloadPDF = async () => {
      if (!resumeRef.current || isGeneratingForDownload) return;

      setIsGeneratingForDownload(true);
      const blob = await generatePdfBlob();
      if (blob) {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `${name.toLowerCase().replace(/\s+/g, '-')}-resume.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      }
      setIsGeneratingForDownload(false);
    };

    return (
      <div className="relative">
        <div className="flex w-full items-center justify-end flex-wrap gap-4 mb-6">
          <Button
            onClick={uploadPdfLogic}
            disabled={saveState !== 'idle' || isUploadingPdf}
            color="blue"
            size="large"
            icon={saveState === 'success' ? faCheck : faSave}
            iconPosition="right"
            className={
              saveState === 'success'
                ? 'bg-green-600 before:bg-green-700/0 hover:before:bg-green-700/20'
                : ''
            }
          >
            {saveState === 'saving' ? 'Saving...' : saveState === 'success' ? 'Saved!' : 'Save PDF'}
          </Button>
          <Button
            onClick={handleDownloadPDF}
            disabled={isGeneratingForDownload || isUploadingPdf || saveState !== 'idle'}
            color="blue"
            size="large"
            icon={faArrowDownToLine}
            iconPosition="right"
          >
            {isGeneratingForDownload
              ? 'Generating for Download...'
              : isUploadingPdf
                ? 'Syncing PDF...'
                : 'Download PDF'}
          </Button>
        </div>

        <div
          ref={resumeRef}
          className="flex flex-col px-10 py-12 bg-white max-w-[612px] max-md:px-5 min-h-[11.7in] w-[8.3in] mx-auto"
          style={{
            position: 'sticky',
            top: '20px',
            pageBreakAfter: 'avoid',
            pageBreakInside: 'avoid',
          }}
        >
          <div className="grid grid-cols-3">
            {/* Resume Preview - Left Column */}
            <div className="col-span-1">
              {/* Personal Details */}
              <ul className="block space-y-4 text-text-secondary text-sm border-b border-brand-red pb-4 pr-4">
                <li>
                  <span className="inline-block font-bold">E:</span> {email}
                </li>
                <li>
                  <span className="inline-block font-bold">P:</span> {phone}
                </li>
                <li>{location}</li>
              </ul>

              {/* Education */}
              {education.length > 0 && (
                <div
                  className="border-b border-brand-red py-6"
                  style={{ pageBreakInside: 'avoid' }}
                >
                  <span className="pa-eyebrow text-brand-red mb-2 pr-4">Education</span>
                  <ul className="space-y-6 pr-4">
                    {education.map((edu, index) => (
                      <li key={`education-item-${index}`} className="space-y-3 list-none">
                        <div className="flex items-center gap-x-4 gap-y-2">
                          <div className="size-1 rounded-full bg-brand-red" />

                          <div className="flex flex-wrap items-center gap-4">
                            <h3 className="font-bold text-sm text-text-primary">{edu.school}</h3>
                            <p className="text-sm text-text-primary">{edu.period}</p>
                          </div>
                        </div>

                        <Richtext
                          content={edu.details}
                          className="prose-sm text-text-secondary pl-5"
                        />
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Experience */}
              {experience.length > 0 && (
                <div
                  className="border-b border-brand-red py-6"
                  style={{ pageBreakInside: 'avoid' }}
                >
                  <span className="pa-eyebrow text-brand-red mb-2 pr-4">Work experience</span>
                  <ul className="space-y-6 pr-4">
                    {experience.map((exp, index) => (
                      <li key={`experience-item-${index}`} className="space-y-3 list-none">
                        <div className="flex items-center gap-4">
                          <div className="size-1 rounded-full bg-brand-red" />

                          <div className="flex items-center gap-4">
                            <h3 className="font-bold text-sm text-text-primary">{exp.title}</h3>
                            <p className="text-sm text-text-primary">{exp.period}</p>
                          </div>
                        </div>

                        <Richtext
                          content={exp.description}
                          className="prose-sm text-text-secondary pl-5"
                        />
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Sports */}
              {sports.length > 0 && (
                <div
                  className="flex z-0 flex-col py-6 w-full max-md:max-w-full"
                  style={{ pageBreakInside: 'avoid' }}
                >
                  <span className="pa-eyebrow text-brand-red mb-2 pr-4">Sports</span>
                  <div className="flex flex-wrap gap-2 pr-4">
                    {sports.map((sport, index) => {
                      const slug = sport.name.toLowerCase().replace(/[^a-z0-9]+/g, '-');
                      const icon = SPORT_ICONS[slug as keyof typeof SPORT_ICONS] || faTrophy;
                      return <Tag key={index} label={sport.name} icon={icon} />;
                    })}
                  </div>
                </div>
              )}
            </div>

            {/* Resume Preview - Right Column */}
            <div className="col-span-2 border-l border-brand-red">
              {/* Profile Image and Name */}
              <div className="flex z-0 flex-col pb-6 w-full border-b border-brand-red max-md:max-w-full">
                <div className="flex flex-wrap gap-10 justify-between items-center w-full pl-4 max-md:max-w-full">
                  <div className="flex gap-4 items-center self-stretch my-auto font-bold leading-none min-w-[240px]">
                    <div className="relative w-24 h-24 rounded-full overflow-hidden mb-4 bg-gray-200">
                      {actualDisplaySrc ? (
                        <Image
                          src={actualDisplaySrc}
                          alt="Profile photo"
                          fill
                          className="object-cover"
                          sizes="96px"
                          unoptimized
                        />
                      ) : null}
                    </div>
                    <div className="flex flex-col self-stretch my-auto">
                      <span className="pa-eyebrow text-brand-red mb-2">RESUME</span>
                      <h4 className="text-2xl text-text-primary">{name}</h4>
                    </div>
                  </div>
                </div>

                <Richtext content={summary} className="prose-sm text-text-secondary pl-4" />
              </div>

              {/* Involvement */}
              {involvement.length > 0 && (
                <div className="py-6 pl-4" style={{ pageBreakInside: 'avoid' }}>
                  <span className="pa-eyebrow text-brand-red mb-2">
                    School / Community Involvement
                  </span>
                  <ul className="space-y-6">
                    {involvement.map((item, index) => (
                      <li key={`involvement-item-${index}`} className="space-y-3 list-none">
                        <div className="flex items-center gap-4">
                          <div className="w-1 h-1 rounded-full bg-brand-red" />

                          <div className="flex items-center gap-4">
                            <h3 className="font-bold text-sm text-text-primary">{item.title}</h3>
                            <p className="text-sm text-text-primary">{item.period}</p>
                          </div>
                        </div>

                        <Richtext
                          content={item.description}
                          className="prose-sm text-text-secondary"
                        />
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }
);

ResumePreview.displayName = 'ResumePreview';
