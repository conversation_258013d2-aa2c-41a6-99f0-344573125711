import React, { FC, forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { faArrowRotateRight } from '@fortawesome/pro-light-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Switch } from '@headlessui/react';
import { useQueryClient } from '@tanstack/react-query';
import { AvatarEditorField, AvatarEditorFieldRef } from '@/components/shared/AvatarEditorField';
import Button from '@/components/shared/Button';
import SiteInput from '@/components/shared/form/SiteInput';
import { WysiwygEditor } from '@/components/shared/form/WysiwygEditor';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import { useResume } from '@/hooks/useResume';
import { ResumeService } from '@/services/resume';
import { useAuthStore } from '@/stores/auth.store';
import type { ProfileContent } from '@/types/resume';

interface FormState extends ProfileContent {
  pendingAvatarFile?: File | 'USE_PROFILE_AVATAR';
}

const MAX_BIO_LENGTH = 250;

export interface ProfileFormHandle {
  handleReset: () => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
}

export interface ProfileFormProps {
  isEnabled?: boolean;
}

export const ProfileForm = forwardRef<ProfileFormHandle, ProfileFormProps>(
  ({ isEnabled = false }, ref) => {
    const { user } = useAuthStore();
    const { story, isLoadingStory, avatarUrl } = usePositiveAthleteProfile();
    const { updateSection, resume, isLoading: isLoadingResume } = useResume();
    const queryClient = useQueryClient();
    const pendingFileRef = useRef<File | null>(null);
    const avatarEditorRef = useRef<AvatarEditorFieldRef>(null);
    const isInitializedRef = useRef(false);
    const isResettingRef = useRef(false);

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [canSaveAvatar, setCanSaveAvatar] = useState(false);
    const [formState, setFormState] = useState<FormState>({
      name: '',
      description: '',
      showPhoto: false,
    });
    const [serverState, setServerState] = useState<FormState | null>(null);

    // Initialize form state from resume data - only runs once
    useEffect(() => {
      if (!isInitializedRef.current && !isLoadingResume && resume?.sections?.length) {
        const profileSection = resume.sections.find(section => section.section_type === 'profile');
        if (profileSection) {
          const content = profileSection.content as ProfileContent;
          const newState = {
            name: content.name || (user ? `${user.first_name} ${user.last_name}` : ''),
            description: content.description || '',
            showPhoto: content.showPhoto ?? false,
            pendingAvatarFile: undefined,
          };
          setFormState(newState);
          setServerState(newState);
          isInitializedRef.current = true;
        }
      }
    }, [resume, isLoadingResume, user]);

    // Update form state when resume changes, but preserve pendingAvatarFile
    useEffect(() => {
      if (
        isInitializedRef.current &&
        !isLoadingResume &&
        resume?.sections?.length &&
        !isResettingRef.current
      ) {
        const profileSection = resume.sections.find(section => section.section_type === 'profile');
        if (profileSection) {
          const content = profileSection.content as ProfileContent;
          setFormState(prev => ({
            name: content.name || (user ? `${user.first_name} ${user.last_name}` : ''),
            description: content.description || '',
            showPhoto: content.showPhoto ?? false,
            pendingAvatarFile: prev.pendingAvatarFile,
          }));
        }
      }
    }, [resume, isLoadingResume, user, formState.pendingAvatarFile]);

    const handleReset = async () => {
      if (!user || !story || !resume) return;

      const newFormState = {
        name: `${user.first_name} ${user.last_name}`.trim(),
        description: story.content || '',
        showPhoto: true,
        pendingAvatarFile: 'USE_PROFILE_AVATAR' as const,
      };

      // Set the resetting flag
      isResettingRef.current = true;

      // Return a promise that resolves when the save is complete
      return new Promise<void>((resolve, reject) => {
        setFormState(newFormState);

        setTimeout(async () => {
          try {
            setIsSubmitting(true);

            await updateSection(
              {
                type: 'profile',
                content: {
                  name: newFormState.name,
                  description: newFormState.description,
                  showPhoto: newFormState.showPhoto,
                },
                is_enabled: true,
              },
              {
                onSuccess: () => {
                  setServerState({
                    ...newFormState,
                    pendingAvatarFile: undefined,
                  });
                },
                onError: error => {
                  console.error('Failed to reset section:', error);
                  setFormState(prev => ({
                    ...prev,
                    pendingAvatarFile: undefined,
                  }));
                  setServerState(prev => ({
                    ...prev!,
                    pendingAvatarFile: undefined,
                  }));
                  reject(error);
                },
                onSettled: () => {
                  setIsSubmitting(false);
                  // Clear the resetting flag
                  isResettingRef.current = false;
                  resolve();
                },
              }
            );
          } catch (error) {
            console.error('Failed to reset section:', error);
            setIsSubmitting(false);
            // Clear the resetting flag
            isResettingRef.current = false;
            reject(error);
          }
        }, 0);
      });
    };

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();

      if (!resume || !hasChanges) return;

      try {
        setIsSubmitting(true);

        // Handle avatar editor changes first (e.g., cropping is done)
        if (canSaveAvatar && avatarEditorRef.current) {
          await avatarEditorRef.current.save(); // This should trigger onSave -> handleAvatarChange in ProfileForm
        }

        const sectionUpdateData: Parameters<typeof updateSection>[0] = {
          type: 'profile',
          content: {
            name: formState.name,
            description: formState.description,
            showPhoto: formState.showPhoto,
          },
          is_enabled: true,
          // onSuccess, onError, onSettled will be added below
        };

        let avatarActionCompleted = false;

        // Case 1: Reset to main profile avatar
        if (formState.pendingAvatarFile === 'USE_PROFILE_AVATAR') {
          try {
            await ResumeService.resetResumeAvatar(resume.id);
            // No need to set sectionUpdateData.avatar here
            avatarActionCompleted = true;
          } catch (error) {
            console.error('Failed to reset avatar:', error);
            setIsSubmitting(false);
            return; // Stop if avatar reset fails
          }
        }
        // Case 2: Upload a new avatar file
        else if (pendingFileRef.current && formState.showPhoto) {
          sectionUpdateData.avatar = pendingFileRef.current;
          // The updateSection call below will handle the upload
          avatarActionCompleted = true; // Marking it as handled to avoid issues with serverState
        }

        // Now, update the section content (and avatar if sectionUpdateData.avatar is set)
        await updateSection(sectionUpdateData, {
          onSuccess: async () => {
            setServerState({
              name: formState.name,
              description: formState.description,
              showPhoto: formState.showPhoto,
              // pendingAvatarFile is cleared from serverState representation
            });
            setFormState(prev => ({
              ...prev,
              pendingAvatarFile: undefined, // Clear pending file from form state
            }));
            if (pendingFileRef.current && sectionUpdateData.avatar) {
              pendingFileRef.current = null; // Clear the ref if it was used
            }
            if (avatarEditorRef.current?.isEditing) {
              avatarEditorRef.current.reset();
            }
            await queryClient.invalidateQueries({ queryKey: ['resumes'] });
            await queryClient.invalidateQueries({ queryKey: ['positiveAthleteProfile'] }); // Invalidate profile too
          },
          onError: error => {
            // Potentially revert optimistic updates or show error to user
          },
          onSettled: () => {
            setIsSubmitting(false);
          },
        });
      } catch (error) {
        console.error('Error in handleSubmit (ProfileForm):', error);
        setIsSubmitting(false);
      }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFormState(prev => ({ ...prev, [name]: value }));
    };

    const handleDescriptionChange = (content: string) => {
      setFormState(prev => ({ ...prev, description: content }));
    };

    const handlePhotoToggle = (checked: boolean) => {
      setFormState(prev => ({
        ...prev,
        showPhoto: checked,
      }));
    };

    const handleAvatarChange = (file: File) => {
      pendingFileRef.current = file;
      setFormState(prev => ({
        ...prev,
        pendingAvatarFile: file,
      }));
    };

    const handleAvatarReadyToSave = (ready: boolean) => {
      setCanSaveAvatar(ready);
    };

    const currentAvatarUrl = React.useMemo(() => {
      if (formState.pendingAvatarFile === 'USE_PROFILE_AVATAR' && formState.showPhoto) {
        return avatarUrl || undefined;
      }
      if (formState.pendingAvatarFile instanceof File) {
        return undefined;
      }
      if (formState.showPhoto && resume?.avatar?.url) {
        return resume.avatar.url;
      }
      return undefined;
    }, [formState.pendingAvatarFile, formState.showPhoto, resume?.avatar?.url, avatarUrl]);

    const hasChanges = React.useMemo(() => {
      if (!serverState) return false;

      const hasAvatarChanges = Boolean(
        pendingFileRef.current ||
          formState.pendingAvatarFile instanceof File ||
          formState.pendingAvatarFile === 'USE_PROFILE_AVATAR' ||
          formState.pendingAvatarFile !== serverState.pendingAvatarFile ||
          canSaveAvatar
      );

      const hasContentChanges =
        formState.name !== serverState.name ||
        formState.description !== serverState.description ||
        formState.showPhoto !== serverState.showPhoto;

      return hasAvatarChanges || hasContentChanges;
    }, [formState, serverState, canSaveAvatar]);

    useImperativeHandle(ref, () => ({
      handleReset,
      handleSubmit,
    }));

    if (isLoadingResume) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="text-gray-500">Loading resume data...</div>
        </div>
      );
    }

    return (
      <form onSubmit={handleSubmit} className="flex flex-col gap-6">
        <p className="text-body-md text-gray-500">
          Quickly give us a sense for who you are with a short bio that speaks to your interests,
          background, qualities, and skills.
        </p>

        <div className="flex justify-end">
          <button
            type="button"
            onClick={handleReset}
            disabled={isLoadingStory || isSubmitting}
            className="flex items-center gap-2 text-body-sm font-medium text-brand-red hover:text-[#B31229] transition-colors disabled:opacity-50"
          >
            <FontAwesomeIcon icon={faArrowRotateRight} className="h-4 w-4" />
            <span>Reset to Profile</span>
          </button>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-body-md font-medium">Show Photo</span>
          <Switch
            checked={formState.showPhoto}
            onChange={handlePhotoToggle}
            className={`${
              formState.showPhoto ? 'bg-brand-red' : 'bg-gray-200'
            } relative inline-flex h-6 w-11 items-center rounded-full transition-colors`}
          >
            <span
              className={`${
                formState.showPhoto ? 'translate-x-6' : 'translate-x-1'
              } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
            />
          </Switch>
        </div>

        {formState.showPhoto && (
          <div className="space-y-4">
            <AvatarEditorField
              ref={avatarEditorRef}
              onSave={handleAvatarChange}
              onReadyToSave={handleAvatarReadyToSave}
              description="Upload a professional headshot for your resume."
              currentImageUrl={currentAvatarUrl}
            />
          </div>
        )}

        <SiteInput
          label="Name"
          name="name"
          value={formState.name}
          onChange={handleInputChange}
          placeholder="Your full name"
          required
        />

        <div className="space-y-2">
          <label className="text-body-sm font-medium">Bio</label>
          <WysiwygEditor
            value={formState.description}
            onChange={handleDescriptionChange}
            maxLength={MAX_BIO_LENGTH}
          />
        </div>

        <div className="flex justify-end">
          <Button
            type="submit"
            variant="filled"
            color={hasChanges ? 'red' : 'white'}
            disabled={isSubmitting || isLoadingResume || !resume || !hasChanges}
          >
            {isSubmitting ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </form>
    );
  }
);

ProfileForm.displayName = 'ProfileForm';
