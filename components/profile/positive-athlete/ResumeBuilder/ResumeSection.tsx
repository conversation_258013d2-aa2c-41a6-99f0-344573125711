import React, { FC, ReactNode, useEffect } from 'react';
import { faChevronDown, faChevronUp } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Switch } from '@headlessui/react';

interface ResumeSectionProps {
  title: string;
  description: string;
  children: ReactNode | ((isEnabled: boolean) => ReactNode);
  hasToggle?: boolean;
  defaultExpanded?: boolean;
  initialEnabled?: boolean;
  canEnable?: boolean;
}

export const ResumeSection: FC<ResumeSectionProps> = ({
  title,
  description,
  children,
  hasToggle = false,
  defaultExpanded = false,
  initialEnabled = false,
  canEnable = true,
}) => {
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);
  const [isEnabled, setIsEnabled] = React.useState(initialEnabled);

  // Update isExpanded when defaultExpanded changes
  useEffect(() => {
    setIsExpanded(defaultExpanded);
  }, [defaultExpanded]);

  const handleToggleChange = (checked: boolean) => {
    if (checked && !canEnable) {
      return;
    }
    setIsEnabled(checked);
  };

  const renderChildren = () => {
    if (typeof children === 'function') {
      return children(isEnabled);
    }
    return children;
  };

  return (
    <div className="rounded-2xl bg-surface-secondary">
      <div className="p-6">
        <div className="flex items-center gap-4">
          {hasToggle && (
            <Switch
              checked={isEnabled}
              onChange={handleToggleChange}
              className={`${
                isEnabled ? 'bg-brand-red' : 'bg-gray-200'
              } relative inline-flex h-6 w-10 items-center rounded-full shadow-switch transition-colors focus:outline-none ${
                !canEnable && !isEnabled ? 'cursor-not-allowed opacity-50' : ''
              }`}
            >
              <span
                className={`${
                  isEnabled ? 'translate-x-5' : 'translate-x-1'
                } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
              />
            </Switch>
          )}

          <div className="flex flex-1 items-center justify-between">
            <h2 className="text-heading-md font-semibold text-gray-900">{title}</h2>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-500 hover:text-gray-700 transition-colors"
            >
              <FontAwesomeIcon
                icon={isExpanded ? faChevronUp : faChevronDown}
                className="h-4 w-4"
              />
            </button>
          </div>
        </div>

        {!isExpanded && <p className="mt-4 text-body-md text-gray-500">{description}</p>}
      </div>

      {isExpanded && <div className="px-6 pb-6">{renderChildren()}</div>}
    </div>
  );
};
