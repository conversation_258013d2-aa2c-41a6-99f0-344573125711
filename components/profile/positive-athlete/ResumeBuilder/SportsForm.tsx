import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { faArrowRotateRight } from '@fortawesome/pro-light-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { InfoCard } from '@/components/shared/cards/InfoCard';
import { SportSelector, type SportItem } from '@/components/shared/sports/SportSelector';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import { useResume } from '@/hooks/useResume';
import type { ListContent, Sport } from '@/types/resume';

interface SportsFormProps {
  isEnabled?: boolean;
}

export interface SportsFormHandle {
  handleReset: () => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
}

// Convert resume Sport type to SportItem type
const convertToSportItem = (sport: Sport, index: number): SportItem => ({
  id: sport.id,
  name: sport.name,
  icon: undefined,
  isCustom: false,
  // Use the sport's ID from the platform if it exists
  sportId: sport.id ? parseInt(sport.id) : undefined,
  order: index,
});

// Convert SportItem type to resume Sport type
const convertToSport = (item: SportItem): Sport => ({
  id: item.sportId?.toString() || item.id,
  name: item.name,
  level: 'Varsity', // Default to Varsity since this is required
  dateRange: '2023-2024', // Default to current school year since this is required
  achievements: 'Active team member', // Default description since this is required
});

export const SportsForm = forwardRef<SportsFormHandle, SportsFormProps>(
  ({ isEnabled = false }, ref) => {
    const { updateSection, resume, isLoading: isLoadingResume } = useResume();
    const { sports, isLoadingSports } = usePositiveAthleteProfile();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [items, setItems] = useState<SportItem[]>([]);
    const [serverState, setServerState] = useState<SportItem[] | null>(null);
    const [initialEnabled, setInitialEnabled] = useState(isEnabled);
    const [validationError, setValidationError] = useState<string | null>(null);

    // Initialize form state from resume data
    useEffect(() => {
      if (!isLoadingResume && resume?.sections?.length) {
        const sportsSection = resume.sections.find(section => section.section_type === 'sports');
        if (sportsSection) {
          const content = sportsSection.content as ListContent<Sport>;
          const sportsItems = content.items || [];
          const convertedItems = sportsItems.map(convertToSportItem);
          setItems(convertedItems);
          setServerState(convertedItems);
          setInitialEnabled(sportsSection.is_enabled);
        } else {
          // Initialize with empty state if section doesn't exist
          setItems([]);
          setServerState([]);
          setInitialEnabled(false);
        }
      }
    }, [resume, isLoadingResume]);

    const hasContent = items.length > 0;

    const hasChanges = React.useMemo(() => {
      if (
        items.length === 0 &&
        (!serverState || serverState.length === 0) &&
        isEnabled === initialEnabled
      ) {
        return false;
      }

      const itemsChanged = !serverState || JSON.stringify(items) !== JSON.stringify(serverState);
      const enabledChanged = isEnabled !== initialEnabled;

      return (itemsChanged && items.length > 0) || enabledChanged;
    }, [items, serverState, isEnabled, initialEnabled]);

    const handleSave = async () => {
      if (!resume || !hasChanges) return;

      try {
        setIsSubmitting(true);

        await updateSection(
          {
            type: 'sports',
            content: { items: items.map(convertToSport) },
            is_enabled: isEnabled,
          },
          {
            onSuccess: () => {
              setServerState(items);
              setInitialEnabled(isEnabled);
            },
            onError: error => {
              console.error('Failed to update resume section:', error);
              setValidationError('Failed to save sports. Please try again.');
            },
            onSettled: () => {
              setIsSubmitting(false);
            },
          }
        );
      } catch (error) {
        console.error('Failed to update resume section:', error);
        setValidationError('Failed to save sports. Please try again.');
        setIsSubmitting(false);
      }
    };

    const handleReset = () => {
      if (!sports) return;

      // Map profile sports to SportItem format
      const newItems: SportItem[] = sports.map((sport, index) => ({
        id: sport.id.toString(),
        name: sport.name,
        icon: sport.icon,
        isCustom: sport.isCustom || false,
        sportId: sport.id,
        order: index,
      }));

      setItems(newItems);
      // Don't update server state yet - wait for user to save
      // This matches the behavior of other reset implementations
    };

    useImperativeHandle(ref, () => ({
      handleReset,
      handleSubmit: handleSave,
    }));

    if (isLoadingResume) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="text-gray-500">Loading resume data...</div>
        </div>
      );
    }

    return (
      <div className="flex flex-col gap-6">
        <div className="flex justify-end">
          <button
            type="button"
            onClick={handleReset}
            disabled={isLoadingSports || isSubmitting}
            className="flex items-center gap-2 text-body-sm font-medium text-brand-red hover:text-[#B31229] transition-colors disabled:opacity-50"
          >
            <FontAwesomeIcon icon={faArrowRotateRight} className="h-4 w-4" />
            <span>Reset to Profile</span>
          </button>
        </div>

        {validationError && (
          <div className="mb-4">
            <InfoCard type="error" message={validationError} />
          </div>
        )}

        <SportSelector
          sports={items}
          onChange={setItems}
          onValidationError={setValidationError}
          allowCustomSportCreation={false}
        />

        <div className="flex justify-end">
          <button
            onClick={handleSave}
            disabled={isSubmitting || isLoadingResume || !resume || !hasChanges}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              hasChanges
                ? 'bg-brand-red text-white hover:bg-[#B31229]'
                : 'bg-gray-100 text-gray-400'
            }`}
          >
            {isSubmitting ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    );
  }
);

SportsForm.displayName = 'SportsForm';
