import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { faArrowRotateRight } from '@fortawesome/pro-light-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { DynamicListEditor } from '@/components/shared/dynamic-list';
import SiteInput from '@/components/shared/form/SiteInput';
import { Textarea } from '@/components/shared/form/Textarea';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import { useResume } from '@/hooks/useResume';
import type { Experience, ListContent } from '@/types/resume';

interface ExperienceFormProps {
  isEnabled?: boolean;
}

export interface ExperienceFormHandle {
  handleReset: () => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
}

export const ExperienceForm = forwardRef<ExperienceFormHandle, ExperienceFormProps>(
  ({ isEnabled = false }, ref) => {
    const { updateSection, resume, isLoading: isLoadingResume } = useResume();
    const { workExperiences, isLoadingWorkExperiences } = usePositiveAthleteProfile();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [items, setItems] = useState<Experience[]>([]);
    const [serverState, setServerState] = useState<Experience[] | null>(null);
    const [initialEnabled, setInitialEnabled] = useState(isEnabled);

    // Initialize form state from resume data
    useEffect(() => {
      if (!isLoadingResume && resume?.sections?.length) {
        const experienceSection = resume.sections.find(
          section => section.section_type === 'experience'
        );
        if (experienceSection) {
          const content = experienceSection.content as ListContent<Experience>;
          const experienceItems = content.items || [];
          setItems(experienceItems);
          setServerState(experienceItems);
          setInitialEnabled(experienceSection.is_enabled);
        } else {
          // Initialize with empty state if section doesn't exist
          setItems([]);
          setServerState([]);
          setInitialEnabled(false);
        }
      }
    }, [resume, isLoadingResume]);

    const hasContent = items.length > 0;

    const hasChanges = React.useMemo(() => {
      if (
        items.length === 0 &&
        (!serverState || serverState.length === 0) &&
        isEnabled === initialEnabled
      ) {
        return false;
      }

      // Check if lengths are different
      if (!serverState || items.length !== serverState.length) {
        return true;
      }

      // Check if order or content has changed by comparing each item in sequence
      const itemsChanged = items.some((item, index) => {
        const serverItem = serverState[index];
        return (
          item.companyName !== serverItem.companyName ||
          item.role !== serverItem.role ||
          item.dateRange !== serverItem.dateRange ||
          item.description !== serverItem.description ||
          item.id !== serverItem.id
        );
      });

      const enabledChanged = isEnabled !== initialEnabled;

      return (itemsChanged && items.length > 0) || enabledChanged;
    }, [items, serverState, isEnabled, initialEnabled]);

    const handleAdd = () => {
      const newItem: Experience = {
        id: crypto.randomUUID(),
        companyName: '',
        role: '',
        dateRange: '',
        description: '',
      };
      setItems(prev => [...prev, newItem]);
    };

    const handleRemove = (id: string) => {
      setItems(prev => prev.filter(item => item.id !== id));
    };

    const handleUpdate = (id: string, updates: Partial<Omit<Experience, 'id'>>) => {
      setItems(prev =>
        prev.map(item => {
          if (item.id === id) {
            return { ...item, ...updates };
          }
          return item;
        })
      );
    };

    const handleReorder = (reorderedItems: Experience[]) => {
      setItems(reorderedItems);
    };

    const handleSave = async () => {
      if (!resume || !hasChanges) return;

      try {
        setIsSubmitting(true);

        await updateSection(
          {
            type: 'experience',
            content: { items },
            is_enabled: isEnabled,
          },
          {
            onSuccess: () => {
              setServerState(items);
              setInitialEnabled(isEnabled);
            },
            onError: error => {
              console.error('Failed to update resume section:', error);
            },
            onSettled: () => {
              setIsSubmitting(false);
            },
          }
        );
      } catch (error) {
        console.error('Failed to update resume section:', error);
        setIsSubmitting(false);
      }
    };

    const handleReset = () => {
      if (!workExperiences) return;

      // Map work experiences to resume experiences format
      const newItems: Experience[] = workExperiences.map(experience => ({
        id: crypto.randomUUID(),
        companyName: experience.name,
        role: '', // Role is not in work experiences
        dateRange: experience.date,
        description: experience.description,
      }));

      setItems(newItems);
      // Don't update server state yet - wait for user to save
      // This matches the behavior of other reset implementations
    };

    useImperativeHandle(ref, () => ({
      handleReset,
      handleSubmit: handleSave,
    }));

    if (isLoadingResume) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="text-gray-500">Loading resume data...</div>
        </div>
      );
    }

    return (
      <div className="flex flex-col gap-6">
        <div className="flex justify-end">
          <button
            type="button"
            onClick={handleReset}
            disabled={isLoadingWorkExperiences || isSubmitting}
            className="flex items-center gap-2 text-body-sm font-medium text-brand-red hover:text-[#B31229] transition-colors disabled:opacity-50"
          >
            <FontAwesomeIcon icon={faArrowRotateRight} className="h-4 w-4" />
            <span>Reset to Profile</span>
          </button>
        </div>

        <DynamicListEditor
          items={items}
          onAdd={handleAdd}
          onRemove={handleRemove}
          onUpdate={handleUpdate}
          onReorder={handleReorder}
          addButtonText="Add Another Experience"
          getTitle={item => `ITEM ${items.indexOf(item) + 1}`}
          renderFields={item => (
            <div className="flex flex-col gap-4">
              <SiteInput
                label="Company Name"
                value={item.companyName}
                onChange={e => handleUpdate(item.id, { companyName: e.target.value })}
                placeholder="Company Name"
                required
                hideLabel
              />

              <SiteInput
                label="DATE OR RANGE"
                value={item.dateRange}
                onChange={e => handleUpdate(item.id, { dateRange: e.target.value })}
                placeholder="2022-2023"
                required
              />

              <Textarea
                label="DESCRIPTION"
                value={item.description}
                onChange={e => handleUpdate(item.id, { description: e.target.value })}
                placeholder="Briefly describe this experience"
                rows={4}
                required
              />
            </div>
          )}
        />

        <div className="flex justify-end">
          <button
            onClick={handleSave}
            disabled={isSubmitting || isLoadingResume || !resume || !hasChanges}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              hasChanges
                ? 'bg-brand-red text-white hover:bg-[#B31229]'
                : 'bg-gray-100 text-gray-400'
            }`}
          >
            {isSubmitting ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    );
  }
);

ExperienceForm.displayName = 'ExperienceForm';
