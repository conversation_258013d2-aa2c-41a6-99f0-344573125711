import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { faArrowRotateRight } from '@fortawesome/pro-light-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import SiteInput from '@/components/shared/form/SiteInput';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import { useResume } from '@/hooks/useResume';
import { useAuthStore } from '@/stores/auth.store';
import type { ContactContent } from '@/types/resume';

export interface ContactInfoFormHandle {
  handleReset: () => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
}

export const ContactInfoForm = forwardRef<ContactInfoFormHandle>((props, ref) => {
  const { user } = useAuthStore();
  const { updateSection, resume, isLoading: isLoadingResume } = useResume();
  const { details, isLoadingDetails } = usePositiveAthleteProfile();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formState, setFormState] = useState<ContactContent>({
    email: '',
    phone: '',
    location: '',
  });
  const [serverState, setServerState] = useState<ContactContent | null>(null);

  // Initialize form state from resume data
  useEffect(() => {
    if (!isLoadingResume && resume?.sections?.length) {
      const contactSection = resume.sections.find(section => section.section_type === 'contact');
      if (contactSection) {
        const content = contactSection.content as ContactContent;
        const newState = {
          email: content.email || user?.email || '',
          phone: content.phone || '',
          location: content.location || '',
        };
        setFormState(newState);
        setServerState(newState);
      }
    }
  }, [resume, isLoadingResume, user]);

  const hasChanges = React.useMemo(() => {
    if (!serverState) return true;
    return (
      formState.email !== serverState.email ||
      formState.phone !== serverState.phone ||
      formState.location !== serverState.location
    );
  }, [formState, serverState]);

  const handleReset = async () => {
    if (!user || !details || !resume) return;

    const newFormState = {
      email: details.email || user.email || '',
      phone: details.phone || '',
      location: details.city || '',
    };

    setFormState(newFormState);
    // Don't update server state yet - wait for user to save
    // This matches the behavior of the Profile form
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!resume || !hasChanges) return;

    try {
      setIsSubmitting(true);

      await updateSection(
        {
          type: 'contact',
          content: formState,
          is_enabled: true,
        },
        {
          onSuccess: () => {
            setServerState(formState);
          },
          onError: error => {
            console.error('Failed to update resume section:', error);
          },
          onSettled: () => {
            setIsSubmitting(false);
          },
        }
      );
    } catch (error) {
      console.error('Failed to update resume section:', error);
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormState(prev => ({ ...prev, [name]: value }));
  };

  useImperativeHandle(ref, () => ({
    handleReset,
    handleSubmit,
  }));

  if (isLoadingResume) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-500">Loading resume data...</div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-6">
      <p className="text-body-md text-gray-500">
        Including your contact information in your resume allows potential employers or recruiters
        to get in touch.
      </p>

      <div className="flex justify-end">
        <button
          type="button"
          onClick={handleReset}
          disabled={isLoadingDetails || isSubmitting}
          className="flex items-center gap-2 text-body-sm font-medium text-brand-red hover:text-[#B31229] transition-colors disabled:opacity-50"
        >
          <FontAwesomeIcon icon={faArrowRotateRight} className="h-4 w-4" />
          <span>Reset to Profile</span>
        </button>
      </div>

      <SiteInput
        label="Email"
        name="email"
        type="email"
        value={formState.email}
        onChange={handleInputChange}
        placeholder="<EMAIL>"
        required
      />

      <SiteInput
        label="Phone"
        name="phone"
        type="tel"
        value={formState.phone}
        onChange={handleInputChange}
        placeholder="(*************"
      />

      <SiteInput
        label="Location"
        name="location"
        value={formState.location}
        onChange={handleInputChange}
        placeholder="City, State"
      />

      <div className="flex justify-end">
        <Button
          type="submit"
          variant="filled"
          color={hasChanges ? 'red' : 'white'}
          disabled={isSubmitting || isLoadingResume || !resume || !hasChanges}
        >
          {isSubmitting ? 'Saving...' : 'Save'}
        </Button>
      </div>
    </form>
  );
});

ContactInfoForm.displayName = 'ContactInfoForm';
