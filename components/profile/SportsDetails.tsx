'use client';

import React from 'react';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import * as solidIcons from '@fortawesome/free-solid-svg-icons';
import Tag from '@/components/shared/Tag';
import { useSportsStore } from '@/stores/sportsStore';
import type { SportEntry } from '@/types/profile';

interface SportsDetailsProps {
  sports: SportEntry[];
}

export function SportsDetails({ sports }: SportsDetailsProps) {
  const availableSports = useSportsStore(state => state.sports);

  if (sports.length === 0) {
    return <p className="text-gray-500 italic">No sports listed yet.</p>;
  }

  return (
    <div className="flex flex-wrap gap-2">
      {sports.map(sport => {
        const sportInfo = availableSports.find(s => s.slug === sport.sport);
        const iconName = sportInfo?.icon
          ? `fa${sportInfo.icon
              .split('-')
              .map((part: string) => part.charAt(0).toUpperCase() + part.slice(1))
              .join('')}`
          : undefined;

        return (
          <Tag
            key={sport.id}
            label={sport.type === 'custom' ? sport.sport : sportInfo?.label || sport.sport}
            icon={
              iconName
                ? (solidIcons as unknown as Record<string, IconDefinition>)[iconName]
                : undefined
            }
          />
        );
      })}
    </div>
  );
}
