import React, { useState } from 'react';
import { TabGroup, TabPanel, TabPanels } from '@headlessui/react';
import { UploadAvatarModal } from '@/components/profile/modals/UploadAvatarModal';
import { ResumeBuilder } from '@/components/profile/positive-athlete/ResumeBuilder';
import { Awards } from '@/components/profile/sections/Awards';
import { BadgesAndCertifications } from '@/components/profile/sections/BadgesAndCertifications';
import { CommunityInvolvement } from '@/components/profile/sections/CommunityInvolvement';
import { Details } from '@/components/profile/sections/Details';
import { NominationsAndEndorsements } from '@/components/profile/sections/NominationsAndEndorsements';
import PublicToggle from '@/components/profile/sections/PublicToggle';
import { Recruiter } from '@/components/profile/sections/Recruiter';
import { Resume } from '@/components/profile/sections/Resume';
import Sports from '@/components/profile/sections/Sports';
import { Story } from '@/components/profile/sections/Story';
import { WorkExperience } from '@/components/profile/sections/WorkExperience';
import { AwardsAndScholarships } from '@/components/profile/shared/AwardsAndScholarships';
import { positiveAthleteProfileTabsList } from '@/components/profile/shared/constants';
import { ProfileHeader } from '@/components/profile/shared/ProfileHeader';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import { usePublicProfile } from '@/hooks/usePublicProfile';
import { useUserSettings } from '@/hooks/useUserSettings';
import { useAuthStore } from '@/stores/auth.store';
import { useModalStore } from '@/stores/modal.store';

export function ProfessionalProfile() {
  const { user } = useAuthStore();
  const { open } = useModalStore();
  const { details, updateAvatar, recruiterEnabled, updateRecruiter, isLoadingRecruiter } =
    usePositiveAthleteProfile();
  const {
    isProfilePublic,
    updateProfileVisibility,
    isLoadingProfileVisibility,
    isUpdatingProfileVisibility,
  } = useUserSettings();
  const userIdAsString = user?.id ? String(user.id) : '';
  const { achievements, isLoadingAchievements, achievementsError } =
    usePublicProfile(userIdAsString);
  const [activeTab, setActiveTab] = useState('about');
  const [isPublicViewToggled, setIsPublicViewToggled] = useState(false);
  const [isResumeBuilderActive, setIsResumeBuilderActive] = useState(false);

  const userInfo = user
    ? {
        first_name: user.first_name,
        last_name: user.last_name,
        graduation_year: user.graduation_year || undefined,
        profile_type: user.profile_type,
      }
    : {};

  const handleAvatarUpload = async (file: File): Promise<any> => {
    try {
      const result = await updateAvatar({ file });
      return result;
    } catch (error: any) {
      console.error('Failed to upload avatar:', error);
      // Re-throw to let the upload handler manage the error display
      throw error;
    }
  };

  const handleRecruiterToggle = (checked: boolean) => {
    updateRecruiter(checked, {
      onError: error => {
        console.error('Failed to update recruiter status:', error);
      },
    });
  };

  const handleToggleIsProfilePublic = (checked: boolean) => {
    updateProfileVisibility(checked, {
      onError: error => {
        console.error('Failed to update profile visibility:', error);
      },
    });
  };

  if (isResumeBuilderActive) {
    return <ResumeBuilder onBack={() => setIsResumeBuilderActive(false)} />;
  }

  // Convert activeTab string to index for Headless UI
  const getTabIndex = (tabId: string) => {
    return positiveAthleteProfileTabsList.findIndex(tab => tab.id === tabId);
  };

  // Handle tab change from Headless UI (index) to our component (id)
  const handleTabChange = (index: number) => {
    setActiveTab(positiveAthleteProfileTabsList[index].id);
  };

  const handlePublicViewToggle = (isPublic: boolean) => {
    setIsPublicViewToggled(isPublic);
  };

  const selectedIndex = getTabIndex(activeTab);

  return (
    <div className="min-h-screen bg-surface-secondary">
      <TabGroup selectedIndex={selectedIndex} onChange={handleTabChange}>
        <ProfileHeader
          user={userInfo}
          tabList={positiveAthleteProfileTabsList}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          isPublic={isPublicViewToggled}
          profileId={user?.id}
          hasAchievements={user?.has_achievements ?? false}
          onPublicChange={handlePublicViewToggle}
          onAvatarClick={() => open(<UploadAvatarModal onSave={handleAvatarUpload} />, '2xl')}
        />

        <TabPanels>
          {/* About Tab Panel */}
          <TabPanel>
            <div className="pa-container pb-20 bg-surface-secondary">
              <div className="pa-profile-grid">
                {/* Left Column */}
                <div className="pa-profile-grid-left">
                  <Details isViewOnly={isPublicViewToggled} />

                  <Sports isViewOnly={isPublicViewToggled} />

                  {/* <Awards /> */}
                  <AwardsAndScholarships
                    awards={achievements?.awards}
                    scholarships={achievements?.scholarships}
                    isLoading={isLoadingAchievements}
                    error={achievementsError}
                  />

                  {!isPublicViewToggled && (
                    <>
                      <Recruiter
                        checked={recruiterEnabled}
                        onChange={handleRecruiterToggle}
                        disabled={isLoadingRecruiter}
                      />

                      <PublicToggle
                        checked={isProfilePublic}
                        onChange={handleToggleIsProfilePublic}
                        disabled={isLoadingProfileVisibility || isUpdatingProfileVisibility}
                      />
                    </>
                  )}
                </div>

                {/* Main Content - Center and Right */}
                <div className="pa-profile-grid-right">
                  <Resume
                    isViewOnly={isPublicViewToggled}
                    onEdit={() => setIsResumeBuilderActive(true)}
                    className="hidden lg:block"
                  />

                  <WorkExperience isViewOnly={isPublicViewToggled} />

                  <CommunityInvolvement isViewOnly={isPublicViewToggled} />

                  <Story isViewOnly={isPublicViewToggled} />
                </div>
              </div>
            </div>
          </TabPanel>

          {/* Badges & Certifications Tab Panel */}
          <TabPanel>
            <div className="pa-container pb-20 bg-surface-secondary">
              <BadgesAndCertifications userId={user?.id} isPublicView={isPublicViewToggled} />
            </div>
          </TabPanel>

          {/* Nominations & Endorsements Tab Panel */}
          <TabPanel>
            <div className="pa-container pb-20 bg-surface-secondary">
              <NominationsAndEndorsements userId={user?.id} displayEndorsementButton={false} />
            </div>
          </TabPanel>
        </TabPanels>
      </TabGroup>
    </div>
  );
}
