'use client';

import React from 'react';

export interface WorkExperienceItem {
  id: string | number;
  name: string;
  date: string;
  description: string;
}

interface CardWorkExperienceProps {
  workExperiences: WorkExperienceItem[];
  isLoading?: boolean;
  emptyComponent?: React.ReactNode;
  onEmptyClick?: () => void;
}

export const CardWorkExperience = ({
  workExperiences = [],
  isLoading = false,
  emptyComponent,
  onEmptyClick,
}: CardWorkExperienceProps) => {
  if (isLoading) {
    return (
      <div className="animate-pulse space-y-3">
        <div className="h-4 bg-gray-100 rounded w-3/4"></div>
        <div className="h-4 bg-gray-100 rounded"></div>
        <div className="h-4 bg-gray-100 rounded w-5/6"></div>
      </div>
    );
  }

  if (!workExperiences || workExperiences.length === 0) {
    if (emptyComponent) {
      return (
        <div onClick={onEmptyClick} className={onEmptyClick ? 'cursor-pointer' : undefined}>
          {emptyComponent}
        </div>
      );
    }
    return <p className="text-text-secondary text-sm">No work experience available.</p>;
  }

  return (
    <ul className="space-y-6">
      {workExperiences.map(experience => (
        <li key={experience.id} className="space-y-3 list-none">
          <div className="flex items-center gap-4">
            <div className="size-1 rounded-full bg-brand-red" />

            <div className="flex items-center gap-4">
              <h3 className="font-bold text-sm text-text-primary">{experience.name}</h3>
              <p className="text-sm text-text-primary">{experience.date}</p>
            </div>
          </div>
          <p className="text-sm text-text-secondary pl-5">{experience.description}</p>
        </li>
      ))}
    </ul>
  );
};
