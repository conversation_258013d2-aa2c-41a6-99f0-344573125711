import {
  faBadgeCheck as faBadgeCheckRegular,
  faMessageCheck as faMessageCheckRegular,
  faRectanglesMixed as faRectanglesMixedRegular,
} from '@fortawesome/pro-regular-svg-icons';
import {
  faBadgeCheck as faBadgeCheckSolid,
  faMessageCheck as faMessageCheckSolid,
  faRectanglesMixed as faRectanglesMixedSolid,
} from '@fortawesome/pro-solid-svg-icons';

export const positiveAthleteProfileTabsList = [
  {
    id: 'about',
    label: 'About',
    iconInactive: faRectanglesMixedRegular,
    iconActive: faRectanglesMixedSolid,
  }, // First Tab
  {
    id: 'badges',
    label: 'Badges & Certifications',
    iconInactive: faBadgeCheckRegular,
    iconActive: faBadgeCheckSolid,
  }, // Second Tab
  {
    id: 'nominations',
    label: 'Nominations & Endorsements',
    iconInactive: faMessageCheckRegular,
    iconActive: faMessageCheckSolid,
  }, // Third Tab
];

export const positiveCoachProfileTabsList = [
  {
    id: 'about',
    label: 'About',
    iconInactive: faRectanglesMixedRegular,
    iconActive: faRectanglesMixedSolid,
  }, // First Tab
  {
    id: 'nominations',
    label: 'Nominations & Endorsements',
    iconInactive: faMessageCheckRegular,
    iconActive: faMessageCheckSolid,
  }, // Second Tab
];
