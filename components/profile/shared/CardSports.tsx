'use client';

import React, { ComponentType, useRef, useState } from 'react';
import {
  faChevronCircleLeft,
  faChevronCircleRight,
  faFlag,
  IconDefinition,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import type { SwiperRef } from 'swiper/react';
import { SportsOtherCard } from '@/components/shared/spline';
import 'swiper/css';
import 'swiper/css/navigation';
import { EmptyState } from '@/components/profile/sections/EmptyState';
import CardHeader from '@/components/shared/cards/CardHeader';
import { normalizeSportName, SportMapping, SportName } from '@/types/sports';

// Define the type for our sport components
type SportsProps = { className?: string };
type SportComponent = ComponentType<SportsProps>;

// Import sport card components only when needed
const getSportComponent = (
  sportComponentMap: SportMapping<SportComponent>,
  sportName: string
): SportComponent => {
  // Ensure we have a valid sportName
  if (!sportName) {
    return SportsOtherCard;
  }

  // Try direct lookup first
  if (sportComponentMap[sportName]) {
    return sportComponentMap[sportName];
  }

  // Try to normalize the sport name
  const normalizedName = normalizeSportName(sportName);
  if (normalizedName !== sportName) {
    // Check if we can find a component with the normalized name
    if (typeof normalizedName === 'string' && sportComponentMap[normalizedName as SportName]) {
      return sportComponentMap[normalizedName as SportName];
    }
  }

  // Always use SportsOtherCard as fallback for unmatched sports
  return SportsOtherCard;
};

export interface SportItem {
  id?: string | number;
  uniqueId?: string | number;
  name: string;
  // Add any other properties that might be used
}

interface CardSportsProps {
  sports: SportItem[];
  sportComponentMap: SportMapping<SportComponent>;
  isLoading?: boolean;
  cardHeaderTitle?: string;
  cardHeaderViewOnlyTitle?: string;
  emptyMessage?: string;
  buttonLabel?: string;
  buttonIcon?: IconDefinition;
  buttonHandleClick?: () => void;
  isViewOnly?: boolean;
  renderSportElement?: (sport: SportItem, index: number) => React.ReactNode;
}

export function CardSports({
  sports = [],
  sportComponentMap,
  isLoading = false,
  cardHeaderTitle = 'Sports',
  cardHeaderViewOnlyTitle = 'Sports',
  emptyMessage = 'No sports added yet.',
  renderSportElement,
  buttonLabel,
  buttonIcon,
  buttonHandleClick,
  isViewOnly = false,
}: CardSportsProps) {
  const [activeIndex, setActiveIndex] = useState(0);
  const swiperRef = useRef<SwiperRef>(null);

  const handlePrevSlide = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  const handleNextSlide = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideNext();
    }
  };

  if (isLoading) {
    return <div className="animate-pulse h-32 bg-gray-100 rounded-lg" />;
  }

  if (!sports || sports.length === 0) {
    return isViewOnly ? (
      <>
        <CardHeader
          title={cardHeaderTitle}
          viewOnlyTitle={cardHeaderViewOnlyTitle}
          titleIcon={faFlag}
          className="mb-8"
        />
        <p className="text-gray-500 italic">{emptyMessage}</p>
      </>
    ) : (
      <>
        <CardHeader
          title={cardHeaderTitle}
          titleIcon={faFlag}
          viewOnlyTitle={cardHeaderViewOnlyTitle}
          isViewOnly={isViewOnly}
          buttonLabel={buttonLabel}
          buttonIcon={buttonIcon}
          handleClick={buttonHandleClick}
          className="mb-8"
          // className="flex absolute top-0 left-0 w-full items-center justify-between my-6 px-8 z-10"
        />
        <EmptyState message="Add a sport" onClick={buttonHandleClick} />
      </>
    );
  }

  return (
    <div className="relative">
      <CardHeader
        title={cardHeaderTitle}
        titleIcon={faFlag}
        viewOnlyTitle={cardHeaderViewOnlyTitle}
        isViewOnly={isViewOnly}
        buttonLabel={buttonLabel}
        buttonIcon={buttonIcon}
        handleClick={buttonHandleClick}
        className="flex absolute top-0 left-0 w-full items-center justify-between my-6 px-8 z-10"
      >
        {sports?.length && (
          <div className="bg-brand-red text-white text-sm/none font-bold px-3 py-0.5 rounded-full min-w-9 text-center">
            {activeIndex + 1}/{sports.length}
          </div>
        )}
      </CardHeader>

      <Swiper
        ref={swiperRef}
        modules={[Navigation]}
        slidesPerView={1}
        className="w-full"
        onSlideChange={swiper => setActiveIndex(swiper.activeIndex)}
      >
        {sports.map((sport, index) => {
          // Get the component for this sport
          const SportComponent = getSportComponent(sportComponentMap, sport.name);

          // If custom rendering is provided, use it
          if (renderSportElement) {
            return (
              <SwiperSlide key={sport.uniqueId || sport.id || `sport-${index}`}>
                {renderSportElement(sport, index)}
              </SwiperSlide>
            );
          }

          // Default rendering
          return (
            <SwiperSlide
              key={sport.uniqueId || sport.id || `sport-${index}`}
              className="relative overflow-hidden"
            >
              <div className="h-[360px] w-full overflow-hidden relative flex items-end justify-end">
                <SportComponent className="h-full w-full [&_div_canvas]:!aspect-[360/280] [&_div_canvas]:!w-full [&_div_canvas]:!h-auto [&_div_canvas]:scale-125 [&_div]:flex [&_div]:items-end [&_div]:justify-end [&_div_canvas]:absolute [&_div_canvas]:bottom-0 [&_div_canvas]:right-0" />

                <span
                  className="absolute top-16 right-6 h-full flex items-center z-10 text-gray-500 text-4xl font-bold font-oxanium uppercase tracking-wider"
                  style={{ writingMode: 'vertical-lr' }}
                >
                  {sport.name}
                </span>
              </div>
            </SwiperSlide>
          );
        })}
      </Swiper>

      {/* Custom navigation arrows - only show when there's more than one sport */}
      {sports?.length > 1 && (
        <div className="absolute bottom-6 left-6 flex items-center gap-4 z-50">
          <button
            onClick={handlePrevSlide}
            className={`flex items-center justify-center text-brand-red cursor-pointer transition-opacity ${
              activeIndex === 0 ? 'opacity-30 pointer-events-none' : ''
            }`}
            aria-label="Previous slide"
            aria-disabled={activeIndex === 0}
          >
            <FontAwesomeIcon icon={faChevronCircleLeft} className="!size-6" />
          </button>
          <button
            onClick={handleNextSlide}
            className={`flex items-center justify-center text-brand-red cursor-pointer transition-opacity ${
              activeIndex === sports.length - 1 ? 'opacity-30 pointer-events-none' : ''
            }`}
            aria-label="Next slide"
            aria-disabled={activeIndex === sports.length - 1}
          >
            <FontAwesomeIcon icon={faChevronCircleRight} className="!size-6" />
          </button>
        </div>
      )}
    </div>
  );
}
