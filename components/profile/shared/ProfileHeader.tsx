'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faPaperPlane } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Tab, TabList } from '@headlessui/react';
import Avatar from '@/components/shared/Avatar';
import Button from '@/components/shared/Button';
import Toggle from '@/components/shared/form/Toggle';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import { cn } from '@/lib/utils';
import type { ProfilePhoto } from '@/services/positive-athlete-profile.service';
import { useModalStore } from '@/stores/modal.store';
import ShareModal from '../modals/ShareModal';
import { UploadImagesModal } from '../modals/UploadImagesModal';
import { ProfileMenuDropdown } from './ProfileMenuDropdown';

const LazyProfileHeaderCarousel = dynamic(
  () => import('./ProfileHeaderCarousel').then(mod => mod.ProfileHeaderCarousel),
  {
    ssr: false,
  }
);

interface TabItem {
  id: string;
  label: string;
  iconActive: IconDefinition;
  iconInactive: IconDefinition;
}

interface ProfileHeaderProps {
  user: {
    first_name?: string;
    last_name?: string;
    graduation_year?: number;
    profile_type?: string;
  };
  tabList: TabItem[];
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
  isPublic?: boolean;
  profileId?: number;
  isParentProfile?: boolean;
  hasAchievements?: boolean;
  isAthleticsDirectorProfile?: boolean;
  onPublicChange?: (isPublic: boolean) => void;
  onAvatarClick?: () => void;
}

export function ProfileHeader({
  user,
  tabList,
  activeTab = 'about',
  onTabChange = () => {},
  isPublic = false,
  profileId = 1,
  onPublicChange = () => {},
  isParentProfile = false,
  hasAchievements = false,
  isAthleticsDirectorProfile = false,
  onAvatarClick,
}: ProfileHeaderProps) {
  const { open, close } = useModalStore();
  const { photos = [], isLoadingPhotos, avatarUrl, isLoadingAvatar } = usePositiveAthleteProfile();

  const defaultPhoto: ProfilePhoto = {
    id: 'default',
    url: '/images/profile-banner.png',
    thumbnail_url: '/images/profile-banner.png',
    width: 1920,
    height: 1080,
    order: 0,
    focal_point: { x: 0.5, y: 0.5 },
  };
  const hasPhotos = photos.length > 0;

  const handleEditImages = () => {
    open(<UploadImagesModal />, 'lg');
  };

  // TODO: Wire up the proper profileId
  const handleOpenShareModal = () => {
    open(<ShareModal profileId={profileId} onClose={close} />, '2xl');
  };

  if (isLoadingPhotos) {
    return (
      <div className="relative mb-8 pt-10">
        <div className="pa-container relative mb-10">
          <div className="w-full h-[280px] rounded-lg bg-neutral-100 animate-pulse" />
        </div>
      </div>
    );
  }

  return (
    <div className="relative mb-8 pt-10">
      <div className="pa-container-wide relative mb-10">
        {/* Mobile Share*/}
        {!isAthleticsDirectorProfile && (
          <div className="xl:hidden flex w-full justify-end items-center gap-[12px] mb-4">
            {!isParentProfile && (
              <div className="flex items-center gap-[12px] px-[8px]">
                <span className="text-[14px] text-text-blue">Public View</span>
                <Toggle checked={isPublic} onChange={onPublicChange} />
              </div>
            )}
            <Button
              color="blue"
              size="small"
              icon={faPaperPlane}
              iconPosition="right"
              onClick={handleOpenShareModal}
            >
              Share
            </Button>
          </div>
        )}

        {/* Photo Carousel or Default Banner */}
        <div className="relative z-[1]">
          {hasPhotos ? (
            <LazyProfileHeaderCarousel
              photos={photos}
              onEditClick={handleEditImages}
              className="rounded-lg overflow-hidden"
              isPublic={isPublic}
            />
          ) : (
            <div
              className="relative w-full h-[280px] rounded-lg overflow-hidden cursor-pointer"
              onClick={handleEditImages}
            >
              <Image
                src={defaultPhoto.url}
                alt="Profile banner"
                fill
                unoptimized
                sizes="100vw"
                priority
                className="object-cover hover:opacity-90 transition-opacity"
              />
            </div>
          )}
        </div>

        {/* Mobile Avatar */}
        <div className="flex items-center relative justify-center -mt-20 z-40 lg:hidden">
          <Avatar
            src={avatarUrl || undefined}
            firstName={user?.first_name}
            lastName={user?.last_name}
            size="xl"
            isUploadState={!avatarUrl}
            hasAchievements={hasAchievements}
            onClick={onAvatarClick}
          />
        </div>

        <div className="items-end justify-between hidden lg:flex md:ml-4 2xl:ml-16 gap-2 lg:-mt-20 relative z-10">
          {/* Desktop Avatar */}
          <div className="hidden lg:block">
            <Avatar
              src={avatarUrl || undefined}
              firstName={user?.first_name}
              lastName={user?.last_name}
              size="xl"
              isUploadState={!avatarUrl}
              hasAchievements={hasAchievements}
              onClick={onAvatarClick}
            />
          </div>

          {/* Desktop Navbar - Tab Buttons */}
          {!isAthleticsDirectorProfile && (
            <div className="hidden lg:flex items-center justify-end lg:mb-8 gap-3">
              <div className="flex gap-3">
                <TabList className="flex gap-3">
                  {tabList?.map(tab => (
                    <Tab
                      key={tab.id}
                      className={({ selected }) =>
                        cn(
                          'py-2.5 px-2 relative text-sm flex items-center gap-2 text-brand-blue outline-none',
                          selected ? 'font-bold' : 'font-normal'
                        )
                      }
                    >
                      {({ selected }) => (
                        <>
                          <FontAwesomeIcon
                            icon={selected ? tab.iconActive : tab.iconInactive}
                            className="size-4 text-brand-blue"
                          />
                          {tab.label}
                        </>
                      )}
                    </Tab>
                  ))}
                </TabList>
              </div>

              {/* Desktop Share */}
              <div className="hidden xl:flex items-center gap-3">
                {!isParentProfile && (
                  <div className="flex items-center gap-3 px-2">
                    <span className="text-sm text-brand-blue">Public View</span>
                    <Toggle checked={isPublic} onChange={onPublicChange} />
                  </div>
                )}
                <Button
                  color="blue"
                  size="small"
                  icon={faPaperPlane}
                  iconPosition="right"
                  onClick={handleOpenShareModal}
                >
                  Share
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Profile Info */}
      <div className="pa-container text-center lg:text-left">
        <h1 className="text-4xl font-bold font-oxanium leading-tight text-text-primary">
          {user?.first_name} {user?.last_name}
        </h1>
        <p className="text-brand-red font-oxanium text-xl font-bold uppercase tracking-widest mt-1">
          {user?.graduation_year
            ? `Class of ${user?.graduation_year}`
            : user?.profile_type?.replace('_', ' ')}
        </p>
      </div>

      {/* Mobile Nav - Tab Buttons */}
      {!isAthleticsDirectorProfile && (
        <div className="lg:hidden w-full max-w-56 mx-auto px-4 mt-4">
          <ProfileMenuDropdown items={tabList} value={activeTab} onChange={onTabChange} />
        </div>
      )}
    </div>
  );
}
