'use client';

import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import Image from 'next/image';
import { faPencil } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import type { Swiper as SwiperType } from 'swiper';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import Button from '@/components/shared/Button';
import { MEDIA_QUERY_DESKTOP, useMediaQuery } from '@/hooks/utils/useMediaQuery';
import { faChevronLeft, faChevronRight } from '@/lib/fontawesome';
import { cn } from '@/lib/utils';
import type { ProfilePhoto } from '@/services/positive-athlete-profile.service';
// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';

interface ProfileHeaderCarouselProps {
  photos: ProfilePhoto[];
  isPublic?: boolean;
  onEditClick?: () => void;
  className?: string;
}

interface NavigationButtonProps {
  direction: 'prev' | 'next';
  containerRect: DOMRect | null;
  onClick: () => void;
  disabled: boolean;
}

function NavigationButton({ direction, containerRect, onClick, disabled }: NavigationButtonProps) {
  const isDesktop = useMediaQuery(MEDIA_QUERY_DESKTOP);

  if (!containerRect) return null;

  const isNext = direction === 'next';

  // Calculate position based on screen size
  const leftPosition = isNext
    ? isDesktop
      ? containerRect.right - 16
      : containerRect.right - 28
    : isDesktop
      ? containerRect.left - 16
      : containerRect.left - 28;

  const style: React.CSSProperties = {
    position: 'fixed',
    top: containerRect.top + 280 / 2, // Half of the carousel height (280px)
    transform: 'translateY(-50%)',
    zIndex: 20,
    opacity: disabled ? 0 : 1,
    pointerEvents: disabled ? 'none' : 'auto',
    transition: 'opacity 0.3s ease',
    left: leftPosition,
  };

  return (
    <button
      onClick={onClick}
      className={cn(
        'w-10 h-10 rounded-full bg-white border-[3px] border-brand-red flex items-center justify-center',
        'shadow-[0_0_0_4px_white]'
      )}
      style={style}
    >
      <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center">
        <FontAwesomeIcon
          icon={isNext ? faChevronRight : faChevronLeft}
          className="w-4 h-4 text-brand-red"
        />
      </div>
    </button>
  );
}

export function ProfileHeaderCarousel({
  photos,
  onEditClick,
  isPublic = false,
  className,
}: ProfileHeaderCarouselProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerRect, setContainerRect] = useState<DOMRect | null>(null);
  const [swiper, setSwiper] = useState<SwiperType | null>(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  useEffect(() => {
    if (!containerRef.current) return;

    const updateRect = () => {
      setContainerRect(containerRef.current?.getBoundingClientRect() ?? null);
    };

    updateRect();
    window.addEventListener('resize', updateRect);
    window.addEventListener('scroll', updateRect);

    return () => {
      window.removeEventListener('resize', updateRect);
      window.removeEventListener('scroll', updateRect);
    };
  }, []);

  useEffect(() => {
    if (swiper) {
      setIsBeginning(swiper.isBeginning);
      setIsEnd(swiper.isEnd);

      const updateState = () => {
        setIsBeginning(swiper.isBeginning);
        setIsEnd(swiper.isEnd);
      };

      swiper.on('slideChange', updateState);
      swiper.on('snapGridLengthChange', updateState);

      return () => {
        swiper.off('slideChange', updateState);
        swiper.off('snapGridLengthChange', updateState);
      };
    }
  }, [swiper, photos]);

  return (
    <>
      <div ref={containerRef} className={cn('relative z-10', className)}>
        <Swiper
          modules={[Navigation]}
          onSwiper={setSwiper}
          slidesPerView="auto"
          spaceBetween={12}
          className="w-full"
          watchOverflow={true}
          observer={true}
          observeParents={true}
          resizeObserver={true}
          watchSlidesProgress={true}
          onSlideChange={() => {
            if (swiper) {
              setIsBeginning(swiper.isBeginning);
              setIsEnd(swiper.isEnd);
            }
          }}
          onResize={() => {
            if (swiper) {
              setIsBeginning(swiper.isBeginning);
              setIsEnd(swiper.isEnd);
            }
          }}
        >
          {photos.map((photo, index) => {
            const aspectRatio = photo.width && photo.height ? photo.width / photo.height : 16 / 9;
            const slideWidth = 280 * aspectRatio;

            return (
              <SwiperSlide key={photo.url} style={{ width: `${slideWidth}px` }}>
                <div
                  className="relative h-[280px] rounded-lg overflow-hidden"
                  style={{ width: `${slideWidth}px` }}
                >
                  <Image
                    src={photo.url}
                    alt="Profile banner"
                    fill
                    unoptimized
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    priority={index === 0}
                    className="object-cover scale-[1.15] transition-transform duration-300"
                    style={{
                      objectPosition: photo.focal_point
                        ? `${photo.focal_point.x * 100}% ${photo.focal_point.y * 100}%`
                        : 'center',
                      transformOrigin: photo.focal_point
                        ? `${photo.focal_point.x * 100}% ${photo.focal_point.y * 100}%`
                        : 'center',
                    }}
                  />
                </div>
              </SwiperSlide>
            );
          })}
        </Swiper>

        {/* Navigation Buttons Portal */}
        {typeof window !== 'undefined' &&
          containerRect &&
          createPortal(
            <>
              {!isBeginning && (
                <NavigationButton
                  direction="prev"
                  containerRect={containerRect}
                  onClick={() => swiper?.slidePrev()}
                  disabled={false}
                />
              )}
              {!isEnd && (
                <NavigationButton
                  direction="next"
                  containerRect={containerRect}
                  onClick={() => swiper?.slideNext()}
                  disabled={false}
                />
              )}
            </>,
            document.body
          )}

        {/* Edit Button */}
        {onEditClick && !isPublic && (
          <Button
            onClick={onEditClick}
            className={cn('!absolute top-2 right-2 z-20')}
            color="red"
            size="small"
            icon={faPencil}
            iconPosition="right"
          >
            Edit Images
          </Button>
        )}
      </div>
    </>
  );
}
