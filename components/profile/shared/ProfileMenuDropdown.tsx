'use client';

import React from 'react';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faChevronDown } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Listbox, ListboxButton, ListboxOption, ListboxOptions } from '@headlessui/react';
import { cn } from '@/lib/utils';

interface MenuItem {
  id: string;
  label: string;
  iconActive: IconDefinition;
  iconInactive: IconDefinition;
}

interface ProfileMenuDropdownProps {
  items: MenuItem[];
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export function ProfileMenuDropdown({
  items,
  value,
  onChange,
  className,
}: ProfileMenuDropdownProps) {
  return (
    <Listbox value={value} onChange={onChange}>
      <div className={cn('relative', className)}>
        <ListboxButton className="relative w-full py-3 px-4 text-left text-brand-blue font-bold bg-white border border-gray-200 rounded-lg cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500">
          {({ open }) => {
            // Find the selected item
            const selectedItem = items.find(item => item.id === value);

            return (
              <>
                <span className="flex items-center gap-2">
                  {selectedItem && (
                    <FontAwesomeIcon
                      icon={selectedItem.iconActive}
                      className="size-4 text-current"
                      aria-hidden="true"
                    />
                  )}
                  <span className="block truncate">{selectedItem?.label || ''}</span>
                </span>

                <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <FontAwesomeIcon
                    icon={faChevronDown}
                    className={`size-4 text-current transition-transform duration-300 ${open ? '-rotate-180' : 'rotate-0'}`}
                    aria-hidden="true"
                  />
                </span>
              </>
            );
          }}
        </ListboxButton>
        <ListboxOptions className="absolute w-full min-w-64 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden focus:outline-none z-50">
          {items.map(item => (
            <ListboxOption
              key={item.id}
              value={item.id}
              className={({ active, selected }) =>
                cn(
                  'relative py-3 px-4 cursor-pointer select-none text-brand-blue text-sm',
                  'flex items-center gap-2',
                  selected && 'font-bold'
                )
              }
            >
              {({ selected }) => (
                <>
                  <FontAwesomeIcon
                    icon={selected ? item.iconActive : item.iconInactive}
                    className="size-4 text-current"
                    aria-hidden="true"
                  />
                  {item.label}
                </>
              )}
            </ListboxOption>
          ))}
        </ListboxOptions>
      </div>
    </Listbox>
  );
}
