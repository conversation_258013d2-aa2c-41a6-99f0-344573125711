'use client';

import React from 'react';

export interface InvolvementItem {
  id: string | number;
  title: string;
  date_range: string;
  description: string;
}

interface CardInvolvmentProps {
  involvements: InvolvementItem[];
  isLoading?: boolean;
  emptyComponent?: React.ReactNode;
  onEmptyClick?: () => void;
}

export const CardInvolvment = ({
  involvements = [],
  isLoading = false,
  emptyComponent,
  onEmptyClick,
}: CardInvolvmentProps) => {
  if (isLoading) {
    return (
      <div className="animate-pulse space-y-3">
        <div className="h-4 bg-gray-100 rounded w-3/4"></div>
        <div className="h-4 bg-gray-100 rounded"></div>
        <div className="h-4 bg-gray-100 rounded w-5/6"></div>
      </div>
    );
  }

  if (!involvements || involvements.length === 0) {
    if (emptyComponent) {
      return (
        <div onClick={onEmptyClick} className={onEmptyClick ? 'cursor-pointer' : undefined}>
          {emptyComponent}
        </div>
      );
    }
    return <p className="text-text-secondary text-sm">No involvement information available.</p>;
  }

  return (
    <ul className="space-y-6">
      {involvements.map(involvement => (
        <li key={involvement.id} className="space-y-3 list-none">
          <div className="flex items-center gap-4">
            <div className="w-1 h-1 rounded-full bg-brand-red" />

            <div className="flex items-center gap-4">
              <h3 className="font-bold text-sm text-text-primary">{involvement.title}</h3>
              <p className="text-sm text-text-primary">{involvement.date_range}</p>
            </div>
          </div>

          <p className="text-sm text-text-secondary pl-5">{involvement.description}</p>
        </li>
      ))}
    </ul>
  );
};
