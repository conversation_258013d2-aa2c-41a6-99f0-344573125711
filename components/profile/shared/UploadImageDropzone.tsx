'use client';

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import Image from 'next/image';
import { faCloudArrowUp } from '@fortawesome/pro-light-svg-icons';
import { faTrash } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import Button from '@/components/shared/Button';

export interface UploadImageDropzoneProps {
  onFileSelect: (file: File | null) => void;
  className?: string;
  currentImageUrl?: string | null;
  dropzoneText?: {
    default: string;
    active: string;
    subText: string;
  };
}

export function UploadImageDropzone({
  onFileSelect,
  className = '',
  currentImageUrl = null,
  dropzoneText = {
    default: 'Drag & drop your image here',
    active: 'Drop your image here',
    subText: 'or click to browse',
  },
}: UploadImageDropzoneProps) {
  const [selectedImage, setSelectedImage] = useState<string | null>(currentImageUrl);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles && acceptedFiles.length > 0) {
        const file = acceptedFiles[0];

        // Validate file type - only allow PNG and JPG
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
        const allowedExtensions = ['.png', '.jpg', '.jpeg'];

        const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

        if (!allowedTypes.includes(file.type) || !allowedExtensions.includes(fileExtension)) {
          setError(
            'Only PNG and JPG images are allowed. WebP and other formats are not supported.'
          );
          return;
        }

        // Validate file size (max a 5MB)
        if (file.size > 5 * 1024 * 1024) {
          setError('File size exceeds 5MB limit. Please choose a smaller file.');
          return;
        }

        // Clear any previous errors
        setError(null);

        // Create preview URL
        const url = URL.createObjectURL(file);
        setSelectedImage(url);

        // Pass the file to parent component
        onFileSelect(file);
      }
    },
    [onFileSelect]
  );

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type - only allow PNG and JPG
      const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
      const allowedExtensions = ['.png', '.jpg', '.jpeg'];

      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

      if (!allowedTypes.includes(file.type) || !allowedExtensions.includes(fileExtension)) {
        setError('Only PNG and JPG images are allowed. WebP and other formats are not supported.');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('File size exceeds 5MB limit. Please choose a smaller file.');
        return;
      }

      // Clear any previous errors
      setError(null);

      const url = URL.createObjectURL(file);
      setSelectedImage(url);
      onFileSelect(file);
    }
  };

  const handleDelete = () => {
    setSelectedImage(null);
    onFileSelect(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/png': ['.png'],
      'image/jpeg': ['.jpeg', '.jpg'],
    },
    maxFiles: 1,
    noClick: true,
  });

  React.useEffect(() => {
    // Update the state if the currentImageUrl prop changes
    setSelectedImage(currentImageUrl);
  }, [currentImageUrl]);

  React.useEffect(() => {
    return () => {
      // Clean up the URL when component unmounts
      if (selectedImage && selectedImage !== currentImageUrl) {
        URL.revokeObjectURL(selectedImage);
      }
    };
  }, [selectedImage, currentImageUrl]);

  return (
    <div className={clsx('flex w-full', className)}>
      {error && <div className="mb-4 p-4 bg-red-50 text-red-700 rounded-lg">{error}</div>}

      <div className="relative w-full flex overflow-hidden">
        {!selectedImage ? (
          <div
            {...getRootProps()}
            onClick={handleClick}
            className={`flex flex-col items-center justify-center w-full h-44 rounded-2xl cursor-pointer bg-surface-secondary hover:bg-neutral-50 transition-colors ${
              isDragActive ? 'border-2 border-dashed border-blue-500 bg-blue-50' : ''
            }`}
          >
            <input
              type="file"
              accept="image/png,image/jpeg"
              onChange={handleFileChange}
              ref={fileInputRef}
              className="hidden"
            />
            <div className="text-neutral-500 text-center">
              <FontAwesomeIcon icon={faCloudArrowUp} className="h-8 w-8 mb-2" />
              <div className="mb-2">
                {isDragActive ? dropzoneText.active : dropzoneText.default}
              </div>
              <div className="text-sm">{dropzoneText.subText}</div>
            </div>
          </div>
        ) : (
          <div className="w-full block space-y-2">
            <div className="relative flex w-full bg-surface-secondary justify-center border border-gray-300 rounded-2xl items-center p-1">
              <div className="relative block space-y-2 w-full h-44">
                <Image
                  src={selectedImage}
                  alt="Selected image"
                  fill
                  className="object-contain"
                  sizes="(max-width: 500px) 100vw, 500px"
                />
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                color="red"
                variant="text"
                size="small"
                onClick={handleDelete}
                icon={faTrash}
                aria-label="Delete"
              >
                Delete
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
