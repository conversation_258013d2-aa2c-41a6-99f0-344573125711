import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import { getProfile } from '@/services/profile.service';

interface BasicInfoProps {
  userId: number;
}

export async function BasicInfo({ userId }: BasicInfoProps) {
  const response = await getProfile(userId);
  if (!response?.data?.data) {
    return null;
  }
  const { details } = response.data.data;

  return (
    <Card elevation="card">
      <CardHeader title="Details" className="mb-8" />

      <div className="space-y-4">
        <div>
          <p className="text-sm text-muted-foreground">Location</p>
          <p>
            {details.state}, {details.county}
          </p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">High School</p>
          <p>{details.highSchool}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Current GPA</p>
          <p>{details.currentGpa}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Class Rank</p>
          <p>{details.classRank}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Gender</p>
          <p>{details.gender}</p>
        </div>
      </div>
    </Card>
  );
}
