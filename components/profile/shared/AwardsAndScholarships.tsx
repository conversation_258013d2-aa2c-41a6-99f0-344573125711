import React from 'react';
import { faDiploma } from '@fortawesome/pro-regular-svg-icons';
import Richtext from '@/components/shared/blocks/Richtext';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import IconAward from '@/components/svgs/IconAward';
import { PublicAward, PublicScholarship } from '@/services/public-profile.service';

interface AwardsAndScholarshipsProps {
  awards?: PublicAward[];
  scholarships?: PublicScholarship[];
  isLoading?: boolean;
  error?: Error | null;
}

export function AwardsAndScholarships({
  awards,
  scholarships,
  isLoading,
  error,
}: AwardsAndScholarshipsProps) {
  const hasAwards = awards && awards.length > 0;
  const hasScholarships = scholarships && scholarships.length > 0;

  // Only return null if not loading, no error, and no data
  if (!isLoading && !error && !hasAwards && !hasScholarships) {
    return null;
  }

  return (
    <Card>
      <CardHeader title="Awards & Scholarships" titleIcon={faDiploma} className="mb-8" />

      <div className="space-y-6">
        {' '}
        {/* Added a container for content with consistent spacing */}
        {isLoading ? (
          <p>Loading awards and scholarships...</p>
        ) : error ? (
          <p className="text-red-500">Error loading awards and scholarships: {error.message}</p>
        ) : (
          <>
            {hasAwards && (
              <ul className="block space-y-4">
                {awards?.map((award, index) => (
                  <li key={`award-${index}`} className="flex gap-4">
                    <IconAward className="shrink-0" aria-hidden="true" />
                    <div className="block space-y-2">
                      <h6 className="text-sm font-bold text-text-primary">{award.name}</h6>
                      <Richtext content={award.details} className="prose-sm" />
                    </div>
                  </li>
                ))}
              </ul>
            )}
            {hasAwards && hasScholarships && <hr className="my-4" />}{' '}
            {/* Added margin to separator */}
            {hasScholarships && (
              <ul className="block space-y-4">
                {/* Consider adding a title like <h5>Scholarships</h5> if needed, similar to how it was before */}
                {scholarships?.map(scholarship => (
                  <li key={scholarship.id} className="flex gap-4">
                    <IconAward className="shrink-0" aria-hidden="true" />
                    <div className="block space-y-2">
                      <h6 className="text-sm font-bold text-text-primary">{scholarship.name}</h6>
                      <Richtext content={scholarship.details} className="prose-sm" />
                    </div>
                  </li>
                ))}
              </ul>
            )}
            {/* If there are no awards and no scholarships but we didn't hit the early null return (e.g. loading just finished) */}
            {!hasAwards && !hasScholarships && <p>No awards or scholarships to display.</p>}
          </>
        )}
      </div>
    </Card>
  );
}
