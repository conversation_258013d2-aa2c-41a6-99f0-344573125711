'use client';

import { useState } from 'react';
import {
  faBadgeCheck,
  faMessageCheck,
  faPaperPlane,
  faRectanglesMixed,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Avatar from '@/components/shared/Avatar';
import Button from '@/components/shared/Button';
import Toggle from '@/components/shared/form/Toggle';
import PhotoGrid from '@/components/shared/PhotoGrid';
import { cn } from '@/lib/utils';
import { useModalStore } from '@/stores/modal.store';
import { Profile } from '@/types/profile';
import ShareModal from './modals/ShareModal';

interface EditableProfileHeaderProps {
  profile: Profile;
  isOwnProfile: boolean;
  onEdit?: () => void;
}

export function EditableProfileHeader({
  profile,
  isOwnProfile,
  // @ts-ignore
  onEdit,
}: EditableProfileHeaderProps) {
  const [activeTab, setActiveTab] = useState('about');
  const [isPublic, setIsPublic] = useState(false);
  const { open, close } = useModalStore();

  const tabs = [
    { id: 'about', label: 'About', icon: faRectanglesMixed },
    { id: 'badges', label: 'Badges & Certifications', icon: faBadgeCheck },
    { id: 'nominations', label: 'Nomination & Endorsements', icon: faMessageCheck },
  ];

  const handleOpenShareModal = () => {
    open(<ShareModal profileId={profile.id} onClose={close} />, '2xl');
  };

  // Use default banner if no photos are available
  const photos = profile.photos?.length
    ? profile.photos
    : [{ url: '/images/profile-banner.png', width: 1920, height: 1080 }];

  return (
    <div className="relative mb-8 pt-[40px]">
      <div className="pa-container relative mb-[40px]">
        {/* Photo Grid Background */}
        <PhotoGrid photos={photos} height={280} className="rounded-lg overflow-hidden" />

        {/* Avatar */}
        <div className="absolute top-[196px] left-[120px]">
          <div className="bg-surface-secondary rounded-full p-[12px]">
            <Avatar
              src={profile.avatarUrl}
              firstName={profile.firstName}
              lastName={profile.lastName}
              size="xl"
              hasAchievements={profile?.has_achievements ?? false}
            />
          </div>
        </div>

        {/* Navbar */}
        <div className="flex items-center justify-end gap-[12px] mt-[24px]">
          <div className="flex gap-[12px]">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  'py-[11px] px-[12px] relative text-[14px] leading-[1.2] flex items-center gap-[8px] text-text-blue',
                  activeTab === tab.id ? 'font-bold' : ''
                )}
              >
                <FontAwesomeIcon icon={tab.icon} className={cn('w-4 h-4 text-text-blue')} />
                {tab.label}
              </button>
            ))}
          </div>
          <div className="flex items-center gap-[12px]">
            {isOwnProfile && (
              <div className="flex items-center gap-[12px] px-[8px]">
                <span className="text-[14px] text-text-blue">Public View</span>
                <Toggle checked={isPublic} onChange={setIsPublic} />
              </div>
            )}
            <Button
              color="blue"
              size="small"
              icon={faPaperPlane}
              iconPosition="right"
              onClick={handleOpenShareModal}
            >
              Share
            </Button>
          </div>
        </div>
      </div>

      {/* Profile Info */}
      <div className="pa-container">
        <h1 className="text-3xl font-bold">
          {profile.firstName} {profile.lastName}
        </h1>
        <p className="text-gray-600">Class of {profile.details?.graduationYear}</p>
      </div>
    </div>
  );
}
