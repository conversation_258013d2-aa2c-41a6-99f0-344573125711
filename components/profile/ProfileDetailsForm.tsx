import React, { ChangeEvent } from 'react';
import SelectInput from '@/components/shared/form/SelectInput';
import SiteInput from '@/components/shared/form/SiteInput';
import { CareerInterest, ProfileDetails } from '@/types/profile';
import Button from '../shared/Button';
import { CareerInterestForm } from './CareerInterestForm';

interface ProfileDetailsFormProps {
  details: ProfileDetails;
  careerInterests: CareerInterest[];
  isLoading: boolean;
  error: string | null | undefined;
  onSave: () => void;
  onCancel: () => void;
  onChange: (details: ProfileDetails) => void;
  onCareerInterestsChange: (interests: CareerInterest[]) => void;
}

export function ProfileDetailsForm({
  details,
  careerInterests,
  isLoading,
  error,
  onSave,
  onCancel,
  onChange,
  onCareerInterestsChange,
}: ProfileDetailsFormProps) {
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSave();
  };

  const handleChange = (field: keyof ProfileDetails, value: string | number) => {
    onChange({
      ...details,
      [field]: value,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <SelectInput
            label="State"
            value={details.state || ''}
            onChange={(value: string) => handleChange('state', value)}
            options={[{ label: 'Michigan', value: 'Michigan' }]} // This should come from a proper list
          />
        </div>
        <div>
          <SelectInput
            label="County"
            value={details.county || ''}
            onChange={(value: string) => handleChange('county', value)}
            options={[{ label: 'Dupage', value: 'Dupage' }]} // This should come from a proper list
          />
        </div>
      </div>

      <SiteInput
        label="High School"
        value={details.highSchool || ''}
        onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange('highSchool', e.target.value)}
      />

      <div className="grid grid-cols-2 gap-4">
        <SelectInput
          label="Graduation Year"
          value={details.graduationYear?.toString() || ''}
          onChange={(value: string) => handleChange('graduationYear', parseInt(value))}
          options={[{ label: '2026', value: '2026' }]} // This should be generated dynamically
        />
        <SiteInput
          label="Current GPA"
          type="number"
          step="0.01"
          min="0"
          max="4.0"
          value={details.currentGpa?.toString() || ''}
          onChange={(e: ChangeEvent<HTMLInputElement>) =>
            handleChange('currentGpa', parseFloat(e.target.value))
          }
        />
      </div>

      <SiteInput
        label="Class Rank"
        placeholder="e.g. 16/402"
        value={details.classRank || ''}
        onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange('classRank', e.target.value)}
      />

      <div className="grid grid-cols-3 gap-4">
        <SelectInput
          label="Gender"
          value={details.gender || ''}
          onChange={(value: string) => handleChange('gender', value)}
          options={[
            { label: 'Male', value: 'Male' },
            { label: 'Female', value: 'Female' },
            { label: 'Other', value: 'Other' },
          ]}
        />
        <SiteInput
          label="Height"
          placeholder="e.g. 5'11&quot;"
          value={details.height || ''}
          onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange('height', e.target.value)}
        />
        <SiteInput
          label="Weight"
          placeholder="e.g. 160 lbs."
          value={details.weight || ''}
          onChange={(e: ChangeEvent<HTMLInputElement>) => handleChange('weight', e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">Career Interests</label>
        <CareerInterestForm interests={careerInterests} onChange={onCareerInterestsChange} />
      </div>

      {error && <p className="text-red-500 text-sm">{error}</p>}

      <div className="flex justify-between pt-4">
        <Button color="white" variant="text" onClick={onCancel} type="button" disabled={isLoading}>
          Cancel
        </Button>
        <Button color="blue" type="submit" disabled={isLoading}>
          Save
        </Button>
      </div>
    </form>
  );
}
