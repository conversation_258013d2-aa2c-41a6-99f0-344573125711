'use client';

import React from 'react';
import Richtext from '@/components/shared/blocks/Richtext';

interface StoryDetailsProps {
  content: string;
}

export function StoryDetails({ content }: StoryDetailsProps) {
  if (!content) {
    return <p className="text-text-secondary italic">No story shared yet.</p>;
  }

  return <Richtext content={content} className="prose-sm text-text-secondary" />;
}
