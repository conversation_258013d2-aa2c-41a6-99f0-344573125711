'use client';

import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useDraggable,
  useDroppable,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { faGripVertical, faTrash } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import SelectInput from '@/components/shared/form/SelectInput';
import { useCareerInterestStore } from '@/stores/careerInterestStore';
import type { CareerInterest } from '@/types/profile';

interface CareerInterestFormProps {
  interests: CareerInterest[];
  onChange: (interests: CareerInterest[]) => void;
}

interface DroppableContainerProps {
  id: string;
  children: React.ReactNode;
}

function DroppableContainer({ id, children }: DroppableContainerProps) {
  const { setNodeRef } = useDroppable({ id });
  return <div ref={setNodeRef}>{children}</div>;
}

interface DraggableInterestItemProps {
  interest: CareerInterest;
  interestOptions: { label: string; value: string }[];
  onUpdateInterest: (value: string) => void;
  onRemove: () => void;
}

function DraggableInterestItem({
  interest,
  interestOptions,
  onUpdateInterest,
  onRemove,
}: DraggableInterestItemProps) {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({
    id: interest.id.toString(),
  });

  const style = transform
    ? {
        transform: CSS.Transform.toString(transform),
      }
    : undefined;

  return (
    <div ref={setNodeRef} style={style} className="flex gap-4 items-start touch-none mb-4">
      <div {...attributes} {...listeners} className="pt-3 cursor-grab text-gray-400">
        <FontAwesomeIcon icon={faGripVertical} />
      </div>
      <div className="flex-1">
        <SelectInput
          label="Career Interest"
          value={interest.slug}
          onChange={onUpdateInterest}
          options={interestOptions}
          placeholder="Select a career interest"
          hideLabel
        />
      </div>
      <button onClick={onRemove} className="pt-3 text-gray-400 hover:text-red-600">
        <FontAwesomeIcon icon={faTrash} />
      </button>
    </div>
  );
}

export function CareerInterestForm({ interests = [], onChange }: CareerInterestFormProps) {
  const availableInterests = useCareerInterestStore(state => state.careers);
  const sensors = useSensors(useSensor(PointerSensor), useSensor(KeyboardSensor));

  const handleAddInterest = () => {
    const newInterest: CareerInterest = {
      id: Date.now(), // Use timestamp as temporary ID
      name: '',
      slug: '',
    };
    onChange([...interests, newInterest]);
  };

  const handleRemoveInterest = (index: number) => {
    onChange(interests.filter((_, i) => i !== index));
  };

  const handleUpdateInterest = (index: number, value: string) => {
    const careerInfo = availableInterests.find(c => c.slug === value);
    if (!careerInfo) return;

    const newInterests = [...interests];
    newInterests[index] = {
      ...newInterests[index],
      name: careerInfo.label,
      slug: value,
    };
    onChange(newInterests);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = interests.findIndex(interest => interest.id.toString() === active.id);
      const newIndex = interests.findIndex(interest => interest.id.toString() === over.id);

      const newInterests = [...interests];
      const [movedItem] = newInterests.splice(oldIndex, 1);
      newInterests.splice(newIndex, 0, movedItem);

      onChange(newInterests);
    }
  };

  const interestOptions = availableInterests.map(interest => ({
    label: interest.label,
    value: interest.slug,
  }));

  return (
    <div className="space-y-6">
      <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <div className="space-y-4">
          {interests.map((interest, index) => (
            <DroppableContainer key={interest.id} id={interest.id.toString()}>
              <DraggableInterestItem
                interest={interest}
                interestOptions={interestOptions}
                onUpdateInterest={value => handleUpdateInterest(index, value)}
                onRemove={() => handleRemoveInterest(index)}
              />
            </DroppableContainer>
          ))}
        </div>
      </DndContext>

      <Button
        color="white"
        variant="text"
        onClick={handleAddInterest}
        type="button"
        className="w-full"
      >
        Add Career Interest
      </Button>
    </div>
  );
}
