'use client';

import React from 'react';
import Tag, { CAREER_TAG_ICONS } from '@/components/shared/Tag';
import { useCareerInterestStore } from '@/stores/careerInterestStore';
import type { CareerInterest } from '@/types/profile';

interface CareerInterestDetailsProps {
  interests: CareerInterest[];
}

export function CareerInterestDetails({ interests = [] }: CareerInterestDetailsProps) {
  const availableInterests = useCareerInterestStore(state => state.careers);

  if (interests.length === 0) {
    return <p className="text-gray-500 italic">No career interests listed yet.</p>;
  }

  return (
    <div className="flex flex-wrap gap-2">
      {interests.map(interest => {
        const interestInfo = availableInterests.find(i => i.slug === interest.slug);
        const label = interestInfo?.label || interest.name;

        return <Tag key={interest.id} label={label} icon={CAREER_TAG_ICONS[label]} />;
      })}
    </div>
  );
}
