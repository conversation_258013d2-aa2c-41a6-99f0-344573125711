'use client';

import React from 'react';
import { faGlobe } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { FacebookIcon } from '@/components/icons/FacebookIcon';
import { HudlIcon } from '@/components/icons/HudlIcon';
import { InstagramIcon } from '@/components/icons/InstagramIcon';
import { TwitterIcon } from '@/components/icons/TwitterIcon';
import { useAuth } from '@/hooks/useAuth';
import { useUnifiedProfile, type UnifiedProfileDetails } from '@/hooks/useUnifiedProfile';
import type { ProfileDetails } from '@/services/positive-athlete-profile.service';
import { ProfileTypes } from '@/stores/auth.store';
import { useProfileStore } from '@/stores/profileStore';
import { CareerInterest, Profile } from '@/types/profile';
import { CareerInterestDetails } from './CareerInterestDetails';

interface ProfileDetailsProps {
  profile: Pick<Profile, 'socialLinks'> & {
    careerInterests: CareerInterest[];
  };
}

// Type guards
function isUnifiedProfileDetails(details: any): details is UnifiedProfileDetails {
  return (
    details && typeof details === 'object' && ('interests' in details || 'interest_ids' in details)
  );
}

function isProfileDetails(details: any): details is ProfileDetails {
  return details && typeof details === 'object';
}

export function ProfileDetails({ profile }: ProfileDetailsProps) {
  const { profileType } = useAuth();
  const { details } = useUnifiedProfile();

  // Profile type checks
  const isCollegeAthlete = profileType === ProfileTypes.COLLEGE_ATHLETE;
  const isProfessional = profileType === ProfileTypes.PROFESSIONAL;
  const isPositiveAthlete = profileType === ProfileTypes.POSITIVE_ATHLETE;

  // Fallback to store for backward compatibility, but prioritize unified profile for alumni
  const storeDetails = useProfileStore(state => state.details);
  const storeCareerInterests = useProfileStore(state => state.careerInterests);

  const { socialLinks, careerInterests = [] } = profile;

  // For college athletes and professionals, prioritize unified profile data
  // For positive athletes, fallback to store if unified data is not available
  const currentDetails = isCollegeAthlete || isProfessional ? details : details || storeDetails;

  const currentCareerInterests =
    details?.interests || storeCareerInterests.length > 0 ? storeCareerInterests : careerInterests;

  const DetailRow = ({ label, value }: { label: string; value?: string | number | null }) => {
    if (!value) return null;
    return (
      <div className="flex items-start gap-[8px]">
        <span className="text-text-primary font-bold">{label}:</span>
        <span className="text-text-secondary">{value}</span>
      </div>
    );
  };

  // Ensure URL has protocol
  const ensureProtocol = (url: string | undefined | null): string => {
    if (!url) return '';

    // If URL already has protocol, return as is
    if (/^https?:\/\//i.test(url)) {
      return url;
    }

    // Otherwise, add https:// protocol
    return `https://${url}`;
  };

  // Safe property access for location
  const getLocation = (): string | undefined => {
    if (!currentDetails) return undefined;

    const state = currentDetails.state;
    let county: string | undefined;

    if (isUnifiedProfileDetails(currentDetails)) {
      county = currentDetails.county_name || undefined;
    } else if (isProfileDetails(currentDetails)) {
      county = (currentDetails as any).county || undefined;
    }

    return [state, county].filter(Boolean).join(', ') || undefined;
  };

  // Safe property access for different profile types
  const getFieldValue = (
    unifiedField: keyof UnifiedProfileDetails,
    legacyField?: string
  ): string | number | undefined => {
    if (!currentDetails) return undefined;

    let value: any;
    if (isUnifiedProfileDetails(currentDetails)) {
      value = currentDetails[unifiedField];
    } else if (legacyField && isProfileDetails(currentDetails)) {
      value = (currentDetails as any)[legacyField];
    }

    // Convert null to undefined
    return value === null ? undefined : value;
  };

  // Get social links from unified profile if available, otherwise from props
  const currentSocialLinks = details
    ? {
        instagram: details.instagram,
        facebook: details.facebook,
        twitter: details.twitter,
        hudl: details.hudl,
        website: details.website || details.custom_link,
      }
    : socialLinks;

  return (
    <div className="">
      {/* Basic Details */}
      <div className="mb-6">
        <DetailRow label="Location" value={getLocation()} />

        {/* Alumni-specific fields */}
        {isCollegeAthlete && <DetailRow label="College" value={getFieldValue('college')} />}

        {isProfessional && (
          <>
            <DetailRow label="Employer" value={getFieldValue('employer')} />
            <DetailRow label="Job Title" value={getFieldValue('job_title')} />
          </>
        )}

        {/* High school field for non-professionals */}
        {!isProfessional && (
          <DetailRow label="High School" value={getFieldValue('school_name', 'highSchool')} />
        )}

        {/* Academic fields */}
        {(isPositiveAthlete || isCollegeAthlete) && (
          <>
            <DetailRow
              label={isCollegeAthlete ? 'College GPA' : 'Current GPA'}
              value={(() => {
                // For college athletes, ensure we get GPA from unified profile (college athlete endpoint)
                // which contains the metadata GPA, not the user table GPA
                const gpa = isCollegeAthlete
                  ? details?.gpa // Prioritize unified profile GPA for college athletes
                  : getFieldValue('gpa', 'currentGpa');
                return typeof gpa === 'number' ? gpa.toFixed(2) : undefined;
              })()}
            />
            <DetailRow label="Class Rank" value={getFieldValue('class_rank', 'classRank')} />
          </>
        )}

        <DetailRow label="Gender" value={getFieldValue('gender')} />
      </div>

      {/* Social Media Links */}
      <div className="mb-[32px] flex flex-col gap-[16px]">
        {Object.values(currentSocialLinks ?? {}).some(link => link) && (
          <div className="flex gap-[16px]">
            {currentSocialLinks?.instagram && (
              <a
                href={ensureProtocol(currentSocialLinks.instagram)}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-[#E31837] transition-colors"
              >
                <InstagramIcon className="w-5 h-5" />
              </a>
            )}
            {currentSocialLinks?.facebook && (
              <a
                href={ensureProtocol(currentSocialLinks.facebook)}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-[#E31837] transition-colors"
              >
                <FacebookIcon className="w-5 h-5" />
              </a>
            )}
            {currentSocialLinks?.twitter && (
              <a
                href={ensureProtocol(currentSocialLinks.twitter)}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-[#E31837] transition-colors"
              >
                <TwitterIcon className="w-5 h-5" />
              </a>
            )}
            {currentSocialLinks?.hudl && (
              <a
                href={ensureProtocol(currentSocialLinks.hudl)}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-[#E31837] transition-colors"
              >
                <HudlIcon className="w-5 h-5" />
              </a>
            )}
          </div>
        )}

        {/* Website */}
        {currentSocialLinks?.website && (
          <div>
            <a
              href={ensureProtocol(currentSocialLinks.website)}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 text-gray-600 hover:text-[#E31837] transition-colors"
            >
              <FontAwesomeIcon icon={faGlobe} className="w-4 h-4" />
              <span>Personal Website</span>
            </a>
          </div>
        )}
      </div>

      {/* Career Interests */}
      <div>
        <h3 className="text-[14px] font-bold text-primary mb-[12px]">Career Interests:</h3>
        <CareerInterestDetails interests={currentCareerInterests} />
      </div>
    </div>
  );
}
