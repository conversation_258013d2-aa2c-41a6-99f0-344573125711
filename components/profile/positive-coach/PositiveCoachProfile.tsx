import React, { useState } from 'react';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faTrophy } from '@fortawesome/pro-regular-svg-icons';
import { TabGroup, TabPanel, TabPanels } from '@headlessui/react';
import { UploadAvatarModal } from '@/components/profile/modals/UploadAvatarModal';
import { ResumeBuilder } from '@/components/profile/positive-athlete/ResumeBuilder';
import { Awards } from '@/components/profile/sections/Awards';
import { CommunityInvolvement } from '@/components/profile/sections/CommunityInvolvement';
import { Details } from '@/components/profile/sections/Details';
import { NominationsAndEndorsements } from '@/components/profile/sections/NominationsAndEndorsements';
import { Resume } from '@/components/profile/sections/Resume';
import Sports from '@/components/profile/sections/Sports';
import { Story } from '@/components/profile/sections/Story';
import { WorkExperience as TeamSuccesses } from '@/components/profile/sections/WorkExperience';
import { AwardsAndScholarships } from '@/components/profile/shared/AwardsAndScholarships';
import { positiveCoachProfileTabsList } from '@/components/profile/shared/constants';
import { ProfileHeader } from '@/components/profile/shared/ProfileHeader';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import { usePublicProfile } from '@/hooks/usePublicProfile';
import { useAuthStore } from '@/stores/auth.store';
import { useModalStore } from '@/stores/modal.store';

export function PositiveCoachProfile() {
  const { user } = useAuthStore();
  const { open } = useModalStore();
  const { updateAvatar } = usePositiveAthleteProfile();
  const [activeTab, setActiveTab] = useState('about');
  const [isPublicViewToggled, setIsPublicViewToggled] = useState(false);
  const [isResumeBuilderActive, setIsResumeBuilderActive] = useState(false);

  const userIdAsString = user?.id ? String(user.id) : '';
  const { achievements, isLoadingAchievements, achievementsError } =
    usePublicProfile(userIdAsString);

  const userInfo = user
    ? {
        first_name: user.first_name,
        last_name: user.last_name,
        profile_type: user.profile_type,
      }
    : {};

  const handleAvatarUpload = async (file: File): Promise<any> => {
    try {
      const result = await updateAvatar({ file });
      return result;
    } catch (error: any) {
      console.error('Failed to upload avatar:', error);
      // Re-throw to let the upload handler manage the error display
      throw error;
    }
  };

  if (isResumeBuilderActive) {
    return <ResumeBuilder onBack={() => setIsResumeBuilderActive(false)} />;
  }

  // Convert activeTab string to index for Headless UI
  const getTabIndex = (tabId: string) => {
    return positiveCoachProfileTabsList.findIndex(tab => tab.id === tabId);
  };

  // Handle tab change from Headless UI (index) to our component (id)
  const handleTabChange = (index: number) => {
    setActiveTab(positiveCoachProfileTabsList[index].id);
  };

  const handlePublicViewToggle = (isPublic: boolean) => {
    setIsPublicViewToggled(isPublic);
  };

  const selectedIndex = getTabIndex(activeTab);

  return (
    <div className="min-h-screen bg-surface-secondary">
      <TabGroup selectedIndex={selectedIndex} onChange={handleTabChange}>
        <ProfileHeader
          user={userInfo}
          tabList={positiveCoachProfileTabsList}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          isPublic={isPublicViewToggled}
          profileId={user?.id}
          onPublicChange={handlePublicViewToggle}
          onAvatarClick={() => open(<UploadAvatarModal onSave={handleAvatarUpload} />, '2xl')}
        />

        <TabPanels>
          {/* About Tab Panel */}
          <TabPanel>
            <div className="pa-container pb-20 bg-surface-secondary">
              <div className="pa-profile-grid">
                {/* Left Column */}
                <div className="pa-profile-grid-left">
                  <Details isViewOnly={isPublicViewToggled} />

                  <Sports
                    cardHeaderTitle="Sports coached"
                    cardHeaderViewOnlyTitle="Sports coached"
                    isViewOnly={isPublicViewToggled}
                  />

                  {/* <Awards /> */}
                  <AwardsAndScholarships
                    awards={achievements?.awards}
                    scholarships={achievements?.scholarships}
                    isLoading={isLoadingAchievements}
                    error={achievementsError}
                  />
                </div>

                {/* Main Content - Center and Right */}
                <div className="pa-profile-grid-right">
                  <Story cardHeaderTitle="Story" isViewOnly={isPublicViewToggled} />

                  <TeamSuccesses
                    cardHeaderTitle="Team successes"
                    cardHeaderViewOnlyTitle="Team successes"
                    cardHeaderTitleIcon={faTrophy as IconDefinition}
                    isViewOnly={isPublicViewToggled}
                  />

                  <CommunityInvolvement
                    cardHeaderTitle="School / Community Involvement"
                    isViewOnly={isPublicViewToggled}
                  />

                  <Resume
                    isViewOnly={isPublicViewToggled}
                    onEdit={() => setIsResumeBuilderActive(true)}
                    className="hidden lg:block"
                  />
                </div>
              </div>
            </div>
          </TabPanel>

          {/* Nominations & Endorsements Tab Panel */}
          <TabPanel>
            <div className="pa-container pb-20 bg-surface-secondary">
              <NominationsAndEndorsements userId={user?.id} displayEndorsementButton={false} />
            </div>
          </TabPanel>
        </TabPanels>
      </TabGroup>
    </div>
  );
}
