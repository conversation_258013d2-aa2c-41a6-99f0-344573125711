'use client';

import React from 'react';
import { DynamicListEditor } from '@/components/shared/dynamic-list';
import type { DynamicListItem } from '@/components/shared/dynamic-list/types';
import ModalFormContainer from '@/components/shared/form/ModalFormContainer';
import SiteInput from '@/components/shared/form/SiteInput';
import { Textarea } from '@/components/shared/form/Textarea';
import type { WorkExperience } from '@/services/positive-athlete-profile.service';
import { useModalStore } from '@/stores/modal.store';

// Interface for internal use with DynamicListEditor
interface WorkExperienceItem extends DynamicListItem {
  name: string;
  date: string;
  description: string;
  originalId?: number; // Store the original numeric ID if it exists
}

interface EditWorkExperienceModalProps {
  editDialogTitle?: string;
  editDialogDescription?: string;
  initialExperiences: WorkExperience[];
  onSave: (experiences: WorkExperience[]) => void;
  isSaving?: boolean;
  error?: string | null;
  titleContext?: string;
}

export function EditWorkExperienceModal({
  editDialogTitle,
  editDialogDescription,
  initialExperiences,
  onSave,
  isSaving = false,
  error = null,
  titleContext = 'Work Experience',
}: EditWorkExperienceModalProps) {
  const { close } = useModalStore();

  // Convert WorkExperience[] to WorkExperienceItem[] for internal use
  const [experiences, setExperiences] = React.useState<WorkExperienceItem[]>(
    initialExperiences.map(exp => ({
      // Always use string ID for DynamicListEditor compatibility
      id: exp.id ? exp.id.toString() : `temp-${Math.random()}`,
      name: exp.name,
      date: exp.date,
      description: exp.description,
      // Store original numeric ID if it exists
      originalId: exp.id,
    }))
  );

  const [validationError, setValidationError] = React.useState<string | null>(null);

  const handleAdd = () => {
    const newExperience: WorkExperienceItem = {
      id: `temp-${Math.random()}`, // Temporary string ID for new experiences
      name: '',
      date: '',
      description: '',
    };
    setExperiences([...experiences, newExperience]);
    setValidationError(null);
  };

  const handleRemove = (id: string) => {
    setExperiences(experiences.filter(exp => exp.id !== id));
    setValidationError(null);
  };

  const handleUpdate = (id: string, updates: Partial<Omit<WorkExperienceItem, 'id'>>) => {
    setExperiences(experiences.map(exp => (exp.id === id ? { ...exp, ...updates } : exp)));
  };

  const handleReorder = (reorderedItems: WorkExperienceItem[]) => {
    setExperiences(reorderedItems);
  };

  const validateForm = (): boolean => {
    const hasEmptyFields = experiences.some(item => !item.name || !item.date || !item.description);

    if (hasEmptyFields) {
      setValidationError('Please fill in all fields for each work experience.');
      return false;
    }

    return true;
  };

  const handleSave = () => {
    if (!validateForm()) return;

    // Convert WorkExperienceItem[] back to WorkExperience[] for API
    const workExperiences: WorkExperience[] = experiences.map(exp => {
      // For experiences with original numeric IDs, include the ID
      if (exp.originalId) {
        return {
          id: exp.originalId,
          name: exp.name,
          date: exp.date,
          description: exp.description,
        };
      }

      // For new experiences, don't include an ID
      return {
        name: exp.name,
        date: exp.date,
        description: exp.description,
      };
    });

    onSave(workExperiences);
  };

  const handleClose = () => {
    close();
  };

  return (
    <ModalFormContainer
      title={
        editDialogTitle ||
        (titleContext === 'Team successes' ? 'Edit Team Successes' : 'Edit Work Experience')
      }
      description={
        editDialogDescription ||
        (titleContext === 'Team successes'
          ? "Highlight your team's achievements, including tournaments, championships, and special recognition."
          : "Highlight any work experience you've done like internships, part-time jobs, or summer gigs.")
      }
      isLoading={isSaving}
      error={error}
      validationError={error && (validationError || error || 'An error occurred')}
      handleSave={handleSave}
      handleClose={handleClose}
    >
      {/* Dynamic List Editor */}
      <DynamicListEditor
        items={experiences}
        onAdd={handleAdd}
        onRemove={handleRemove}
        onUpdate={handleUpdate}
        onReorder={handleReorder}
        getTitle={item =>
          item.name || (titleContext === 'Team successes' ? 'New Team Success' : 'New Experience')
        }
        renderFields={item => (
          <>
            <SiteInput
              label={item.name}
              value={item.name}
              onChange={e => handleUpdate(item.id, { name: e.target.value })}
              placeholder={
                titleContext === 'Team successes'
                  ? 'e.g., District Championship'
                  : 'e.g., Intern at Steerage Engineering'
              }
              hideLabel
            />

            <SiteInput
              label="Date or Range"
              type="text"
              value={item.date}
              onChange={e => handleUpdate(item.id, { date: e.target.value })}
              placeholder="e.g., 2022-2023"
            />

            <Textarea
              label="Description"
              value={item.description}
              onChange={e => handleUpdate(item.id, { description: e.target.value })}
              placeholder={
                titleContext === 'Team successes'
                  ? 'Describe the achievement and its significance'
                  : 'Briefly describe this experience'
              }
              rows={4}
            />
          </>
        )}
        addButtonText="Add Another Item"
      />
    </ModalFormContainer>
  );
}
