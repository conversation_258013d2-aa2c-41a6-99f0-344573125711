import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import {
  faFacebookF as faFacebook,
  faInstagram,
  faXTwitter,
} from '@fortawesome/free-brands-svg-icons';
import { faChevronUp } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Disclosure, DisclosureButton, DisclosurePanel, Transition } from '@headlessui/react';
import clsx from 'clsx';
import SiteInput from '@/components/shared/form/SiteInput';

// Generic interface for social links data
interface SocialLinksData {
  instagram?: string | null;
  facebook?: string | null;
  twitter?: string | null;
  hudl?: string | null;
  custom_link?: string | null;
}

interface EditDetailsModalSocialLinksProps {
  formData: SocialLinksData;
  onChange: (field: keyof SocialLinksData) => (e: React.ChangeEvent<HTMLInputElement>) => void;
}

interface ValidationState {
  [key: string]: {
    isValid: boolean;
    message: string;
  };
}

export function EditDetailsModalSocialLinks({
  formData,
  onChange,
}: EditDetailsModalSocialLinksProps) {
  const [validation, setValidation] = useState<ValidationState>({
    instagram: { isValid: true, message: '' },
    facebook: { isValid: true, message: '' },
    twitter: { isValid: true, message: '' },
    hudl: { isValid: true, message: '' },
    custom_link: { isValid: true, message: '' },
  });

  // Validate URLs
  const validateUrl = (url: string | undefined) => {
    if (!url) {
      // Empty values are valid (fields are optional)
      return { isValid: true, message: '' };
    }

    // Check if it has a protocol
    if (!/^https?:\/\//i.test(url)) {
      return {
        isValid: false,
        message: 'URL must start with http:// or https://',
      };
    }

    try {
      new URL(url);
      return { isValid: true, message: '' };
    } catch (e) {
      return { isValid: false, message: 'Please enter a valid URL' };
    }
  };

  // Custom change handler that validates on change
  const handleChange =
    (field: keyof SocialLinksData) => (e: React.ChangeEvent<HTMLInputElement>) => {
      // Call the parent onChange handler
      onChange(field)(e);

      // Validate and update validation state
      const result = validateUrl(e.target.value);
      setValidation(prev => ({
        ...prev,
        [field]: result,
      }));
    };

  return (
    <div className="bg-[#F7F7F7] p-6 rounded-2xl">
      <Disclosure defaultOpen={false}>
        <DisclosureButton className="w-full flex items-center justify-between group">
          <h3 className="text-lg font-semibold text-text-primary">Social Links</h3>
          <FontAwesomeIcon
            icon={faChevronUp}
            className="h-4 w-4 text-text-primary transition-transform duration-200 rotate-180 group-data-[open]:rotate-0"
          />
        </DisclosureButton>
        <DisclosurePanel className="space-y-4 mt-6">
          <SiteInput
            label="INSTAGRAM LINK"
            name="instagram"
            type="text"
            value={formData.instagram}
            onChange={handleChange('instagram')}
            placeholder="https://instagram.com/username"
            icon={faInstagram}
            className="pl-12"
            isFailedValidation={!validation.instagram.isValid}
            description={validation.instagram.message}
          />
          <SiteInput
            label="FACEBOOK LINK"
            name="facebook"
            type="text"
            value={formData.facebook}
            onChange={handleChange('facebook')}
            placeholder="https://facebook.com/username"
            icon={faFacebook}
            className="pl-12"
            isFailedValidation={!validation.facebook.isValid}
            description={validation.facebook.message}
          />
          <SiteInput
            label="X LINK"
            name="twitter"
            type="text"
            value={formData.twitter}
            onChange={handleChange('twitter')}
            placeholder="https://twitter.com/username"
            icon={faXTwitter}
            className="pl-12"
            isFailedValidation={!validation.twitter.isValid}
            description={validation.twitter.message}
          />
          <SiteInput
            label="HUDL LINK"
            name="hudl"
            type="text"
            value={formData.hudl}
            onChange={handleChange('hudl')}
            placeholder="https://hudl.com/profile/username"
            icon={
              <Image
                src="/images/hudl-mark.svg"
                alt="Hudl"
                width={14}
                height={14}
                className="text-text-primary"
              />
            }
            className="pl-12"
            isFailedValidation={!validation.hudl.isValid}
            description={validation.hudl.message}
          />
          <SiteInput
            label="CUSTOM LINK"
            name="custom_link"
            type="text"
            value={formData.custom_link}
            onChange={handleChange('custom_link')}
            placeholder="https://example.com"
            isFailedValidation={!validation.custom_link.isValid}
            description={validation.custom_link.message}
          />
        </DisclosurePanel>
      </Disclosure>
    </div>
  );
}
