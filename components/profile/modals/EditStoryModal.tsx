'use client';

import React from 'react';
import ModalFormContainer from '@/components/shared/form/ModalFormContainer';
import { WysiwygEditor } from '@/components/shared/form/WysiwygEditor';
import { useModalStore } from '@/stores/modal.store';

interface EditStoryModalProps {
  initialContent?: string;
  onSave?: (content: string) => void;
}

export function EditStoryModal({ initialContent = '', onSave }: EditStoryModalProps) {
  const { close } = useModalStore();
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [content, setContent] = React.useState(initialContent);

  const handleSave = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (onSave) {
        await onSave(content);
        handleClose();
      }
    } catch (err) {
      setError('Failed to save your story. Please try again.');
      console.error('Error saving story:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    close();
  };

  return (
    <ModalFormContainer
      title="Edit Your Story"
      description="Share your journey and experiences. What makes you a positive athlete?"
      isLoading={isLoading}
      error={error}
      validationError={error && (error || 'An error occurred')}
      handleSave={handleSave}
      handleClose={handleClose}
    >
      <WysiwygEditor
        value={content}
        onChange={setContent}
        placeholder="Share your journey and experiences..."
      />
    </ModalFormContainer>
  );
}
