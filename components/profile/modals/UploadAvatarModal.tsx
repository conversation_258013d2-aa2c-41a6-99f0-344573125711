'use client';

import React from 'react';
import { AvatarEditor, AvatarEditorRef, UploadResult } from '@/components/shared/AvatarEditor';
import ModalFormContainer from '@/components/shared/form/ModalFormContainer';
import { useAvatarUpload } from '@/hooks/useAvatarUpload';
import { useModalStore } from '@/stores/modal.store';

interface UploadAvatarModalProps {
  onSave?: (file: File) => Promise<any>;
  onSuccess?: (result: any) => void;
  onError?: (error: string) => void;
}

export function UploadAvatarModal({ onSave, onSuccess, onError }: UploadAvatarModalProps) {
  const { close } = useModalStore();
  const [canSave, setCanSave] = React.useState(false);
  const editorRef = React.useRef<AvatarEditorRef>(null);

  const avatarUpload = useAvatarUpload({
    uploadFn: async (file: File) => {
      if (!onSave) {
        throw new Error('No upload function provided');
      }
      const result = await onSave(file);
      return result;
    },
    onSuccess: result => {
      if (onSuccess) {
        onSuccess(result);
      }
      handleClose();
    },
    onError: error => {
      if (onError) {
        onError(typeof error === 'string' ? error : error.message);
      }
      // Don't close modal on error - let user retry or manually close
    },
  });

  const handleSave = async () => {
    if (editorRef.current && !avatarUpload.isUploading) {
      try {
        // Get the file from the editor
        const file = await editorRef.current.getFile();

        if (file) {
          // Upload using the avatar upload hook
          await avatarUpload.upload(file);
        }
      } catch (error: any) {
        // Error is already handled by useAvatarUpload
      }
    }
  };

  const handleClose = () => {
    if (!avatarUpload.isUploading) {
      close();
    }
  };

  const handleEditorUpload = async (file: File): Promise<UploadResult> => {
    // This is called by AvatarEditor when using the upload pattern
    try {
      const result = await avatarUpload.upload(file);
      return result;
    } catch (error: any) {
      return {
        success: false,
        error: typeof error === 'string' ? error : error.message || 'Upload failed',
      };
    }
  };

  return (
    <ModalFormContainer
      title="Upload Profile Photo"
      description="Choose a photo to represent you on your profile. You can adjust and crop the image to make
            sure it looks just right."
      isLoading={avatarUpload.isUploading}
      allowSave={canSave && !avatarUpload.isUploading}
      handleSave={handleSave}
      handleClose={handleClose}
      error={avatarUpload.error}
    >
      <AvatarEditor
        ref={editorRef}
        onReadyToSave={ready => {
          setCanSave(ready);
        }}
        onUpload={onSave ? handleEditorUpload : undefined}
        className="w-[472px]"
        isUploading={avatarUpload.isUploading}
      />
    </ModalFormContainer>
  );
}
