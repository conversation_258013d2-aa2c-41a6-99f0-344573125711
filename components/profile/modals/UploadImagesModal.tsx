'use client';

import React from 'react';
import { useDropzone } from 'react-dropzone';
import Image from 'next/image';
import { faCheck, faTrash, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { AxiosError } from 'axios';
import clsx from 'clsx';
import Button from '@/components/shared/Button';
import { InfoCard } from '@/components/shared/cards/InfoCard';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import type { FocalPoint } from '@/services/positive-athlete-profile.service';
import { useModalStore } from '@/stores/modal.store';

interface ImageWithFocalPoint {
  file: File;
  focalPoint: FocalPoint;
  url: string;
}

interface UploadImagesModalProps {
  onSave?: (files: File[]) => void;
}

export function UploadImagesModal({ onSave }: UploadImagesModalProps) {
  const { close } = useModalStore();
  const {
    photos,
    updatePhotos,
    isUpdatingPhotos: isUpdating,
    photoUpdateError: updateError,
  } = usePositiveAthleteProfile();

  const [selectedImage, setSelectedImage] = React.useState<string | null>(null);
  const [selectedFiles, setSelectedFiles] = React.useState<ImageWithFocalPoint[]>([]);
  const [selectedThumbnail, setSelectedThumbnail] = React.useState<number>(0);
  const previewRef = React.useRef<HTMLDivElement>(null);
  const [isDraggingFocalPoint, setIsDraggingFocalPoint] = React.useState(false);
  const [photosToDelete, setPhotosToDelete] = React.useState<string[]>([]);

  // Cleanup URLs when component unmounts or files are removed
  React.useEffect(() => {
    return () => {
      selectedFiles.forEach(file => {
        URL.revokeObjectURL(file.url);
      });
    };
  }, [selectedFiles]);

  // Track focal point changes for existing photos
  const [modifiedFocalPoints, setModifiedFocalPoints] = React.useState<Record<string, FocalPoint>>(
    {}
  );

  // Memoize allPhotos to prevent unnecessary recalculations
  const allPhotos = React.useMemo(() => {
    const existingPhotos = photos
      .filter(photo => !photosToDelete.includes(photo.id))
      .map(photo => ({
        ...photo,
        focal_point: modifiedFocalPoints[photo.id] || photo.focal_point,
      }));

    return [
      ...existingPhotos,
      ...selectedFiles.map(f => ({
        id: f.url,
        url: f.url,
        thumbnail_url: f.url,
        width: null,
        height: null,
        order: 0,
        focal_point: f.focalPoint,
      })),
    ];
  }, [photos, selectedFiles, photosToDelete, modifiedFocalPoints]);

  // Memoize currentPhoto
  const currentPhoto = React.useMemo(
    () => allPhotos[selectedThumbnail],
    [allPhotos, selectedThumbnail]
  );

  // Initialize selectedFiles from existing photos
  React.useEffect(() => {
    if (photos.length > 0) {
      setSelectedImage(photos[0].url);
      setSelectedThumbnail(0);
      setPhotosToDelete([]);
      setModifiedFocalPoints({});
    }
  }, [photos]);

  const onDrop = React.useCallback(
    (acceptedFiles: File[]) => {
      const remainingSlots = 10 - (photos.length - photosToDelete.length);
      const newFiles = acceptedFiles
        .filter(file => file.type.startsWith('image/'))
        .slice(0, remainingSlots)
        .map(file => {
          const url = URL.createObjectURL(file);
          return {
            file,
            url,
            focalPoint: { x: 0.5, y: 0.5 }, // Center by default
          };
        });

      if (newFiles.length > 0) {
        setSelectedFiles(prev => {
          // Cleanup URLs for any files that will be removed
          const combined = [...prev, ...newFiles];
          const toRemove = combined.slice(remainingSlots);
          toRemove.forEach(file => URL.revokeObjectURL(file.url));
          return combined.slice(0, remainingSlots);
        });
        if (!selectedImage) {
          setSelectedImage(newFiles[0].url);
          setSelectedThumbnail(0);
        }
      }
    },
    [photos, photosToDelete.length, selectedImage]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png'],
    },
    maxSize: 5 * 1024 * 1024, // 5MB
    multiple: true,
    noClick: !!selectedImage, // Disable click when image is selected
  });

  const handleFocalPointMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDraggingFocalPoint(true);
  };

  const handleFocalPointMouseMove = React.useCallback(
    (e: MouseEvent) => {
      if (!isDraggingFocalPoint || !previewRef.current) return;

      const rect = previewRef.current.getBoundingClientRect();
      const x = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
      const y = Math.max(0, Math.min(1, (e.clientY - rect.top) / rect.height));

      // Update local state for both new and existing photos
      if (selectedThumbnail >= photos.length - photosToDelete.length) {
        setSelectedFiles(prev =>
          prev.map((file, i) =>
            i === selectedThumbnail - (photos.length - photosToDelete.length)
              ? { ...file, focalPoint: { x, y } }
              : file
          )
        );
      } else {
        const photo = photos[selectedThumbnail];
        if (photo) {
          setModifiedFocalPoints(prev => ({
            ...prev,
            [photo.id]: { x, y },
          }));
        }
      }
    },
    [isDraggingFocalPoint, selectedThumbnail, photos, photosToDelete.length]
  );

  const handleFocalPointMouseUp = React.useCallback(() => {
    setIsDraggingFocalPoint(false);
  }, []);

  React.useEffect(() => {
    if (isDraggingFocalPoint) {
      window.addEventListener('mousemove', handleFocalPointMouseMove);
      window.addEventListener('mouseup', handleFocalPointMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleFocalPointMouseMove);
      window.removeEventListener('mouseup', handleFocalPointMouseUp);
    };
  }, [isDraggingFocalPoint, handleFocalPointMouseMove, handleFocalPointMouseUp]);

  // Memoize handlers
  const handleThumbnailClick = React.useCallback(
    (index: number) => {
      setSelectedThumbnail(index);
      if (index < photos.length) {
        setSelectedImage(photos[index].url);
      } else {
        setSelectedImage(selectedFiles[index - photos.length].url);
      }
    },
    [photos, selectedFiles]
  );

  const handleDelete = React.useCallback(
    (index: number, e?: React.MouseEvent) => {
      e?.stopPropagation();

      const photoToDelete = allPhotos[index];
      if (!photoToDelete) return;

      // If deleting an existing photo
      if (index < photos.length) {
        setPhotosToDelete(prev => [...prev, photoToDelete.id]);
      }
      // If deleting a new photo
      else {
        setSelectedFiles(prev => {
          const fileIndex = index - photos.filter(p => !photosToDelete.includes(p.id)).length;
          const fileToDelete = prev[fileIndex];
          if (fileToDelete) {
            URL.revokeObjectURL(fileToDelete.url);
          }
          return prev.filter((_, i) => i !== fileIndex);
        });
      }

      // Update selection after deletion
      const remainingPhotos = allPhotos.filter((_, i) => i !== index);
      if (remainingPhotos.length === 0) {
        setSelectedImage(null);
        setSelectedThumbnail(0);
      } else {
        // If we deleted the current selection or something before it
        if (index <= selectedThumbnail) {
          const newIndex = Math.max(0, selectedThumbnail - 1);
          setSelectedThumbnail(newIndex);
          const nextPhoto = remainingPhotos[newIndex];
          if (nextPhoto) {
            setSelectedImage(nextPhoto.url);
          }
        }
      }
    },
    [allPhotos, photos, photosToDelete, selectedThumbnail]
  );

  const handleSave = React.useCallback(() => {
    const hasChanges =
      selectedFiles.length > 0 ||
      photosToDelete.length > 0 ||
      Object.keys(modifiedFocalPoints).length > 0;

    if (hasChanges) {
      // For new photos
      const newPhotosData = selectedFiles.map(file => ({
        file: file.file,
        focal_point: file.focalPoint,
      }));

      // For existing photos with modified focal points
      const currentPhoto = photos[selectedThumbnail];
      let updatePhotoId: string | undefined;
      let focalPoint: FocalPoint | undefined;

      // If we have a current photo and it has modified focal points
      if (currentPhoto && modifiedFocalPoints[currentPhoto.id]) {
        updatePhotoId = currentPhoto.id;
        focalPoint = modifiedFocalPoints[currentPhoto.id];
      }

      updatePhotos(
        {
          photos: newPhotosData,
          update_photo_id: updatePhotoId,
          focal_point: focalPoint,
          delete_photo_ids: photosToDelete,
        },
        {
          onSuccess: () => {
            // Call onSave callback with the new files
            if (onSave) {
              onSave(selectedFiles.map(f => f.file));
            }
            // Reset all state
            setSelectedFiles([]);
            setPhotosToDelete([]);
            setModifiedFocalPoints({});
            close();
          },
        }
      );
    } else {
      close();
    }
  }, [
    selectedFiles,
    photosToDelete,
    modifiedFocalPoints,
    photos,
    selectedThumbnail,
    updatePhotos,
    close,
    onSave,
  ]);

  // Cleanup function for when modal is closed
  const handleClose = React.useCallback(() => {
    setSelectedFiles([]);
    setPhotosToDelete([]);
    setModifiedFocalPoints({});
    close();
  }, [close]);

  return (
    <div className="relative p-4 lg:p-[64px] w-full max-w-[600px] bg-white rounded-[32px] shadow-lg">
      {/* Close Button */}
      <button
        onClick={handleClose}
        className="absolute top-6 right-6 text-neutral-600 hover:text-neutral-800 transition-colors"
      >
        <FontAwesomeIcon icon={faXmark} className="text-lg" />
      </button>

      {/* Header */}
      <div className="mb-10">
        <h2 className="text-[24px] font-bold mb-4">Upload Images</h2>
        <p className="text-neutral-500">
          Add anywhere between 1 and 10 images to share on your profile. Move the focal point
          selector once you upload images to make sure they display optimally in your profile
          banner.
        </p>
      </div>

      {/* Error Message */}
      {updateError && (
        <div className="mb-6">
          <InfoCard
            type="error"
            message={
              updateError instanceof Error
                ? updateError.message
                : (updateError as AxiosError<{ message: string }>).response?.data?.message ||
                  'An error occurred while saving your photos. Please try again.'
            }
            buttonText="Try Again"
            onButtonClick={() => {
              if (selectedFiles.length > 0) {
                updatePhotos({
                  photos: selectedFiles.map(file => ({
                    file: file.file,
                    focal_point: file.focalPoint,
                  })),
                });
              }
            }}
          />
        </div>
      )}

      {/* Main Image Preview */}
      <div className="mb-10">
        <div
          ref={previewRef}
          {...(selectedImage ? {} : getRootProps())}
          className={clsx(
            // 'relative w-[472px] h-[240px] rounded-2xl border overflow-hidden transition-colors duration-200',
            'relative w-full max-w-[472px] aspect-video rounded-2xl border overflow-hidden transition-colors duration-200',
            {
              'border-blue-400 bg-blue-50': isDragActive,
              'border-neutral-300 bg-neutral-100': !isDragActive,
              'cursor-default': selectedImage,
              'cursor-pointer': !selectedImage,
            }
          )}
        >
          {!selectedImage && <input {...getInputProps()} />}
          {selectedImage && (
            <>
              <div className="relative w-full h-full">
                <Image
                  src={selectedImage}
                  alt="Selected photo"
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  priority
                  unoptimized
                />
              </div>
              <div
                onMouseDown={handleFocalPointMouseDown}
                className="absolute w-4 h-4 rounded-full bg-white border-2 border-[#002855] cursor-move hover:scale-110 transition-transform"
                style={{
                  left: `${(currentPhoto?.focal_point?.x ?? 0.5) * 100}%`,
                  top: `${(currentPhoto?.focal_point?.y ?? 0.5) * 100}%`,
                  transform: 'translate(-50%, -50%)',
                }}
              />
            </>
          )}
          {selectedImage ? (
            <div className="mt-2 px-1 flex justify-between text-sm text-neutral-500">
              <span>{currentPhoto?.id || 'filename.jpg'}</span>
              <button
                onClick={e => handleDelete(selectedThumbnail, e)}
                className="text-red-600 flex items-center gap-2"
              >
                Delete <FontAwesomeIcon icon={faTrash} />
              </button>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-neutral-500">
              <div className="w-4 h-4 rounded-full bg-neutral-200 border-2 border-white mb-4" />
              <p className="text-sm">
                {isDragActive
                  ? 'Drop images here...'
                  : 'Drag & drop images here, or click to select'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Thumbnail Grid */}
      <div className="mb-10">
        <div className="w-full max-w-[472px] flex flex-wrap gap-2 lg:gap-[18px]">
          {allPhotos.map((photo, index) => (
            <div
              key={photo.id}
              onClick={() => handleThumbnailClick(index)}
              className="group relative w-20 h-20 cursor-pointer"
            >
              <div
                className={`relative w-full h-full rounded-2xl overflow-hidden flex items-center justify-center ${
                  index === selectedThumbnail
                    ? 'border-[4px] border-[#002855] p-[2px]'
                    : 'border border-neutral-300'
                }`}
              >
                <div className="relative w-16 h-16">
                  <Image
                    src={photo.url}
                    alt={`Thumbnail ${index + 1}`}
                    fill
                    className="w-full h-full object-cover rounded-xl"
                    unoptimized
                  />
                </div>
                <button
                  onClick={e => {
                    e.stopPropagation();
                    handleDelete(index, e);
                  }}
                  className="absolute top-1 right-1 w-5 h-5 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                >
                  <FontAwesomeIcon icon={faTrash} className="text-white text-xs" />
                </button>
              </div>
            </div>
          ))}
          {allPhotos.length < 10 && (
            <button
              onClick={() => {
                const input = document.createElement('input');
                input.type = 'file';
                input.multiple = true;
                input.accept = 'image/*';
                input.onchange = e => {
                  const files = Array.from((e.target as HTMLInputElement).files || []);
                  onDrop(files);
                };
                input.click();
              }}
              className="w-20 h-20 rounded-2xl bg-neutral-100 flex flex-col items-center justify-center cursor-pointer hover:bg-blue-50 transition-colors duration-200 relative"
              style={{
                backgroundImage: `
                  linear-gradient(90deg, #D7DCE0 50%, transparent 50%),
                  linear-gradient(90deg, #D7DCE0 50%, transparent 50%),
                  linear-gradient(0deg, #D7DCE0 50%, transparent 50%),
                  linear-gradient(0deg, #D7DCE0 50%, transparent 50%)
                `,
                backgroundPosition: '0 0, 0 100%, 0 0, 100% 0',
                backgroundSize: '16px 1px, 16px 1px, 1px 16px, 1px 16px',
                backgroundRepeat: 'repeat-x, repeat-x, repeat-y, repeat-y',
              }}
            >
              <div className="text-neutral-500 text-2xl">+</div>
            </button>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-6">
        <Button
          color="blue"
          size="small"
          onClick={handleSave}
          disabled={
            !(
              selectedFiles.length > 0 ||
              photosToDelete.length > 0 ||
              Object.keys(modifiedFocalPoints).length > 0
            ) || isUpdating
          }
          icon={faCheck}
          iconPosition="right"
          className="h-10 px-4 py-2 text-sm font-semibold"
        >
          {isUpdating ? 'Saving...' : 'Save'}
        </Button>
        <Button
          color="white"
          size="small"
          onClick={handleClose}
          disabled={isUpdating}
          icon={faXmark}
          iconPosition="right"
          className="h-10 px-4 py-2 text-sm font-semibold text-navy-600"
        >
          Cancel
        </Button>
      </div>
    </div>
  );
}
