'use client';

import { useEffect, useState } from 'react';
import clsx from 'clsx';
import ComboboxInput from '@/components/shared/form/ComboboxInput';
import { Select } from '@/components/shared/form/deprecated/Select';
import ModalFormContainer from '@/components/shared/form/ModalFormContainer';
import SchoolComboboxInput from '@/components/shared/form/SchoolComboboxInput';
import SelectInput, { SelectOption } from '@/components/shared/form/SelectInput';
import SiteInput from '@/components/shared/form/SiteInput';
import { GRADUATION_YEARS } from '@/constants/education';
import { useAuth } from '@/hooks/useAuth';
import { useCounties } from '@/hooks/useCounties';
import { useSchools } from '@/hooks/useSchools';
import {
  useUnifiedProfile,
  type UnifiedProfileDetails,
  type UnifiedUpdateRequest,
} from '@/hooks/useUnifiedProfile';
import type { Interest } from '@/services/interest.service';
import { ProfileTypes, useAuthStore } from '@/stores/auth.store';
import { useModalStore } from '@/stores/modal.store';
import { STATE_OPTIONS, type StateCode } from '@/utils/constants/states';
import { EditDetailsModalSocialLinks } from './EditDetailsModalSocialLinks';
import { InterestSelector } from './InterestSelector';

interface FormData extends Omit<UnifiedProfileDetails, 'state' | 'county_id' | 'county_name'> {
  state: StateCode | null;
  county: { id: number; name: string } | null;
  selectedSchool: { id: number; name: string } | null;
  interests: Interest[];
}

interface ValidationError {
  field: string;
  message: string;
}

export function EditDetailsModal() {
  const { profileType } = useAuth();
  const { close } = useModalStore();
  const { details, isLoadingDetails, updateDetails, isUpdatingDetails, detailsUpdateError } =
    useUnifiedProfile();
  const {
    counties,
    isSearching,
    setSearchInput: setCountySearchInput,
    setState: setCountyState,
  } = useCounties();

  const isPositiveAthlete = profileType === ProfileTypes.POSITIVE_ATHLETE;
  const isPositiveCoach = profileType === ProfileTypes.POSITIVE_COACH;
  const isCollegeAthlete = profileType === ProfileTypes.COLLEGE_ATHLETE;
  const isProfessional = profileType === ProfileTypes.PROFESSIONAL;
  const isAthleticsDirector = profileType === ProfileTypes.ATHLETICS_DIRECTOR;
  const isAlumni = isCollegeAthlete || isProfessional;

  const showMetricsFields = isPositiveAthlete || isCollegeAthlete;
  const showCareerInterests = !isAthleticsDirector || !isPositiveCoach;
  const showCountyField = isPositiveAthlete || isPositiveCoach || isAthleticsDirector;
  const showHighSchoolField = isPositiveAthlete || isPositiveCoach || isAthleticsDirector;

  // const { isPositiveCoach } = useAuthStore();

  const [formData, setFormData] = useState<FormData>({
    state: null,
    county: null,
    school_id: null,
    school_name: null,
    graduation_year: null,
    gpa: null,
    class_rank: null,
    gender: null,
    height_in_inches: null,
    weight: null,
    selectedSchool: null,
    interests: [],
    interest_ids: [],
    // Alumni fields
    college: null,
    employer: null,
    job_title: null,
    // Social media fields
    instagram: null,
    facebook: null,
    twitter: null,
    hudl: null,
    website: null,
    custom_link: null,
  });

  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);

  // Set initial form data when details load
  useEffect(() => {
    if (details) {
      const formState = details.state as StateCode | null;
      setFormData({
        ...details,
        state: formState,
        county:
          details.county_id && details.county_name
            ? {
                id: details.county_id,
                name: details.county_name,
              }
            : null,
        selectedSchool: details.school_id
          ? {
              id: details.school_id,
              name: details.school_name ?? '',
            }
          : null,
        interests: details.interests ?? [],
      });

      // Initialize state in useCounties hook if it exists
      if (formState) {
        setCountyState(formState);
        // If we have a county_id, trigger a search to get the county name
        if (details.county_id) {
          setCountySearchInput('');
        }
      }
    }
  }, [details, setCountyState, setCountySearchInput]);

  const handleInputChange =
    (field: keyof UnifiedProfileDetails) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData(prev => ({
        ...prev,
        [field]: e.target.value,
      }));
    };

  const handleSelectChange = (field: keyof UnifiedProfileDetails) => (value: string) => {
    if (field === 'state') {
      const stateCode = value as StateCode;
      setFormData(prev => ({
        ...prev,
        [field]: stateCode,
        county: null,
        // Reset school when state changes
        school_id: null,
        school_name: null,
        selectedSchool: null,
      }));
      // Update state in useCounties hook
      setCountyState(stateCode);
      // Reset county search
      setCountySearchInput('');
    } else if (
      field === 'graduation_year' ||
      field === 'school_id' ||
      field === 'height_in_inches' ||
      field === 'weight'
    ) {
      setFormData(prev => ({
        ...prev,
        [field]: value ? Number(value) : null,
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  // Handle school selection
  const handleSchoolChange = (schoolId: number | null) => {
    setFormData(prev => ({
      ...prev,
      school_id: schoolId,
      // Clear the school_name as we're using the school_id now
      school_name: null,
      // Clear selectedSchool for UI state
      selectedSchool: null,
    }));
  };

  const validateSocialLinks = (): boolean => {
    const errors: ValidationError[] = [];
    const socialMediaFields = [
      'instagram',
      'facebook',
      'twitter',
      'hudl',
      'custom_link',
      'website',
    ];

    for (const field of socialMediaFields) {
      const value = formData[field as keyof FormData];

      if (value && typeof value === 'string') {
        // Check if URL has http or https protocol
        if (!/^https?:\/\//i.test(value)) {
          errors.push({
            field,
            message: `${field.replace('_', ' ').toUpperCase()} must start with http:// or https://`,
          });
        } else {
          // Validate URL format
          try {
            new URL(value);
          } catch (e) {
            errors.push({
              field,
              message: `${field.replace('_', ' ').toUpperCase()} is not a valid URL`,
            });
          }
        }
      }
    }

    setValidationErrors(errors);
    return errors.length === 0;
  };

  const handleClose = () => {
    close();
  };

  const handleSave = async () => {
    // Validate social links before saving
    if (!validateSocialLinks()) {
      return;
    }

    const apiData: UnifiedUpdateRequest = {
      ...formData,
      state: formData.state || undefined,
      county_id: formData.county?.id || null,
      school_id: formData.school_id || null,
      // Don't send school_name anymore as we're using school_id
      school_name: '',
    };

    // For coaches, don't send fields that aren't shown in the UI
    if (isPositiveCoach) {
      // Remove fields not applicable for coaches
      delete apiData.graduation_year;
      delete apiData.gpa;
      delete apiData.class_rank;
      delete apiData.height_in_inches;
      delete apiData.weight;
      delete apiData.interest_ids;
    } else {
      // For athletes, include interest_ids
      apiData.interest_ids = formData.interests.map(i => Number(i.id));
    }

    await updateDetails(apiData, {
      onSuccess: () => {
        close();
      },
    });
  };

  const handleInterestSelect = (interests: Interest[]) => {
    setFormData(prev => ({
      ...prev,
      interests,
      interest_ids: interests.map(i => Number(i.id)),
    }));
  };

  if (isLoadingDetails) {
    return <div>Loading...</div>;
  }

  // Convert readonly arrays to mutable arrays for the SelectInput component
  const stateOptions: SelectOption[] = [...STATE_OPTIONS];
  const graduationYearOptions: SelectOption[] = GRADUATION_YEARS.map(year => ({
    value: year.value,
    label: year.label,
  }));
  const genderOptions: SelectOption[] = [
    { value: 'male', label: 'Male' },
    { value: 'female', label: 'Female' },
    { value: 'other', label: 'Other' },
    { value: 'prefer_not_to_say', label: 'Prefer not to say' },
  ];

  return (
    <ModalFormContainer
      title="Edit Your Details"
      description="A complete profile will help match you with opportunities and connections, and will give
            you a better shot at winning awards!"
      isLoading={isUpdatingDetails}
      error={detailsUpdateError?.message}
      validationError={
        validationErrors.length > 0
          ? validationErrors.map(err => err.message).join('\n')
          : detailsUpdateError && 'Failed to save your details. Please try again.'
      }
      handleSave={handleSave}
      handleClose={handleClose}
    >
      <div className="space-y-8">
        {/* Location Section */}
        <div
          className={clsx('grid grid-cols-1 gap-4', {
            'md:grid-cols-2': showCountyField || isAlumni,
          })}
        >
          <SelectInput
            label="STATE"
            value={formData.state || ''}
            onChange={handleSelectChange('state')}
            options={stateOptions}
          />
          {showCountyField && (
            <ComboboxInput
              label="COUNTY"
              value={formData.county}
              onChange={county => {
                setFormData(prev => ({
                  ...prev,
                  county,
                  // Reset school when county changes
                  school_id: null,
                  school_name: null,
                  selectedSchool: null,
                }));
              }}
              options={counties || []}
              disabled={!formData.state}
              placeholder={formData.state ? 'Search for your county' : 'Select a state first'}
              onSearchChange={setCountySearchInput}
              isLoading={isSearching}
              loadingText="Loading counties..."
              emptyText="Type to search counties"
            />
          )}

          {/* Now these fields will be properly wired to the actual API */}
          {isCollegeAthlete && (
            <SiteInput
              label="College"
              value={formData.college}
              onChange={handleInputChange('college')}
            />
          )}

          {isProfessional && (
            <>
              <SiteInput
                label="Employer"
                value={formData.employer}
                onChange={handleInputChange('employer')}
              />
              <SiteInput
                label="Job Title"
                value={formData.job_title}
                onChange={handleInputChange('job_title')}
              />
            </>
          )}
        </div>

        {/* School Section */}
        <div
          className={clsx('grid grid-cols-1 gap-4', {
            'md:grid-cols-2': showHighSchoolField && isPositiveAthlete,
            hidden: isAlumni,
          })}
        >
          {showHighSchoolField && (
            <SchoolComboboxInput
              label="HIGH SCHOOL"
              value={formData.school_id ? Number(formData.school_id) : null}
              onChange={handleSchoolChange}
              stateCode={formData.state || undefined}
              countyId={formData.county?.id}
              disabled={!formData.state || !formData.county}
              placeholder={
                !formData.state || !formData.county
                  ? 'Select a state and county first'
                  : 'Search for your high school'
              }
            />
          )}

          {isPositiveAthlete && (
            <SelectInput
              label="GRADUATION YEAR"
              value={formData.graduation_year?.toString() || ''}
              onChange={handleSelectChange('graduation_year')}
              options={graduationYearOptions}
            />
          )}
        </div>

        {/* Academic Section - Only for athletes */}
        {isPositiveAthlete && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SiteInput
              label="CURRENT GPA"
              name="gpa"
              type="number"
              value={formData.gpa}
              onChange={handleInputChange('gpa')}
              min={0}
              max={4.5}
              step={0.01}
            />
            <SiteInput
              label="CLASS RANK"
              name="class_rank"
              value={formData.class_rank}
              onChange={handleInputChange('class_rank')}
              placeholder="e.g. 16/402"
            />
          </div>
        )}

        {/* Academic Section - Only for alumni */}
        {isCollegeAthlete && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SelectInput
              label="college graduation year"
              value={formData.graduation_year?.toString() || ''}
              onChange={handleSelectChange('graduation_year')}
              options={graduationYearOptions}
            />
            <SiteInput
              label="current gpa"
              value={formData.gpa}
              onChange={handleInputChange('gpa')}
              min={0}
              max={4.5}
              step={0.01}
            />
          </div>
        )}

        {/* Physical Stats Section */}
        {showMetricsFields && (
          <div className={`grid grid-cols-1 ${!isPositiveCoach ? 'md:grid-cols-3' : ''} gap-4`}>
            <SelectInput
              label="GENDER"
              value={formData.gender || ''}
              onChange={handleSelectChange('gender')}
              options={genderOptions}
            />

            <div>
              <label className="pa-eyebrow text-text-primary mb-2">HEIGHT</label>
              <div className="flex gap-2">
                <SiteInput
                  label="FEET"
                  hideLabel
                  type="number"
                  value={
                    formData.height_in_inches ? Math.floor(formData.height_in_inches / 12) : null
                  }
                  onChange={e => {
                    const feet = parseInt(e.target.value || '0');
                    const inches = formData.height_in_inches ? formData.height_in_inches % 12 : 0;
                    setFormData(prev => ({
                      ...prev,
                      height_in_inches: feet * 12 + inches,
                    }));
                  }}
                  min={3}
                  max={8}
                  placeholder="ft"
                  className="w-20"
                />
                <SiteInput
                  label="INCHES"
                  hideLabel
                  type="number"
                  value={formData.height_in_inches ? formData.height_in_inches % 12 : null}
                  onChange={e => {
                    const inches = parseInt(e.target.value || '0');
                    const feet = formData.height_in_inches
                      ? Math.floor(formData.height_in_inches / 12)
                      : 0;
                    setFormData(prev => ({
                      ...prev,
                      height_in_inches: feet * 12 + inches,
                    }));
                  }}
                  min={0}
                  max={11}
                  placeholder="in"
                  className="w-20"
                />
              </div>
            </div>

            <SiteInput
              label="WEIGHT"
              name="weight"
              type="number"
              value={formData.weight}
              onChange={handleInputChange('weight')}
              min={50}
              max={400}
              placeholder="Weight in lbs"
            />
          </div>
        )}

        {/* Social Links Section */}
        <EditDetailsModalSocialLinks formData={formData} onChange={handleInputChange} />

        {/* Career Interests Section - Only show for non-coaches */}
        {showCareerInterests && (
          <div>
            <InterestSelector
              selectedInterests={formData.interests}
              onSelect={handleInterestSelect}
            />
          </div>
        )}
      </div>
    </ModalFormContainer>
  );
}
