'use client';

import React, { useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { DynamicListEditor } from '@/components/shared/dynamic-list';
import type { DynamicListItem } from '@/components/shared/dynamic-list/types';
import ModalFormContainer from '@/components/shared/form/ModalFormContainer';
import SiteInput from '@/components/shared/form/SiteInput';
import { Textarea } from '@/components/shared/form/Textarea';
import type { CommunityInvolvement } from '@/services/positive-athlete-profile.service';
import { useModalStore } from '@/stores/modal.store';

// Adapter type to handle string IDs for DynamicListEditor
interface InvolvementItem extends Omit<CommunityInvolvement, 'id'>, DynamicListItem {}

interface EditInvolvementModalProps {
  initialInvolvements?: CommunityInvolvement[];
  onSave: (involvements: Omit<CommunityInvolvement, 'id' | 'order'>[]) => Promise<void>;
  isSaving?: boolean;
  error?: string | null;
}

export function EditInvolvementModal({
  initialInvolvements = [],
  onSave,
  isSaving = false,
  error = null,
}: EditInvolvementModalProps) {
  const { close } = useModalStore();
  const [involvements, setInvolvements] = useState<InvolvementItem[]>(() =>
    initialInvolvements.map(item => ({
      ...item,
      id: item.id?.toString() || uuidv4(),
    }))
  );
  const [validationError, setValidationError] = useState<string | null>(null);

  useEffect(() => {
    setInvolvements(
      initialInvolvements.map(item => ({
        ...item,
        id: item.id?.toString() || uuidv4(),
      }))
    );
  }, [initialInvolvements]);

  const handleAdd = () => {
    const newInvolvement: InvolvementItem = {
      id: uuidv4(),
      title: '',
      date_range: '',
      description: '',
    };
    setInvolvements(prev => [...prev, newInvolvement]);
    setValidationError(null);
  };

  const handleRemove = (id: string) => {
    setInvolvements(prev => prev.filter(item => item.id !== id));
    setValidationError(null);
  };

  const handleUpdate = (id: string, updates: Partial<Omit<InvolvementItem, 'id'>>) => {
    setInvolvements(prev => prev.map(item => (item.id === id ? { ...item, ...updates } : item)));
  };

  const handleReorder = (reorderedItems: InvolvementItem[]) => {
    setInvolvements(reorderedItems);
  };

  const handleClose = () => {
    close();
  };

  const validateForm = (): boolean => {
    const hasEmptyFields = involvements.some(
      item => !item.title || !item.date_range || !item.description
    );

    if (hasEmptyFields) {
      setValidationError('Please fill in all fields for each involvement.');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    // Convert back to CommunityInvolvement type for API
    const apiInvolvements: Omit<CommunityInvolvement, 'id' | 'order'>[] = involvements.map(
      ({ id: _id, ...rest }) => rest
    );

    try {
      await onSave(apiInvolvements);
    } catch (err) {
      // Error will be handled by parent component
      console.error('Failed to save involvements:', err);
    }
  };

  const renderFields = (item: InvolvementItem) => (
    <>
      <SiteInput
        label={item.title}
        value={item.title}
        onChange={e => handleUpdate(item.id, { title: e.target.value })}
        placeholder="e.g., Student Government Treasurer"
        hideLabel
      />

      <SiteInput
        label="Date or Range"
        type="text"
        value={item.date_range}
        onChange={e => handleUpdate(item.id, { date_range: e.target.value })}
        placeholder="e.g., 2022-2023"
      />

      <Textarea
        label="Description"
        value={item.description}
        onChange={e => handleUpdate(item.id, { description: e.target.value })}
        placeholder="Briefly describe this activity"
        rows={4}
      />
    </>
  );

  return (
    <ModalFormContainer
      title="Your School / Community Involvement"
      description="Highlight your school achievements and ways you've positively engaged with your community."
      isLoading={isSaving}
      error={error}
      validationError={error && (validationError || error || 'An error occurred')}
      handleSave={handleSave}
      handleClose={handleClose}
    >
      <DynamicListEditor
        items={involvements}
        onAdd={handleAdd}
        onRemove={handleRemove}
        onUpdate={handleUpdate}
        onReorder={handleReorder}
        getTitle={item => item.title || 'New Involvement'}
        renderFields={renderFields}
        addButtonText="Add Another Item"
      />
    </ModalFormContainer>
  );
}
