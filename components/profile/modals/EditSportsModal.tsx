'use client';

import React, { useState } from 'react';
import ModalFormContainer from '@/components/shared/form/ModalFormContainer';
import { SportSelector, type SportItem } from '@/components/shared/sports/SportSelector';
import type { Sport, SportUpdate } from '@/services/positive-athlete-profile.service';
import { useModalStore } from '@/stores/modal.store';

interface EditSportsModalProps {
  initialSports: Sport[];
  onSave: (sports: SportUpdate[]) => Promise<void>;
  isSaving?: boolean;
  error?: string | null;
  titleContext?: string;
}

export function EditSportsModal({
  initialSports = [],
  onSave,
  isSaving = false,
  error = null,
  titleContext = 'Sports',
}: EditSportsModalProps) {
  const { close } = useModalStore();
  const [validationError, setValidationError] = useState<string | null>(null);
  const [sports, setSports] = useState<SportItem[]>(() => {
    return initialSports.map((sport, index) => ({
      id: sport.uniqueId || `temp-${crypto.randomUUID()}`,
      name: sport.name,
      icon: sport.icon,
      isCustom:
        sport.uniqueId?.startsWith('custom-') || Boolean(sport.isCustom || sport.customName),
      customName: sport.uniqueId?.startsWith('custom-')
        ? sport.name
        : sport.isCustom
          ? sport.customName || sport.name
          : undefined,
      sportId: sport.uniqueId?.startsWith('custom-') ? undefined : sport.id,
      order: sport.order ?? index,
    }));
  });

  const validateForm = (): boolean => {
    if (sports.length === 0) {
      setValidationError('Please add at least one sport.');
      return false;
    }

    const hasEmptyFields = sports.some(
      item => (!item.sportId && !item.customName) || (item.isCustom && !item.customName)
    );

    if (hasEmptyFields) {
      setValidationError('Please select a sport or enter a custom sport name for each entry.');
      return false;
    }

    // Validate that all non-custom sports have valid IDs
    const hasInvalidSportId = sports.some(
      item => !item.isCustom && (!item.sportId || item.sportId === -1)
    );

    if (hasInvalidSportId) {
      setValidationError('Please select valid sports from the dropdown.');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    const apiSports = sports.map((item, index) => ({
      id: item.isCustom ? undefined : item.sportId,
      customName: item.isCustom ? item.customName : undefined,
      order: index,
      isCustom: item.isCustom,
    }));

    try {
      await onSave(apiSports);
      close();
    } catch (error) {
      console.error('Failed to save sports:', error);
      setValidationError('Failed to save sports. Please try again.');
    }
  };

  return (
    <ModalFormContainer
      title={titleContext === 'Sports coached' ? 'Edit Your Sports Coached' : 'Edit Your Sports'}
      description={
        titleContext === 'Sports coached'
          ? 'Add the sports that you coach. This information helps us match you with appropriate programs and opportunities.'
          : 'Add the sports that you play. The sports you select will qualify you for regional and state Positive Athlete awards!'
      }
      isLoading={isSaving}
      error={error}
      validationError={error && (validationError || error || 'An error occurred')}
      handleSave={handleSave}
      handleClose={() => close()}
    >
      <SportSelector sports={sports} onChange={setSports} onValidationError={setValidationError} />
    </ModalFormContainer>
  );
}
