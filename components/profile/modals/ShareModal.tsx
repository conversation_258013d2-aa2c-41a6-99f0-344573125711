'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faFacebook, faInstagram, faXTwitter } from '@fortawesome/free-brands-svg-icons';
import { faCopy, faLink, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import SiteInput from '@/components/shared/form/SiteInput';

interface ShareModalProps {
  profileId: number;
  onClose: () => void;
}

interface SocialMediaLink {
  url: string;
  label: string;
  icon: IconDefinition;
}

export default function ShareModal({ profileId, onClose }: ShareModalProps) {
  const [isCopied, setIsCopied] = useState(false);
  const router = useRouter();

  // Get the base URL (handles both development and production environments)
  const baseUrl =
    process.env.NEXT_PUBLIC_BASE_URL ||
    (typeof window !== 'undefined' ? window.location.origin : '');

  const profileUrl = `${baseUrl}/pa/${profileId}`;

  const socialMediaLinks: SocialMediaLink[] = [
    {
      url: `https://www.instagram.com/share?url=${encodeURIComponent(profileUrl)}`,
      label: 'Share on Instagram',
      icon: faInstagram,
    },
    {
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(profileUrl)}`,
      label: 'Share on Facebook',
      icon: faFacebook,
    },
    {
      url: `https://twitter.com/intent/tweet?url=${encodeURIComponent(profileUrl)}`,
      label: 'Share on X',
      icon: faXTwitter,
    },
  ];

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(profileUrl);
      setIsCopied(true);
      // Reset the copied state after 2 seconds
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <div className="w-full max-w-[600px] lg:min-w-[600px] p-6">
      <div className="flex flex-wrap items-center justify-between gap-4 mb-2">
        <h2 className="text-xl font-semibold">Share this Profile</h2>

        <button type="button" onClick={onClose} aria-label="Close">
          <span className="sr-only">Close</span>
          <FontAwesomeIcon icon={faXmark} className="size-4" aria-hidden="true" />
        </button>
      </div>

      <p className="text-sm text-text-secondary mb-8">Spread the word about this athlete!</p>

      <div className="mb-6">
        <div className="block space-y-1 mb-6">
          <span className="pa-eyebrow text-text-primary">Share on social media</span>

          <ul className="flex gap-4 mt-4">
            {socialMediaLinks.map((link, index) => (
              <li key={index}>
                <a
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center bg-surface-secondary text-text-secondary hover:bg-gray-200 transition-colors rounded-lg shrink-0 size-12"
                  aria-label={link.label}
                >
                  <FontAwesomeIcon icon={link.icon} className="!size-6" />
                </a>
              </li>
            ))}
          </ul>
        </div>

        <div className="flex gap-4">
          <div className="block space-y-1 w-full">
            <div className="flex items-end gap-4 flex-wrap w-full justify-between lg:flex-nowrap">
              <div className="w-full">
                <SiteInput label="copy link" value={profileUrl} icon={faLink} readOnly />
              </div>

              <Button
                color="blue"
                size="small"
                icon={faCopy}
                iconPosition="right"
                className="shrink-0"
                onClick={handleCopyLink}
              >
                {isCopied ? 'Copied!' : 'Copy Link'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      <Button color="blue" size="small" icon={faXmark} iconPosition="right" onClick={onClose}>
        Close
      </Button>
    </div>
  );
}
