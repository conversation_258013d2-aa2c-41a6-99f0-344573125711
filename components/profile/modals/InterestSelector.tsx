'use client';

import React, { useEffect, useState } from 'react';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faCheck, faPlus, faSearch, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Transition } from '@headlessui/react';
import Button from '@/components/shared/Button';
import SiteInput from '@/components/shared/form/SiteInput';
import Tag, { CAREER_TAG_ICONS } from '@/components/shared/Tag';
import { useInterests } from '@/hooks/useInterests';
import type { Interest } from '@/services/interest.service';
import { getInterestIcon } from '@/utils/icons';

interface InterestSelectorProps {
  selectedInterests: Interest[];
  onSelect: (interests: Interest[]) => void;
  label?: string;
}

export function InterestSelector({
  selectedInterests,
  onSelect,
  label = 'CAREER INTERESTS',
}: InterestSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [localSelectedInterests, setLocalSelectedInterests] =
    useState<Interest[]>(selectedInterests);
  const { interests, isLoading, setSearchInput } = useInterests();

  useEffect(() => {
    setLocalSelectedInterests(selectedInterests);
  }, [selectedInterests]);

  const handleSelect = (interest: Interest) => {
    if (!localSelectedInterests.find(i => i.id === interest.id)) {
      const newInterests = [...localSelectedInterests, interest];
      setLocalSelectedInterests(newInterests);
      onSelect(newInterests);
    }
  };

  const handleRemove = (interestId: number, e?: React.MouseEvent) => {
    e?.stopPropagation(); // Prevent opening the selector when removing
    const newInterests = localSelectedInterests.filter(i => i.id !== interestId);
    setLocalSelectedInterests(newInterests);
    onSelect(newInterests);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setSearchInput(value);
  };

  return (
    <div>
      {label && <h3 className="pa-eyebrow text-text-primary mb-4">{label}</h3>}

      {/* Main Display Field */}
      <div>
        <div
          className="flex items-center justify-between min-h-[48px] px-4 py-2 bg-white border border-neutral-200 rounded-lg hover:border-neutral-300 cursor-pointer transition-colors"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex gap-2 flex-wrap">
            {localSelectedInterests.length === 0 ? (
              <span className="text-neutral-500">Select your career interests</span>
            ) : (
              localSelectedInterests.map(interest => (
                <span
                  key={interest.id}
                  className="inline-flex items-center gap-2 px-3 py-1 bg-brand-red font-semibold text-white rounded text-sm"
                >
                  <FontAwesomeIcon icon={CAREER_TAG_ICONS[interest.name]} className="h-3 w-3" />
                  {interest.name}
                  <button
                    onClick={e => handleRemove(interest.id, e)}
                    className="text-white hover:text-white/80"
                  >
                    <FontAwesomeIcon icon={faXmark} className="h-3 w-3" />
                  </button>
                </span>
              ))
            )}
          </div>
          <FontAwesomeIcon
            icon={isOpen ? faXmark : faPlus}
            className="text-neutral-500 h-4 w-4 flex-shrink-0"
          />
        </div>

        {/* Selector Panel */}
        <Transition
          show={isOpen}
          enter="transition-all duration-200 ease-out"
          enterFrom="opacity-0 -translate-y-2"
          enterTo="opacity-100 translate-y-0"
          leave="transition-all duration-200 ease-out"
          leaveFrom="opacity-100 translate-y-0"
          leaveTo="opacity-0 -translate-y-2"
        >
          <div className="mt-2">
            {/* Search Input */}
            <SiteInput
              label="Search"
              type="search"
              icon={faSearch}
              placeholder="Search career interests..."
              value={query}
              onChange={handleSearchChange}
              hideLabel
            />

            {/* Results */}
            <div className="mt-2 max-h-[240px] overflow-auto">
              {isLoading ? (
                <div className="p-4 text-center text-neutral-500">Loading...</div>
              ) : interests.length === 0 ? (
                <div className="p-4 text-center text-neutral-500">No results found</div>
              ) : (
                <div className="space-y-1">
                  {interests.map(interest => (
                    <button
                      key={interest.id}
                      onClick={() => handleSelect(interest)}
                      className={`w-full flex items-center gap-3 px-4 py-2 text-left hover:bg-neutral-50 ${
                        localSelectedInterests.find(i => i.id === interest.id)
                          ? 'text-brand-blue font-medium'
                          : 'text-neutral-700'
                      }`}
                    >
                      <FontAwesomeIcon icon={CAREER_TAG_ICONS[interest.name]} className="h-4 w-4" />
                      {interest.name}
                      {localSelectedInterests.find(i => i.id === interest.id) && (
                        <FontAwesomeIcon icon={faCheck} className="h-4 w-4 ml-auto" />
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3 mt-4 pt-4 border-t border-neutral-200">
              <Button color="white" size="small" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button color="blue" size="small" onClick={() => setIsOpen(false)}>
                Done
              </Button>
            </div>
          </div>
        </Transition>
      </div>
    </div>
  );
}
