'use client';

import { useEffect, useMemo, useState } from 'react';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import {
  faCheck,
  faHandHoldingSeedling,
  faHandshake,
  faHeart,
  faPeopleGroup,
  faPuzzlePiece,
  faSmile,
  faSunBright,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Checkbox, Field, Fieldset, Label } from '@headlessui/react';
import clsx from 'clsx';
import ModalFormContainer from '@/components/shared/form/ModalFormContainer';
import SiteInput from '@/components/shared/form/SiteInput';
import { useAuth } from '@/hooks/useAuth';
import { useEndorsementsList } from '@/hooks/useEndorsementsList';
import { useSubmitEndorsement } from '@/hooks/useEndorseSelection';
import type { PublicEndorsementRequestData } from '@/services/endorse-selection.service';
import { useModalStore } from '@/stores/modal.store';

// Define the structure for endorsement options used by the component
interface EndorsementOption {
  id: number;
  name: string;
  icon: IconDefinition;
}

// Map endorsement IDs to icons (similar to EndorsementCard)
const getEndorsementIcon = (id: number): IconDefinition => {
  const iconMap: Record<number, IconDefinition> = {
    1: faSunBright, // Optimistic
    2: faPeopleGroup, // Puts Team First
    3: faSmile, // Encouraging
    4: faHandshake, // Respectful
    5: faPuzzlePiece, // Admits Imperfections
    6: faHeart, // True Heart for Others
    7: faHandHoldingSeedling, // Embraces Service
  };
  return iconMap[id] || faSmile; // Default icon
};

// Static list of endorsement options based on the provided data structure - This will be replaced
// const endorsementOptions: EndorsementOption[] = [
//   { id: 1, name: 'Optimistic', icon: getEndorsementIcon(1) },
//   { id: 2, name: 'Puts Team First', icon: getEndorsementIcon(2) },
//   { id: 3, name: 'Encouraging', icon: getEndorsementIcon(3) },
//   { id: 4, name: 'Respectful', icon: getEndorsementIcon(4) },
//   { id: 5, name: 'Admits Imperfections', icon: getEndorsementIcon(5) },
//   { id: 6, name: 'True Heart for Others', icon: getEndorsementIcon(6) },
//   { id: 7, name: 'Embraces Service', icon: getEndorsementIcon(7) },
// ];

// Define the shape of the form data
interface FormData {
  name: string;
  relation: string;
}

interface EndorsementModalProps {
  profileUserId: number; // ID of the athlete being endorsed
}

export function EndorsementModal({ profileUserId }: EndorsementModalProps) {
  const { user: loggedInUser } = useAuth(); // Renamed to avoid confusion with profile user
  const { close } = useModalStore();
  const {
    endorsements: apiEndorsements,
    isLoading: isLoadingEndorsements,
    error: endorsementsError,
  } = useEndorsementsList();

  const {
    mutate: submitEndorsement,
    isPending: isSubmittingEndorsement, // Renamed from isLoading to avoid conflict
    error: submissionError,
    isSuccess: submissionSuccess,
  } = useSubmitEndorsement({
    onSuccess: data => {
      console.log('Endorsement submitted successfully:', data);
      // Optionally close modal or show success message
      // close();
    },
    onError: error => {
      console.error('Error submitting endorsement:', error);
      // Error is already captured in submissionError, ModalFormContainer will display it
    },
  });

  const [formData, setFormData] = useState<FormData>({ name: '', relation: '' });
  const [selectedEndorsements, setSelectedEndorsements] = useState<number[]>([]);
  const [formError, setFormError] = useState<string | null>(null);

  // Derive endorsementOptions for rendering from the fetched API data
  const endorsementOptions = useMemo(() => {
    return (apiEndorsements || []).map(endorsement => ({
      id: endorsement.id,
      name: endorsement.name,
      icon: getEndorsementIcon(endorsement.id),
    }));
  }, [apiEndorsements]);

  // Set initial form data for name when user data is available
  useEffect(() => {
    if (loggedInUser?.first_name) {
      const fullName = `${loggedInUser.first_name}${loggedInUser.last_name ? ` ${loggedInUser.last_name}` : ''}`;
      setFormData(prev => ({ ...prev, name: fullName }));
    }
  }, [loggedInUser]);

  const handleInputChange = (field: keyof FormData) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value,
    }));
    setFormError(null); // Clear form error on input change
  };

  const handleEndorsementChange = (endorsementId: number) => {
    setSelectedEndorsements(
      prevSelected =>
        prevSelected.includes(endorsementId)
          ? prevSelected.filter(id => id !== endorsementId) // Remove if exists
          : [...prevSelected, endorsementId] // Add if not exists
    );
    setFormError(null); // Clear form error on selection change
  };

  const handleClose = () => {
    close();
  };

  const handleSave = async () => {
    setFormError(null); // Reset form error
    if (!profileUserId) {
      console.error('Profile user ID is missing.');
      setFormError('Cannot submit endorsement: Profile information is missing.');
      return;
    }
    if (!formData.relation.trim()) {
      setFormError('Please enter your relation to the athlete.');
      return;
    }
    if (selectedEndorsements.length === 0) {
      setFormError('Please select at least one character trait to endorse.');
      return;
    }

    const payload: PublicEndorsementRequestData = {
      userId: profileUserId,
      name: formData.name,
      relation: formData.relation,
      endorsements: selectedEndorsements,
    };
    submitEndorsement(payload);
  };

  const overallIsLoading = isLoadingEndorsements || isSubmittingEndorsement;
  const displayError = submissionError?.message || formError || endorsementsError?.message || null;

  return (
    <ModalFormContainer
      title="Endorse this Athlete"
      description='Select the "OPERATE" character traits that you recognize in this Positive Athlete.'
      isLoading={overallIsLoading}
      error={displayError}
      handleSave={handleSave}
      handleClose={handleClose}
      saveButtonText={isSubmittingEndorsement ? 'Submitting...' : 'Submit Endorsement'}
    >
      <Fieldset disabled={overallIsLoading || submissionSuccess}>
        <div className="space-y-4 mb-10">
          <SiteInput
            label="YOUR NAME"
            name="name"
            type="text"
            value={formData.name}
            onChange={handleInputChange('name')}
            className="pointer-events-none"
            readOnly
          />
          <SiteInput
            label="RELATION TO ATHLETE"
            name="relation"
            type="text"
            value={formData.relation}
            onChange={handleInputChange('relation')}
            placeholder="e.g. Coach, Parent, Friend, etc."
            required
            aria-required="true"
          />
        </div>
        {endorsementsError && !isSubmittingEndorsement && !submissionError && !formError && (
          <div className="mb-4 text-red-600">
            <p>Could not load endorsement options: {endorsementsError.message}</p>
          </div>
        )}
        {!isLoadingEndorsements &&
          !endorsementsError &&
          endorsementOptions.length === 0 &&
          !isSubmittingEndorsement && (
            <div className="mb-4 text-text-secondary">
              <p>No endorsement options available at the moment.</p>
            </div>
          )}
        {submissionSuccess && (
          <div className="mb-4 p-4 text-green-700 bg-green-100 border border-green-300 rounded-md">
            <p>Endorsement submitted successfully! Thank you.</p>
          </div>
        )}
        <div className="space-y-4">
          {endorsementOptions.map(option => (
            <Field key={option.id} className="flex items-center w-full">
              <Checkbox
                checked={selectedEndorsements.includes(option.id)}
                onChange={() => handleEndorsementChange(option.id)}
                className="group peer hidden"
                name="endorsements"
                value={option.id}
              />
              <Label
                className={clsx(
                  'flex w-full cursor-pointer items-center gap-3 rounded-xl border bg-surface-secondary p-4 text-text-primary transition',
                  'hover:bg-surface-tertiary',
                  'peer-data-[checked]:bg-surface-tertiary'
                )}
              >
                <div
                  className={clsx(
                    'flex size-4 shrink-0 items-center justify-center rounded border border-gray-300 bg-white transition',
                    'group-data-[checked]:border-brand-red group-data-[checked]:bg-brand-red'
                  )}
                >
                  <FontAwesomeIcon
                    icon={faCheck}
                    className={clsx(
                      'size-2',
                      selectedEndorsements.includes(option.id) ? 'visible' : 'invisible'
                    )}
                    aria-hidden="true"
                  />
                </div>
                <FontAwesomeIcon
                  icon={option.icon}
                  className={clsx(
                    '!size-5 text-brand-red transition',
                    'peer-data-[checked]:text-brand-red'
                  )}
                />
                <span className="text-xl font-medium">{option.name}</span>
              </Label>
            </Field>
          ))}
        </div>
      </Fieldset>
    </ModalFormContainer>
  );
}
