'use client';

import React, { useState } from 'react';
import ModalFormContainer from '@/components/shared/form/ModalFormContainer';
import { SimpleLogoUploader } from '@/components/shared/SimpleLogoUploader';
import { useModalStore } from '@/stores/modal.store';

interface UploadLogoModalProps {
  onSave?: (file: File) => void;
  currentImageUrl?: string;
}

export function UploadLogoModal({ onSave, currentImageUrl }: UploadLogoModalProps) {
  const { close } = useModalStore();
  const [isLoading, setIsLoading] = useState(false);
  const [canSave, setCanSave] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    // Signal that we can save now
    setCanSave(true);
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);

      // If we have a file and an onSave callback, pass the file to the callback
      if (selectedFile && onSave) {
        onSave(selectedFile);
      }

      // Close the modal
      handleClose();
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    close();
  };

  return (
    <ModalFormContainer
      title="Upload Organization Logo"
      description="Choose a logo to represent your organization on your profile."
      isLoading={isLoading}
      allowSave={canSave}
      handleSave={handleSave}
      handleClose={handleClose}
    >
      <SimpleLogoUploader
        onFileSelect={handleFileSelect}
        onReadyToSave={setCanSave}
        className="w-[472px]"
        currentImageUrl={currentImageUrl}
      />
    </ModalFormContainer>
  );
}
