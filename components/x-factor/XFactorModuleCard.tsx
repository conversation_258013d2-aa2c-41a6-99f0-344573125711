import Image from 'next/image';
import { faClock, faLock, faPlayCircle } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { cn } from '@/lib/utils';

interface XFactorModule {
  id: string;
  name: string;
  description: string;
  cover_image: string | null;
  published: boolean;
  course_id: string | null;
  course_name: string | null;
  duration_minutes: number | null;
  topics: string[];
  has_quiz: boolean | null;
  status: string | null;
  progress: number | null;
  completed_at: string | null;
}

interface XFactorModuleCardProps {
  module: XFactorModule;
  isActive?: boolean;
  onClick?: () => void;
  className?: string;
}

export function XFactorModuleCard({
  module,
  isActive,
  onClick,
  className,
}: XFactorModuleCardProps) {
  const isLocked = !module.published;
  const isCompleted = !!module.completed_at;

  return (
    <button
      onClick={onClick}
      disabled={isLocked}
      className={cn(
        'group block relative aspect-[3/4] rounded-[24px] overflow-hidden transition-all',
        isLocked ? 'cursor-not-allowed' : 'cursor-pointer',
        isActive && 'ring-2 ring-brand-red',
        isCompleted && 'ring-2 ring-green-500',
        className
      )}
    >
      {/* Cover Image with Ken Burns effect */}
      {module.cover_image ? (
        <Image
          src={module.cover_image}
          alt={module.name}
          fill
          unoptimized
          className={cn(
            'object-cover transition-transform duration-[2s]',
            !isLocked && 'group-hover:scale-110'
          )}
        />
      ) : (
        <div className="absolute inset-0 bg-surface-secondary" />
      )}

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent" />

      {/* Content */}
      <div className="absolute bottom-0 left-0 p-6 text-white">
        {/* Icon - Play, Lock, or Check */}
        <div className="w-[24px] h-[24px] flex items-center justify-center mb-[8px]">
          {isLocked ? (
            <FontAwesomeIcon icon={faLock} className="text-white text-[24px] opacity-70" />
          ) : isCompleted ? (
            <FontAwesomeIcon icon={faPlayCircle} className="text-green-500 text-[24px]" />
          ) : (
            <FontAwesomeIcon icon={faPlayCircle} className="text-white text-[24px]" />
          )}
        </div>

        {/* Title and Description */}
        <h3 className="text-[30px] leading-[1.1] font-heavy mb-[8px]">{module.name}</h3>
        <p className="text-[16px] leading-[1.2] font-medium text-white/90 mb-3">
          {module.description}
        </p>

        {/* Duration */}
        {module.duration_minutes && (
          <div className="flex items-center text-sm text-white/80">
            <FontAwesomeIcon icon={faClock} className="w-3 h-3 mr-1" />
            {module.duration_minutes} minutes
          </div>
        )}
      </div>
    </button>
  );
}
