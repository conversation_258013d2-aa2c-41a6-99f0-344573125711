'use client';

import { useEffect } from 'react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useXFactorCourses } from '@/hooks/x-factor/useXFactorCourses';
import { useXFactorModules } from '@/hooks/x-factor/useXFactorModules';
import type { XFactorFilters } from '@/hooks/x-factor/useXFactorView';
import { XFactorCourseCarousel } from './XFactorCourseCarousel';
import { XFactorSearchResults } from './XFactorSearchResults';
import { XFactorTopicGrid } from './XFactorTopicGrid';

interface XFactorBrowseProps {
  filters: XFactorFilters;
  onFilterChange: (filters: Partial<XFactorFilters>) => void;
}

export function XFactorBrowse({ filters, onFilterChange }: XFactorBrowseProps) {
  // Get featured courses for the carousel
  const {
    featured,
    isLoading: isLoadingCourses,
    error: courseError,
  } = useXFactorCourses({
    sort: 'recent',
    status: 'in_progress',
  });

  // Get modules for search results and topic grid
  const {
    topicModules,
    searchResults,
    totalResults,
    isLoading: isLoadingModules,
    error: moduleError,
  } = useXFactorModules(filters.query);

  useEffect(() => {
    console.log('Browse: topicModules', {
      isLoading: isLoadingModules,
      error: moduleError,
      data: topicModules,
      isArray: Array.isArray(topicModules),
      length: topicModules?.length,
    });
  }, [topicModules, isLoadingModules, moduleError]);

  // Show error if both requests failed
  if (courseError && moduleError) {
    return (
      <div className="p-4 text-red-500">
        Error loading content. Please try again later.
        <pre className="mt-2 text-sm">
          {courseError instanceof Error ? courseError.message : String(courseError)}
          {moduleError instanceof Error ? moduleError.message : String(moduleError)}
        </pre>
      </div>
    );
  }

  // Show search results when there's a query
  if (filters.query) {
    if (isLoadingModules) {
      return (
        <div className="flex justify-center py-12">
          <LoadingSpinner />
        </div>
      );
    }

    return (
      <XFactorSearchResults
        query={filters.query}
        results={searchResults}
        totalResults={totalResults}
        onClearSearch={() => onFilterChange({ query: undefined })}
        onBrowseClick={() => onFilterChange({ activeTab: 'browse', query: undefined })}
      />
    );
  }

  // Show browse view when no search
  return (
    <div className="space-y-12 pb-16">
      {/* Featured Course Carousel */}
      {isLoadingCourses ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner />
        </div>
      ) : (
        featured &&
        featured.length > 0 && (
          <section>
            <XFactorCourseCarousel courses={featured} />
          </section>
        )
      )}

      {/* Topic Grid with Modules */}
      <>
        {isLoadingModules ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner />
          </div>
        ) : Array.isArray(topicModules) && topicModules.length > 0 ? (
          <XFactorTopicGrid sections={topicModules} />
        ) : (
          <div className="text-center py-12 text-text-secondary">
            No modules available at this time.
          </div>
        )}
      </>
    </div>
  );
}
