'use client';

import { use<PERSON><PERSON>back, useEffect, useRef, useState } from 'react';
import type { YouTubeEvent, YouTubePlayer, YouTubeProps } from 'react-youtube';
import dynamic from 'next/dynamic';
import { faArrowRight, faCheck } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Quiz } from '@/components/quiz/Quiz';
import Button from '@/components/shared/Button';
import { XFactorExam } from '@/components/x-factor/XFactorExam';
import { useAuth } from '@/hooks/useAuth';
import { MEDIA_QUERY_DESKTOP, useMediaQuery } from '@/hooks/utils/useMediaQuery';
import type { XFactorModule } from '@/services/x-factor-module.service';
import { ModuleType } from '@/services/x-factor-module.service';

const YouTube = dynamic<YouTubeProps>(() => import('react-youtube'), { ssr: false });

interface XFactorModuleDetailProps {
  module: XFactorModule;
  courseId?: number;
  courseTitle?: string;
  moduleOrder?: number;
  onNextModule?: () => void;
  isLastModule?: boolean;
}

export const XFactorModuleDetail = ({
  module,
  courseId,
  courseTitle,
  moduleOrder,
  onNextModule,
  isLastModule,
}: XFactorModuleDetailProps) => {
  const { user } = useAuth();
  const [isQuizUnlocked, setIsQuizUnlocked] = useState(false);
  const [quizScore, setQuizScore] = useState<number | null>(module.score || null);
  const isDesktop = useMediaQuery(MEDIA_QUERY_DESKTOP);
  const [playerReady, setPlayerReady] = useState(false);
  const playerRef = useRef<YouTubePlayer | null>(null);
  const endTimeCheckInterval = useRef<NodeJS.Timeout | null>(null);

  // Update scores when module.score changes
  useEffect(() => {
    setQuizScore(module.score || null);
  }, [module.score]);

  const renderVideoPlayer = (videoId: string | null) => {
    if (!videoId) return null;

    // Prepare player options
    const playerVars: { [key: string]: any } = {
      controls: 1,
      disablekb: 1,
      fs: 0,
      modestbranding: 1,
      rel: 0,
      showinfo: 0,
      iv_load_policy: 3,
      playsinline: 1,
      title: 0,
      cc_load_policy: 0,
      autohide: 1,
      origin: typeof window !== 'undefined' ? window.location.origin : '',
    };

    // Add start time if provided
    if (module.video_start_time) {
      playerVars.start = module.video_start_time;
    }

    return (
      <div
        className={`relative aspect-video bg-gray-900 overflow-hidden ${isDesktop ? 'rounded-[32px]' : '-mx-4'}`}
      >
        <YouTube
          videoId={videoId}
          iframeClassName="absolute inset-0 w-full h-full"
          onEnd={handleVideoEnd}
          onReady={handlePlayerReady}
          onStateChange={handlePlayerStateChange}
          opts={{
            width: '100%',
            height: '100%',
            playerVars,
          }}
        />
      </div>
    );
  };

  // Handle player ready event
  const handlePlayerReady = (event: YouTubeEvent) => {
    playerRef.current = event.target;
    setPlayerReady(true);
  };

  // Handle player state change to implement end time functionality
  const handlePlayerStateChange = (event: YouTubeEvent) => {
    // If video is playing and we have an end time set
    if (event.data === 1 && module.video_end_time !== undefined && module.video_end_time !== null) {
      // Clear any existing interval
      if (endTimeCheckInterval.current) {
        clearInterval(endTimeCheckInterval.current);
      }

      // Set up interval to check current time
      endTimeCheckInterval.current = setInterval(() => {
        if (playerRef.current && module.video_end_time) {
          const currentTime = playerRef.current.getCurrentTime();

          // If current time exceeds end time, pause the video
          if (currentTime >= module.video_end_time) {
            playerRef.current.pauseVideo();

            // Clear the interval
            if (endTimeCheckInterval.current) {
              clearInterval(endTimeCheckInterval.current);
              endTimeCheckInterval.current = null;
            }

            // Mark video as completed
            handleVideoCompleted();
          }
        }
      }, 500); // Check every half second
    } else if (event.data !== 1 && endTimeCheckInterval.current) {
      // If video is not playing, clear the interval
      clearInterval(endTimeCheckInterval.current);
      endTimeCheckInterval.current = null;
    }
  };

  // Clean up interval on component unmount
  useEffect(() => {
    return () => {
      if (endTimeCheckInterval.current) {
        clearInterval(endTimeCheckInterval.current);
      }
    };
  }, []);

  // Handle video completion
  const handleVideoCompleted = () => {
    // Generate a unique key for this module's video completion status
    const storageKey = `video_completed_${module.id}_${user?.id}`;

    // Store completion status in localStorage
    localStorage.setItem(storageKey, 'true');

    // Update quiz unlock state
    setIsQuizUnlocked(true);
  };

  // Original handleVideoEnd function for when video naturally ends
  const handleVideoEnd = (event: YouTubeEvent) => {
    handleVideoCompleted();
  };

  // Check if video is completed or not required
  const checkVideoCompletion = useCallback(() => {
    // If no video URL, quiz is automatically unlocked
    if (!module.video_url) return true;

    // Check localStorage for completion status
    const storageKey = `video_completed_${module.id}_${user?.id}`;
    return localStorage.getItem(storageKey) === 'true';
  }, [module.video_url, module.id, user?.id]);

  // Set initial unlock state
  useEffect(() => {
    setIsQuizUnlocked(checkVideoCompletion());
  }, [checkVideoCompletion]);

  // Track completion status based on module type
  const isComplete = module.has_quiz ? quizScore === 100 : true;

  // If this is an exam module, only show the exam component
  if (module.type === 'exam' && courseId) {
    return (
      <XFactorExam
        moduleId={module.id}
        courseId={courseId}
        onComplete={score => {
          if (onNextModule && !isLastModule) {
            onNextModule();
          }
        }}
      />
    );
  }

  // Handle quiz completion
  const handleQuizComplete = (score: number) => {
    setQuizScore(score);
  };

  // Extract YouTube video ID from URL if present
  const getYoutubeId = (url: string) => {
    const match = url.match(
      /(?:youtu\.be\/|youtube\.com\/(?:embed\/|v\/|watch\?v=|watch\?.+&v=))([^&?]+)/
    );
    return match ? match[1] : null;
  };

  const videoId = module.video_url ? getYoutubeId(module.video_url) : null;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-[324px_1fr] gap-12">
      {/* Left Column - Module Info and Quiz */}
      <div className="space-y-8">
        {/* Module Info Card */}
        <div
          className={`relative overflow-hidden bg-white rounded-[32px] p-8 shadow-[0_8px_20px_rgba(0,0,0,0.04)] ${isComplete ? 'pt-16' : ''}`}
        >
          <div className="space-y-4">
            {courseTitle && (
              <div className="flex flex-col gap-2">
                <h3 className="text-heading-xs font-medium text-secondary">{courseTitle}</h3>
                {moduleOrder && (
                  <span className="text-heading-lg font-bold">
                    Module {moduleOrder}: {module.name}
                  </span>
                )}
              </div>
            )}
            {!courseTitle && (
              <div>
                <h1 className="text-heading-lg font-bold text-[#383838] leading-tight">
                  {module.name}
                </h1>
                <h2 className="mt-2 text-lg font-medium text-[#777B80]">
                  {module.topics?.[0] || 'Uncategorized'}
                </h2>
              </div>
            )}
          </div>
          <p className="text-secondary pt-6">{module.description}</p>
          {courseId && isComplete && (
            <div className="inline-flex items-center w-fit gap-1 bg-brand-red rounded-br-xl px-4 py-3 absolute top-0 left-0">
              <FontAwesomeIcon icon={faCheck} className="text-white" />
              <span className="inline-flex items-center px-3 py-1 text-sm font-medium text-white">
                Completed
              </span>
            </div>
          )}
        </div>

        {/* Video Player (Mobile) - Only show for video type modules */}
        {!isDesktop && module.type === 'video' && renderVideoPlayer(videoId)}

        {/* Quiz Card - Only show for modules that have quizzes */}
        {module.has_quiz && (
          <div className="bg-white rounded-[32px] p-8 shadow-[0_8px_20px_rgba(0,0,0,0.04)]">
            <div className="[&>div]:bg-transparent [&>div]:shadow-none [&>div]:p-0 [&_label]:bg-[#F7F7F7] [&_label]:rounded-2xl [&_label]:hover:bg-[#F0F0F0] [&_label]:border-transparent [&_label.border-blue-500]:bg-[#F7F7F7] [&_label.border-blue-500]:border-[#002855] [&_button]:bg-[#002855] [&_button]:hover:bg-[#001F44] [&_button.bg-green-500]:bg-[#002855] [&_button.hover\:bg-green-600]:hover:bg-[#001F44] [&_h2]:text-[#383838] [&_span.text-gray-500]:text-[#777B80] [&_span.text-gray-700]:text-[#777B80]">
              <Quiz
                moduleId={module.id}
                isUnlocked={isQuizUnlocked}
                hasVideo={!!module.video_url}
                onComplete={handleQuizComplete}
              />
            </div>
          </div>
        )}
      </div>

      {/* Right Column - Video Player and Content */}
      <div className="grid grid-cols-1 gap-y-8">
        {/* Video Player (Desktop) - Only show for video type modules */}
        {isDesktop && module.type === 'video' && renderVideoPlayer(videoId)}

        {/* Module Content - Only show for article type modules */}
        {module.type === 'article' && module.content && (
          <div className="bg-white rounded-[32px] p-8 shadow-[0_8px_20px_rgba(0,0,0,0.04)]">
            <div
              className="prose max-w-none"
              dangerouslySetInnerHTML={{ __html: module.content }}
            />
          </div>
        )}

        {/* Next Module Button */}
        {courseId && !isLastModule && isComplete && (
          <div className="flex justify-end">
            <Button onClick={onNextModule} color="blue" icon={faArrowRight}>
              Next Module
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default XFactorModuleDetail;
