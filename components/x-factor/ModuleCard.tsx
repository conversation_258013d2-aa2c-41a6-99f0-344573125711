import Image from 'next/image';
import { faClock } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import type { Module } from '@/types/course';

interface ModuleCardProps {
  module: Module;
  isActive?: boolean;
  onClick?: () => void;
}

export function ModuleCard({ module, onClick }: ModuleCardProps) {
  return (
    <button
      onClick={onClick}
      disabled={module.locked}
      className={`w-full h-[104px] flex items-center rounded-3xl shadow-card border transition-colors overflow-hidden
       
        ${module.locked ? 'cursor-not-allowed' : 'cursor-pointer'}`}
    >
      {/* Thumbnail */}
      <div className="relative w-[160px] h-full flex-shrink-0 overflow-hidden">
        <Image
          src={module.thumbnailUrl}
          alt={module.title}
          fill
          unoptimized
          className="object-cover"
          sizes="160px"
        />
      </div>

      {/* Content */}
      <div className="px-4 flex-1 text-left">
        <h3 className="font-medium text-gray-900">{module.title}</h3>
        <p className="text-sm text-gray-500 line-clamp-1">{module.description}</p>

        {/* Duration */}
        <div className="flex items-center mt-1 text-sm text-gray-500">
          <FontAwesomeIcon icon={faClock} className="w-3 h-3 mr-1" />
          {module.duration}
        </div>
      </div>
    </button>
  );
}
