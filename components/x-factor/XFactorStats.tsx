'use client';

import React from 'react';
import { faChartLine, faChartSimpleHorizontal } from '@fortawesome/pro-regular-svg-icons';
import LearningStatsGrid from '@/components/profile/sections/LearningStatsGrid';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';

interface XFactorStatsProps {
  averageScore?: number;
  hoursSpent?: number;
  modulesCompleted?: number;
  stateRank?: number;
  nationalRank?: number;
  stateName?: string;
  academicYear?: string;
}

export function XFactorStats({
  averageScore = 0,
  hoursSpent = 0,
  modulesCompleted = 0,
  stateRank = 0,
  nationalRank = 0,
  stateName = 'Georgia',
  academicYear = '2024-25',
}: XFactorStatsProps) {
  return (
    <Card elevation="card" className="h-full">
      <CardHeader title="Learning Stats" titleIcon={faChartSimpleHorizontal} className="mb-8" />

      <LearningStatsGrid
        averageModuleScore={averageScore}
        hoursSpent={hoursSpent}
        modulesCompleted={modulesCompleted}
        stateLeaderboardRank={stateRank}
        nationalLeaderboardRank={nationalRank}
        stateName={stateName}
        academicYear={academicYear}
      />
    </Card>
  );
}
