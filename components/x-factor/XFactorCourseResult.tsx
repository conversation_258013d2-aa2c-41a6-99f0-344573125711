import Image from 'next/image';
import Link from 'next/link';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { XFactorCourse } from '@/services/x-factor-course.service';

interface XFactorCourseCardProps {
  course: XFactorCourse;
}

export function XFactorCourseCard({ course }: XFactorCourseCardProps) {
  return (
    <Link
      href={`/x-factor/courses/${course.id}`}
      className="group relative flex flex-col overflow-hidden rounded-lg bg-white shadow-md hover:shadow-lg transition-shadow"
    >
      {/* Course Image */}
      <div className="relative aspect-[4/3] w-full overflow-hidden">
        {course.coverImageUrl ? (
          <Image
            src={course.coverImageUrl}
            alt={course.title}
            fill
            unoptimized
            className="object-cover transition-transform group-hover:scale-105"
          />
        ) : (
          <div className="h-full w-full bg-gray-200" />
        )}
        {/* Progress Indicator */}
        {course.progress > 0 && (
          <div className="absolute top-4 right-4">
            <CircularProgress value={course.progress} size={40} />
          </div>
        )}
      </div>

      {/* Course Info */}
      <div className="flex flex-col flex-1 p-4">
        <h3 className="text-lg font-semibold line-clamp-2 mb-2">{course.title}</h3>
        <p className="text-sm text-gray-600 line-clamp-2 mb-4">{course.description}</p>

        {/* Course Stats */}
        <div className="mt-auto flex items-center text-sm text-gray-500 space-x-4">
          <div>
            {course.modulesCompleted} / {course.totalModules} modules
          </div>
          {course.averageScore > 0 && <div>{Math.round(course.averageScore)}% avg. score</div>}
        </div>

        {/* Topics */}
        {course.topics.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-2">
            {course.topics.slice(0, 3).map(topic => (
              <span
                key={topic}
                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brand-red bg-opacity-10 text-brand-red"
              >
                {topic}
              </span>
            ))}
            {course.topics.length > 3 && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                +{course.topics.length - 3}
              </span>
            )}
          </div>
        )}
      </div>
    </Link>
  );
}
