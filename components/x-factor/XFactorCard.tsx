import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { faLock } from '@fortawesome/pro-regular-svg-icons';
import { faCheck, faCirclePlay } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { cn } from '@/lib/utils';
import type { XFactorCourse } from '@/services/x-factor-course.service';
import type { XFactorModule } from '@/services/x-factor-module.service';

type CardItem = XFactorCourse | XFactorModule;

interface XFactorCardProps {
  item: CardItem;
  isActive?: boolean;
  className?: string;
  sectionTopic?: string;
  /**
   * @deprecated Use the card's built-in navigation instead
   */
  onClick?: () => void;
}

export function XFactorCard({
  item,
  isActive,
  className,
  sectionTopic,
  onClick,
}: XFactorCardProps) {
  const router = useRouter();
  const isCourse = 'title' in item;
  const isLocked = isCourse ? item.progress === 0 : !item.published;
  const isCompleted = item?.progress && item.progress === 100;
  const title = isCourse ? item.title : item.name;
  const coverImage = isCourse ? item.coverImageUrl || item.coverImageThumbUrl : item.cover_image;

  // Only show progress for modules
  const showProgress = !isCourse && item.progress != null;
  const progress = showProgress ? item.progress : 0;
  const isFullProgress = progress === 100;

  const handleClick = () => {
    if (isLocked) return;

    // Support legacy onClick handlers
    if (onClick) {
      onClick();
      return;
    }

    // If it's a module, navigate to the module detail page
    if (!isCourse) {
      router.push(`/x-factor/modules/${item.id}`);
    }
    // TODO: Implement course navigation when ready
  };

  return (
    <button
      onClick={handleClick}
      disabled={isLocked}
      className={cn(
        // 'group block relative aspect-[3/4] rounded-3xl overflow-hidden text-left',
        'group block relative aspect-[3/4] rounded-3xl overflow-hidden text-left',
        isLocked ? 'cursor-not-allowed' : 'cursor-pointer',
        isActive && 'ring-2 ring-brand-red',
        className
      )}
    >
      <div className="absolute inset-0 transition-transform duration-300 group-hover:scale-[1.02]">
        {/* Cover Image */}
        <Image
          src={coverImage || '/images/course-placeholder.jpg'}
          alt={title}
          fill
          unoptimized
          sizes="(max-width: 768px) 100vw, 280px"
          className="object-cover"
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black via-black/40 to-transparent opacity-80" />

        {/* Content */}
        <div className="absolute inset-x-0 bottom-0 p-6 text-white space-y-1">
          {/* Play Icon */}

          <FontAwesomeIcon icon={faCirclePlay} className="text-white text-2xl" />

          {/* Title and Topic */}
          <h3 className="text-2xl/none font-oxanium font-bold uppercase line-clamp-2">{title}</h3>
          <p className="text-base text-white font-medium">{sectionTopic}</p>

          {/* Progress/Score Indicator */}
          {isCourse ? (
            item.progress > 0 && (
              <div
                className={cn(
                  'inline-flex items-center justify-center rounded-lg text-sm font-medium px-3 py-1',
                  item.progress === 100 ? 'bg-green-500' : 'bg-yellow-500'
                )}
              >
                {item.progress}%
              </div>
            )
          ) : item.score ? (
            <div
              className={cn(
                'inline-flex items-center justify-center rounded-lg text-sm font-medium px-3 py-1',
                item.score >= 80 ? 'bg-green-500' : 'bg-yellow-500'
              )}
            >
              {item.score}%
            </div>
          ) : null}
        </div>
      </div>

      {/* Status Badge */}
      {isCompleted && (
        <div className="absolute top-0 right-0 bg-brand-red text-white text-xs font-medium py-2 px-4 rounded-bl-lg rounded-tr-3xl flex items-center gap-2">
          <FontAwesomeIcon icon={faCheck} className="text-white w-3 h-3" />
          <span>Completed</span>
        </div>
      )}
    </button>
  );
}
