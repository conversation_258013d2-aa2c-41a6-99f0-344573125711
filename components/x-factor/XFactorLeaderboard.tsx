'use client';

import React, { useMemo, useState } from 'react';
import Image from 'next/image';
import { faExclamationTriangle, faSpinner, faTrophy } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { format, getYear } from 'date-fns';
import { ConnectMessageForm } from '@/components/network/ConnectMessageForm';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import XFactorLeaderboardCard from '@/components/x-factor/XFactorLeaderboardCard';
import XFactorLeaderboardSelect, {
  type SelectOption as XFactorLeaderboardSelectOption,
} from '@/components/x-factor/XFactorLeaderboardSelect';
import { useAuth } from '@/hooks/useAuth';
import { useConnectionPermissions } from '@/hooks/useConnectionPermissions';
import { useXFactorLeaderboard } from '@/hooks/x-factor/useXfactorLeaderboard';
import type { UserBasicData } from '@/services/networking.service';
import type {
  XFactorLeaderboardFilters,
  XFactorLeaderboardItem,
} from '@/services/x-factor-leaderboard.service';
import { useModalStore } from '@/stores/modal.store';
import { STATE_OPTIONS } from '@/utils/constants/states';

const formatDateToApi = (date: Date, isEndDate: boolean = false): string => {
  if (isEndDate) {
    // Format as YYYY-MM-DDT23:59:59Z for end_date
    // return format(date, "yyyy-MM-dd'T'HH:mm:ss'Z'"); // This will use current time, then set to end of day conceptually
    // More accurately for end of day, construct it properly:
    // return format(new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59), "yyyy-MM-dd'T'HH:mm:ss'Z'");
    return date.toISOString().split('.')[0] + 'Z'; // Simpler way to get YYYY-MM-DDTHH:mm:ssZ, then adjust time part for end_date.
  } else {
    // Format as YYYY-MM-DDT00:00:00Z for start_date
    // return format(date, "yyyy-MM-dd'T00:00:00Z'");
    const startOfDay = new Date(date.setHours(0, 0, 0, 0));
    return format(startOfDay, "yyyy-MM-dd'T'HH:mm:ss'Z'");
  }
};

interface XFactorLeaderboardProps {
  initialFilters?: XFactorLeaderboardFilters;
}

export function XFactorLeaderboard({ initialFilters }: XFactorLeaderboardProps) {
  const [filters, setFilters] = useState<XFactorLeaderboardFilters>(
    initialFilters ?? {
      per_page: 10, // Default to 10, also enforced by the hook now
      all_time: true,
      academic_year: undefined,
      state: undefined,
      start_date: undefined,
      end_date: undefined,
    }
  );

  const [selectedYearDisplayValue, setSelectedYearDisplayValue] = useState<string>('all_time');

  const { open } = useModalStore();
  const checkCanConnect = useConnectionPermissions();

  const {
    leaderboardItems,
    totalItems,
    isLoading,
    error,
    hasNextPage,
    currentUser,
    fetchNextPage,
    isFetchingNextPage,
    refetchLeaderboard,
  } = useXFactorLeaderboard(filters);

  const { profileType } = useAuth();

  const yearFilterOptions = useMemo(() => {
    const options: XFactorLeaderboardSelectOption[] = [
      { value: 'all_time', label: 'All Time' },
      { value: 'year_to_date', label: 'Year to Date' },
    ];

    const currentCalendarYear = getYear(new Date());

    // Generate options for the current academic year and three prior academic years.
    // Example: If currentCalendarYear is 2025:
    // i = 0: startYear = 2024 (2025-1), endYear = 2025. Label "2024 - 2025", Value "2024-25"
    // i = 1: startYear = 2023 (2025-2), endYear = 2024. Label "2023 - 2024", Value "2023-24"
    // ...and so on for i = 2, i = 3.
    for (let i = 0; i < 4; i++) {
      const academicYearStart = currentCalendarYear - 1 - i;
      const academicYearEnd = currentCalendarYear - i;

      const yearLabel = `${academicYearStart} - ${academicYearEnd}`;
      // Format value as YYYY-YY, e.g., 2024-25. We need a Date object for the end year to format to 'yy'.
      const endYearDateForFormatting = new Date(academicYearEnd, 0, 1); // Month and day don't matter for year formatting
      const yearValue = `${academicYearStart}-${format(endYearDateForFormatting, 'yy')}`;
      options.push({ value: yearValue, label: yearLabel });
    }

    return options;
  }, []);

  const stateOptions = useMemo(() => {
    const options: XFactorLeaderboardSelectOption[] = [{ value: '', label: 'All States' }];
    if (currentUser?.state_code) {
      const userState = STATE_OPTIONS.find(opt => opt.value === currentUser.state_code);
      if (userState) {
        options.push(userState as XFactorLeaderboardSelectOption);
      }
    }
    return options;
  }, [currentUser]);

  const handleStateFilterChange = (stateValue: string) => {
    setFilters(prev => ({
      ...prev,
      state: stateValue === '' ? undefined : stateValue, // Allow unselecting to remove filter
      all_time: !stateValue && !prev.academic_year && !prev.start_date && !prev.end_date, // Re-evaluate all_time
    }));
  };

  const handleYearFilterChange = (yearValue: string) => {
    setSelectedYearDisplayValue(yearValue);
    let newFilters: Partial<XFactorLeaderboardFilters> = {};

    if (yearValue === 'all_time') {
      newFilters = {
        all_time: true,
        academic_year: undefined,
        start_date: undefined,
        end_date: undefined,
      };
    } else if (yearValue === 'year_to_date') {
      const today = new Date();
      const startOfYear = new Date(today.getFullYear(), 0, 1);
      newFilters = {
        all_time: false,
        academic_year: undefined,
        start_date: formatDateToApi(startOfYear, false), // false for isEndDate
        end_date: formatDateToApi(today, true), // true for isEndDate
      };
    } else {
      // yearValue is now directly in YYYY-YY format, e.g., "2024-25"
      newFilters = {
        all_time: false,
        academic_year: yearValue, // Use directly
        start_date: undefined,
        end_date: undefined,
      };
    }
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleConnect = (recipientUser: XFactorLeaderboardItem) => {
    const canConnectWithUser = checkCanConnect(profileType);
    if (!canConnectWithUser) {
      console.log('Cannot connect with this user type or self.');
      return;
    }
    const recipient: UserBasicData = {
      id: recipientUser.id,
      firstName: recipientUser.first_name,
      lastName: recipientUser.last_name,
      profileImageUrl: recipientUser.profile_image_url || null,
    };
    open(<ConnectMessageForm recipient={recipient} />, 'md'); // Corrected to use 'open'
  };

  const memoizedLeaderboardItems = useMemo(() => leaderboardItems, [leaderboardItems]);

  // Calculate start and end item numbers for display
  const startItem = totalItems > 0 ? 1 : 0; // Always starts from 1 for loaded items
  const endItem = memoizedLeaderboardItems.length; // Current number of loaded items

  // Content rendering logic
  const renderContent = () => {
    if (isLoading && memoizedLeaderboardItems.length === 0) {
      return (
        <div className="text-center py-20">
          <FontAwesomeIcon icon={faSpinner} className="animate-spin text-brand-blue text-4xl" />
          <p className="mt-2 text-gray-500">Loading leaderboard...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="text-center py-10">
          <FontAwesomeIcon icon={faExclamationTriangle} size="3x" className="mb-4" />
          <p className="font-semibold">Oops! Something went wrong.</p>
          <p>Error loading leaderboard: {error.message}</p>
          <Button color="blue" variant="filled" size="small" onClick={() => refetchLeaderboard()}>
            Retry
          </Button>
        </div>
      );
    }

    if (memoizedLeaderboardItems.length === 0) {
      // This covers !isLoading && !error && no items
      return (
        <div className="text-center py-20 text-gray-500">
          No leaderboard data found for the selected filters.
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {memoizedLeaderboardItems?.map((item: XFactorLeaderboardItem) => (
          <XFactorLeaderboardCard
            key={item.id}
            id={item.id}
            firstName={item.first_name}
            lastName={item.last_name}
            rank={item.rank}
            graduationYear={item.graduation_year}
            highSchool={item.school_name}
            avatar={item.profile_image_url}
            badgeName={item.badge_name}
            modulesCompleted={item.completed_modules_count}
            sports={item.sports || []}
            additionalSportsCount={
              item.sports && item.sports.length > 1 ? item.sports.length - 1 : undefined
            }
            canConnect={item.id !== currentUser?.id && checkCanConnect(profileType)}
            onConnect={item.id !== currentUser?.id ? () => handleConnect(item) : undefined}
            isCurrentUser={item.id === currentUser?.id}
          />
        ))}
      </div>
    );
  };

  return (
    <Card elevation="card" className="mb-10" noPadding>
      <CardHeader
        title="Leaderboard"
        titleIcon={faTrophy}
        className="px-4 pt-6 lg:px-8 lg:pt-8 mb-8"
        customEdit={
          <div className="flex gap-4">
            <XFactorLeaderboardSelect
              label="State Filter"
              value={filters.state ?? ''}
              onChange={handleStateFilterChange}
              options={stateOptions}
              placeholder="Select State"
              className="min-w-36"
            />

            <XFactorLeaderboardSelect
              label="Year/Timeframe Filter"
              value={selectedYearDisplayValue}
              onChange={handleYearFilterChange}
              options={yearFilterOptions}
              placeholder="Select Year/Timeframe"
              className="min-w-36"
            />
          </div>
        }
      />

      <div className="px-6 pb-6">{renderContent()}</div>

      {/* "Showing X of Y results" and "Load More" button */}
      {/* This section only shows if there are items and hasNextPage is true */}
      {memoizedLeaderboardItems.length > 0 && hasNextPage && (
        <div className="px-6 pb-8 mt-0 text-xs sm:text-sm text-gray-600">
          <div className="mb-4">
            Showing <span className="font-semibold">{endItem}</span> of{' '}
            <span className="font-semibold">{totalItems}</span> results
          </div>
          <Button
            color="blue"
            variant="filled"
            size="small"
            onClick={() => fetchNextPage()}
            disabled={isFetchingNextPage} // Only disable if fetching next page, initial isLoading is handled by main content loader
            className="w-full sm:w-auto"
          >
            {isFetchingNextPage ? (
              <>
                <FontAwesomeIcon icon={faSpinner} className="animate-spin mr-2" /> Loading...
              </>
            ) : (
              'Load More'
            )}
          </Button>
        </div>
      )}
    </Card>
  );
}
