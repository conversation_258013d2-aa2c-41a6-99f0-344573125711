'use client';

import { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { faArrowLeft, faClock } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useQueryClient } from '@tanstack/react-query';
import { differenceInSeconds, intervalToDuration } from 'date-fns';
import Button from '@/components/shared/Button';
import { InfoCard } from '@/components/shared/cards/InfoCard';
import Radio from '@/components/shared/form/Radio';
import { WysiwygEditor } from '@/components/shared/form/WysiwygEditor';
import { QuestionType } from '@/enums/question-type.enum';
import { TestStatus } from '@/enums/test-status.enum';
import { useXFactorExam } from '@/hooks/x-factor/useXFactorExam';
import { useXFactorModule } from '@/hooks/x-factor/useXFactorModules';
import {
  ExamAttempt,
  ExamResponse,
  Question,
  type SubmitExamRequest,
} from '@/services/x-factor-exam.service';
import { useModalStore } from '@/stores/modal.store';
import type { QuestionResponse, TestAttempt } from '@/types/quiz';
import { ExamTimer } from './ExamTimer';
import { ExamExpiredModal } from './modals/ExamExpiredModal';

type ExamSubmitStatus = TestStatus;

interface ExamProps {
  moduleId: number;
  courseId: number;
  onComplete?: (score: number) => void;
}

interface ExamSubmissionResult {
  score: number;
  status: TestStatus;
}

export const XFactorExam = ({ moduleId, courseId, onComplete }: ExamProps) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { setModal, close } = useModalStore();
  const {
    exam,
    isLoading,
    error,
    startAttempt,
    isStartingAttempt,
    submitExam,
    isSubmittingExam,
    currentAttempt,
  } = useXFactorExam(moduleId);

  const { module } = useXFactorModule(moduleId, courseId);

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<Record<number, ExamResponse>>({});
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [submissionResult, setSubmissionResult] = useState<ExamAttempt | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionError, setError] = useState<string | null>(null);

  // Development-only logging
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development' || !exam?.questions) return;

    console.log(
      'RIGHT ANSWERS',
      exam.questions.map(q => (q.answers || []).findIndex(a => a.isCorrect) + 1)
    );
  }, [exam]);

  // Handle exam completion state
  useEffect(() => {
    if (exam?.lastCompletedAttempt && onComplete) {
      const score = exam.lastCompletedAttempt.score ?? 0;
      if (score >= (exam.passingScore ?? 0)) {
        onComplete(score);
      }
    }
  }, [
    exam?.lastCompletedAttempt?.score,
    exam?.passingScore,
    onComplete,
    exam?.lastCompletedAttempt,
  ]);

  const handleSubmitExam = useCallback(async () => {
    if (!exam || !currentAttempt) return;

    try {
      setIsSubmitting(true);
      const submitData: SubmitExamRequest = {
        status: TestStatus.Complete,
        responses: Object.entries(responses).map(([questionId, response]) => ({
          questionId: parseInt(questionId),
          response: response.response,
          responseHtml: response.responseHtml,
        })),
      };

      submitExam(submitData, {
        onSuccess: (result: ExamAttempt) => {
          if (result.score === 100) {
            onComplete?.(result.score);
          }
          setSubmissionResult(result);
        },
        onError: error => {
          console.error('Error submitting exam:', error);
          setError('Failed to submit exam. Please try again.');
        },
      });
    } catch (error) {
      console.error('Error submitting exam:', error);
      setError('Failed to submit exam. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [exam, currentAttempt, responses, submitExam, onComplete]);

  const handleExamExpired = useCallback(async () => {
    if (!currentAttempt || !exam) return;

    try {
      // Prevent duplicate submissions
      if (isSubmitting) return;

      setIsSubmitting(true);
      const submitData: SubmitExamRequest = {
        status: TestStatus.Expired,
        responses: Object.entries(responses).map(([questionId, response]) => ({
          questionId: parseInt(questionId),
          response: response.response,
          responseHtml: response.responseHtml,
        })),
      };

      await submitExam(submitData, {
        onSuccess: () => {
          // Invalidate the course detail query cache
          queryClient.invalidateQueries({
            queryKey: ['x-factor', 'courses', courseId.toString()],
          });

          // Show modal after successful submission
          setModal(
            <ExamExpiredModal
              waitPeriod={exam.waitPeriod}
              onClose={() => {
                close();
                router.push(`/x-factor/courses/${courseId}`);
              }}
            />
          );
        },
        onError: error => {
          console.error('Error submitting expired exam:', error);
          setError('Failed to submit exam. Please try again.');
          setIsSubmitting(false); // Reset submission state on error
        },
      });
    } catch (error) {
      console.error('Error submitting expired exam:', error);
      setError('Failed to submit exam. Please try again.');
      setIsSubmitting(false); // Reset submission state on error
    }
  }, [
    currentAttempt,
    exam,
    responses,
    submitExam,
    setModal,
    close,
    router,
    courseId,
    isSubmitting,
    queryClient,
  ]);

  // Handle timer
  useEffect(() => {
    if (!currentAttempt?.endsAt) return;

    const endsAtDate = new Date(currentAttempt.endsAt);
    let isHandlingExpiration = false;

    const timer = setInterval(() => {
      const now = new Date();
      const remaining = differenceInSeconds(endsAtDate, now);

      if (remaining <= 0 && !isHandlingExpiration) {
        clearInterval(timer);
        isHandlingExpiration = true; // Prevent multiple expirations
        handleExamExpired();
        setTimeRemaining(0);
      } else if (remaining > 0) {
        setTimeRemaining(remaining);
      }
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [currentAttempt?.endsAt, handleExamExpired]);

  // Clear submission state when component unmounts or when exam changes
  useEffect(() => {
    return () => {
      setIsSubmitting(false);
    };
  }, [exam?.id]);

  const formatTime = (seconds: number): string => {
    const duration = intervalToDuration({ start: 0, end: seconds * 1000 });
    const hours = duration.hours?.toString() ?? '0';
    const minutes = duration.minutes?.toString().padStart(2, '0') ?? '00';
    return `${hours}:${minutes}`;
  };

  const handleStartExam = async () => {
    if (exam) {
      await startAttempt();
    }
  };

  const handleAnswerChange = (questionId: number, response: string, responseHtml?: string) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: {
        questionId,
        response,
        responseHtml,
      },
    }));
  };

  // Show loading state
  if (isLoading || isStartingAttempt) {
    return (
      <div className="max-w-3xl mx-auto">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (submissionError) {
    return (
      <div className="max-w-3xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h2 className="text-xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600 mb-4">{submissionError}</p>
          <Button onClick={() => window.location.reload()} color="blue">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  // Show pending grading state
  const isPendingGrading =
    exam?.lastCompletedAttempt &&
    exam.lastCompletedAttempt.status === TestStatus.PendingReview &&
    exam.lastCompletedAttempt.score === 0;

  if (isPendingGrading) {
    return (
      <div className="max-w-3xl mx-auto space-y-6">
        <div className="bg-white rounded-[32px] p-8 shadow-[0_8px_20px_rgba(0,0,0,0.04)]">
          <h1 className="text-2xl font-bold mb-6">EXAM SUBMITTED</h1>
          <div className="mb-6">
            <InfoCard
              type="info"
              message="Your exam has been submitted and is currently pending review by a Positive Athlete proctor. You will be notified once your exam has been graded."
            />
          </div>
          <div className="text-gray-600">
            <p className="mb-4">While you wait for your results, you can:</p>
            <ul className="list-disc list-inside space-y-2 ml-4">
              <li>Review other course materials</li>
              <li>Explore additional courses</li>
              <li>Check out recommended modules</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  // Pre-test state (before attempt creation)
  if (!currentAttempt && exam) {
    return (
      <>
        <ExamTimer timeRemaining={exam.timeLimit} isTimeLimit />

        <div className="max-w-3xl mx-auto space-y-6">
          {/* Exam Card */}
          <div className="bg-white rounded-[32px] p-8 shadow-[0_8px_20px_rgba(0,0,0,0.04)]">
            <h1 className="text-2xl font-bold mb-2">COURSE EXAM</h1>
            <h4 className="text-gray-600 mb-6">{module?.name}</h4>

            <p className="text-gray-600 mb-6">
              This exam will test your knowledge of all content in this certification course. You
              will have {formatTime(exam.timeLimit)} to complete the exam. There are{' '}
              {exam.questions.length} multiple choice questions and one short essay response that
              will be graded by a Positive Athlete proctor. This exam is pass/fail.{' '}
              {exam.passingScore}% or better is a passing grade. It is necessary to pass this exam
              to obtain the Peak Performance Leadership Certification.
            </p>

            <Button onClick={handleStartExam} color="blue" disabled={isStartingAttempt}>
              Begin!
            </Button>
          </div>
        </div>
      </>
    );
  }

  // Active exam state
  if (!exam || !currentAttempt) {
    return null;
  }

  const hasAnsweredAllQuestions = Object.keys(responses).length === exam.questions.length;

  return (
    <>
      <ExamTimer
        timeRemaining={timeRemaining ?? exam.timeLimit}
        isTimeLimit={timeRemaining === null}
      />

      <div className="max-w-3xl mx-auto">
        {/* Intro Card */}
        <div className="bg-white rounded-[32px] p-8 shadow-[0_8px_20px_rgba(0,0,0,0.04)] mb-6">
          <h1 className="text-2xl font-bold mb-6">FINAL EXAM</h1>

          <p className="text-gray-600">
            This exam will test your knowledge of all content in this certification course. You will
            have {formatTime(exam.timeLimit)} to complete the exam. There are{' '}
            {exam.questions.length} multiple choice questions and one short essay response that will
            be graded by a Positive Athlete proctor. This exam is pass/fail. {exam.passingScore}% or
            better is a passing grade. It is necessary to pass this exam to obtain the Peak
            Performance Leadership Certification.
          </p>
        </div>

        {/* Question Cards */}
        <div className="space-y-6">
          {exam.questions.map((question, index) => {
            const response = responses[question.id];
            return (
              <div
                key={question.id}
                className="bg-white rounded-[32px] p-8 shadow-[0_8px_20px_rgba(0,0,0,0.04)]"
              >
                <div className="flex items-center gap-2 mb-4">
                  <span className="font-bold">QUESTION {index + 1}</span>
                </div>
                <p className="text-lg font-medium mb-6">{question.question}</p>
                <div className="space-y-4">
                  {question.type === QuestionType.MultipleChoice && question.answers ? (
                    <Radio
                      options={question.answers.map(answer => ({
                        label: answer.answer,
                        value: answer.id.toString(),
                      }))}
                      value={response?.response || ''}
                      onChange={value => handleAnswerChange(question.id, value)}
                      name={`question-${question.id}`}
                    />
                  ) : (
                    <WysiwygEditor
                      value={response?.responseHtml || ''}
                      onChange={html => handleAnswerChange(question.id, html, html)}
                      placeholder="Write your answer here..."
                    />
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Submit Button Card */}
        <div className="flex justify-end mt-12">
          <Button
            onClick={handleSubmitExam}
            color="blue"
            disabled={!hasAnsweredAllQuestions || isSubmitting}
          >
            Complete and Submit Answers
          </Button>
        </div>
      </div>
    </>
  );
};
