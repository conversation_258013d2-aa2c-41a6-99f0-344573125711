'use client';

import { Fragment } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { faArrowRight, faCheck, faClock } from '@fortawesome/pro-regular-svg-icons';
import { faLock, faLockOpen } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { differenceInSeconds, intervalToDuration, isFuture, isPast, parseISO } from 'date-fns';
import Button from '@/components/shared/Button';
import { ProgressBar } from '@/components/shared/ProgressBar';
import { ProgressCircle } from '@/components/shared/ProgressCircle';
import StatusChip from '@/components/shared/StatusChip';
import { cn } from '@/lib/utils';
import type { XFactorCourse } from '@/services/x-factor-course.service';

interface XFactorCourseDetailProps {
  course: XFactorCourse;
}

export function XFactorCourseDetail({ course }: XFactorCourseDetailProps) {
  // Helper function to format wait time remaining
  const formatWaitTime = (nextAttemptDate: string): string => {
    const now = new Date();
    const target = parseISO(nextAttemptDate);
    const seconds = differenceInSeconds(target, now);
    const duration = intervalToDuration({ start: now, end: target });

    if (duration.weeks && duration.weeks > 0) {
      return `${duration.weeks}w remaining`;
    }
    if (duration.days && duration.days > 0) {
      return `${duration.days}d remaining`;
    }
    if (duration.hours && duration.hours > 0) {
      return `${duration.hours}h remaining`;
    }
    if (duration.minutes && duration.minutes > 0) {
      return `${duration.minutes}m remaining`;
    }
    return 'Less than 1m remaining';
  };

  // Helper function to determine if a module should be unlocked
  const isModuleUnlocked = (moduleIndex: number) => {
    const courseModule = course.modules[moduleIndex];
    const previousModule = moduleIndex > 0 ? course.modules[moduleIndex - 1] : null;

    // First module is always unlocked
    if (moduleIndex === 0) return true;

    // Check if previous module is completed
    if (!previousModule?.completed_at) return false;

    // Check if previous module has a waiting period
    if (previousModule.next_attempt_available_at) {
      return isPast(parseISO(previousModule.next_attempt_available_at));
    }

    // If this module has a next attempt time, check if we've passed it
    if (courseModule.next_attempt_available_at) {
      return isPast(parseISO(courseModule.next_attempt_available_at));
    }

    return true;
  };

  // Helper function to determine if a module is in waiting period
  const isModuleWaiting = (courseModule: XFactorCourse['modules'][0]) => {
    return (
      courseModule.next_attempt_available_at &&
      isFuture(parseISO(courseModule.next_attempt_available_at))
    );
  };

  return (
    <div className="space-y-8 bg-white rounded-t-2xl rounded-b-[32px] overflow-hidden shadow-xl">
      {/* Course Header - Similar to Carousel */}
      <div className="relative h-[320px] overflow-hidden">
        {/* Background Image */}
        {course.coverImageUrl ? (
          <Image
            src={course.coverImageUrl}
            alt={course.title}
            fill
            unoptimized
            sizes="100vw"
            className="object-cover"
            priority
          />
        ) : (
          <div className="absolute inset-0 bg-gray-200" />
        )}

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 to-black/40" />

        {/* Content */}
        <div className="relative h-full px-4 md:px-16 flex flex-col justify-center">
          <div className="flex items-center gap-x-8">
            {/* Progress Circle */}
            <ProgressCircle
              progress={(course.modulesCompleted / course.totalModules) * 100}
              size={120}
              primaryColor="#D50032"
              secondaryColor="#FFFFFF"
              className="hidden md:block"
            />

            <div className="w-full md:max-w-[380px] flex flex-col gap-6">
              <ProgressBar
                progress={(course.modulesCompleted / course.totalModules) * 100}
                className="md:hidden max-w-36"
              />

              <div className="flex flex-col gap-4">
                <StatusChip
                  label={`${Math.round(course.averageScore)}% Avg Score`}
                  variant="success"
                  className="w-fit"
                />

                {/* Title and Progress */}
                <h2 className="text-[32px] leading-[1] font-bold text-white">{course.title}</h2>
                <p className="text-white text-[16px] leading-[1.2]">
                  {course.modulesCompleted}/{course.totalModules} Modules Completed
                </p>
              </div>

              {course.modulesCompleted < course.totalModules && (
                <div>
                  <Button
                    href={`/x-factor/courses/${course.id}/next-module`}
                    color="red"
                    size="small"
                    icon={faArrowRight}
                  >
                    Continue Course
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Module List */}
      <div className="grid grid-cols-[48px_1fr] gap-x-2 md:gap-x-6 gap-y-4 px-4 pb-10">
        {course.modules.map((module, index) => {
          const isWaiting = isModuleWaiting(module);
          const isLocked = !isModuleUnlocked(index);
          const isCompleted = !!module.completed_at;
          const isActive = !isLocked && !isCompleted && !isWaiting;

          return (
            <Fragment key={module.id}>
              {/* Progress Indicator Column */}
              <div className="relative flex md:items-center translate-y-10 md:translate-y-0">
                {/* Vertical connecting line */}
                {index < course.modules.length - 1 && (
                  <div
                    className={cn(
                      'absolute left-5 md:left-1/2 w-0.5 md:-translate-x-1/2',
                      'top-0 h-[calc(100%+20px)] md:top-[calc(50%+24px)] md:h-[calc(100%+16px)]',
                      {
                        'bg-brand-red': isCompleted || isActive,
                        'bg-grey-3': isLocked || isWaiting,
                      }
                    )}
                  />
                )}

                {/* Indicator Circle */}
                <div
                  className={cn(
                    'relative size-10 md:size-12 rounded-full flex items-center justify-center z-10',
                    {
                      'bg-brand-red border-2 border-brand-red': isCompleted && !isWaiting,
                      'bg-white border-2 border-brand-red': isActive,
                      'bg-grey-3 border-2 border-grey-3': isLocked || isWaiting,
                    }
                  )}
                >
                  {isCompleted && !isWaiting ? (
                    <FontAwesomeIcon icon={faCheck} className="text-white text-lg" />
                  ) : isActive ? (
                    <FontAwesomeIcon icon={faLockOpen} className="text-brand-red text-lg" />
                  ) : (
                    <FontAwesomeIcon icon={faLock} className="text-grey-5 text-lg" />
                  )}
                </div>
              </div>

              {/* Module Card */}
              <div
                className={cn(
                  'w-full text-left bg-white rounded-[24px] border border-grey-2 shadow-card',
                  !isLocked && !isWaiting && 'hover:bg-surface-hover transition-colors duration-200'
                )}
              >
                <Link
                  href={
                    isLocked || isWaiting
                      ? '#'
                      : `/x-factor/courses/${course.id}/modules/${module.id}`
                  }
                  className={cn(
                    'block relative overflow-hidden',
                    (isLocked || isWaiting) && 'pointer-events-none'
                  )}
                >
                  {isCompleted && (
                    <span className="flex md:hidden items-center font-sans text-sm gap-x-2 pl-2 pr-4 py-2 z-30 bg-brand-red rounded-bl-lg rounded-tr-3xl absolute top-0 right-0 text-white">
                      <FontAwesomeIcon
                        icon={faCheck}
                        className="text-white size-4"
                        aria-hidden="true"
                      />
                      Completed
                    </span>
                  )}
                  <div className="flex md:h-[104px] aspect-[3/4] md:aspect-auto">
                    {/* Module Thumbnail */}
                    <div className="relative z-10 w-full md:w-[160px] flex-shrink-0 overflow-hidden rounded-3xl md:rounded-l-3xl md:rounded-r-none">
                      {module.cover_image ? (
                        <>
                          <Image
                            src={module.cover_image}
                            alt={module.name}
                            fill
                            unoptimized
                            className="object-cover z-10"
                          />
                          <div className="absolute inset-0 bg-gradient-to-b from-black/5 to-black/50 z-20" />
                        </>
                      ) : (
                        <div className="w-full h-full bg-neutral-500 md:bg-surface-secondary" />
                      )}
                    </div>

                    {/* Module Info (Mobile) */}
                    <div className="flex flex-1 md:hidden absolute inset-0 w-full h-full items-end justify-center px-4 py-6 z-20">
                      <div>
                        <h2 className="text-xl uppercase font-semibold text-white mb-2">
                          {module.name}
                        </h2>

                        <div className="flex items-center gap-4">
                          {module.completed_at && !isWaiting && (
                            <StatusChip
                              label={module.score ? `${module.score}%` : 'Completed'}
                              variant={module.score && module.score >= 90 ? 'success' : 'alert'}
                            />
                          )}
                          {isWaiting && module.next_attempt_available_at && (
                            <StatusChip
                              label={`Waiting Period (${formatWaitTime(module.next_attempt_available_at)})`}
                              variant="alert"
                            />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Module Info (Desktop) */}
                    <div className="hidden flex-1 md:flex items-center justify-between px-8">
                      <div>
                        <h3 className="text-[16px] leading-tight font-semibold text-text-primary mb-2">
                          {module.name}
                        </h3>
                        <div className="flex items-center text-text-secondary text-xs">
                          <div className="flex items-center gap-2">
                            <FontAwesomeIcon icon={faClock} size="sm" className="text-grey-5" />
                            <span>{module.duration_minutes} minutes</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4">
                        {module.completed_at && !isWaiting && (
                          <StatusChip
                            label={module.score ? `${module.score}%` : 'Completed'}
                            variant={module.score && module.score >= 90 ? 'success' : 'alert'}
                          />
                        )}
                        {isWaiting && module.next_attempt_available_at && (
                          <StatusChip
                            label={`Waiting Period (${formatWaitTime(module.next_attempt_available_at)})`}
                            variant="alert"
                          />
                        )}
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            </Fragment>
          );
        })}
      </div>
    </div>
  );
}
