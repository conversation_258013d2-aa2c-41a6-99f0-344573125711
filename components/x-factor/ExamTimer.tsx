import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { faClock } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { intervalToDuration } from 'date-fns';

interface ExamTimerProps {
  timeRemaining: number | null;
  /**
   * If true, indicates this is showing the total time limit rather than remaining time
   */
  isTimeLimit?: boolean;
}

const formatTime = (seconds: number, isTimeLimit: boolean): string => {
  const duration = intervalToDuration({ start: 0, end: seconds * 1000 });

  // Always show hours:minutes for time limit display
  if (isTimeLimit) {
    const hours = duration.hours?.toString() ?? '0';
    const minutes = duration.minutes?.toString().padStart(2, '0') ?? '00';
    return `${hours}:${minutes}`;
  }

  // If less than 1 minute remaining, show minutes:seconds
  if (seconds < 60 && duration.seconds !== undefined) {
    return `0:${duration.seconds.toString().padStart(2, '0')}`;
  }

  // Otherwise show hours:minutes
  const hours = duration.hours?.toString() ?? '0';
  const minutes = duration.minutes?.toString().padStart(2, '0') ?? '00';
  return `${hours}:${minutes}`;
};

export function ExamTimer({ timeRemaining, isTimeLimit }: ExamTimerProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  if (!mounted || timeRemaining === null) return null;

  const timerContent = (
    <div className="flex flex-col items-end gap-2 bg-gray-200 rounded-xl px-6 py-4">
      <p className="text-primary">{isTimeLimit ? 'Time Limit' : 'Time Remaining'}</p>
      <span
        className={`text-primary font-bold text-lg ${
          !isTimeLimit && timeRemaining < 60 ? 'text-red-600' : ''
        }`}
      >
        {formatTime(timeRemaining, isTimeLimit ?? false)}{' '}
        <FontAwesomeIcon icon={faClock} className="text-secondary" />
      </span>
    </div>
  );

  const portalTarget = document.getElementById('exam-timer-portal');
  if (!portalTarget) return timerContent;

  return createPortal(timerContent, portalTarget);
}
