'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { SwiperSlide } from 'swiper/react';
import Button from '@/components/shared/Button';
// import CardCarousel from '@/components/shared/interactive/CardCarousel';
import { ProgressBar } from '@/components/shared/ProgressBar';
import { ProgressCircle } from '@/components/shared/ProgressCircle';
import StatusChip from '@/components/shared/StatusChip';
import type { XFactorCourse } from '@/services/x-factor-course.service';

const LazyCardCarousel = dynamic(() => import('@/components/shared/interactive/CardCarousel'), {
  ssr: false,
});

interface XFactorCourseCarouselCardProps {
  course: XFactorCourse;
}

const XFactorCourseCarouselCard: React.FC<XFactorCourseCarouselCardProps> = ({ course }) => {
  return (
    <figure className="relative min-h-[320px] overflow-hidden rounded-4xl flex items-center">
      {/* Content */}
      <figcaption className="relative h-full mt-6 mb-20 px-4 flex flex-col justify-center z-20 lg:my-4 lg:px-16">
        <div className="flex items-center gap-x-8">
          {/* Progress Circle */}
          <ProgressCircle
            progress={course.progress}
            size={120}
            primaryColor="#D50032"
            secondaryColor="#FFFFFF"
            className="hidden md:block"
          />

          <div className="max-w-[380px] flex flex-col gap-6">
            <ProgressBar
              progress={(course.modulesCompleted / course.totalModules) * 100}
              className="md:hidden max-w-36"
            />

            <div className="flex flex-col gap-4">
              <StatusChip
                label={`${Math.round(course.averageScore)}% Avg Score`}
                variant="success"
                className="w-fit"
              />

              {/* Title and Progress */}
              <h2 className="text-[32px] leading-[1] font-bold text-white">{course.title}</h2>
              <p className="text-white text-[16px] leading-[1.2]">
                {course.modulesCompleted}/{course.totalModules} Modules Completed
              </p>
            </div>

            <div>
              <Button
                href={`/x-factor/courses/${course.id}`}
                color="red"
                size="small"
                icon={faArrowRight}
              >
                Continue Course
              </Button>
            </div>
          </div>
        </div>
      </figcaption>

      {/* Background Image */}
      {course.coverImageUrl ? (
        <Image
          src={course.coverImageUrl}
          alt={course.title}
          fill
          unoptimized
          sizes="100vw"
          className="object-cover"
          priority
        />
      ) : (
        <div className="absolute inset-0 bg-gray-200 z-10" />
      )}

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-black/80 to-black/40 z-10" />
    </figure>
  );
};

interface XFactorCourseCarouselProps {
  courses: XFactorCourse[];
}

export function XFactorCourseCarousel({ courses }: XFactorCourseCarouselProps) {
  // Don't render if no courses
  if (!courses || courses.length === 0) {
    return null;
  }

  // If only one course, render it directly without carousel
  if (courses.length === 1) {
    return (
      <div className="overflow-hidden px-4 -mx-4">
        <XFactorCourseCarouselCard course={courses[0]} />
      </div>
    );
  }

  // Otherwise render the carousel with multiple courses
  return (
    <LazyCardCarousel>
      {courses.map(course => (
        <SwiperSlide key={course.id} className="!h-auto">
          <XFactorCourseCarouselCard course={course} />
        </SwiperSlide>
      ))}
    </LazyCardCarousel>
  );
}
