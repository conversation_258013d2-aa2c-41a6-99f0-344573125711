'use client';

import dynamic from 'next/dynamic';
import type { TopicModules } from '@/services/x-factor-module.service';

// import { XFactorTopicSection } from './XFactorTopicSection';

const LazyXFactorTopicSection = dynamic(
  () => import('@/components/x-factor/XFactorTopicSection').then(mod => mod.XFactorTopicSection),
  {
    ssr: false,
  }
);

interface XFactorTopicGridProps {
  sections: TopicModules[];
}

export function XFactorTopicGrid({ sections }: XFactorTopicGridProps) {
  if (!Array.isArray(sections)) {
    console.error('XFactorTopicGrid: sections is not an array', sections);
    return null;
  }

  if (sections.length === 0) {
    return (
      <div className="text-center py-12 text-text-secondary">No topics available at this time.</div>
    );
  }

  return (
    <div className="space-y-12">
      {sections.map(section => {
        if (!section?.modules?.length) return null;

        return (
          <LazyXFactorTopicSection key={section.id} title={section.name} items={section.modules} />
        );
      })}
    </div>
  );
}
