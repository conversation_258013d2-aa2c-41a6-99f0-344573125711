'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { faEyes } from '@fortawesome/pro-light-svg-icons';
import { faArrowRight, faClock } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';
import { ProgressCircle } from '@/components/shared/ProgressCircle';
import type { XFactorModule } from '@/services/x-factor-module.service';

interface XFactorSearchResultsProps {
  query: string;
  results: XFactorModule[];
  totalResults: number;
  onClearSearch: () => void;
  onBrowseClick: () => void;
}

export function XFactorSearchResults({
  query,
  results,
  totalResults,
  onClearSearch,
  onBrowseClick,
}: XFactorSearchResultsProps) {
  const router = useRouter();

  // If no results, show empty state
  if (results.length === 0) {
    return (
      <>
        <h2 className="text-heading-lg font-semibold text-text-primary mb-6">
          {totalResults} results for &quot;{query}&quot;
        </h2>
        <div className="bg-white rounded-[24px] shadow-card py-20">
          <div className="flex flex-col items-center justify-center">
            <div className="mb-6 text-[40px]">
              <FontAwesomeIcon icon={faEyes} className="text-text-secondary" />
            </div>
            <h2 className="text-heading-xl font-semibold text-text-primary mb-2">No results</h2>
            <p className="text-text-secondary text-lg mb-8">
              Sorry we couldn&apos;t find what you&apos;re looking for!
            </p>
            <Button onClick={onBrowseClick} icon={faArrowRight} iconPosition="right">
              Browse Modules
            </Button>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      {/* Search Results Header */}
      <h2 className="text-heading-lg font-semibold text-text-primary mb-6">
        {totalResults} results for &quot;{query}&quot;
      </h2>

      {/* Results List */}
      <div className="space-y-4">
        {results.map(module => (
          <button
            key={module.id}
            onClick={() => router.push(`/x-factor/modules/${module.id}`)}
            className="w-full text-left bg-white rounded-[24px] border border-grey-2 shadow-card hover:bg-surface-hover transition-colors duration-200"
          >
            <div className="flex h-[104px]">
              {/* Module Thumbnail */}
              <div className="relative w-[160px] flex-shrink-0 overflow-hidden rounded-l-[24px]">
                {module.cover_image ? (
                  <Image
                    src={module.cover_image}
                    alt={module.name}
                    fill
                    unoptimized
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-surface-secondary" />
                )}
              </div>

              {/* Module Info */}
              <div className="flex-1 flex flex-col justify-center px-8">
                <h3 className="text-[16px] leading-tight font-semibold text-text-primary mb-2">
                  {module.name}
                </h3>
                <div className="flex items-center text-text-secondary text-xs">
                  <span>{module.topics?.[0] || 'Leadership'}</span>
                  <div className="w-[1px] h-3 bg-grey-3 mx-3" />
                  <div className="flex items-center gap-2">
                    <FontAwesomeIcon icon={faClock} size="sm" className="text-grey-5" />
                    <span>{module.duration_minutes} minutes</span>
                  </div>
                </div>
              </div>
            </div>
          </button>
        ))}
      </div>
    </>
  );
}
