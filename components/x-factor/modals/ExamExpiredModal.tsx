import { faExclamationTriangle } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { intervalToDuration } from 'date-fns';
import Button from '@/components/shared/Button';

interface ExamExpiredModalProps {
  waitPeriod: number;
  onClose: () => void;
}

const formatTime = (seconds: number): string => {
  if (seconds === 0) {
    return 'immediately';
  }

  const duration = intervalToDuration({ start: 0, end: seconds * 1000 });

  const parts = [];
  if (duration.days && duration.days > 0) {
    parts.push(`${duration.days} day${duration.days !== 1 ? 's' : ''}`);
  }
  if (duration.hours && duration.hours > 0) {
    parts.push(`${duration.hours} hour${duration.hours !== 1 ? 's' : ''}`);
  }
  if (duration.minutes && duration.minutes > 0) {
    parts.push(`${duration.minutes} minute${duration.minutes !== 1 ? 's' : ''}`);
  }

  return parts.join(', ') || '0 minutes';
};

export function ExamExpiredModal({ waitPeriod, onClose }: ExamExpiredModalProps) {
  return (
    <div className="p-8 max-w-lg">
      <div className="flex items-center gap-4 mb-6">
        <h2 className="text-2xl font-bold">Exam Time Expired</h2>
      </div>

      <div className="space-y-4 mb-8">
        <p className="text-gray-600">
          Your exam time has expired. All answers submitted up to this point have been saved.
        </p>
        <p className="text-gray-600">
          You{' '}
          {waitPeriod === 0
            ? 'can attempt this exam again immediately'
            : `will need to wait ${formatTime(waitPeriod)} before you can attempt this exam again`}
          .
        </p>
      </div>

      <div className="flex justify-end">
        <Button onClick={onClose} color="blue">
          Close
        </Button>
      </div>
    </div>
  );
}
