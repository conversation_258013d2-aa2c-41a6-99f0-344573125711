import { useCallback, useEffect, useMemo, useState } from 'react';
import { faArrowLeft } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import debounce from 'lodash/debounce';
import Button from '@/components/shared/Button';
import SearchInput from '@/components/shared/form/SearchInput';
import { XFactorNavigation } from '@/components/x-factor/XFactorNavigation';
import type { XFactorFilters } from '@/hooks/x-factor/useXFactorView';

interface XFactorHeaderProps {
  filters: XFactorFilters;
  displaySearchBar?: boolean;
  onFilterChange: (filters: Partial<XFactorFilters>) => void;
}

export function XFactorHeader({
  filters,
  displaySearchBar = false,
  onFilterChange,
}: XFactorHeaderProps) {
  const [searchInput, setSearchInput] = useState(filters.query || '');

  // Sync search input with filters
  useEffect(() => {
    setSearchInput(filters.query || '');
  }, [filters.query]);

  // Create debounced search handler
  const debouncedSearch = useCallback(
    (value: string) => {
      onFilterChange({ query: value || undefined });
    },
    [onFilterChange]
  );

  // Create memoized debounced function
  const debouncedSearchFn = useMemo(() => debounce(debouncedSearch, 400), [debouncedSearch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      debouncedSearchFn.cancel();
    };
  }, [debouncedSearchFn]);

  // Update filters when search input changes
  useEffect(() => {
    debouncedSearchFn(searchInput);
  }, [searchInput, debouncedSearchFn]);

  const handleClearSearch = () => {
    setSearchInput('');
    onFilterChange({ query: undefined });
  };

  // Get the page title based on active tab
  const getPageTitle = () => {
    switch (filters.activeTab) {
      case 'browse':
        return 'Browse';
      case 'completed':
        return 'Your Completed';
      case 'dashboard':
      default:
        return 'Home';
    }
  };

  return (
    <>
      <div className="sticky left-0 top-0 w-full z-40">
        <XFactorNavigation filters={filters} onFilterChange={onFilterChange} />
      </div>

      <div className="pa-container pt-8 flex items-center justify-between">
        {/* Page Title or Back Button */}
        {filters.query ? (
          <Button
            onClick={handleClearSearch}
            variant="text"
            icon={faArrowLeft}
            iconPosition="left"
            color="blue"
          >
            Back
          </Button>
        ) : (
          <h1 className="hidden lg:block text-heading-2xl font-bold text-primary">
            {getPageTitle()}
          </h1>
        )}

        {/* Search */}
        {displaySearchBar && (
          <div className="flex-1 flex items-center justify-center lg:justify-end">
            <div className="relative w-full max-w-[500px] z-5">
              {/* TODO: Replace with Meilisearch InstantSearch Search Box component/useSearchBox hook */}
              <SearchInput
                value={searchInput}
                placeholder="Search character traits, athletes, or topics..."
                onChange={e => setSearchInput(e.target.value)}
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
}
