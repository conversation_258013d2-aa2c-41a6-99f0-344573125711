import React from 'react';
import Image from 'next/image';

interface XFactorBannerProps {
  title: string;
}

export function XFactorBanner({ title }: XFactorBannerProps) {
  return (
    <div className="relative h-[116px] bg-[#DC052D] overflow-hidden">
      <div className="absolute right-0 h-full w-[500px]">
        <Image
          src="/images/xfactor-banner-bg.png"
          alt="X Factor Banner Background"
          fill
          unoptimized
          sizes="500px"
          className="object-cover"
          priority
        />
      </div>
      <div className="relative h-full pa-container flex justify-between gap-x-2 items-center">
        <h1 className="text-xl lg:text-3xl font-bold text-white">{title}</h1>
        <Image
          src="/images/xfactor-logo-white.svg"
          alt="X Factor"
          width={150}
          height={56}
          priority
          unoptimized
        />
      </div>
    </div>
  );
}
