import { useEffect, useRef, useState } from 'react';
import { faCircleChevronLeft, faCircleChevronRight } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import type { Swiper as SwiperType } from 'swiper';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import type { XFactorCourse } from '@/services/x-factor-course.service';
import type { XFactorModule } from '@/services/x-factor-module.service';
import { XFactorCard } from './XFactorCard';
// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';

type SectionItem = XFactorCourse | XFactorModule;

interface XFactorTopicSectionProps {
  title: string;
  items: SectionItem[];
}

export function XFactorTopicSection({ title, items }: XFactorTopicSectionProps) {
  const [swiper, setSwiper] = useState<SwiperType | null>(null);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  useEffect(() => {
    if (swiper) {
      setIsBeginning(swiper.isBeginning);
      setIsEnd(swiper.isEnd);

      const updateState = () => {
        setIsBeginning(swiper.isBeginning);
        setIsEnd(swiper.isEnd);
      };

      swiper.on('slideChange', updateState);
      swiper.on('snapGridLengthChange', updateState);

      return () => {
        swiper.off('slideChange', updateState);
        swiper.off('snapGridLengthChange', updateState);
      };
    }
  }, [swiper, items]);

  return (
    <div className="mb-8">
      {/* Topic Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">{title}</h2>

        {/* Navigation Buttons */}
        <div className="flex gap-3">
          <button
            onClick={() => swiper?.slidePrev()}
            className="flex items-center justify-center"
            aria-label="Scroll left"
            disabled={isBeginning}
          >
            <FontAwesomeIcon
              icon={faCircleChevronLeft}
              className="text-brand-red"
              style={{ fontSize: '24px', opacity: isBeginning ? 0.5 : 1 }}
            />
          </button>
          <button
            onClick={() => swiper?.slideNext()}
            className="flex items-center justify-center"
            aria-label="Scroll right"
            disabled={isEnd}
          >
            <FontAwesomeIcon
              icon={faCircleChevronRight}
              className="text-brand-red"
              style={{ fontSize: '24px', opacity: isEnd ? 0.5 : 1 }}
            />
          </button>
        </div>
      </div>

      {/* Items Slider Container */}
      <div className="relative overflow-hidden">
        {/* Corrected Fade Effect */}
        <div className="absolute right-0 top-0 bottom-0 w-24 bg-gradient-to-r from-transparent to-surface-secondary z-10 pointer-events-none" />

        {/* Items Slider */}
        <Swiper
          modules={[Navigation]}
          onSwiper={setSwiper}
          slidesPerView="auto"
          spaceBetween={24}
          className="w-full"
          watchOverflow={true}
          observer={true}
          observeParents={true}
          resizeObserver={true}
          watchSlidesProgress={true}
          onSlideChange={() => {
            if (swiper) {
              setIsBeginning(swiper.isBeginning);
              setIsEnd(swiper.isEnd);
            }
          }}
          onResize={() => {
            if (swiper) {
              setIsBeginning(swiper.isBeginning);
              setIsEnd(swiper.isEnd);
            }
          }}
        >
          {items.map(item => (
            <SwiperSlide key={'title' in item ? item.title : item.name} className="!w-auto">
              <XFactorCard item={item} sectionTopic={title} className="w-60" />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
}
