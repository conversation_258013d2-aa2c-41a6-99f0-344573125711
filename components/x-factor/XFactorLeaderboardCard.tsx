'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import * as solidIcons from '@fortawesome/free-solid-svg-icons';
import { faArrowRight, faMessage } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import Avatar from '@/components/shared/Avatar';
import Card from '@/components/shared/cards/Card';
import Tag from '@/components/shared/Tag';
import { useSportsStore } from '@/stores/sportsStore';
import { getAchievedBadgeImagePath } from '@/utils/badgeUtils';

export interface XFactorLeaderboardCardProps {
  id: number;
  firstName: string;
  lastName: string;
  graduationYear: number | null;
  rank: number;
  highSchool?: string | null;
  avatar?: string | null;
  badgeName?: string | null;
  modulesCompleted?: number;
  sports?: string[];
  additionalSportsCount?: number;
  className?: string;
  onConnect?: () => void;
  canConnect?: boolean;
  profileLink?: string;
  isCurrentUser?: boolean;
}

export default function XFactorLeaderboardCard({
  id,
  firstName,
  lastName,
  graduationYear,
  rank,
  highSchool,
  avatar,
  badgeName,
  modulesCompleted,
  sports = [],
  additionalSportsCount,
  className,
  onConnect,
  canConnect = true,
  profileLink,
  isCurrentUser = false,
}: XFactorLeaderboardCardProps) {
  const availableSports = useSportsStore(state => state.sports);

  const displayGraduationYear = graduationYear ? String(graduationYear) : 'N/A';
  const profileHref = profileLink || `/pa/${id}`;

  const initials = `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();

  const cellBaseClass = 'flex flex-col justify-center text-sm lg:py-4';
  const titleClass = `block text-sm ${isCurrentUser ? 'text-gray-50' : 'text-secondary'}`;
  const valueClass = `text-base font-bold xl:text-xl ${isCurrentUser ? 'text-gray-50' : 'text-secondary'}`;

  let primarySportName: string | undefined;
  let primarySportIcon: IconDefinition | undefined;

  if (sports && sports.length > 0) {
    const firstSportName = sports[0];
    const sportInfo = availableSports.find(
      s => s.slug === firstSportName || s.label === firstSportName
    );
    primarySportName = sportInfo?.label || firstSportName;
    if (sportInfo?.icon) {
      const iconName = `fa${sportInfo.icon
        .split('-')
        .map((part: string) => part.charAt(0).toUpperCase() + part.slice(1))
        .join('')}`;
      primarySportIcon = (solidIcons as unknown as Record<string, IconDefinition>)[iconName];
    }
  }

  const actualBadgeImagePath = getAchievedBadgeImagePath(badgeName);

  return (
    <Card
      roundedCorners="3xl"
      elevation="card"
      className={clsx(
        'grid grid-cols-4 ring-1 ring-gray-100 gap-4 items-center w-full text-sm p-4 lg:grid-cols-12 lg:p-0',
        isCurrentUser ? '!bg-brand-blue' : 'bg-white',
        className
      )}
      noPadding
    >
      {/* Avatar & Primary Info */}
      <div className={clsx('col-span-4 flex items-center gap-2 sm:gap-4 lg:ml-6 lg:gap-6')}>
        {/* Rank */}
        <span
          className={clsx(
            'block text-center font-bold text-2xl min-w-8',
            isCurrentUser ? 'text-white ' : 'text-gray-700'
          )}
        >
          {rank}
        </span>

        <div
          className={clsx(
            'size-14 bg-gray-200 rounded-full flex items-center justify-center relative overflow-hidden font-semibold text-lg shrink-0 sm:size-16',
            {
              'shadow-avatar-outer': !isCurrentUser,
            }
          )}
        >
          {initials}

          {avatar && (
            <Image
              src={avatar}
              alt={`${firstName} ${lastName}`}
              className="absolute inset-0 w-full h-full object-cover"
              fill
            />
          )}
        </div>

        <div className="flex flex-col">
          {graduationYear && (
            <span className={clsx('pa-eyebrow', isCurrentUser ? 'text-gray-50' : 'text-brand-red')}>
              CLASS OF {displayGraduationYear}
            </span>
          )}
          <h3
            className={clsx(
              'font-bold text-base leading-tight xl:text-xl/6',
              isCurrentUser ? 'text-white' : 'text-text-primary'
            )}
          >
            {`${firstName} ${lastName}`}
          </h3>
          {highSchool && (
            <span
              className={clsx('text-sm', isCurrentUser ? 'text-gray-50' : 'text-text-secondary')}
            >
              {highSchool}
            </span>
          )}
        </div>

        {/* Mobile Actions */}
        <div className="flex flex-1 justify-end self-start lg:hidden">
          <div className="grid gap-y-4">
            <Link
              href={profileHref}
              aria-label="View this athlete's profile"
              className={clsx(
                'transition-colors',
                isCurrentUser
                  ? 'text-white hover:text-white'
                  : 'text-text-secondary hover:text-brand-blue'
              )}
            >
              <FontAwesomeIcon icon={faArrowRight} className="h-5 w-5" />
            </Link>

            {canConnect && onConnect && (
              <button
                type="button"
                onClick={onConnect}
                aria-label="Connect with this athlete"
                className={clsx(
                  'transition-colors',
                  isCurrentUser
                    ? 'text-white hover:text-white'
                    : 'text-text-secondary hover:text-brand-blue'
                )}
              >
                <FontAwesomeIcon icon={faMessage} className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Sport */}
      <div className={clsx(cellBaseClass, 'col-span-4 lg:col-span-2 items-start')}>
        <span className={titleClass}>Sport</span>
        <div className="flex items-center gap-1 mt-0.5 flex-wrap">
          {primarySportName ? <Tag label={primarySportName} icon={primarySportIcon} /> : null}
          {additionalSportsCount && additionalSportsCount > 0 && (
            <Tag label={`+${additionalSportsCount}`} />
          )}
          {!primarySportName && (!additionalSportsCount || additionalSportsCount === 0) && (
            <span className={valueClass}>N/A</span>
          )}
        </div>
      </div>

      {/* Badge */}
      <div className={clsx(cellBaseClass, 'col-span-2 items-start lg:col-span-3 xl:col-span-2')}>
        <div className="flex items-center gap-2 mt-0.5">
          {actualBadgeImagePath && badgeName ? (
            <Image
              src={actualBadgeImagePath}
              alt={badgeName}
              className="h-14 w-auto"
              width={56}
              height={56}
            />
          ) : badgeName ? (
            <div className="block">
              <span className={clsx('block', titleClass)}>Badge</span>
              <span className={clsx(valueClass, 'block text-base')}>{badgeName}</span>
            </div>
          ) : (
            <span className={clsx(valueClass, 'text-base')}>N/A</span>
          )}
          {actualBadgeImagePath && badgeName && (
            <div className="block">
              <span className={clsx('block', titleClass)}>Badge</span>
              <span className={clsx(valueClass, 'block text-base')}>{badgeName}</span>
            </div>
          )}
        </div>
      </div>

      {/* Modules Completed */}
      <div className={clsx(cellBaseClass, 'col-span-2 items-start self-start lg:self-center')}>
        <span className={titleClass}>Modules Completed</span>
        <span className={clsx(valueClass, 'text-lg')}>{modulesCompleted ?? 0}</span>
      </div>

      {/* Desktop Actions */}
      <div
        className={clsx(
          cellBaseClass,
          'hidden col-span-1 items-center !justify-end gap-3 lg:flex lg:flex-col-reverse lg:mr-6 xl:col-span-2 xl:!flex-row'
        )}
      >
        {canConnect && onConnect && (
          <button
            type="button"
            onClick={onConnect}
            aria-label="Connect with this athlete"
            className={clsx(
              'transition-colors',
              isCurrentUser
                ? 'text-white hover:text-white'
                : 'text-text-secondary hover:text-brand-blue'
            )}
          >
            <FontAwesomeIcon icon={faMessage} className="h-5 w-5" />
          </button>
        )}
        <Link
          href={profileHref}
          aria-label="View this athlete's profile"
          className={clsx(
            'transition-colors',
            isCurrentUser
              ? 'text-white hover:text-white'
              : 'text-text-secondary hover:text-brand-blue'
          )}
        >
          <FontAwesomeIcon icon={faArrowRight} className="h-5 w-5" />
        </Link>
      </div>
    </Card>
  );
}
