import React, { use<PERSON>emo } from 'react';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import {
  faB<PERSON><PERSON><PERSON><PERSON><PERSON>,
  fa<PERSON><PERSON><PERSON><PERSON>,
  fa<PERSON><PERSON><PERSON><PERSON>,
  faTrophy,
} from '@fortawesome/pro-regular-svg-icons';
import BadgesPlaceholder from '@/components/shared/blocks/BadgesPlaceholder';
import type { BadgeCardProps } from '@/components/shared/cards/BadgeCard';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
// import BadgesSwiper from '@/components/shared/interactive/BadgesSwiper';
// import ModuleCarousel from '@/components/shared/interactive/ModuleCarousel';
// import { XFactorCourseCarousel } from '@/components/x-factor/XFactorCourseCarousel';
import { XFactorLeaderboard } from '@/components/x-factor/XFactorLeaderboard';
import { XFactorProgress } from '@/components/x-factor/XFactorProgress';
import { XFactorSearchResults } from '@/components/x-factor/XFactorSearchResults';
import { XFactorStats } from '@/components/x-factor/XFactorStats';
import { useLearningProgress } from '@/hooks/useLearningProgress';
import { useXFactorCourses } from '@/hooks/x-factor/useXFactorCourses';
import type { XFactorFilters } from '@/hooks/x-factor/useXFactorView';
import { ModuleType, type XFactorModule } from '@/services/x-factor-module.service';
import { fpoRecommendedXFactorModules } from '@/views/dashboard/DashboardView';

const BadgesSwiper = dynamic(() => import('@/components/shared/interactive/BadgesSwiper'), {
  ssr: false,
});

const LazyModuleCarousel = dynamic(() => import('@/components/shared/interactive/ModuleCarousel'), {
  ssr: false,
});

const LazyXFactorCourseCarousel = dynamic(
  () =>
    import('@/components/x-factor/XFactorCourseCarousel').then(mod => mod.XFactorCourseCarousel),
  {
    ssr: false,
  }
);

interface XFactorDashboardProps {
  filters: XFactorFilters;
  onFilterChange: (filters: Partial<XFactorFilters>) => void;
}

export function XFactorDashboard({ filters, onFilterChange }: XFactorDashboardProps) {
  const router = useRouter();
  const {
    stats,
    isLoadingStats,
    statsError,
    certificates,
    isLoadingCertificates,
    certificatesError,
    badges,
    isLoadingBadges,
    badgesError,
    investment,
    isLoadingInvestment,
    investmentError,
  } = useLearningProgress();

  // Ensure certificates is always an array using useMemo
  const certificatesArray = useMemo(() => {
    return Array.isArray(certificates) ? certificates : [];
  }, [certificates]);

  // Ensure badges is always an array using useMemo
  const badgesArray = useMemo(() => {
    return Array.isArray(badges) ? badges : [];
  }, [badges]);

  // Get search results when there's a query
  const { featured, searchResults, totalResults, isLoading } = useXFactorCourses({
    search: filters.query,
  });

  // Show search results when there's a query
  if (filters.query) {
    return (
      <XFactorSearchResults
        query={filters.query || ''}
        results={searchResults.map(course => ({
          id: Number(course.id),
          name: course.title,
          description: course.description,
          cover_image: course.coverImageUrl,
          published: true,
          course_id: Number(course.id),
          course_name: course.title,
          duration_minutes: course.totalRuntimeMinutes,
          topics: course.topics,
          has_quiz: false,
          has_exam: course.hasExam || false,
          type: ModuleType.Video,
          progress: course.progress,
          completed_at: course.isCompleted ? course.lastAccessedAt : null,
          next_attempt_available_at: null,
          content: null,
          video_url: null,
          score: course.examScore || null,
          order: null,
          video_start_time: null,
          video_end_time: null,
        }))}
        totalResults={totalResults}
        onClearSearch={() => onFilterChange({ query: undefined })}
        onBrowseClick={() => onFilterChange({ activeTab: 'browse', query: undefined })}
      />
    );
  }

  // Default investment data if undefined
  const investmentData = investment || {
    hoursSpent: 0,
    modulesCompleted: 0,
    lastActive: null,
    topics: [],
    primaryTopic: undefined,
    primaryTopicPercentage: undefined,
  };

  // Default stats data if undefined
  const statsData = stats || {
    coursesCompleted: 0,
    coursesInProgress: 0,
    certificatesEarned: 0,
    badgesEarned: 0,
    totalModulesCompleted: 0,
  };

  // Mock data (move to a data file later)
  const featuredModules = [
    {
      id: '1',
      title: 'Mental Toughness',
      subtitle: 'With Doug Baldwin',
      coverImage: '/mock/mock-cover-1.jpg',
      href: '/x-factor/module/mental-toughness',
    },
  ];

  const mockAchievements = [
    {
      title: 'Starter',
      currentCount: 15,
      targetCount: 25,
      isUnlocked: false,
    },
    // ... rest of the mock data
  ];

  const _isLoading =
    isLoading || isLoadingStats || isLoadingCertificates || isLoadingBadges || isLoadingInvestment;

  const hasError = statsError || certificatesError || badgesError || investmentError;

  return (
    <>
      {/* Content Grid */}
      <div className="grid grid-cols-1 gap-6 mb-10 md:grid-cols-6 xl:grid-cols-5">
        {/* Learning Stats */}
        <div className="col-span-1 md:col-span-3 xl:col-span-2">
          <XFactorStats
            averageScore={statsData.averageModuleScore}
            hoursSpent={statsData.hoursSpent}
            modulesCompleted={statsData.modulesCompleted}
            stateRank={statsData.stateLeaderboardRank}
            nationalRank={statsData.nationalLeaderboardRank}
            stateName={statsData.stateName}
            academicYear={statsData.academicYear}
          />
        </div>

        {/* Learning Time Distribution */}
        <div className="col-span-1 md:col-span-3">
          <XFactorProgress
            topics={investmentData.topics}
            primaryTopic={investmentData.primaryTopic}
            primaryTopicPercentage={investmentData.primaryTopicPercentage}
          />
        </div>
      </div>

      {/* Certificates Section */}
      <div className="mb-10">
        <LazyXFactorCourseCarousel courses={featured} />
      </div>

      {/* X Factor Leaderboard */}
      <XFactorLeaderboard />

      {/* Recommended Modules */}
      <LazyModuleCarousel
        title="Recommended for You"
        modules={fpoRecommendedXFactorModules}
        className="mb-10"
        showFadeGradient
      />

      {/* Badges Section */}
      <Card elevation="card" className="mb-10">
        <CardHeader title="X Factor Badges" titleIcon={faBadgeCheck} className="mb-10">
          <div className="bg-brand-red text-white text-sm/none font-bold px-3 py-0.5 rounded-full min-w-9 text-center">
            {badgesArray.length || 0}
          </div>
        </CardHeader>

        <div className="mb-0">
          {badgesArray.length === 0 ? (
            <BadgesPlaceholder />
          ) : (
            <BadgesSwiper items={badgesArray as BadgeCardProps[]} />
          )}
        </div>
      </Card>

      {/* Achievements Section - This might be badges ^^ ?? */}
      {/* <div className="mt-8">
        <h2 className="text-[15px] leading-[1] font-heavy text-text-primary mb-[32px]">
          ACHIEVEMENTS
        </h2>
        <Carousel
          items={mockAchievements.map(achievement => (
            <AchievementCard key={achievement.title} {...achievement} />
          ))}
          itemsPerPage={4}
          className="mb-8"
        />
      </div> */}
    </>
  );
}
