import { Fragment } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  faCheckCircle as faCheckCircleRegular,
  faGrid2 as faGrid2Regular,
  faHome as faHomeRegular,
} from '@fortawesome/pro-regular-svg-icons';
import {
  faCheckCircle as faCheckCircleSolid,
  faGrid2 as faGrid2Solid,
  faHome as faHomeSolid,
} from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Tab, TabList } from '@headlessui/react';
import type { XFactorFilters } from '@/hooks/x-factor/useXFactorView';

interface XFactorNavigationProps {
  /**
   * Current active tab filters - only used in local context
   */
  filters?: XFactorFilters;
  /**
   * Callback for changing tabs - only used in local context
   */
  onFilterChange?: (filters: Partial<XFactorFilters>) => void;
  /**
   * If true, component will use Next.js navigation instead of local state
   */
  usePageNavigation?: boolean;
}

export function XFactorNavigation({
  filters,
  onFilterChange,
  usePageNavigation = false,
}: XFactorNavigationProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleTabChange = (tab: XFactorFilters['activeTab']) => {
    if (usePageNavigation) {
      // Use Next.js navigation with query params
      const params = new URLSearchParams();
      // Copy existing params
      searchParams?.forEach((value, key) => {
        params.set(key, value);
      });
      params.set('tab', tab);
      router.push(`/x-factor?${params.toString()}`);
    } else if (onFilterChange) {
      // Use local state management
      onFilterChange({ activeTab: tab });
    }
  };

  // Get active tab either from filters prop or URL params
  const activeTab = usePageNavigation
    ? (searchParams?.get('tab') as XFactorFilters['activeTab']) || 'browse'
    : filters?.activeTab || 'browse';

  // If using page navigation, we need to maintain the original implementation
  if (usePageNavigation) {
    return (
      <div className="h-16 bg-[#EDEFF0] px-4 lg:px-20 flex items-center justify-center lg:justify-start">
        <div className="flex items-center space-x-3">
          <button
            className={`h-8 px-3 flex items-center space-x-2 rounded-md font-medium text-sm transition-colors ${
              activeTab === 'dashboard'
                ? 'bg-white text-[#002B5C]'
                : 'text-gray-500 hover:text-[#002B5C]'
            }`}
            onClick={() => handleTabChange('dashboard')}
          >
            <FontAwesomeIcon
              icon={activeTab === 'dashboard' ? faHomeSolid : faHomeRegular}
              className="!size-3.5"
            />
            <span>Home</span>
          </button>
          <button
            className={`h-8 px-3 flex items-center space-x-2 rounded-md font-medium text-sm transition-colors ${
              activeTab === 'browse'
                ? 'bg-white text-[#002B5C]'
                : 'text-gray-500 hover:text-[#002B5C]'
            }`}
            onClick={() => handleTabChange('browse')}
          >
            <FontAwesomeIcon
              icon={activeTab === 'browse' ? faGrid2Solid : faGrid2Regular}
              className="!size-3.5"
            />
            <span>Browse</span>
          </button>
          <button
            className={`h-8 px-3 flex items-center space-x-2 rounded-md font-medium text-sm transition-colors ${
              activeTab === 'completed'
                ? 'bg-white text-[#002B5C]'
                : 'text-gray-500 hover:text-[#002B5C]'
            }`}
            onClick={() => handleTabChange('completed')}
          >
            <FontAwesomeIcon
              icon={activeTab === 'completed' ? faCheckCircleSolid : faCheckCircleRegular}
              className="!size-3.5"
            />
            <span>Your Completed</span>
          </button>
        </div>
      </div>
    );
  }

  // Use HeadlessUI TabList for enhanced accessibility
  return (
    <div className="h-16 bg-[#EDEFF0] px-4 lg:px-20 flex items-center justify-center lg:justify-start">
      <TabList className="flex items-center space-x-3">
        <Tab as={Fragment}>
          {({ selected }) => (
            <button
              className={`h-8 px-3 flex items-center space-x-2 rounded-md text-sm transition-colors ${
                selected
                  ? 'bg-white text-[#002B5C] font-semibold'
                  : 'text-gray-500 hover:text-[#002B5C] font-medium'
              }`}
            >
              <FontAwesomeIcon
                icon={selected ? faHomeSolid : faHomeRegular}
                className="!size-3.5"
              />
              <span>Home</span>
            </button>
          )}
        </Tab>
        <Tab as={Fragment}>
          {({ selected }) => (
            <button
              className={`h-8 px-3 flex items-center space-x-2 rounded-md text-sm transition-colors ${
                selected
                  ? 'bg-white text-[#002B5C] font-semibold'
                  : 'text-gray-500 hover:text-[#002B5C] font-medium'
              }`}
            >
              <FontAwesomeIcon
                icon={selected ? faGrid2Solid : faGrid2Regular}
                className="!size-3.5"
              />
              <span>Browse</span>
            </button>
          )}
        </Tab>
        <Tab as={Fragment}>
          {({ selected }) => (
            <button
              className={`h-8 px-3 flex items-center space-x-2 rounded-md text-sm transition-colors ${
                selected
                  ? 'bg-white text-[#002B5C] font-semibold'
                  : 'text-gray-500 hover:text-[#002B5C] font-medium'
              }`}
            >
              <FontAwesomeIcon
                icon={selected ? faCheckCircleSolid : faCheckCircleRegular}
                className="!size-3.5"
              />
              <span>Your Completed</span>
            </button>
          )}
        </Tab>
      </TabList>
    </div>
  );
}
