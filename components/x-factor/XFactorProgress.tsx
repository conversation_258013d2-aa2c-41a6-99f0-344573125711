'use client';

import React, { useMemo } from 'react';
import { faPie<PERSON><PERSON>, faSpinnerThird } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { LearningInvestmentChart } from '@/components/profile/sections/LearningInvestmentChart';
import ChartPlaceholder from '@/components/shared/blocks/ChartPlaceholder';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import { TopicInvestment } from '@/services/learning-progress.service';

// For backward compatibility, maintain Category interface
interface Category {
  name: string;
  percentage: number;
  highlighted?: boolean;
}

interface XFactorProgressProps {
  // Support both categories and topics for backward compatibility
  title?: string;
  headerContent?: React.ReactNode;
  categories?: Category[];
  topics?: TopicInvestment[];
  primaryCategory?: string;
  primaryTopic?: string;
  primaryTopicPercentage?: number;
  displayAside?: boolean;
}

export function XFactorProgress({
  title = 'Learning Time',
  headerContent,
  categories,
  topics,
  primaryCategory,
  primaryTopic,
  primaryTopicPercentage,
  displayAside = true,
}: XFactorProgressProps) {
  // Convert categories to topics format if needed
  const convertedTopics = useMemo(() => {
    if (topics && topics.length > 0) {
      return topics;
    }

    if (categories && categories.length > 0) {
      return categories.map(category => ({
        name: category.name,
        percentage: category.percentage,
        minutesSpent: 0, // Default value
        modulesCompleted: 0, // Default value
      }));
    }

    return [];
  }, [categories, topics]);

  // Find primary topic/category
  const effectivePrimaryTopic = useMemo(() => {
    if (primaryTopic) return primaryTopic;
    if (primaryCategory) return primaryCategory;

    if (categories && categories.length > 0) {
      const highlighted = categories.find(c => c.highlighted);
      if (highlighted) return highlighted.name;
    }

    if (convertedTopics.length > 0) {
      return convertedTopics[0].name;
    }

    return undefined;
  }, [categories, convertedTopics, primaryCategory, primaryTopic]);

  // If there's no data, show a placeholder
  if (convertedTopics.length === 0) {
    return (
      <Card elevation="card" className="flex flex-col h-full">
        <CardHeader
          title={title}
          titleIcon={faPieChart}
          customEdit={headerContent}
          className="mb-8"
        />

        <ChartPlaceholder displayTitle displayAside={displayAside} />
      </Card>
    );
  }

  return (
    <Card elevation="card" className="flex flex-col h-full">
      <CardHeader
        title={title}
        titleIcon={faPieChart}
        customEdit={headerContent}
        className="mb-8"
      />

      <LearningInvestmentChart
        topics={convertedTopics}
        primaryTopic={effectivePrimaryTopic}
        primaryTopicPercentage={primaryTopicPercentage}
        displayAside={displayAside}
      />
    </Card>
  );
}
