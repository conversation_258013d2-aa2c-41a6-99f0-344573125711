'use client';

import { useEffect, useRef } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import axios from '@/lib/axios';
import { getCurrentUser, logout } from '@/services/auth.service';
import { useAuthStore } from '@/stores/auth.store';

export default function AuthProvider({ children }: { children: React.ReactNode }) {
  const initialized = useRef(false);
  const loginFn = useRef(useAuthStore.getState().login);
  const logoutFn = useRef(useAuthStore.getState().logout);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (initialized.current) return;
    initialized.current = true;

    const initialize = async () => {
      const user = await getCurrentUser();
      if (user) {
        loginFn.current({ user });
      }
    };

    initialize();

    // Add response interceptor for 401 handling
    const interceptor = axios.interceptors.response.use(
      response => response,
      async error => {
        if (error.response?.status === 401) {
          // Don't redirect if we're already on login-related pages
          const currentPath = pathname || '';
          const isAuthRoute = ['/login', '/register', '/forgot-password', '/reset-password'].some(
            route => currentPath.startsWith(route)
          );

          // console.log('isAuthRoute', isAuthRoute);

          if (!isAuthRoute) {
            // console.log('logging out');
            await logout();
            router.push('/login');
          }
        }
        return Promise.reject(error);
      }
    );

    // Cleanup interceptor on unmount
    return () => {
      axios.interceptors.response.eject(interceptor);
    };
  }, [router, pathname]);

  return <>{children}</>;
}
