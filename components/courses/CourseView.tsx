'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { faCheck, faLock, faUnlock } from '@fortawesome/free-solid-svg-icons';
import { faArrowRight } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ProgressCircle } from '@/components/shared/ProgressCircle';
import { ModuleCard } from '@/components/x-factor/ModuleCard';
import type { Course } from '@/types/course';
import Button from '../shared/Button';
import StatusChip from '../shared/StatusChip';

interface CourseViewProps {
  course: Course;
}

export function CourseView({ course }: CourseViewProps) {
  const [activeModuleId, setActiveModuleId] = useState<string | null>(null);

  return (
    <div className="pt-[40px] px-[80px] pb-[80px] bg-surface-secondary">
      {/* Header */}
      <div className="relative h-[400px] rounded-t-[24px] overflow-hidden">
        {/* Background Image */}
        <Image
          src="/mock/mock-cover-1.jpg"
          alt={course.title}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 to-black/40" />

        {/* Content */}
        <div className="relative h-full p-8 flex flex-col justify-center">
          <div className="flex items-center space-x-6">
            {/* Progress Circle */}
            <ProgressCircle
              progress={course.progress}
              size={120}
              primaryColor="#D50032"
              secondaryColor="#FFFFFF"
            />

            <div className="flex flex-col max-w-[380px] gap-[24px]">
              <div className="flex flex-col gap-[16px]">
                {/* Score Badge */}
                <StatusChip
                  label={`${course.averageScore}% Avg Score`}
                  variant="success"
                  className="w-fit"
                />

                {/* Title and Progress */}
                <h1 className="text-[32px] leading-[1] font-bold text-white">{course.title}</h1>
                <p className="text-white text-[16px] leading-[1]">
                  {course.modulesCompleted}/{course.totalModules} Modules Completed
                </p>
              </div>

              <div>
                <Button
                  href={`/x-factor/courses/${course.id}/${course.modules.find(module => !module.completed)?.id}`}
                  color="red"
                  size="small"
                  icon={faArrowRight}
                >
                  Continue Course
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Module List */}
      <div className="bg-white rounded-b-[24px] p-[32px]">
        <div className="space-y-4">
          {course.modules.map((module, index) => {
            // Determine if this is the next available module
            const isNextAvailable =
              !module.completed && (index === 0 || course.modules[index - 1].completed);

            return (
              <div key={module.id} className="flex items-center">
                {/* Progress Tracker */}
                <div className="flex flex-col items-center mr-4 relative">
                  {/* Continuous Vertical Line */}
                  {index !== course.modules.length - 1 && (
                    <div
                      className={`absolute top-1/2 w-[1px] h-[104px] ${
                        module.completed ? 'bg-brand-red' : 'bg-stroke-weak'
                      }`}
                    />
                  )}

                  {/* Circle Indicator */}
                  <div
                    className={`w-[40px] h-[40px] rounded-full border-[1px] flex items-center justify-center z-10 
                    ${
                      module.id === activeModuleId
                        ? 'border-brand-red bg-red-50'
                        : module.completed
                          ? 'border-brand-red bg-brand-red'
                          : isNextAvailable
                            ? 'border-brand-red bg-surface-tertiary'
                            : 'border-surface-tertiary bg-surface-tertiary'
                    }`}
                  >
                    {module.completed && (
                      <FontAwesomeIcon icon={faCheck} className="w-4 h-4 text-white" />
                    )}
                    {!module.completed && !module.locked && module.id !== activeModuleId && (
                      <FontAwesomeIcon
                        icon={faUnlock}
                        className={`w-4 h-4 ${isNextAvailable ? 'text-brand-red' : 'text-gray-400'}`}
                      />
                    )}
                    {module.locked && (
                      <FontAwesomeIcon icon={faLock} className="w-4 h-4 text-text-secondary" />
                    )}
                    {module.id === activeModuleId && !module.completed && (
                      <div className="w-3 h-3 rounded-full bg-brand-red border-brand-red" />
                    )}
                  </div>
                </div>

                {/* Module Card */}
                <div className="flex-1">
                  <Link href={`/x-factor/courses/${course.id}/${module.id}`}>
                    <ModuleCard
                      module={module}
                      isActive={module.id === activeModuleId}
                      onClick={() => !module.locked && setActiveModuleId(module.id)}
                    />
                  </Link>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
