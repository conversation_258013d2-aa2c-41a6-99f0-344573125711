'use client';

import React from 'react';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faBriefcase } from '@fortawesome/pro-regular-svg-icons';
import {
  CardWorkExperience,
  WorkExperienceItem,
} from '@/components/profile/shared/CardWorkExperience';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';

// Interface for the API work experience data
export interface ApiWorkExperienceItem {
  id: string | number;
  title: string;
  company?: string;
  date_range: string;
  description: string;
}

interface PublicWorkExperienceProps {
  // Accept either API format or any format with proper type checking
  workExperiences?: ApiWorkExperienceItem[] | any[];
  experiences?: ApiWorkExperienceItem[] | any[]; // Alias for workExperiences for backward compatibility
  isLoading?: boolean;
  cardHeaderTitle?: string;
  cardHeaderViewOnlyTitle?: string;
  cardHeaderIcon?: IconDefinition;
}

export function PublicWorkExperience({
  workExperiences = [],
  experiences = [],
  isLoading = false,
  cardHeaderTitle = 'Work Experience',
  cardHeaderViewOnlyTitle = 'Work Experience',
  cardHeaderIcon = faBriefcase,
}: PublicWorkExperienceProps) {
  // Use workExperiences if provided, otherwise fall back to experiences
  const experiencesToUse = workExperiences.length > 0 ? workExperiences : experiences;

  // Check for shape of data to determine if it needs transformation
  const isApiFormat =
    experiencesToUse.length > 0 &&
    'title' in experiencesToUse[0] &&
    'date_range' in experiencesToUse[0];

  // Transform API experiences to match the WorkExperienceItem interface if needed
  const formattedExperiences: WorkExperienceItem[] =
    experiencesToUse.length > 0
      ? isApiFormat
        ? (experiencesToUse as ApiWorkExperienceItem[]).map(exp => ({
            id: exp.id,
            name:
              exp.company && exp.company !== 'N/A' ? `${exp.title} at ${exp.company}` : exp.title,
            date: exp.date_range,
            description: exp.description,
          }))
        : experiencesToUse.map(exp => ({
            id: exp.id || Math.random().toString(),
            name: exp.name || exp.title || 'Unknown',
            date: exp.date || exp.date_range || '',
            description: exp.description || '',
          }))
      : [];

  return (
    <Card>
      <CardHeader
        title={cardHeaderTitle}
        titleIcon={cardHeaderIcon}
        className="mb-8"
        isViewOnly={true}
      />

      <CardWorkExperience
        workExperiences={formattedExperiences}
        isLoading={isLoading}
        emptyComponent={
          <p className="text-text-secondary text-sm italic">No work experience listed.</p>
        }
      />
    </Card>
  );
}
