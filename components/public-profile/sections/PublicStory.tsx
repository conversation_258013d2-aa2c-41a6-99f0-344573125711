'use client';

import React from 'react';
import { faFeatherAlt } from '@fortawesome/pro-regular-svg-icons';
import Richtext from '@/components/shared/blocks/Richtext';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import type { PublicStory } from '@/services/public-profile.service';

interface PublicStoryProps {
  cardHeaderTitle?: string;
  story: PublicStory | null;
}

export function PublicStory({ cardHeaderTitle = 'Story', story }: PublicStoryProps) {
  return (
    <Card>
      <CardHeader title={cardHeaderTitle} titleIcon={faFeatherAlt} className="mb-8" isViewOnly />

      {!story?.content || story?.content === '' ? (
        <span className="block text-text-secondary text-sm italic">
          Check back to see their story.
        </span>
      ) : (
        <Richtext content={story?.content} className="prose-sm text-text-secondary" />
      )}
    </Card>
  );
}
