'use client';

import React from 'react';
import { faSchool } from '@fortawesome/pro-regular-svg-icons';
import { CardInvolvment, InvolvementItem } from '@/components/profile/shared/CardInvolvment';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';

interface PublicInvolvementProps {
  involvements: InvolvementItem[];
}

export function PublicInvolvement({ involvements = [] }: PublicInvolvementProps) {
  return (
    <Card>
      <CardHeader
        title="School/Community Involvement"
        titleIcon={faSchool}
        className="mb-8"
        isViewOnly
      />

      <CardInvolvment
        involvements={involvements}
        emptyComponent={
          <p className="text-text-secondary text-sm italic">No community involvement listed.</p>
        }
      />
    </Card>
  );
}
