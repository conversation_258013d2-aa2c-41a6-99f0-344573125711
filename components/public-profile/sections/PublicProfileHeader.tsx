'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { faArrowLeft, faMessage, faPaperPlane } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Tab, TabList } from '@headlessui/react';
import clsx from 'clsx';
import ShareModal from '@/components/profile/modals/ShareModal';
// import { ProfileHeaderCarousel } from '@/components/profile/shared/ProfileHeaderCarousel';
import { ProfileMenuDropdown } from '@/components/profile/shared/ProfileMenuDropdown';
import Avatar from '@/components/shared/Avatar';
import Button from '@/components/shared/Button';
import { useAuth } from '@/hooks/useAuth';
import type { ProfilePhoto } from '@/services/positive-athlete-profile.service';
import { ProfileType, ProfileTypes, useAuthStore } from '@/stores/auth.store';
import { useModalStore } from '@/stores/modal.store';
import { getTabListItems } from '@/utils/profile-utils';

const LazyProfileHeaderCarousel = dynamic(
  () =>
    import('@/components/profile/shared/ProfileHeaderCarousel').then(
      mod => mod.ProfileHeaderCarousel
    ),
  {
    ssr: false,
  }
);

interface PublicProfileHeaderProps {
  profileId: number;
  firstName: string;
  lastName: string;
  avatarUrl?: string;
  photos?: ProfilePhoto[];
  graduationYear?: string;
  isLoading?: boolean;
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
  profileType?: ProfileType;
  showMessageButton?: boolean;
  showShareButton?: boolean;
  isParentProfile?: boolean;
  onMessageClick?: () => void;
  hasAchievements?: boolean;
}

export function PublicProfileHeader({
  profileId,
  firstName,
  lastName,
  avatarUrl,
  photos = [],
  graduationYear,
  isLoading = false,
  activeTab = 'about',
  onTabChange,
  profileType,
  showMessageButton = false,
  showShareButton = true,
  onMessageClick,
  isParentProfile = false,
  hasAchievements = false,
}: PublicProfileHeaderProps) {
  const { open, close } = useModalStore();
  const { isLoggedIn } = useAuth();
  const router = useRouter();
  const defaultPhoto: ProfilePhoto = {
    id: 'default',
    url: '/images/profile-banner.png',
    thumbnail_url: '/images/profile-banner.png',
    width: 1920,
    height: 1080,
    order: 0,
    focal_point: { x: 0.5, y: 0.5 },
  };
  const hasPhotos = photos.length > 0;

  const handleOpenShareModal = () => {
    open(<ShareModal profileId={profileId} onClose={close} />, '2xl');
  };

  // Create a safe handler for tab changes that works even if onTabChange is undefined
  const handleTabChange = (tabId: string) => {
    if (onTabChange) {
      onTabChange(tabId);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  // Only get tab list items if we have a valid profile type
  const profileTabsList = profileType ? getTabListItems(profileType) : [];

  const showSecondaryNavigationBar =
    profileType === ProfileTypes.ATHLETICS_DIRECTOR || profileType === ProfileTypes.SPONSOR;

  if (isLoading) {
    return (
      <div className="relative mb-8 pt-10">
        <div className="pa-container relative mb-10">
          <div className="w-full h-[280px] rounded-lg bg-neutral-100 animate-pulse" />
        </div>
      </div>
    );
  }

  return (
    <div className="relative mb-8 pt-10">
      <div className="pa-container-wide relative mb-10">
        {/* Mobile Share*/}
        {!showSecondaryNavigationBar && (
          <div className="xl:hidden flex w-full justify-end items-center gap-3 mb-4">
            {showShareButton && isLoggedIn && (
              <Button
                color="blue"
                size="small"
                icon={faPaperPlane}
                iconPosition="right"
                onClick={handleOpenShareModal}
              >
                Share
              </Button>
            )}

            {/* Mobile Message Button */}
            {showMessageButton && onMessageClick && (
              <Button
                color="blue"
                size="small"
                icon={faMessage}
                iconPosition="right"
                onClick={onMessageClick}
              >
                Message
              </Button>
            )}
          </div>
        )}

        {/* Photo Carousel or Default Banner */}
        <div className="relative z-[1]">
          {hasPhotos ? (
            <LazyProfileHeaderCarousel photos={photos} className="rounded-lg overflow-hidden" />
          ) : (
            <div className="relative w-full h-[280px] rounded-lg overflow-hidden">
              <Image
                src={defaultPhoto.url}
                alt="Profile banner"
                fill
                unoptimized
                sizes="100vw"
                priority
                className="object-cover"
              />
            </div>
          )}
        </div>

        {/* Mobile Avatar */}
        <div className="flex items-center relative justify-center -mt-20 z-40 pointer-events-none lg:hidden">
          <Avatar
            src={avatarUrl}
            firstName={firstName}
            lastName={lastName}
            size="xl"
            hasAchievements={hasAchievements}
          />
        </div>

        <div className="items-end justify-between hidden lg:flex md:ml-4 2xl:ml-16 gap-2 lg:-mt-20 relative z-10">
          {/* Desktop Avatar */}
          <div className="hidden pointer-events-none lg:block">
            <Avatar
              src={avatarUrl}
              firstName={firstName}
              lastName={lastName}
              size="xl"
              hasAchievements={hasAchievements}
            />
          </div>

          {/* Desktop Navbar - Tab Buttons */}
          {!showSecondaryNavigationBar && (
            <div className="hidden lg:flex items-center justify-end lg:mb-8 gap-3">
              <div className="flex gap-3">
                {/* Note: TabList is automatically connected to the parent TabGroup */}
                <TabList className="flex gap-3">
                  {profileTabsList.map(tab => (
                    <Tab
                      key={tab.id}
                      className={({ selected }) =>
                        clsx(
                          'py-2.5 px-2 relative text-sm flex items-center gap-2 text-brand-blue outline-none',
                          selected ? 'font-bold' : 'font-normal'
                        )
                      }
                    >
                      {({ selected }) => (
                        <>
                          <FontAwesomeIcon
                            icon={selected ? tab.iconActive : tab.iconInactive}
                            className="size-4 text-brand-blue"
                          />
                          {tab.label}
                        </>
                      )}
                    </Tab>
                  ))}
                </TabList>
              </div>

              {/* Desktop Share and Message Buttons */}
              <div className="hidden xl:flex items-center gap-3">
                {showMessageButton && onMessageClick && (
                  <Button
                    color="blue"
                    size="small"
                    icon={faMessage}
                    iconPosition="right"
                    onClick={onMessageClick}
                  >
                    Message
                  </Button>
                )}
                {showShareButton && isLoggedIn && (
                  <Button
                    color="blue"
                    size="small"
                    icon={faPaperPlane}
                    iconPosition="right"
                    onClick={handleOpenShareModal}
                  >
                    Share
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Profile Info */}
      <div className="pa-container text-center lg:text-left">
        {isLoggedIn && !isParentProfile && (
          <Button
            variant="text"
            color="blue"
            icon={faArrowLeft}
            iconPosition="left"
            className="mb-2"
            onClick={handleGoBack}
          >
            Go Back
          </Button>
        )}

        <h1 className="text-4xl font-bold font-oxanium leading-tight text-text-primary">
          {firstName} {lastName}
        </h1>

        <span className="block mt-1 pa-eyebrow text-brand-red">
          {profileType?.replace(/_/g, ' ')}
        </span>

        {graduationYear && (
          <p className="pa-eyebrow text-brand-blue mt-1">Class of {graduationYear}</p>
        )}
      </div>

      {/* Mobile Nav - Tab Buttons */}
      {!showSecondaryNavigationBar && (
        <div className="lg:hidden w-full max-w-56 mx-auto px-4 mt-4">
          <ProfileMenuDropdown
            items={profileTabsList}
            value={activeTab}
            onChange={handleTabChange}
          />
        </div>
      )}
    </div>
  );
}
