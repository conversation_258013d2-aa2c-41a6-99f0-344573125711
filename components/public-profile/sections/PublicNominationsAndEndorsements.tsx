import React, { useMemo, useState } from 'react';
import { faChevronDown, faMessageCheck, faThumbsUp } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { EndorsementModal } from '@/components/profile/modals/EndorsementModal';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import EndorsementCard from '@/components/shared/cards/EndorsementCard';
import NominationCard from '@/components/shared/cards/NominationCard';
import { Spinner } from '@/components/shared/Spinner';
import { useEndorsements } from '@/hooks/useEndorsements';
import { useNominations } from '@/hooks/useNominations';
import { useModalStore } from '@/stores/modal.store';

interface PublicNominationsAndEndorsementsProps {
  userId?: number;
  displayEndorsementButton?: boolean;
}

export function PublicNominationsAndEndorsements({
  userId,
  displayEndorsementButton = false,
}: PublicNominationsAndEndorsementsProps) {
  const { open } = useModalStore();
  const [showAllNominations, setShowAllNominations] = useState(false);

  const { endorsements, isLoadingEndorsements, endorsementsError, endorsementStats } =
    useEndorsements(userId);

  const { nominations, isLoadingNominations, nominationsError, nominationStats } =
    useNominations(userId);

  // Log data to console for debugging
  React.useEffect(() => {
    // Check for potential issues with endorser data
    if (endorsements && endorsements.length > 0) {
      endorsements.forEach((category, i) => {
        if (category.endorsers && category.endorsers.length > 0) {
          category.endorsers.forEach((endorser, j) => {
            if (!endorser.name && !endorser.endorserName) {
              console.warn(
                `Warning: Endorser ${j + 1} in category "${category.name}" has no name or endorserName:`,
                endorser
              );
            }
          });
        } else {
          console.warn(
            `Warning: Category "${category.name}" has no endorsers or endorsers is not an array`
          );
        }
      });
    }
  }, [endorsements, endorsementStats, nominations, nominationStats]);

  const isLoading = isLoadingEndorsements || isLoadingNominations;
  const hasError = endorsementsError || nominationsError;

  const toggleShowAllNominations = () => {
    setShowAllNominations(!showAllNominations);
  };

  // Display only the first 2 nominations unless showAllNominations is true
  const visibleNominations =
    nominations && nominations.length > 0
      ? showAllNominations
        ? nominations
        : nominations.slice(0, 2)
      : [];

  // Calculate how many more nominations are hidden
  const hiddenNominationsCount = nominations && nominations.length > 2 ? nominations.length - 2 : 0;

  // Sort endorsements by count in descending order
  const sortedEndorsements = useMemo(() => {
    if (!endorsements) return [];
    return [...endorsements].sort((a, b) => b.count - a.count);
  }, [endorsements]);

  const handleOpenEndorsementModal = () => {
    open(<EndorsementModal profileUserId={Number(userId)} />, 'lg');
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-center items-center h-40">
          <Spinner />
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center text-red-500">
          <p>There was an error loading your nominations and endorsements data.</p>
          <p className="text-sm mt-2">Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Card elevation="card" className="mb-10">
        <CardHeader title="Nominations" titleIcon={faMessageCheck} className="mb-8">
          <span className="bbg-brand-red text-white text-sm/none font-bold px-3 py-0.5 rounded-full min-w-9 text-center">
            {nominationStats?.totalNominations || 0}
          </span>
        </CardHeader>

        {/* Nominations */}
        <div className="space-y-6">
          {visibleNominations.length > 0 ? (
            visibleNominations.map(nomination => (
              <NominationCard
                key={nomination.id}
                nominatorName={nomination.nominatorName}
                nominatorRole={nomination.nominatorRole}
                date={nomination.date}
                content={nomination.content}
                sport={nomination.sport}
                schoolName={nomination.schoolName}
              />
            ))
          ) : (
            <p className="text-gray-500 italic">No nominations found.</p>
          )}
        </div>

        {hiddenNominationsCount > 0 && (
          <div className="mt-6 text-left">
            <button
              type="button"
              onClick={toggleShowAllNominations}
              className="text-brand-blue font-medium flex items-center"
              aria-label={
                showAllNominations ? 'Show Less' : `See ${hiddenNominationsCount} More Nominations`
              }
            >
              {showAllNominations ? 'Show Less' : `See ${hiddenNominationsCount} More Nominations`}
              <FontAwesomeIcon
                icon={faChevronDown}
                className={`ml-2 h-4 w-4 transition-transform ${showAllNominations ? 'rotate-180' : ''}`}
              />
            </button>
          </div>
        )}
      </Card>

      <Card elevation="card" className="mb-10">
        <CardHeader
          title="Endorsements"
          titleIcon={faThumbsUp}
          customEdit={
            displayEndorsementButton && (
              <>
                {/* TODO: Add a conditional statement here to show only if not user or if coach/admin */}
                <Button
                  size="small"
                  icon={faThumbsUp}
                  iconPosition="left"
                  onClick={handleOpenEndorsementModal}
                >
                  Endorse This Athlete
                </Button>
              </>
            )
          }
          className="mb-8"
        >
          <span className="bg-brand-red text-white text-sm/none font-bold px-3 py-0.5 rounded-full min-w-9 text-center">
            {endorsementStats?.totalEndorsements || 0}
          </span>
        </CardHeader>

        {/* Endorsements */}
        <div className="space-y-4">
          {sortedEndorsements.length > 0 ? (
            sortedEndorsements.map(endorsement => (
              <EndorsementCard
                key={endorsement.id}
                id={endorsement.id}
                name={endorsement.name}
                count={endorsement.count}
                endorsers={endorsement.endorsers || []}
              />
            ))
          ) : (
            <p className="text-gray-500 italic">No endorsements found.</p>
          )}
        </div>
      </Card>
    </>
  );
}
