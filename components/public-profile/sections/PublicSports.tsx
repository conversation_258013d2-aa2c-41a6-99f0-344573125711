'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import type { SportItem } from '@/components/profile/shared/CardSports';
import Card from '@/components/shared/cards/Card';
import {
  SportsBadmintonCard,
  SportsBaseballCard,
  SportsBasketballCard,
  SportsBowlingCard,
  SportsCricketCard,
  SportsCrossCountryCard,
  SportsEquestrianCard,
  SportsESportsCard,
  SportsFieldHockeyCard,
  SportsFlagFootballCard,
  SportsFootballCard,
  SportsGolfCard,
  SportsHandballCard,
  SportsIceHockeyCard,
  SportsLacrosseCard,
  SportsOtherCard,
  SportsRugbyCard,
  SportsShootingCard,
  SportsSkiingSnowboardingCard,
  SportsSoccerCard,
  SportsSoftballCard,
  SportsTableTennisCard,
  SportsTennisCard,
  SportsTrackAndFieldCard,
  SportsVolleyballCard,
  SportsWaterPoloCard,
  SportsWeightliftingCard,
} from '@/components/shared/spline';
import type { PublicSport } from '@/services/public-profile.service';
import { SportMapping, SportName } from '@/types/sports';

const LazyCardSports = dynamic(
  () => import('@/components/profile/shared/CardSports').then(mod => mod.CardSports),
  {
    ssr: false,
  }
);

// Define the type for our sport components
type SportComponent = React.ComponentType<{ className?: string }>;

// Direct mapping of sport names to their corresponding components
const sportComponentMap: SportMapping<SportComponent> = {
  // Core sports with dedicated components from SportName enum
  [SportName.SOCCER]: SportsSoccerCard,
  [SportName.FOOTBALL]: SportsFootballCard,
  [SportName.BASEBALL]: SportsBaseballCard,
  [SportName.ICE_HOCKEY]: SportsIceHockeyCard,
  [SportName.BASKETBALL]: SportsBasketballCard,
  [SportName.TRACK_AND_FIELD]: SportsTrackAndFieldCard,
  'Track and Field': SportsTrackAndFieldCard, // Legacy support
  [SportName.CROSS_COUNTRY]: SportsCrossCountryCard,
  [SportName.GOLF]: SportsGolfCard,
  [SportName.WEIGHTLIFTING]: SportsWeightliftingCard,
  [SportName.TABLE_TENNIS]: SportsTableTennisCard,
  [SportName.ESPORTS]: SportsESportsCard,
  [SportName.TENNIS]: SportsTennisCard,
  [SportName.EQUESTRIAN]: SportsEquestrianCard,
  [SportName.BADMINTON]: SportsBadmintonCard,
  [SportName.FIELD_HOCKEY]: SportsFieldHockeyCard,
  [SportName.SHOOTING]: SportsShootingCard,
  [SportName.SOFTBALL]: SportsSoftballCard,
  [SportName.FLAG_FOOTBALL]: SportsFlagFootballCard,
  [SportName.HANDBALL]: SportsHandballCard,
  [SportName.SKIING_SNOWBOARDING]: SportsSkiingSnowboardingCard,
  [SportName.SKIING]: SportsSkiingSnowboardingCard,
  [SportName.SNOWBOARDING]: SportsSkiingSnowboardingCard,
  [SportName.BOWLING]: SportsBowlingCard,
  [SportName.VOLLEYBALL]: SportsVolleyballCard,
  [SportName.WATER_POLO]: SportsWaterPoloCard,
  [SportName.CRICKET]: SportsCricketCard,
  [SportName.RUGBY]: SportsRugbyCard,
  [SportName.LACROSSE]: SportsLacrosseCard,

  // Map remaining sports from the enum to existing or default components
  [SportName.WRESTLING]: SportsWeightliftingCard, // Use weightlifting for wrestling
  [SportName.SWIMMING_AND_DIVING]: SportsWaterPoloCard, // Use water polo for swimming
  [SportName.CHEERLEADING]: SportsOtherCard, // Default for cheerleading
  [SportName.DANCE]: SportsOtherCard, // Default for dance
  [SportName.GYMNASTICS]: SportsOtherCard, // Default for gymnastics
  [SportName.ROWING]: SportsOtherCard, // Default for rowing
  [SportName.ULTIMATE_FRISBEE]: SportsOtherCard, // Default for ultimate frisbee
  [SportName.MOUNTAIN_BIKING]: SportsOtherCard, // Default for mountain biking
  [SportName.CYCLING]: SportsOtherCard, // Default for cycling
  [SportName.ARCHERY]: SportsShootingCard, // Use shooting for archery
  [SportName.FENCING]: SportsOtherCard, // Default for fencing
  'E-Sports': SportsESportsCard, // Alternate spelling
};

interface PublicSportsProps {
  sports: PublicSport[];
}

export function PublicSports({ sports = [] }: PublicSportsProps) {
  // Map the PublicSport objects to SportItem objects
  const mappedSports: SportItem[] = sports.map(sport => ({
    id: sport.id?.toString() || undefined,
    name: sport.name,
    uniqueId: `public-sport-${sport.id || Math.random()}`,
  }));

  return (
    <Card noPadding={!!mappedSports.length}>
      <LazyCardSports
        sports={mappedSports}
        sportComponentMap={sportComponentMap}
        emptyMessage="No sports listed"
        isViewOnly
      />
    </Card>
  );
}
