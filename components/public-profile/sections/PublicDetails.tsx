'use client';

import React from 'react';
import { faUserCircle } from '@fortawesome/pro-regular-svg-icons';
import { CardDetails } from '@/components/profile/shared/CardDetails';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import type {
  PublicCareerInterest,
  PublicDetails as PublicDetailsType,
  PublicProfile,
} from '@/services/public-profile.service';
import { ProfileTypes } from '@/stores/auth.store';

interface PublicDetailsProps {
  details?: PublicDetailsType;
  profile?: PublicProfile;
  careerInterests: PublicCareerInterest[];
}

export function PublicDetails({ details, profile, careerInterests }: PublicDetailsProps) {
  console.log('details', details);
  console.log('profile', profile);

  const profileType = profile?.profile_type;

  const isPositiveCoach = profileType === ProfileTypes.POSITIVE_COACH;
  const isAthleticsDirector = profileType === ProfileTypes.ATHLETICS_DIRECTOR;

  const showInterests = !isAthleticsDirector && !isPositiveCoach;

  const countyLabel = details?.county ?? details?.county_name ?? null;

  const positiveAthleteDetails = [
    {
      label: 'County & State',
      value:
        details?.state || countyLabel
          ? [countyLabel, details?.state].filter(Boolean).join(', ')
          : null,
    },
    {
      label: 'High School',
      value: details?.school_name,
    },
    {
      label: 'Current GPA',
      value: details?.gpa,
      formatter: (value: number) => value.toFixed(1),
    },
    {
      label: 'Current Class Rank',
      value: details?.class_rank,
    },
    {
      label: 'Gender',
      value: details?.gender,
      formatter: (value: string) => value.charAt(0).toUpperCase() + value.slice(1),
    },
  ];

  const positiveCoachDetails = [
    {
      label: 'County & State',
      value:
        details?.state || countyLabel
          ? [countyLabel, details?.state].filter(Boolean).join(', ')
          : null,
    },
    {
      label: 'High School',
      value: profile?.school_name ?? details?.school_name ?? null,
    },
  ];

  const collegeAthleteDetails = [
    {
      label: 'State',
      value: details?.state ? details?.state : null,
    },
    {
      label: 'College',
      value: details?.college ? details?.college : null,
    },
    {
      label: 'Current GPA',
      value: details?.gpa,
      formatter: (value: number) => value.toFixed(1),
    },
    {
      label: 'Gender',
      value: details?.gender,
      formatter: (value: string) => value.charAt(0).toUpperCase() + value.slice(1),
    },
    {
      label: 'Height',
      value: details?.height_in_inches ? details?.height_in_inches : null,
      formatter: (value: number) => {
        const feet = Math.floor(value / 12);
        const inches = value % 12;
        return `${feet}' ${inches}''`;
      },
    },
    {
      label: 'Weight',
      value: details?.weight ? details?.weight : null,
    },
  ];

  const professionalDetails = [
    {
      label: 'State',
      value: details?.state ? details?.state : null,
    },
    {
      label: 'Employer',
      value: details?.employer ? details?.employer : null,
    },
  ];

  const athleticsDirectorDetails = [
    {
      label: 'County & State',
      value:
        details?.state || countyLabel
          ? [countyLabel, details?.state].filter(Boolean).join(', ')
          : null,
    },
    {
      label: 'High School',
      value: details?.school_name,
    },
  ];

  // Prepare detail items for the CardDetails component
  const detailItems = (() => {
    switch (profileType) {
      case ProfileTypes.POSITIVE_ATHLETE:
        return positiveAthleteDetails;
      case ProfileTypes.POSITIVE_COACH:
        return positiveCoachDetails;
      case ProfileTypes.COLLEGE_ATHLETE:
        return collegeAthleteDetails;
      case ProfileTypes.PROFESSIONAL:
        return professionalDetails;
      case ProfileTypes.ATHLETICS_DIRECTOR:
        return athleticsDirectorDetails;
      default:
        return positiveAthleteDetails;
    }
  })();

  // Prepare social links
  const socialLinks = profile
    ? {
        instagram: profile.instagram || undefined,
        facebook: profile.facebook || undefined,
        twitter: profile.twitter || undefined,
        hudl: profile.hudl || undefined,
        custom_link: profile.custom_link || undefined,
        linkedin: profile.linkedin || undefined,
      }
    : undefined;

  // Transform career interests to match the expected format
  const interests = careerInterests?.map(interest => ({
    id: interest.id || 0, // Ensure we always have a valid id
    name: interest.name,
    // We don't have icon in the public data, but the component expects it
    icon: undefined,
  }));

  return (
    <Card>
      <CardHeader title="Details" titleIcon={faUserCircle} className="mb-8" isViewOnly />

      <CardDetails
        detailItems={detailItems}
        socialLinks={socialLinks}
        interests={showInterests ? interests : undefined}
      />
    </Card>
  );
}
