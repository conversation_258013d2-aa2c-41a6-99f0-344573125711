import React from 'react';
import { faTrophy } from '@fortawesome/pro-regular-svg-icons';
import { AwardsAndScholarships } from '@/components/profile/shared/AwardsAndScholarships';
import { PublicDetails } from '@/components/public-profile/sections/PublicDetails';
import { PublicInvolvement } from '@/components/public-profile/sections/PublicInvolvement';
import { PublicSports } from '@/components/public-profile/sections/PublicSports';
import { PublicStory } from '@/components/public-profile/sections/PublicStory';
import { PublicWorkExperience as PublicSchoolSuccesses } from '@/components/public-profile/sections/PublicWorkExperience';
import { AboutProps } from '@/components/public-profile/types';

export function ADAbout({
  details,
  profile,
  sports,
  story,
  involvements,
  workExperiences,
  careerInterests,
  achievements,
  isLoadingAchievements,
  achievementsError,
}: AboutProps & {
  achievements?: { awards: any[]; scholarships: any[] };
  isLoadingAchievements?: boolean;
  achievementsError?: Error | null;
}) {
  return (
    <div className="pa-profile-grid">
      {/* Left Column */}
      <div className="pa-profile-grid-left">
        <PublicDetails details={details} profile={profile} careerInterests={careerInterests} />
        {/* <PublicSports sports={sports} /> */}

        <AwardsAndScholarships
          awards={achievements?.awards}
          scholarships={achievements?.scholarships}
          isLoading={isLoadingAchievements}
          error={achievementsError}
        />
      </div>

      {/* Main Content - Center and Right */}
      <div className="pa-profile-grid-right">
        {story ? (
          <PublicStory cardHeaderTitle="Bio" story={story} />
        ) : (
          <p>Check back to see their bio.</p>
        )}

        {/* School Successes */}
        <PublicSchoolSuccesses
          cardHeaderIcon={faTrophy}
          cardHeaderTitle="School Successes"
          experiences={workExperiences || []}
        />

        {/* Public Involvement */}
        <PublicInvolvement involvements={involvements || []} />
      </div>
    </div>
  );
}
