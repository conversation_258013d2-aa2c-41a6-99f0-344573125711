import React from 'react';
import { AwardsAndScholarships } from '@/components/profile/shared/AwardsAndScholarships';
import { PublicDetails } from '@/components/public-profile/sections/PublicDetails';
import { PublicInvolvement } from '@/components/public-profile/sections/PublicInvolvement';
import { PublicSports } from '@/components/public-profile/sections/PublicSports';
import { PublicStory } from '@/components/public-profile/sections/PublicStory';
import { PublicWorkExperience } from '@/components/public-profile/sections/PublicWorkExperience';
import { AboutProps } from '@/components/public-profile/types';

export function PositiveAthleteAbout({
  details,
  profile,
  sports,
  story,
  involvements,
  workExperiences,
  careerInterests,
  achievements,
  isLoadingAchievements,
  achievementsError,
}: AboutProps & {
  achievements?: { awards: any[]; scholarships: any[] };
  isLoadingAchievements?: boolean;
  achievementsError?: Error | null;
}) {
  return (
    <div className="pa-profile-grid">
      {/* Left Column */}
      <div className="pa-profile-grid-left">
        <PublicDetails details={details} profile={profile} careerInterests={careerInterests} />
        <PublicSports sports={sports} />

        <AwardsAndScholarships
          awards={achievements?.awards}
          scholarships={achievements?.scholarships}
          isLoading={isLoadingAchievements}
          error={achievementsError}
        />
      </div>

      {/* Main Content - Center and Right */}
      <div className="pa-profile-grid-right">
        {story ? <PublicStory story={story} /> : <p>Check back to see their story.</p>}

        {/* Public Involvement */}
        <PublicInvolvement involvements={involvements || []} />

        {/* Public Work experience */}
        <PublicWorkExperience experiences={workExperiences || []} />
      </div>
    </div>
  );
}
