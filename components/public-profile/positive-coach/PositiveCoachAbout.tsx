import React from 'react';
import { faTrophy } from '@fortawesome/pro-regular-svg-icons';
import { PublicDetails } from '@/components/public-profile/sections/PublicDetails';
import { PublicInvolvement } from '@/components/public-profile/sections/PublicInvolvement';
import { PublicSports } from '@/components/public-profile/sections/PublicSports';
import { PublicStory } from '@/components/public-profile/sections/PublicStory';
import { PublicWorkExperience as PublicTeamSuccesses } from '@/components/public-profile/sections/PublicWorkExperience';
import { AboutProps } from '@/components/public-profile/types';

export function PositiveCoachAbout({
  details,
  profile,
  sports,
  story,
  involvements,
  workExperiences,
  careerInterests,
}: AboutProps) {
  return (
    <div className="pa-profile-grid">
      {/* Left Column */}
      <div className="pa-profile-grid-left">
        <PublicDetails details={details} profile={profile} careerInterests={careerInterests} />
        <PublicSports sports={sports} />
      </div>

      {/* Main Content - Center and Right */}
      <div className="pa-profile-grid-right">
        {story ? <PublicStory story={story} /> : <p>Check back to see their story.</p>}

        <PublicTeamSuccesses
          cardHeaderTitle="Team Successes"
          cardHeaderViewOnlyTitle="Team Successes"
          cardHeaderIcon={faTrophy}
          experiences={workExperiences || []}
        />

        <PublicInvolvement involvements={involvements || []} />
      </div>
    </div>
  );
}
