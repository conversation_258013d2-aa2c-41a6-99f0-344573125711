import {
  PublicCareerInterest,
  PublicDetails,
  PublicInvolvement,
  PublicProfile,
  PublicSport,
  PublicStory,
  PublicWorkExperience,
} from '@/services/public-profile.service';

/**
 * Common props interface for About components like PositiveAthleteAbout and PositiveCoachAbout
 */
export interface AboutProps {
  details: PublicDetails | undefined;
  profile: PublicProfile | undefined;
  sports: PublicSport[];
  story: PublicStory | undefined;
  involvements: PublicInvolvement[];
  workExperiences: PublicWorkExperience[];
  careerInterests: PublicCareerInterest[];
}
