import React from 'react';
import Image from 'next/image';
import { faBuilding, faEnvelope, faGlobe } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Richtext from '@/components/shared/blocks/Richtext';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import Tag from '@/components/shared/Tag';
import { useSponsorOrganization } from '@/hooks/useSponsorOrganization';
import type { Organization } from '@/services/sponsorOrganization.service';

interface OrganizationCardProps {
  sponsorId?: number;
  organizationId?: number;
  organizationData?: Partial<Organization>;
  children?: React.ReactNode;
}

export default function OrganizationCard({
  sponsorId,
  organizationId,
  organizationData,
  children,
}: OrganizationCardProps) {
  // If organizationData is provided directly, use that
  // Otherwise fetch organization data using the provided ID
  const { getSpecificOrganization, getOrganization } = useSponsorOrganization();

  const { data: fetchedOrganization, isLoading } = organizationId
    ? getSpecificOrganization(organizationId)
    : getOrganization();

  // Use provided data or fetched data
  const organization = organizationData || fetchedOrganization;

  if (isLoading) {
    return (
      <Card>
        <CardHeader title="About" titleIcon={faBuilding} className="mb-8" />
        <div className="animate-pulse">
          <div className="h-24 w-24 bg-gray-200 mx-auto mb-4 rounded"></div>
          <div className="h-6 bg-gray-200 w-48 mx-auto mb-4 rounded"></div>
          <div className="h-16 bg-gray-200 mb-4 rounded"></div>
        </div>
      </Card>
    );
  }

  if (!organization) {
    return (
      <Card>
        <CardHeader title="About" titleIcon={faBuilding} className="mb-8" />
        <p className="text-text-secondary text-center">Organization information not available</p>
      </Card>
    );
  }

  // Get organization name and logo
  const organizationName = organization.name || '';
  const organizationLogo = organization.logo_url || '';
  const organizationAbout = organization.about || '';
  const website = organization.website || '';

  // Helper functions to determine if value is email or website
  const isEmail = (value: string): boolean => {
    return value.includes('@') && !value.includes('http');
  };

  const isWebsite = (value: string): boolean => {
    return !isEmail(value);
  };

  return (
    <Card>
      <CardHeader title="About" titleIcon={faBuilding} className="mb-8" />

      <div className="flex flex-wrap gap-4 items-center">
        {/* Organization logo */}
        <div className="flex justify-center mb-4">
          <div className="p-4 border border-surface-secondary rounded-lg">
            {organizationLogo ? (
              <Image
                src={organizationLogo}
                alt={organizationName || 'Organization logo'}
                width={0}
                height={0}
                className="w-full max-w-28 h-auto object-contain"
              />
            ) : (
              <div className="h-24 w-24 bg-gray-200 rounded flex items-center justify-center text-gray-500 text-2xl">
                {organizationName?.charAt(0) || 'C'}
              </div>
            )}
          </div>
        </div>

        {/* Organization name */}
        <h3 className="text-xl font-semibold text-center mb-4">{organizationName}</h3>
      </div>

      {/* Organization description */}
      {organizationAbout && (
        <Richtext content={organizationAbout} className="prose-sm text-text-secondary mb-4" />
      )}

      {/* Organization website or email */}
      {website && (
        <div className="mb-8">
          <h4 className="font-medium text-gray-900 mb-2">
            {isEmail(website) ? 'Email' : 'Website'}
          </h4>
          <a
            href={
              isEmail(website)
                ? `mailto:${website}`
                : website.startsWith('http')
                  ? website
                  : `https://${website}`
            }
            {...(isWebsite(website) ? { target: '_blank', rel: 'noopener noreferrer' } : {})}
            className="text-text-primary flex gap-2 transition-colors undderline leading-none hover:text-brand-red"
          >
            {/* Check if the website value is an email or a URL */}
            <FontAwesomeIcon
              icon={isEmail(website) ? faEnvelope : faGlobe}
              className="h-4 w-4"
              aria-hidden="true"
            />
            {website}
          </a>
        </div>
      )}

      {children}
    </Card>
  );
}
