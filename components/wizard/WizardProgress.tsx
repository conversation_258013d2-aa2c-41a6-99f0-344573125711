import { useWizardStore } from '@/stores/wizardStore';

interface WizardProgressProps {
  className?: string;
}

export const WizardProgress = ({ className = '' }: WizardProgressProps) => {
  const { currentStep, totalSteps } = useWizardStore();

  return (
    <div className={`flex gap-2 ${className}`}>
      {Array.from({ length: totalSteps }).map((_, index) => (
        <div
          key={index}
          className={`h-2 rounded-full transition-all ${
            index === currentStep
              ? 'w-10 bg-brand-red'
              : index < currentStep
                ? 'w-2 bg-brand-red/30'
                : 'w-2 bg-gray-200'
          }`}
        />
      ))}
    </div>
  );
};
