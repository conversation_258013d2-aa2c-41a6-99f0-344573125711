import {
  faArrowRight,
  faBadgeCheck,
  faBriefcase,
  faDiploma,
  faGraduationCap,
  faShareNodes,
  faUserCircle,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { isAxiosError } from 'axios';
import Button from '@/components/shared/Button';
import { usePositiveAthleteOnboarding } from '@/hooks/usePositiveAthleteOnboarding';
import type { OnboardingStepResponse } from '@/services/positiveAthleteOnboarding.service';
import { usePositiveAthleteOnboardingStore } from '@/stores/positiveAthleteOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';

interface WelcomeStepProps {
  nomineeName: string;
  nominatedBy: string;
}

export const WelcomeStep = ({ nomineeName, nominatedBy }: WelcomeStepProps) => {
  const { nextStep } = useWizardStore();
  const { setCurrentStep, setAccountInfo, setErrors } = usePositiveAthleteOnboardingStore();

  const { startOnboarding } = usePositiveAthleteOnboarding({
    onSuccess: (response: OnboardingStepResponse | { redirect: string }) => {
      if ('current_step' in response) {
        setCurrentStep(response.current_step);
        if (response.prefill) {
          setAccountInfo(response.prefill);
        }
        nextStep();
      }
    },
    onError: (error: unknown) => {
      if (isAxiosError(error) && error.response?.data) {
        setErrors(error.response.data);
      }
    },
  });

  const handleStart = () => {
    startOnboarding.mutate();
  };

  const features = [
    {
      icon: faDiploma,
      title: 'Qualify for regional and state awards and scholarships',
    },
    {
      icon: faGraduationCap,
      title: 'Browse a Library of hundreds of character development resources',
    },
    {
      icon: faBadgeCheck,
      title: 'Earn leadership certifications',
    },
    {
      icon: faBriefcase,
      title: 'Access exclusive workforce and education opportunities',
    },
    {
      icon: faShareNodes,
      title: 'Connect with other Positive Athletes and alumni',
    },
    {
      icon: faUserCircle,
      title: 'Build a public profile with your story and accomplishments',
    },
  ];

  return (
    <div className="flex flex-col gap-6 md:gap-10">
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-text-primary">
          Congrats on your Nomination, {nomineeName}!
        </h1>
      </div>

      <p className="text-text-secondary">
        Positive Athlete is a recognition program that celebrates high character, high school
        student athletes! Your nomination by <strong>{nominatedBy}</strong> has earned you access to
        the Positive Athlete app where you can:
      </p>

      <div className="grid grid-cols-2 xl:grid-cols-3 gap-4">
        {features.map((feature, index) => (
          <div key={index} className="rounded-2xl bg-surface-secondary px-4 py-6 space-y-2 lg:p-6">
            <FontAwesomeIcon icon={feature.icon} className="!size-8 text-brand-red" />
            <p className="text-text-secondary">{feature.title}</p>
          </div>
        ))}
      </div>

      <p className="text-text-secondary">
        Set up your profile to see what {nominatedBy} wrote and get the most from Positive Athlete!
      </p>

      <div className="flex justify-end">
        <Button
          onClick={handleStart}
          color="blue"
          size="small"
          icon={faArrowRight}
          className="px-6"
          disabled={startOnboarding.isPending}
        >
          {startOnboarding.isPending ? 'Starting...' : "Let's Go!"}
        </Button>
      </div>
    </div>
  );
};
