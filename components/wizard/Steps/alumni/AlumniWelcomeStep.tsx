import React, { useState } from 'react';
import {
  faArrowRight,
  faBriefcase,
  faBuildingColumns,
  faGraduationCap,
  faMagnifyingGlass,
} from '@fortawesome/pro-regular-svg-icons';
// FontAwesomeIcon is used by IconRadioGroup, so no need to import it here directly unless used elsewhere
import { isAxiosError } from 'axios';
import Button from '@/components/shared/Button';
// Import IconRadioGroup instead of the generic Radio
import IconRadioGroup from '@/components/shared/form/IconRadioGroup';
import type { RadioOption as IconRadioOption } from '@/components/shared/form/IconRadioGroup'; // Import the option type
import { useAlumniOnboarding } from '@/hooks/useAlumniOnboarding';
import type {
  AlumniLifeStagePayload,
  AlumniOnboardingStepResponse,
} from '@/services/alumni-onboarding.service';
import { useAlumniOnboardingStore } from '@/stores/alumniOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';

interface AlumniWelcomeStepProps {
  firstName?: string;
  // lastName is not used in the greeting for this version
}

// This interface matches the original structure, which IconRadioGroup can largely use
interface LifeStageOptionData {
  id: string; // Will be used as 'value' for IconRadioGroup items
  name: string; // Will be used as 'name' for IconRadioGroup items
  icon: any; // Will be used as 'icon' for IconRadioGroup items
  intended_profile_type: 'college_athlete' | 'professional';
  life_stage_value: string;
}

const lifeStageDataSource: LifeStageOptionData[] = [
  {
    id: 'college',
    name: "I'm still in college or pursuing higher education",
    icon: faBuildingColumns,
    intended_profile_type: 'college_athlete',
    life_stage_value: 'college_student',
  },
  {
    id: 'working',
    name: "I'm working now",
    icon: faBriefcase,
    intended_profile_type: 'professional',
    life_stage_value: 'professional',
  },
  {
    id: 'graduated_no_work_plans',
    name: "I've graduated, but don't have plans to work",
    icon: faGraduationCap,
    intended_profile_type: 'professional',
    life_stage_value: 'professional',
  },
  {
    id: 'looking_for_work',
    name: "I'm looking for work",
    icon: faMagnifyingGlass,
    intended_profile_type: 'professional',
    life_stage_value: 'professional',
  },
];

// Transform lifeStageDataSource for the IconRadioGroup component
const iconRadioGroupItems: IconRadioOption[] = lifeStageDataSource.map(option => ({
  name: option.name, // Maps to IconRadioGroup's RadioOption 'name'
  value: option.id, // Maps to IconRadioGroup's RadioOption 'value'
  icon: option.icon, // Maps to IconRadioGroup's RadioOption 'icon'
}));

export const AlumniWelcomeStep = ({ firstName }: AlumniWelcomeStepProps) => {
  const { nextStep: wizardNextStep } = useWizardStore();
  const {
    setLifeStageSelection,
    setErrors,
    setCurrentStep,
    lifeStageSelection: initialLifeStageData,
  } = useAlumniOnboardingStore();

  const [selectedOptionId, setSelectedOptionId] = useState<string | undefined>(
    lifeStageDataSource.find(opt => opt.life_stage_value === initialLifeStageData?.life_stage)?.id
  );

  const { submitLifeStage } = useAlumniOnboarding({
    onSuccess: (response: AlumniOnboardingStepResponse | { redirect: string }) => {
      if ('current_step' in response) {
        setCurrentStep(response.current_step);
        const fullSelectedOption = lifeStageDataSource.find(opt => opt.id === selectedOptionId);
        if (fullSelectedOption) {
          setLifeStageSelection({
            life_stage: fullSelectedOption.life_stage_value,
            intended_profile_type: fullSelectedOption.intended_profile_type,
          });
        }
        wizardNextStep();
      }
    },
    onError: error => {
      if (isAxiosError(error) && error.response?.data) {
        setErrors(error.response.data);
      } else {
        setErrors({ message: 'An unexpected error occurred', errors: {} });
      }
    },
  });

  const handleContinue = () => {
    const fullSelectedOption = lifeStageDataSource.find(opt => opt.id === selectedOptionId);
    if (fullSelectedOption) {
      const payload: AlumniLifeStagePayload = {
        life_stage: fullSelectedOption.life_stage_value,
        intended_profile_type: fullSelectedOption.intended_profile_type,
      };
      submitLifeStage.mutate(payload);
    } else {
      setErrors({
        message: 'Please select an option to continue.',
        errors: { life_stage: ['This field is required.'] },
      });
    }
  };

  return (
    <div className="flex flex-col gap-6 md:gap-10 max-w-2xl mx-auto">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold text-text-primary">What&apos;s next for you?</h1>
      </div>

      <p className="text-text-secondary">
        It looks like you&apos;ve graduated from college. Whether or not you&apos;re in the
        workforce yet, Positive Athlete will continue to provide valuable connections with other
        Positive Athletes and exclusive opportunities.
        <br />
        Take a moment to update your profile.
      </p>

      <IconRadioGroup
        items={iconRadioGroupItems}
        value={selectedOptionId}
        onChange={setSelectedOptionId}
        columns={2} // Set to 2 columns as requested
        name="lifeStageSelection" // Provide a name for the group
      />

      <div className="flex justify-end mt-4">
        <Button
          onClick={handleContinue}
          color="blue"
          size="small"
          icon={faArrowRight}
          className="px-6"
          disabled={submitLifeStage.isPending || !selectedOptionId}
        >
          {submitLifeStage.isPending ? 'Saving...' : 'Continue'}
        </Button>
      </div>
    </div>
  );
};
