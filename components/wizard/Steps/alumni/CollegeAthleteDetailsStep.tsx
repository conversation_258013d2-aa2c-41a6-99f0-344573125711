import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { faArrowLeft, faArrowRight } from '@fortawesome/pro-light-svg-icons';
import { isAxiosError } from 'axios';
import { EditDetailsModalSocialLinks as SocialLinksAccordion } from '@/components/profile/modals/EditDetailsModalSocialLinks';
import { UploadAvatarModal } from '@/components/profile/modals/UploadAvatarModal';
import Avatar from '@/components/shared/Avatar';
import Button from '@/components/shared/Button';
import SelectInput from '@/components/shared/form/SelectInput';
import type { SelectOption } from '@/components/shared/form/SelectInput';
import SiteInput from '@/components/shared/form/SiteInput';
import ConfirmationDialog from '@/components/wizard/Steps/ConfirmationDialog';
import { OnboardingInterestSelector } from '@/components/wizard/Steps/OnboardingInterestSelector';
import { useAlumniOnboarding } from '@/hooks/useAlumniOnboarding';
import { useFormErrors } from '@/hooks/useFormErrors';
import type {
  AlumniCollegeDetailsPayload,
  AlumniOnboardingStepResponse,
} from '@/services/alumni-onboarding.service';
import type { Interest } from '@/services/interest.service';
import { ProfileDetails } from '@/services/positive-athlete-profile.service';
import {
  useAlumniOnboardingStore,
  type AlumniCollegeDetailsData,
} from '@/stores/alumniOnboardingStore';
import { useModalStore } from '@/stores/modal.store';
import { useWizardStore } from '@/stores/wizardStore';
import { STATE_OPTIONS } from '@/utils/constants/states';

// GRADUATION_YEARS can be dynamic based on current year
const currentYear = new Date().getFullYear();
const GRADUATION_YEARS = Array.from({ length: 10 }, (_, i) => {
  const year = currentYear + i - 2; // Range around current year
  return { value: year.toString(), label: year.toString() };
});

const GENDER_OPTIONS: SelectOption[] = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
  { value: 'prefer_not_to_say', label: 'Prefer not to say' },
];

// No initialData prop for this step directly, data comes from store
export const CollegeAthleteDetailsStep = () => {
  const router = useRouter();
  const {
    setCurrentStep: setAlumniCurrentStep,
    collegeDetails,
    setCollegeDetails,
    accountInfo, // For avatar fallback
    setErrors: setStoreErrors,
    reset: resetAlumniStore,
  } = useAlumniOnboardingStore();
  const { previousStep: wizardPreviousStep } = useWizardStore();
  const { open, close } = useModalStore();
  const { fieldErrors, setErrors, clearError, clearAllErrors } = useFormErrors();
  const [selectedInterests, setSelectedInterests] = useState<Interest[]>([]);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(
    collegeDetails.profile_photo_url || undefined // Use profile_photo_url from store
  );

  const handleMutationError = (error: unknown) => {
    if (isAxiosError(error) && error.response?.data) {
      setStoreErrors(error.response.data);
      setErrors(error.response.data); // For local form field errors
    } else {
      setStoreErrors({ message: 'An unexpected error occurred', errors: {} });
    }
  };

  const { submitCollegeDetails, completeOnboarding } = useAlumniOnboarding({
    // Removed global onSuccess and onError, will be handled in .mutate() calls
  });

  useEffect(() => {
    // Pre-fill selectedInterests if collegeDetails.career_interests exists
    if (collegeDetails.career_interests && collegeDetails.career_interests.length > 0) {
      // Directly use the Interest objects from the store
      setSelectedInterests(collegeDetails.career_interests);
    }

    // Use profile_photo_url for setting the avatarUrl
    if (typeof collegeDetails.profile_photo_url === 'string') {
      setAvatarUrl(collegeDetails.profile_photo_url);
    }

    // Pre-fill collegeDetails.state from accountInfo.state if not already set
    if (!collegeDetails.state && accountInfo.state) {
      setCollegeDetails({ ...collegeDetails, state: accountInfo.state });
    }
  }, [collegeDetails, accountInfo.state, setCollegeDetails]);

  // Sync local avatarUrl with store's profile_photo_url
  useEffect(() => {
    setAvatarUrl(collegeDetails.profile_photo_url || undefined);
  }, [collegeDetails.profile_photo_url]);

  useEffect(() => {
    return () => {
      clearAllErrors();
    };
  }, [clearAllErrors]);

  const handleChange =
    (field: keyof AlumniCollegeDetailsData) =>
    (value: string | React.ChangeEvent<HTMLInputElement>) => {
      const newValue = typeof value === 'string' ? value : value.target.value;
      clearError(field as string);
      setCollegeDetails({
        ...collegeDetails,
        [field]: newValue,
      });
    };

  const handleInterestSelect = (interests: Interest[]) => {
    clearError('career_interests');
    setSelectedInterests(interests);
    setCollegeDetails({
      // Store full Interest objects in Zustand
      ...collegeDetails,
      career_interests: interests, // Store the array of Interest objects
    });
  };

  const handleAvatarClick = () => {
    open(
      <UploadAvatarModal
        onSave={async (file: File) => {
          // Save the file for form submission instead of uploading immediately
          setAvatarFile(file);
          const url = URL.createObjectURL(file);
          setAvatarUrl(url);
          setCollegeDetails({ ...collegeDetails, profile_photo_url: url });
          return Promise.resolve({ success: true }); // Return success for compatibility
        }}
        onSuccess={result => {
          // Modal will close automatically on success
        }}
        onError={error => {
          console.error('Avatar upload error:', error);
          // Error will be displayed in the modal
        }}
      />,
      '2xl'
    );
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    clearAllErrors();

    const payload: Omit<AlumniCollegeDetailsPayload, 'profile_photo'> = {
      college: collegeDetails.college,
      state: collegeDetails.state,
      graduation_year: collegeDetails.graduation_year,
      gpa: collegeDetails.gpa,
      gender: collegeDetails.gender,
      height: collegeDetails.height,
      weight: collegeDetails.weight,
      // Map to array of IDs for the payload
      career_interests: collegeDetails.career_interests
        ? collegeDetails.career_interests.map(i => i.id.toString())
        : [],
      twitter: collegeDetails.twitter,
      instagram: collegeDetails.instagram,
      facebook: collegeDetails.facebook,
      hudl: collegeDetails.hudl,
      custom_link: collegeDetails.custom_link,
    };

    const formData = new FormData();
    Object.entries(payload).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        if (Array.isArray(value)) {
          value.forEach(item => formData.append(`${key}[]`, item.toString()));
        } else {
          formData.append(key, value.toString());
        }
      }
    });

    // Only append avatarFile (the File object) if it exists
    if (avatarFile) {
      formData.append('profile_photo', avatarFile);
    }
    // Do not attempt to use collegeDetails.profile_photo or profile_photo_url for submission here

    submitCollegeDetails.mutate(formData, {
      onSuccess: response => {
        if (response.data.current_step === 'completed') {
          setAlumniCurrentStep('completed');
          open(
            <ConfirmationDialog
              title="Awesome!"
              description="Thanks for adding your details. You can always edit these later."
              handleSubmit={() => {
                close();
                completeOnboarding.mutate(undefined, {
                  onSuccess: completionResponse => {
                    router.push(completionResponse.data.redirect);
                    resetAlumniStore();
                  },
                  onError: err => {
                    handleMutationError(err);
                  },
                });
              }}
              handleClose={close}
            />
          );
        } else {
          // Handle case where submission is successful but step is not 'completed'
          handleMutationError(
            new Error(`Submission succeeded but current step is: ${response.data.current_step}`)
          );
        }
      },
      onError: handleMutationError,
    });
  };

  const socialLinkFields: (keyof AlumniCollegeDetailsData)[] = [
    'twitter',
    'instagram',
    'facebook',
    'hudl',
    'custom_link',
  ];

  // Prepare data for SocialLinksAccordion, assuming it expects fields like 'twitter', 'instagram' etc.
  const socialDataForAccordion = {
    twitter: collegeDetails.twitter || '',
    instagram: collegeDetails.instagram || '',
    facebook: collegeDetails.facebook || '',
    hudl: collegeDetails.hudl || '',
    custom_link: collegeDetails.custom_link || '',
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-2">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">
          College Athlete Details
        </h1>
        <p className="text-base md:text-body-lg text-gray-500">
          Tell us about your college experience.
        </p>
      </div>

      <div className="py-4">
        <Avatar
          src={avatarUrl}
          firstName={accountInfo.first_name}
          lastName={accountInfo.last_name}
          size="xl"
          isUploadState={!avatarUrl}
          onClick={handleAvatarClick}
        />
      </div>

      <form className="space-y-6" onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <SelectInput
            label="State"
            id="state"
            options={[...STATE_OPTIONS]}
            value={collegeDetails.state || ''}
            onChange={handleChange('state')}
            isFailedValidation={Boolean(fieldErrors['state'])}
            description={fieldErrors['state']}
            aria-invalid={Boolean(fieldErrors['state'])}
            aria-errormessage={fieldErrors['state'] ? 'state-error' : undefined}
          />

          <SiteInput
            label="College / University Name"
            id="college"
            value={collegeDetails.college || ''}
            onChange={handleChange('college')}
            isFailedValidation={Boolean(fieldErrors['college'])}
            description={fieldErrors['college']}
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <SelectInput
            label="Expected Graduation Year"
            id="graduation_year"
            value={collegeDetails.graduation_year || ''}
            onChange={handleChange('graduation_year')}
            options={GRADUATION_YEARS}
            isFailedValidation={Boolean(fieldErrors['graduation_year'])}
            description={fieldErrors['graduation_year']}
          />
          <SiteInput
            label="GPA"
            id="gpa"
            value={collegeDetails.gpa || ''}
            onChange={handleChange('gpa')}
            isFailedValidation={Boolean(fieldErrors['gpa'])}
            description={fieldErrors['gpa']}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <SelectInput
            label="Gender"
            id="gender"
            value={collegeDetails.gender || ''}
            onChange={handleChange('gender')}
            options={GENDER_OPTIONS}
            isFailedValidation={Boolean(fieldErrors['gender'])}
            description={fieldErrors['gender']}
          />
          <div>
            <label className="pa-eyebrow text-text-primary mb-2">HEIGHT (e.g., 5ft 10in)</label>
            <div className="flex gap-2">
              <SiteInput
                label="Feet"
                hideLabel
                type="number"
                value={collegeDetails.height ? Math.floor(Number(collegeDetails.height) / 12) : ''}
                onChange={e => {
                  const feet = parseInt(e.target.value || '0');
                  const inches = collegeDetails.height ? Number(collegeDetails.height) % 12 : 0;
                  setCollegeDetails({
                    ...collegeDetails,
                    height: (feet * 12 + inches).toString(),
                  });
                  clearError('height');
                }}
                min={3}
                max={8}
                placeholder="ft"
                className="w-20"
                isFailedValidation={Boolean(fieldErrors['height'])}
              />
              <SiteInput
                label="Inches"
                hideLabel
                type="number"
                value={collegeDetails.height ? Number(collegeDetails.height) % 12 : ''}
                onChange={e => {
                  const inches = parseInt(e.target.value || '0');
                  const feet = collegeDetails.height
                    ? Math.floor(Number(collegeDetails.height) / 12)
                    : 0;
                  setCollegeDetails({
                    ...collegeDetails,
                    height: (feet * 12 + inches).toString(),
                  });
                  clearError('height');
                }}
                min={0}
                max={11}
                placeholder="in"
                className="w-20"
                isFailedValidation={Boolean(fieldErrors['height'])}
              />
            </div>
            {fieldErrors['height'] && (
              <p className="mt-1 text-sm text-red-600">{fieldErrors['height']}</p>
            )}
          </div>
          <SiteInput
            label="Weight (lbs)"
            id="weight"
            type="number"
            value={collegeDetails.weight || ''}
            onChange={handleChange('weight')}
            isFailedValidation={Boolean(fieldErrors['weight'])}
            placeholder="e.g., 160"
            description={fieldErrors['weight']}
          />
        </div>

        <div className="pb-4">
          <OnboardingInterestSelector
            selectedInterests={selectedInterests}
            onSelect={handleInterestSelect}
            label="Career Interests"
          />
          {fieldErrors['career_interests'] && (
            <p className="mt-1 text-sm text-red-600">{fieldErrors['career_interests']}</p>
          )}
        </div>

        <SocialLinksAccordion
          formData={socialDataForAccordion as any}
          onChange={handleChange as any}
        />

        <div className="flex justify-between pt-6">
          <Button
            onClick={() => {
              setAlumniCurrentStep('account_info');
              wizardPreviousStep();
            }}
            variant="text"
            color="blue"
            icon={faArrowLeft}
            iconPosition="left"
            type="button"
          >
            Back
          </Button>

          <Button
            type="submit"
            color="blue"
            size="small"
            className="px-6"
            icon={faArrowRight}
            disabled={submitCollegeDetails.isPending}
          >
            {submitCollegeDetails.isPending ? 'Saving...' : 'Finish Onboarding'}
          </Button>
        </div>
      </form>
    </div>
  );
};
