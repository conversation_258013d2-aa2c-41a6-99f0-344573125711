import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { faArrowLeft, faArrowRight } from '@fortawesome/pro-light-svg-icons';
import { isAxiosError } from 'axios';
import { EditDetailsModalSocialLinks as SocialLinksAccordion } from '@/components/profile/modals/EditDetailsModalSocialLinks';
import { UploadAvatarModal } from '@/components/profile/modals/UploadAvatarModal';
import Avatar from '@/components/shared/Avatar';
import Button from '@/components/shared/Button';
import SelectInput from '@/components/shared/form/SelectInput';
import SiteInput from '@/components/shared/form/SiteInput';
import ConfirmationDialog from '@/components/wizard/Steps/ConfirmationDialog';
import { OnboardingInterestSelector } from '@/components/wizard/Steps/OnboardingInterestSelector';
import { useAlumniOnboarding } from '@/hooks/useAlumniOnboarding';
import { useFormErrors } from '@/hooks/useFormErrors';
import type {
  AlumniOnboardingStepResponse,
  AlumniProfessionalDetailsPayload,
} from '@/services/alumni-onboarding.service';
import type { Interest } from '@/services/interest.service';
import {
  useAlumniOnboardingStore,
  type AlumniProfessionalDetailsData,
} from '@/stores/alumniOnboardingStore';
import { useModalStore } from '@/stores/modal.store';
import { useWizardStore } from '@/stores/wizardStore';
import { STATE_OPTIONS } from '@/utils/constants/states';

export const ProfessionalDetailsStep = () => {
  const router = useRouter();
  const {
    setCurrentStep: setAlumniCurrentStep,
    professionalDetails,
    setProfessionalDetails,
    accountInfo, // For avatar fallback
    setErrors: setStoreErrors,
    reset: resetAlumniStore,
  } = useAlumniOnboardingStore();
  const { previousStep: wizardPreviousStep } = useWizardStore();
  const { open } = useModalStore();
  const { fieldErrors, setErrors, clearError, clearAllErrors } = useFormErrors();
  const [selectedInterests, setSelectedInterests] = useState<Interest[]>([]);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(
    professionalDetails.profile_photo_url || undefined
  );

  const handleMutationError = (error: unknown) => {
    if (isAxiosError(error) && error.response?.data) {
      setStoreErrors(error.response.data);
      setErrors(error.response.data);
    } else {
      setStoreErrors({ message: 'An unexpected error occurred', errors: {} });
    }
  };

  const { submitProfessionalDetails, completeOnboarding } = useAlumniOnboarding({
    // Removed global onSuccess and onError, will be handled in .mutate() calls
  });

  useEffect(() => {
    if (professionalDetails.interests && professionalDetails.interests.length > 0) {
      setSelectedInterests(professionalDetails.interests);
    }
    setAvatarUrl(professionalDetails.profile_photo_url || undefined);
  }, [professionalDetails.interests, professionalDetails.profile_photo_url]);

  useEffect(() => {
    return () => {
      clearAllErrors();
    };
  }, [clearAllErrors]);

  const handleChange =
    (field: keyof AlumniProfessionalDetailsData) =>
    (value: string | React.ChangeEvent<HTMLInputElement>) => {
      const newValue = typeof value === 'string' ? value : value.target.value;
      clearError(field as string);
      setProfessionalDetails({
        ...professionalDetails,
        [field]: newValue,
      });
    };

  const handleInterestSelect = (interests: Interest[]) => {
    clearError('interests');
    setSelectedInterests(interests);
    setProfessionalDetails({
      ...professionalDetails,
      interests: interests,
    });
  };

  const handleAvatarClick = () => {
    open(
      <UploadAvatarModal
        onSave={async (file: File) => {
          // Save the file for form submission instead of uploading immediately
          setAvatarFile(file);
          const url = URL.createObjectURL(file);
          setAvatarUrl(url);
          setProfessionalDetails({ ...professionalDetails, profile_photo_url: url });
          return Promise.resolve({ success: true }); // Return success for compatibility
        }}
        onSuccess={result => {
          // Modal will close automatically on success
        }}
        onError={error => {
          console.error('Avatar upload error:', error);
          // Error will be displayed in the modal
        }}
      />,
      '2xl'
    );
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    clearAllErrors();

    const payload: Omit<
      AlumniProfessionalDetailsPayload,
      'profile_photo' | 'token' | 'intended_profile_type'
    > = {
      employer: professionalDetails.employer,
      job_title: professionalDetails.job_title,
      state: professionalDetails.state,
      interests: professionalDetails.interests
        ? professionalDetails.interests.map(i => i.id.toString())
        : [],
      twitter: professionalDetails.twitter,
      instagram: professionalDetails.instagram,
      facebook: professionalDetails.facebook,
      hudl: professionalDetails.hudl,
      custom_link: professionalDetails.custom_link,
    };

    const formData = new FormData();
    Object.entries(payload).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        if (Array.isArray(value)) {
          value.forEach(item => formData.append(`${key}[]`, item.toString()));
        } else {
          formData.append(key, value.toString());
        }
      }
    });

    if (avatarFile) {
      formData.append('profile_photo', avatarFile);
    }

    submitProfessionalDetails.mutate(formData, {
      onSuccess: response => {
        // Assuming AxiosResponse<AlumniOnboardingStepResponse>
        if (response.data.current_step === 'completed') {
          setAlumniCurrentStep('completed');
          open(
            <ConfirmationDialog
              title="Awesome!"
              description="Thanks for adding your details. You can always edit these later."
              handleSubmit={() => {
                useModalStore.getState().close();
                completeOnboarding.mutate(undefined, {
                  onSuccess: completionResponse => {
                    router.push(completionResponse.data.redirect);
                    resetAlumniStore();
                  },
                  onError: err => {
                    handleMutationError(err);
                  },
                });
              }}
              handleClose={() => useModalStore.getState().close()}
            />
          );
        } else {
          handleMutationError(
            new Error(`Submission succeeded but current step is: ${response.data.current_step}`)
          );
        }
      },
      onError: handleMutationError,
    });
  };

  const socialLinkFields: (keyof AlumniProfessionalDetailsData)[] = [
    'twitter',
    'instagram',
    'facebook',
    'hudl',
    'custom_link',
  ];
  const socialDataForAccordion = {
    twitter: professionalDetails.twitter || '',
    instagram: professionalDetails.instagram || '',
    facebook: professionalDetails.facebook || '',
    hudl: professionalDetails.hudl || '',
    custom_link: professionalDetails.custom_link || '',
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-2">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">
          Professional Details
        </h1>
        <p className="text-base md:text-body-lg text-gray-500">
          Share some information about your professional life.
        </p>
      </div>

      <div className="py-4">
        <Avatar
          src={avatarUrl}
          firstName={accountInfo.first_name}
          lastName={accountInfo.last_name}
          size="xl"
          isUploadState={!avatarUrl}
          onClick={handleAvatarClick}
        />
      </div>

      <form className="space-y-6" onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <SelectInput
            label="State"
            id="state"
            options={[...STATE_OPTIONS]}
            value={professionalDetails.state || ''}
            onChange={handleChange('state')}
            isFailedValidation={Boolean(fieldErrors['state'])}
            description={fieldErrors['state']}
            aria-invalid={Boolean(fieldErrors['state'])}
            aria-errormessage={fieldErrors['state'] ? 'state-error' : undefined}
          />

          <SiteInput
            label="Current Employer / Company"
            id="employer"
            value={professionalDetails.employer || ''}
            onChange={handleChange('employer')}
            isFailedValidation={Boolean(fieldErrors['employer'])}
            description={fieldErrors['employer']}
          />
        </div>

        <SiteInput
          label="Job Title"
          id="job_title"
          value={professionalDetails.job_title || ''}
          onChange={handleChange('job_title')}
          isFailedValidation={Boolean(fieldErrors['job_title'])}
          description={fieldErrors['job_title']}
        />

        <div className="pb-4">
          <OnboardingInterestSelector
            selectedInterests={selectedInterests}
            onSelect={handleInterestSelect}
            label="industry"
          />
          {fieldErrors['interests'] && (
            <p className="mt-1 text-sm text-red-600">{fieldErrors['interests']}</p>
          )}
        </div>

        <SocialLinksAccordion
          formData={socialDataForAccordion as any}
          onChange={handleChange as any}
        />

        <div className="flex justify-between pt-6">
          <Button
            onClick={() => {
              setAlumniCurrentStep('account_info');
              wizardPreviousStep();
            }}
            variant="text"
            color="blue"
            icon={faArrowLeft}
            iconPosition="left"
            type="button"
          >
            Back
          </Button>

          <Button
            type="submit"
            color="blue"
            size="small"
            className="px-6"
            icon={faArrowRight}
            disabled={submitProfessionalDetails.isPending}
          >
            {submitProfessionalDetails.isPending ? 'Saving...' : 'Finish Onboarding'}
          </Button>
        </div>
      </form>
    </div>
  );
};
