import React, { useState } from 'react';
import { isAxiosError } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { DynamicListEditor } from '@/components/shared/dynamic-list';
import type { DynamicListItem } from '@/components/shared/dynamic-list/types';
import SiteInput from '@/components/shared/form/SiteInput';
import { Textarea } from '@/components/shared/form/Textarea';
import StepsNavigation from '@/components/wizard/Steps/StepsNavigation';
import { usePositiveCoachOnboarding } from '@/hooks/usePositiveCoachOnboarding';
import type { InvolvementItem as ApiInvolvementItem } from '@/services/positiveAthleteOnboarding.service';
import { usePositiveAthleteOnboardingStore } from '@/stores/positiveAthleteOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';

// Adapter type to handle string IDs for DynamicListEditor
interface InvolvementItemWithId extends Omit<ApiInvolvementItem, 'order'>, DynamicListItem {
  id: string;
}

interface CoachInvolvementStepProps {
  heading?: string;
  subHeading?: string;
  initialData?: {
    items: ApiInvolvementItem[];
  };
}

export const CoachInvolvementStep = ({
  heading = 'Step 4: List your School / Community Involvement',
  subHeading = 'Highlight your school achievements and ways you&apos;ve positively engaged with your community. This step is optional - you can skip if you prefer not to add any involvement.',
  initialData,
}: CoachInvolvementStepProps) => {
  const { nextStep } = useWizardStore();
  const { setCurrentStep, involvement, setInvolvement } = usePositiveAthleteOnboardingStore();
  const [validationError, setValidationError] = useState<string | null>(null);

  // Initialize involvement items
  const [involvements, setInvolvements] = useState<InvolvementItemWithId[]>(() => {
    const initialItems = involvement.items || initialData?.items || [];
    return initialItems.map(item => ({
      id: (item as any).id?.toString() || uuidv4(),
      name: item.name,
      date_range: item.date_range,
      description: item.description,
    }));
  });

  const { submitCommunityInvolvement } = usePositiveCoachOnboarding({
    onSuccess: response => {
      if ('current_step' in response) {
        setCurrentStep(response.current_step);
        nextStep();
      }
    },
    onError: (error: unknown) => {
      console.error('Error submitting involvement:', error);
      if (isAxiosError(error) && error.response?.data) {
        setValidationError(error.response.data.message || 'An error occurred');
      } else {
        setValidationError('An unexpected error occurred. Please try again.');
      }
    },
  });

  const handleAdd = () => {
    const newInvolvement: InvolvementItemWithId = {
      id: uuidv4(),
      name: '',
      date_range: '',
      description: '',
    };
    setInvolvements(prev => [...prev, newInvolvement]);
    setValidationError(null);
  };

  const handleRemove = (id: string) => {
    setInvolvements(prev => prev.filter(item => item.id !== id));
    setValidationError(null);
  };

  const handleUpdate = (id: string, updates: Partial<Omit<InvolvementItemWithId, 'id'>>) => {
    setInvolvements(prev => prev.map(item => (item.id === id ? { ...item, ...updates } : item)));
  };

  const handleReorder = (reorderedItems: InvolvementItemWithId[]) => {
    setInvolvements(reorderedItems);
  };

  const validateForm = (): boolean => {
    // Clear any previous validation errors
    setValidationError(null);

    // For Next Step button, we require at least one valid involvement
    if (involvements.length === 0) {
      setValidationError(
        'Please add at least one involvement or use Skip if you prefer not to add any.'
      );
      return false;
    }

    // Check for incomplete items
    const hasEmptyFields = involvements.some(
      item => !item.name || !item.date_range || !item.description
    );

    if (hasEmptyFields) {
      setValidationError(
        'Please fill in all fields for each involvement or remove incomplete ones.'
      );
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateForm()) return;

    // Only include complete involvements in submission
    const validInvolvements = involvements.filter(
      item => item.name && item.date_range && item.description
    );

    // Convert to API format
    const apiInvolvements: ApiInvolvementItem[] = validInvolvements.map((item, index) => ({
      name: item.name,
      date_range: item.date_range,
      description: item.description,
      order: index,
    }));

    // Update local store
    setInvolvement({ items: apiInvolvements });

    // Submit to API
    submitCommunityInvolvement.mutate({ items: apiInvolvements });
  };

  const renderFields = (item: InvolvementItemWithId) => (
    <>
      <SiteInput
        label="Activity Name"
        value={item.name}
        onChange={e => handleUpdate(item.id, { name: e.target.value })}
        placeholder="e.g., Student Government Treasurer"
      />

      <SiteInput
        label="Date or Range"
        type="text"
        value={item.date_range}
        onChange={e => handleUpdate(item.id, { date_range: e.target.value })}
        placeholder="e.g., 2022-2023"
      />

      <Textarea
        label="Description"
        value={item.description}
        onChange={e => handleUpdate(item.id, { description: e.target.value })}
        placeholder="Briefly describe this activity"
        rows={4}
      />
    </>
  );

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-6">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">{heading}</h1>

        <p className="text-base md:text-body-lg text-gray-500">{subHeading}</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <DynamicListEditor
          items={involvements}
          onAdd={handleAdd}
          onRemove={handleRemove}
          onUpdate={handleUpdate}
          onReorder={handleReorder}
          getTitle={item => item.name || 'New Item'}
          renderFields={renderFields}
          addButtonText="Add Another Item"
        />

        {validationError && <div className="text-sm text-red-500 mt-2">{validationError}</div>}

        <StepsNavigation
          backStep="sports"
          isSubmitting={submitCommunityInvolvement.isPending}
          showSkip={true}
          submitLabel="Next Step"
        />
      </form>
    </div>
  );
};
