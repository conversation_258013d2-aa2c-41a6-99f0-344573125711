import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  faArrowLeft,
  faArrowRight,
  faEnvelope,
  faMobileButton,
} from '@fortawesome/pro-regular-svg-icons';
import { isAxiosError } from 'axios';
import Button from '@/components/shared/Button';
import PasswordInput from '@/components/shared/form/PasswordInput';
import SelectInput from '@/components/shared/form/SelectInput';
import SiteInput from '@/components/shared/form/SiteInput';
import { useFormErrors } from '@/hooks/useFormErrors';
import { usePositiveCoachOnboarding } from '@/hooks/usePositiveCoachOnboarding';
import type { AccountInfoPayload } from '@/services/positiveAthleteOnboarding.service';
import { usePositiveAthleteOnboardingStore } from '@/stores/positiveAthleteOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';
import { STATE_OPTIONS } from '@/utils/constants/states';

interface CoachAccountInfoStepProps {
  initialData?: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    street: string;
    unit: string;
    city: string;
    state: string;
    zipCode: string;
  };
}

interface FieldError {
  field: string;
  message: string;
}

interface ValidationErrors {
  message: string;
  errors: Record<string, string[]>;
}

export const CoachAccountInfoStep = ({ initialData }: CoachAccountInfoStepProps) => {
  const router = useRouter();
  const { setCurrentStep, accountInfo, setAccountInfo } = usePositiveAthleteOnboardingStore();
  const { nextStep, previousStep } = useWizardStore();
  const { fieldErrors, setErrors, clearError, clearAllErrors } = useFormErrors();

  const { submitAccountInfo } = usePositiveCoachOnboarding({
    onSuccess: response => {
      if ('current_step' in response) {
        setCurrentStep(response.current_step);
        nextStep();
      }
    },
    onError: (error: unknown) => {
      if (isAxiosError(error) && error.response?.data) {
        setErrors(error.response.data);
      }
    },
  });

  // Initialize form data from initialData prop only if store is empty
  useEffect(() => {
    if (initialData && !accountInfo.first_name && !accountInfo.last_name) {
      setAccountInfo({
        first_name: initialData.firstName,
        last_name: initialData.lastName,
        email: initialData.email,
        phone: initialData.phone,
        street_address: initialData.street,
        unit: initialData.unit,
        city: initialData.city,
        state: initialData.state,
        zip_code: initialData.zipCode,
      });
    }
  }, [initialData, setAccountInfo, accountInfo.first_name, accountInfo.last_name]);

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      clearAllErrors();
    };
  }, [clearAllErrors]);

  const handleChange = (field: string) => (value: string | React.ChangeEvent<HTMLInputElement>) => {
    const newValue = typeof value === 'string' ? value : value.target.value;
    const mappedField =
      {
        firstName: 'first_name',
        lastName: 'last_name',
        street: 'street_address',
        zipCode: 'zip_code',
      }[field] || field;

    // Clear error for this field
    clearError(mappedField);

    setAccountInfo({
      ...accountInfo,
      [mappedField]: newValue,
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Clear any existing errors
    clearAllErrors();

    // Validate password length
    if (accountInfo.password && accountInfo.password.length < 6) {
      setErrors({
        message: 'Validation failed',
        errors: {
          password: ['Password must be at least 6 characters long'],
        },
      });
      return;
    }

    const payload: AccountInfoPayload = {
      first_name: accountInfo.first_name || '',
      last_name: accountInfo.last_name || '',
      email: accountInfo.email || '',
      phone: accountInfo.phone || '',
      street_address: accountInfo.street_address || '',
      unit: accountInfo.unit || '',
      city: accountInfo.city || '',
      state: accountInfo.state || '',
      zip_code: accountInfo.zip_code || '',
      password: accountInfo.password || '',
    };

    submitAccountInfo.mutate(payload);
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-6">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">
          Step 1: Confirm your Account Info
        </h1>

        <p className="text-base md:text-body-lg text-gray-500">
          Make sure we&apos;ve got the correct info. These details will not be visible to anyone
          else.
        </p>
      </div>

      <form className="space-y-6" onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SiteInput
              label="First Name"
              id="firstName"
              type="text"
              value={accountInfo.first_name || ''}
              onChange={handleChange('firstName')}
              isFailedValidation={Boolean(fieldErrors['first_name'])}
              description={fieldErrors['first_name']}
              aria-invalid={Boolean(fieldErrors['first_name'])}
              aria-errormessage={fieldErrors['first_name'] ? 'first-name-error' : undefined}
              required
            />

            <SiteInput
              label="Last Name"
              id="lastName"
              type="text"
              value={accountInfo.last_name || ''}
              onChange={handleChange('lastName')}
              isFailedValidation={Boolean(fieldErrors['last_name'])}
              description={fieldErrors['last_name']}
              aria-invalid={Boolean(fieldErrors['last_name'])}
              aria-errormessage={fieldErrors['last_name'] ? 'last-name-error' : undefined}
              required
            />
          </div>

          <SiteInput
            label="Email"
            id="email"
            type="email"
            value={accountInfo.email || ''}
            onChange={handleChange('email')}
            icon={faEnvelope}
            isFailedValidation={Boolean(fieldErrors['email'])}
            description={fieldErrors['email']}
            aria-invalid={Boolean(fieldErrors['email'])}
            aria-errormessage={fieldErrors['email'] ? 'email-error' : undefined}
            required
          />

          <SiteInput
            label="Phone"
            id="phone"
            type="tel"
            value={accountInfo.phone || ''}
            onChange={handleChange('phone')}
            icon={faMobileButton}
            isFailedValidation={Boolean(fieldErrors['phone'])}
            description={fieldErrors['phone']}
            aria-invalid={Boolean(fieldErrors['phone'])}
            aria-errormessage={fieldErrors['phone'] ? 'phone-error' : undefined}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SiteInput
              label="Street Address"
              id="street"
              type="text"
              value={accountInfo.street_address || ''}
              onChange={handleChange('street')}
              isFailedValidation={Boolean(fieldErrors['street_address'])}
              description={fieldErrors['street_address']}
              aria-invalid={Boolean(fieldErrors['street_address'])}
              aria-errormessage={fieldErrors['street_address'] ? 'street-address-error' : undefined}
            />

            <SiteInput
              label="Unit or Apartment"
              id="unit"
              type="text"
              value={accountInfo.unit || ''}
              onChange={handleChange('unit')}
              isFailedValidation={Boolean(fieldErrors['unit'])}
              description={fieldErrors['unit']}
              aria-invalid={Boolean(fieldErrors['unit'])}
              aria-errormessage={fieldErrors['unit'] ? 'unit-error' : undefined}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <SiteInput
              label="City"
              id="city"
              type="text"
              value={accountInfo.city || ''}
              onChange={handleChange('city')}
              isFailedValidation={Boolean(fieldErrors['city'])}
              description={fieldErrors['city']}
              aria-invalid={Boolean(fieldErrors['city'])}
              aria-errormessage={fieldErrors['city'] ? 'city-error' : undefined}
            />

            <SelectInput
              label="State"
              id="state"
              options={[...STATE_OPTIONS]}
              value={accountInfo.state || ''}
              onChange={handleChange('state')}
              isFailedValidation={Boolean(fieldErrors['state'])}
              description={fieldErrors['state']}
              aria-invalid={Boolean(fieldErrors['state'])}
              aria-errormessage={fieldErrors['state'] ? 'state-error' : undefined}
            />

            <SiteInput
              label="Zip Code"
              id="zipCode"
              type="text"
              value={accountInfo.zip_code || ''}
              onChange={handleChange('zipCode')}
              isFailedValidation={Boolean(fieldErrors['zip_code'])}
              description={fieldErrors['zip_code']}
              aria-invalid={Boolean(fieldErrors['zip_code'])}
              aria-errormessage={fieldErrors['zip_code'] ? 'zip-code-error' : undefined}
            />
          </div>

          {/* TODO: Add validation that should be at least 6 charaters long */}
          <div className="relative">
            <PasswordInput
              label="Set a Password"
              id="password"
              value={accountInfo.password || ''}
              onChange={handleChange('password')}
              isFailedValidation={Boolean(fieldErrors['password'])}
              description={fieldErrors['password'] || 'Password must be at least 6 characters long'}
              aria-invalid={Boolean(fieldErrors['password'])}
              aria-errormessage={fieldErrors['password'] ? 'password-error' : undefined}
            />
          </div>
        </div>

        <div className="flex justify-between pt-6">
          <Button
            onClick={() => {
              setCurrentStep('start');
              previousStep();
            }}
            variant="text"
            color="blue"
            icon={faArrowLeft}
            iconPosition="left"
            type="button"
          >
            Back
          </Button>

          <Button
            type="submit"
            color="blue"
            size="small"
            icon={faArrowRight}
            disabled={submitAccountInfo.isPending}
          >
            {submitAccountInfo.isPending ? 'Saving...' : 'Next Step'}
          </Button>
        </div>
      </form>
    </div>
  );
};
