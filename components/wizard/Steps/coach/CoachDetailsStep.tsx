import React, { useEffect, useState } from 'react';
import { faArrowLeft, faArrowRight } from '@fortawesome/pro-light-svg-icons';
import { isAxiosError } from 'axios';
import { EditDetailsModalSocialLinks as SocialLinksAccordion } from '@/components/profile/modals/EditDetailsModalSocialLinks';
import { UploadAvatarModal } from '@/components/profile/modals/UploadAvatarModal';
import Avatar from '@/components/shared/Avatar';
import Button from '@/components/shared/Button';
import ComboboxInput from '@/components/shared/form/ComboboxInput';
import SchoolComboboxInput from '@/components/shared/form/SchoolComboboxInput';
import SelectInput from '@/components/shared/form/SelectInput';
import type { SelectOption } from '@/components/shared/form/SelectInput';
import SiteInput from '@/components/shared/form/SiteInput';
import { useCounties } from '@/hooks/useCounties';
import { useFormErrors } from '@/hooks/useFormErrors';
import { usePositiveCoachOnboarding } from '@/hooks/usePositiveCoachOnboarding';
import type { County } from '@/services/county.service';
import type { Interest } from '@/services/interest.service';
import { ProfileDetails } from '@/services/positive-athlete-profile.service';
import type { StudentDetailsPayload } from '@/services/positiveAthleteOnboarding.service';
import type { School } from '@/services/school.service';
import { useModalStore } from '@/stores/modal.store';
import { usePositiveAthleteOnboardingStore } from '@/stores/positiveAthleteOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';
import { STATE_OPTIONS, type StateCode } from '@/utils/constants/states';

interface CoachDetailsStepProps {
  initialData?: {
    state: string;
    county: string;
    highSchool: string;
    school_id?: string;
    graduationYear: string;
    currentGpa: string;
    currentClassRank: string;
    gender: string;
    height: string;
    weight: string;
    careerInterests: string[];
    profile_photo?: string | null;
  };
}

export const CoachDetailsStep = ({ initialData }: CoachDetailsStepProps) => {
  const {
    setCurrentStep,
    studentDetails: coachDetails,
    setStudentDetails: setCoachDetails,
    accountInfo,
  } = usePositiveAthleteOnboardingStore();
  const { nextStep, previousStep } = useWizardStore();
  const { open } = useModalStore();
  const { fieldErrors, setErrors, clearError, clearAllErrors } = useFormErrors();
  const {
    counties,
    isSearching: isLoadingCounties,
    setSearchInput: setCountySearchInput,
    setState: setCountyState,
  } = useCounties();
  const [selectedCounty, setSelectedCounty] = useState<County | null>(null);

  // Store the avatar file for form submission
  const [avatarFile, setAvatarFile] = useState<File | null>(null);

  // Initialize avatarUrl from the store's profile_photo_url (via coachDetails alias)
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(
    coachDetails.profile_photo_url || undefined
  );

  // Effect to update local avatarUrl if store changes (e.g., after initialData processing)
  useEffect(() => {
    setAvatarUrl(coachDetails.profile_photo_url || undefined);
  }, [coachDetails.profile_photo_url]);

  const stateOptions = [...STATE_OPTIONS] as SelectOption[];

  const { submitDetails } = usePositiveCoachOnboarding({
    onSuccess: response => {
      if ('current_step' in response) {
        setCurrentStep(response.current_step);
        nextStep();
      }
    },
    onError: (error: unknown) => {
      if (isAxiosError(error) && error.response?.data) {
        setErrors(error.response.data);
      }
    },
  });

  // Initialize form data from initialData prop
  useEffect(() => {
    // Only populate from initialData if the store's details for this step are not yet set (e.g., state is missing)
    if (initialData && !coachDetails.state) {
      // Prepare the details for the store, matching StudentDetailsDTO fields relevant to coach
      const detailsForStore: Partial<
        StudentDetailsPayload & { profile_photo_url?: string | null }
      > = {
        state: initialData.state,
        county: initialData.county,
        high_school: initialData.highSchool, // Assuming coaches might be associated with a high school
        school_id: initialData.school_id,
        // Coach specific fields might differ, adjust as needed, but use profile_photo_url for avatar
        profile_photo_url: initialData.profile_photo, // Use profile_photo_url for the preview
      };

      // Apply the details to the state (setCoachDetails is an alias for setStudentDetails)
      setCoachDetails(detailsForStore);
    }
  }, [initialData, coachDetails.state, setCoachDetails]);

  // Set county state when coachDetails.state changes
  useEffect(() => {
    if (coachDetails.state) {
      setCountyState(coachDetails.state as StateCode);

      // Initial search to load counties when state is selected
      setCountySearchInput('');
    } else {
      // Reset county state when state is cleared
      setCountyState(null);
      setSelectedCounty(null);
    }
  }, [coachDetails.state, setCountyState, setCountySearchInput]);

  // Update selectedCounty when counties are loaded
  useEffect(() => {
    if (coachDetails.county && counties.length > 0) {
      const county = counties.find(c => c.id.toString() === coachDetails.county);
      if (county) {
        setSelectedCounty(county);
      }
    }
  }, [counties, coachDetails.county]);

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      clearAllErrors();
    };
  }, [clearAllErrors]);

  const handleChange = (field: string) => (value: string | React.ChangeEvent<HTMLInputElement>) => {
    const newValue = typeof value === 'string' ? value : value.target.value;
    const mappedField =
      {
        graduationYear: 'graduation_year',
        currentGpa: 'current_gpa',
        currentClassRank: 'current_class_rank',
        careerInterests: 'career_interests',
      }[field] || field;

    // Clear error for this field
    clearError(mappedField);

    // If changing state, also reset county
    if (field === 'state') {
      setCoachDetails({
        ...coachDetails,
        [mappedField]: newValue,
        county: '', // Reset county when state changes
      });
      setSelectedCounty(null);
    } else {
      setCoachDetails({
        ...coachDetails,
        [mappedField]: newValue,
      });
    }
  };

  // Handle county selection
  const handleCountyChange = (county: County | null) => {
    setSelectedCounty(county);
    clearError('county');

    if (county) {
      setCoachDetails({
        ...coachDetails,
        county: county.id.toString(),
      });
    } else {
      setCoachDetails({
        ...coachDetails,
        county: '',
      });
    }
  };

  // Handle county search input changes
  const handleCountySearchChange = (query: string) => {
    setCountySearchInput(query);
  };

  // Handle school selection
  const handleSchoolChange = (schoolId: number | null) => {
    clearError('school_id');
    clearError('high_school');

    const schoolIdString = schoolId ? schoolId.toString() : '';

    setCoachDetails({
      ...coachDetails,
      school_id: schoolIdString,
      high_school: '', // Clear the text field as we're using the school_id now
    });
  };

  const handleAvatarClick = () => {
    // Open the UploadAvatarModal component
    open(
      <UploadAvatarModal
        onSave={async (file: File) => {
          // Save the file for form submission instead of uploading immediately
          setAvatarFile(file);
          const url = URL.createObjectURL(file);
          setAvatarUrl(url);
          setCoachDetails({ ...coachDetails, profile_photo_url: url });
          return Promise.resolve({ success: true }); // Return success for compatibility
        }}
        onSuccess={result => {
          // Modal will close automatically on success
        }}
        onError={error => {
          console.error('Avatar upload error:', error);
          // Error will be displayed in the modal
        }}
      />,
      '2xl'
    );
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    clearAllErrors();

    // Ensure all required fields are present
    const requiredFields = ['state', 'county', 'school_id'] as const;

    const missingFields = requiredFields.filter(field => !coachDetails[field]);

    if (missingFields.length > 0) {
      setErrors({
        message: 'Please fill in all required fields',
        errors: missingFields.reduce(
          (acc, field) => ({
            ...acc,
            [field]: ['This field is required'],
          }),
          {}
        ),
      });
      return;
    }

    // Create a FormData object to handle file uploads
    const formData = new FormData();

    // Add all the regular fields from coachDetails to FormData,
    // excluding client-side only URLs or stale photo strings.
    Object.entries(coachDetails).forEach(([key, value]) => {
      if (key === 'profile_photo_url') {
        // Exclude profile_photo_url, it's for client-side preview
        return;
      }
      // Exclude 'profile_photo' as well if it's coming from coachDetails (store);
      // it should only be added from avatarFile (the actual File object).
      if (key === 'profile_photo') {
        return;
      }

      if (value !== null && value !== undefined) {
        if (Array.isArray(value)) {
          // Handle arrays
          value.forEach(item => formData.append(`${key}[]`, String(item)));
        } else {
          formData.append(key, String(value));
        }
      }
    });

    // Add the avatar file (File object) if we have one
    if (avatarFile) {
      formData.append('profile_photo', avatarFile);
    }
    // If avatarFile is null (e.g., after navigation and no re-upload),
    // 'profile_photo' will not be appended to FormData. This is correct.

    // Submit the FormData
    submitDetails.mutate(formData as any);
  };

  // Handle form submission - use onKeyDown to prevent Enter key from submitting the form
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Prevent Enter key from submitting the form
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-6">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">
          Step 2: Verify your Details
        </h1>

        <p className="text-base md:text-body-lg text-gray-500">
          Complete as many of these details as you&apos;re able. Each additional piece of
          information goes a long way toward qualifying you for awards and scholarships.
        </p>
      </div>

      {/* Avatar upload section */}
      <div className="py-4">
        <Avatar
          src={avatarUrl}
          firstName={accountInfo.first_name}
          lastName={accountInfo.last_name}
          size="xl"
          isUploadState={!avatarUrl}
          onClick={handleAvatarClick}
        />
      </div>

      <form className="space-y-6" onSubmit={handleSubmit} onKeyDown={handleKeyDown}>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SelectInput
              label="state"
              id="state"
              value={coachDetails.state || ''}
              onChange={handleChange('state')}
              options={stateOptions}
              isFailedValidation={Boolean(fieldErrors['state'])}
              description={fieldErrors['state']}
              aria-invalid={Boolean(fieldErrors['state'])}
              aria-errormessage={fieldErrors['state'] ? 'state-error' : undefined}
            />

            <ComboboxInput
              label="county"
              value={selectedCounty}
              onChange={handleCountyChange}
              options={counties}
              disabled={!coachDetails.state}
              placeholder={coachDetails.state ? 'Search for your county' : 'Select a state first'}
              onSearchChange={handleCountySearchChange}
              isLoading={isLoadingCounties}
              loadingText="Loading counties..."
              emptyText={'No counties found, try a different search term'}
              isFailedValidation={Boolean(fieldErrors['county'])}
              description={fieldErrors['county']}
              displayValue={county => county?.name || ''}
            />
          </div>

          <SchoolComboboxInput
            label="High School"
            value={coachDetails.school_id ? parseInt(coachDetails.school_id) : null}
            onChange={handleSchoolChange}
            countyId={selectedCounty?.id}
            stateCode={coachDetails.state as StateCode}
            disabled={!coachDetails.state || !coachDetails.county}
            placeholder={
              !coachDetails.state || !coachDetails.county
                ? 'Select a state and county first'
                : 'Search for your high school'
            }
            isFailedValidation={
              Boolean(fieldErrors['school_id']) || Boolean(fieldErrors['high_school'])
            }
            description={fieldErrors['school_id'] || fieldErrors['high_school']}
          />

          <SocialLinksAccordion
            formData={coachDetails as Partial<ProfileDetails>}
            onChange={handleChange}
          />
        </div>

        <div className="flex justify-between pt-6">
          <Button
            onClick={() => {
              setCurrentStep('account_info');
              previousStep();
            }}
            variant="text"
            color="blue"
            icon={faArrowLeft}
            iconPosition="left"
            type="button"
          >
            Back
          </Button>

          <Button
            type="submit"
            color="blue"
            size="small"
            className="px-6"
            icon={faArrowRight}
            disabled={submitDetails.isPending}
          >
            {submitDetails.isPending ? 'Saving...' : 'Next Step'}
          </Button>
        </div>
      </form>
    </div>
  );
};
