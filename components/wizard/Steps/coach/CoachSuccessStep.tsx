import React, { useState } from 'react';
import { isAxiosError } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { DynamicListEditor } from '@/components/shared/dynamic-list';
import type { DynamicListItem } from '@/components/shared/dynamic-list/types';
import SiteInput from '@/components/shared/form/SiteInput';
import { Textarea } from '@/components/shared/form/Textarea';
import StepsNavigation from '@/components/wizard/Steps/StepsNavigation';
import { usePositiveCoachOnboarding } from '@/hooks/usePositiveCoachOnboarding';
import type { InvolvementItem as WorkExperienceItem } from '@/services/positiveAthleteOnboarding.service';
import { usePositiveAthleteOnboardingStore } from '@/stores/positiveAthleteOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';

// Adapter type to handle string IDs for DynamicListEditor
interface ExperienceItemWithId extends Omit<WorkExperienceItem, 'order'>, DynamicListItem {
  id: string;
}

interface CoachSuccessStepProps {
  heading?: string;
  subHeading?: string;
  initialData?: {
    items: WorkExperienceItem[];
  };
}

export const CoachSuccessStep = ({ heading, subHeading, initialData }: CoachSuccessStepProps) => {
  const { nextStep } = useWizardStore();
  const { setCurrentStep, workExperience, setWorkExperience } = usePositiveAthleteOnboardingStore();
  const [validationError, setValidationError] = useState<string | null>(null);

  // Initialize work experience items
  const [experiences, setExperiences] = useState<ExperienceItemWithId[]>(() => {
    const initialItems = workExperience.items || initialData?.items || [];
    return initialItems.map(item => ({
      id: (item as any).id?.toString() || uuidv4(),
      name: item.name,
      date_range: item.date_range,
      description: item.description,
    }));
  });

  const { submitTeamSuccesses } = usePositiveCoachOnboarding({
    onSuccess: response => {
      if ('current_step' in response) {
        setCurrentStep(response.current_step);
        nextStep();
      }
    },
    onError: (error: unknown) => {
      console.error('Error submitting work experience:', error);
      if (isAxiosError(error) && error.response?.data) {
        setValidationError(error.response.data.message || 'An error occurred');
      } else {
        setValidationError('An unexpected error occurred. Please try again.');
      }
    },
  });

  const handleAdd = () => {
    const newExperience: ExperienceItemWithId = {
      id: uuidv4(),
      name: '',
      date_range: '',
      description: '',
    };
    setExperiences(prev => [...prev, newExperience]);
    setValidationError(null);
  };

  const handleRemove = (id: string) => {
    setExperiences(prev => prev.filter(item => item.id !== id));
    setValidationError(null);
  };

  const handleUpdate = (id: string, updates: Partial<Omit<ExperienceItemWithId, 'id'>>) => {
    setExperiences(prev => prev.map(item => (item.id === id ? { ...item, ...updates } : item)));
  };

  const handleReorder = (reorderedItems: ExperienceItemWithId[]) => {
    setExperiences(reorderedItems);
  };

  const validateForm = (): boolean => {
    // Clear any previous validation errors
    setValidationError(null);

    // For Next Step button, we require at least one valid experience
    if (experiences.length === 0) {
      setValidationError(
        'Please add at least one work experience or use Skip if you prefer not to add any.'
      );
      return false;
    }

    // Check for incomplete items
    const hasEmptyFields = experiences.some(
      item => !item.name || !item.date_range || !item.description
    );

    if (hasEmptyFields) {
      setValidationError(
        'Please fill in all fields for each work experience or remove incomplete ones.'
      );
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateForm()) return;

    // Only include complete experiences in submission
    const validExperiences = experiences.filter(
      item => item.name && item.date_range && item.description
    );

    // Convert to API format
    const apiExperiences: WorkExperienceItem[] = validExperiences.map((item, index) => ({
      name: item.name,
      date_range: item.date_range,
      description: item.description,
      order: index,
    }));

    // Update local store
    setWorkExperience({ items: apiExperiences });

    // Format payload according to TeamSuccessesDTO.php expectations
    // Backend expects a 'successes' array of objects, not 'team_or_athlete' and 'achievements'
    submitTeamSuccesses.mutate({
      successes: apiExperiences,
    });
  };

  const renderFields = (item: ExperienceItemWithId) => (
    <>
      <SiteInput
        label="Job Title/Position"
        value={item.name}
        onChange={e => handleUpdate(item.id, { name: e.target.value })}
        placeholder="e.g., Intern, Steerage Engineering"
      />

      <SiteInput
        label="Date or Range"
        type="text"
        value={item.date_range}
        onChange={e => handleUpdate(item.id, { date_range: e.target.value })}
        placeholder="e.g., 2022-2023"
      />

      <Textarea
        label="Description"
        value={item.description}
        onChange={e => handleUpdate(item.id, { description: e.target.value })}
        placeholder="Briefly describe this experience"
        rows={4}
      />
    </>
  );

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-6">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">{heading}</h1>

        <p className="text-base md:text-body-lg text-gray-500">{subHeading}</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <DynamicListEditor
          items={experiences}
          onAdd={handleAdd}
          onRemove={handleRemove}
          onUpdate={handleUpdate}
          onReorder={handleReorder}
          getTitle={item => item.name || 'New Experience'}
          renderFields={renderFields}
          addButtonText="Add Another Item"
        />

        {validationError && <div className="text-sm text-red-500 mt-2">{validationError}</div>}

        <StepsNavigation
          backStep="involvement"
          isSubmitting={submitTeamSuccesses.isPending}
          showSkip={true}
          submitLabel="Next Step"
        />
      </form>
    </div>
  );
};
