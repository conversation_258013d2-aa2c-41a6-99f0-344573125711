import React, { useState } from 'react';
import { isAxiosError } from 'axios';
import { OnboardingSportsSelector } from '@/components/wizard/Steps/OnboardingSportsSelector';
import type { SportItem } from '@/components/wizard/Steps/OnboardingSportsSelector';
import StepsNavigation from '@/components/wizard/Steps/StepsNavigation';
import { usePositiveCoachOnboarding } from '@/hooks/usePositiveCoachOnboarding';
import type { Sport, SportsPayload } from '@/services/positiveAthleteOnboarding.service';
import { usePositiveAthleteOnboardingStore } from '@/stores/positiveAthleteOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';

interface CoachSportsStepProps {
  heading?: string;
  subHeading?: string;
  initialData?: {
    sports: Sport[];
  };
}

export const CoachSportsStep = ({ heading, subHeading, initialData }: CoachSportsStepProps) => {
  const { nextStep } = useWizardStore();
  const { setCurrentStep, setSports, sports: storedSports } = usePositiveAthleteOnboardingStore();
  const [validationError, setValidationError] = useState<string | null>(null);

  // Convert stored sports to SportItem format for SportSelector
  const [sports, setSportsLocal] = useState<SportItem[]>(() => {
    const sportsToUse = storedSports.sports || initialData?.sports || [];
    return sportsToUse.map((sport, index) => ({
      id: sport.id ? `${sport.id}` : `temp-${crypto.randomUUID()}`,
      name: sport.name || '',
      isCustom: false, // Only platform sports during onboarding
      sportId: sport.id,
      order: sport.order ?? index,
    }));
  });

  const { submitSports } = usePositiveCoachOnboarding({
    onSuccess: response => {
      if ('current_step' in response) {
        setCurrentStep(response.current_step);
        nextStep();
      }
    },
    onError: (error: unknown) => {
      console.error('Error submitting sports:', error);
      if (isAxiosError(error) && error.response?.data) {
        setValidationError(error.response.data.message || 'An error occurred');
      } else {
        setValidationError('An unexpected error occurred. Please try again.');
      }
    },
  });

  const validateForm = (): boolean => {
    // Clear any previous validation errors
    setValidationError(null);

    // For Next Step button, require at least one valid sport
    const validSports = sports.filter(item => !!item.sportId);

    if (validSports.length === 0) {
      setValidationError(
        'Please add at least one sport or use Skip if you prefer not to add any sports.'
      );
      return false;
    }

    // Check if any sports have missing sportId
    const hasEmptySports = sports.some(item => !item.sportId);

    if (hasEmptySports) {
      setValidationError('Please select sports from the dropdown or remove incomplete entries.');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Filter out incomplete sports before submitting
    const validSports = sports.filter(item => !!item.sportId);

    const apiSports: Sport[] = validSports.map((item, index) => ({
      id: item.sportId,
      name: item.name,
      order: index,
      // Explicitly use 0 instead of false to pass Laravel validation
      is_custom: false, // Type cast to any to avoid TypeScript errors
    }));

    // Update local store
    setSports({ sports: apiSports });

    // Submit to API
    submitSports.mutate({ sports: apiSports });
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-6">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">{heading}</h1>

        <p className="text-base md:text-body-lg text-gray-500">{subHeading}</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <OnboardingSportsSelector sports={sports} onChange={setSportsLocal} />

        {validationError && <div className="text-sm text-red-500 mt-2">{validationError}</div>}

        <StepsNavigation
          backStep="student_details"
          isSubmitting={submitSports.isPending}
          showSkip={true}
          submitLabel="Next Step"
        />
      </form>
    </div>
  );
};
