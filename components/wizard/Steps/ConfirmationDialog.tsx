import React from 'react';
import Image from 'next/image';
import { faArrowRight, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';

interface ConfirmationDialogProps {
  title?: string;
  description?: string;
  handleSubmit: () => void;
  handleClose: () => void;
  className?: string;
  children?: React.ReactNode;
}

export function ConfirmationDialog({
  title = 'Awesome!',
  description = 'Thanks for adding your details. You can always edit these later.',
  handleSubmit,
  handleClose,
  className = '',
  children,
}: ConfirmationDialogProps) {
  return (
    <div
      className={`relative p-4 w-full max-w-[500px] bg-white rounded-4xl shadow-lg lg:p-16 ${className}`}
    >
      {/* X Submit Button */}
      <button
        type="button"
        className="absolute top-6 right-6 text-text-primary hover:text-neutral-600 transition-colors"
        onClick={handleClose}
        aria-label="Close"
      >
        <FontAwesomeIcon icon={faXmark} className="size-4" aria-hidden="true" />
      </button>

      {/* Header */}
      <div className="block space-y-4 mb-10">
        <Image
          src="/images/positive-athlete.svg"
          alt="Positive Athlete"
          width={100}
          height={28}
          className="h-7 w-auto"
        />

        <h2 className="pt-4 text-xl font-bold text-text-primary">{title}</h2>
        <p className="text-sm text-text-secondary">{description}</p>
      </div>

      {children}

      <div className="flex gap-4 items-center">
        <Button size="small" icon={faArrowRight} onClick={handleSubmit}>
          Continue to App
        </Button>
      </div>
    </div>
  );
}

export default ConfirmationDialog;
