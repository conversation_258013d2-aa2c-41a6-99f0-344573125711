import React from 'react';
import { faArrowLeft, faArrowRight, faArrowRightToLine } from '@fortawesome/pro-light-svg-icons';
import Button from '@/components/shared/Button';
import { usePositiveAthleteOnboardingStore } from '@/stores/positiveAthleteOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';

interface StepsNavigationProps {
  backStep: string; // The step identifier to go back to
  isSubmitting: boolean;
  showSkip?: boolean; // Whether to show the Skip button
  onSubmit?: () => void; // Custom submit handler if needed
  submitLabel?: string;
}

export const StepsNavigation: React.FC<StepsNavigationProps> = ({
  backStep,
  isSubmitting,
  showSkip = false,
  onSubmit,
  submitLabel = 'Next Step',
}) => {
  const { nextStep, previousStep } = useWizardStore();
  const { setCurrentStep } = usePositiveAthleteOnboardingStore();

  const handleSkip = () => {
    // Skip just advances to the next step without submitting any data
    nextStep();
  };

  const handleBack = () => {
    setCurrentStep(backStep);
    previousStep();
  };

  return (
    <div className="flex justify-between pt-6">
      <div className="flex gap-4">
        <Button
          onClick={handleBack}
          variant="text"
          color="white"
          icon={faArrowLeft}
          iconPosition="left"
          type="button"
        >
          Back
        </Button>

        {showSkip && (
          <Button
            onClick={handleSkip}
            variant="text"
            icon={faArrowRightToLine}
            color="blue"
            type="button"
          >
            Skip
          </Button>
        )}
      </div>

      {onSubmit ? (
        <Button type="button" color="blue" size="small" disabled={isSubmitting} onClick={onSubmit}>
          {isSubmitting ? 'Saving...' : submitLabel}
        </Button>
      ) : (
        <Button type="submit" color="blue" size="small" icon={faArrowRight} disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : submitLabel}
        </Button>
      )}
    </div>
  );
};

export default StepsNavigation;
