import React, { useEffect, useState } from 'react';
import { faArrowLeft, faArrowRight } from '@fortawesome/pro-light-svg-icons';
import { isAxiosError } from 'axios';
import { EditDetailsModalSocialLinks as SocialLinksAccordion } from '@/components/profile/modals/EditDetailsModalSocialLinks';
import { UploadAvatarModal } from '@/components/profile/modals/UploadAvatarModal';
import Avatar from '@/components/shared/Avatar';
import Button from '@/components/shared/Button';
import ComboboxInput from '@/components/shared/form/ComboboxInput';
import SchoolComboboxInput from '@/components/shared/form/SchoolComboboxInput';
import SelectInput from '@/components/shared/form/SelectInput';
import type { SelectOption } from '@/components/shared/form/SelectInput';
import SiteInput from '@/components/shared/form/SiteInput';
import { useCounties } from '@/hooks/useCounties';
import { useFormErrors } from '@/hooks/useFormErrors';
import { usePositiveAthleteOnboarding } from '@/hooks/usePositiveAthleteOnboarding';
import type { County } from '@/services/county.service';
import type { Interest } from '@/services/interest.service';
import { ProfileDetails } from '@/services/positive-athlete-profile.service';
import type { StudentDetailsPayload } from '@/services/positiveAthleteOnboarding.service';
import type { School } from '@/services/school.service';
import { useModalStore } from '@/stores/modal.store';
import { usePositiveAthleteOnboardingStore } from '@/stores/positiveAthleteOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';
import { STATE_OPTIONS, type StateCode } from '@/utils/constants/states';
import { OnboardingInterestSelector } from './OnboardingInterestSelector';

// Extend StudentDetailsPayload to include profile_photo
interface ExtendedStudentDetails extends Partial<StudentDetailsPayload> {
  profile_photo?: string | null;
}

interface StudentDetailsStepProps {
  initialData?: {
    state: string;
    county: string;
    highSchool: string;
    school_id?: string;
    graduationYear: string;
    currentGpa: string;
    currentClassRank: string;
    gender: string;
    height: string;
    weight: string;
    careerInterests: string[];
    profile_photo?: string | null;
  };
}

interface ValidationErrors {
  message: string;
  errors: Record<string, string[]>;
}

// Placeholder options - replace with actual data
const GRADUATION_YEARS = Array.from({ length: 10 }, (_, i) => {
  const year = new Date().getFullYear() + i;
  return { value: year.toString(), label: year.toString() };
});

const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
  { value: 'prefer_not_to_say', label: 'Prefer not to say' },
];

export const StudentDetailsStep = ({ initialData }: StudentDetailsStepProps) => {
  const { setCurrentStep, studentDetails, setStudentDetails, accountInfo } =
    usePositiveAthleteOnboardingStore();
  const { nextStep, previousStep } = useWizardStore();
  const { open } = useModalStore();
  const { fieldErrors, setErrors, clearError, clearAllErrors } = useFormErrors();
  const {
    counties,
    isSearching: isLoadingCounties,
    setSearchInput: setCountySearchInput,
    setState: setCountyState,
  } = useCounties();
  const [selectedCounty, setSelectedCounty] = useState<County | null>(null);
  const [selectedInterests, setSelectedInterests] = useState<Interest[]>([]);

  // Store the avatar file for form submission
  const [avatarFile, setAvatarFile] = useState<File | null>(null);

  // Initialize avatarUrl from the store's profile_photo_url
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(
    studentDetails.profile_photo_url || undefined
  );

  // Effect to update local avatarUrl if store changes (e.g., after initialData processing)
  useEffect(() => {
    setAvatarUrl(studentDetails.profile_photo_url || undefined);
  }, [studentDetails.profile_photo_url]);

  const stateOptions = [...STATE_OPTIONS] as SelectOption[];

  const { submitDetails } = usePositiveAthleteOnboarding({
    onSuccess: response => {
      if ('current_step' in response) {
        setCurrentStep(response.current_step);
        nextStep();
      }
    },
    onError: (error: unknown) => {
      if (isAxiosError(error) && error.response?.data) {
        setErrors(error.response.data);
      }
    },
  });

  // Initialize form data from initialData prop
  useEffect(() => {
    // Only populate from initialData if the store's studentDetails for this step are not yet set (e.g., state is missing)
    if (initialData && !studentDetails.state) {
      // Prepare the student details for the store, matching StudentDetailsDTO
      const detailsForStore: Partial<
        StudentDetailsPayload & { profile_photo_url?: string | null }
      > = {
        state: initialData.state,
        county: initialData.county,
        high_school: initialData.highSchool,
        school_id: initialData.school_id,
        graduation_year: initialData.graduationYear,
        current_gpa: initialData.currentGpa,
        current_class_rank: initialData.currentClassRank,
        gender: initialData.gender,
        height: initialData.height,
        weight: initialData.weight,
        career_interests: initialData.careerInterests,
        profile_photo_url: initialData.profile_photo, // Use profile_photo_url for the preview
      };

      // Apply the details to the state
      setStudentDetails(detailsForStore);

      // If we have career interests, convert them to Interest objects
      if (initialData.careerInterests && initialData.careerInterests.length > 0) {
        // This is placeholder - you may need to fetch full Interest objects from the API
        const interests: Interest[] = initialData.careerInterests.map(id => ({
          id: Number(id),
          name: `Interest ${id}`, // This would be replaced with actual data
          icon: null,
        }));
        setSelectedInterests(interests);
      }
    }
    // Depend on initialData itself and a flag field from studentDetails like 'state'
    // to re-run if initialData changes and store hasn't been populated for this step.
  }, [initialData, studentDetails.state, setStudentDetails]); // Added setStudentDetails to dependency array for completeness

  // Set county state when studentDetails.state changes
  useEffect(() => {
    if (studentDetails.state) {
      setCountyState(studentDetails.state as StateCode);

      // Initial search to load counties when state is selected
      setCountySearchInput('');
    } else {
      // Reset county state when state is cleared
      setCountyState(null);
      setSelectedCounty(null);
    }
  }, [studentDetails.state, setCountyState, setCountySearchInput]);

  // Update selectedCounty when counties are loaded
  useEffect(() => {
    if (studentDetails.county && counties.length > 0) {
      const county = counties.find(c => c.id.toString() === studentDetails.county);
      if (county) {
        setSelectedCounty(county);
      }
    }
  }, [counties, studentDetails.county]);

  // Clear errors on unmount
  useEffect(() => {
    return () => {
      clearAllErrors();
    };
  }, [clearAllErrors]);

  const handleChange = (field: string) => (value: string | React.ChangeEvent<HTMLInputElement>) => {
    const newValue = typeof value === 'string' ? value : value.target.value;
    const mappedField =
      {
        graduationYear: 'graduation_year',
        currentGpa: 'current_gpa',
        currentClassRank: 'current_class_rank',
        careerInterests: 'career_interests',
      }[field] || field;

    // Clear error for this field
    clearError(mappedField);

    // If changing state, also reset county
    if (field === 'state') {
      setStudentDetails({
        ...studentDetails,
        [mappedField]: newValue,
        county: '', // Reset county when state changes
      });
      setSelectedCounty(null);
    } else {
      setStudentDetails({
        ...studentDetails,
        [mappedField]: newValue,
      });
    }
  };

  // Handle county selection
  const handleCountyChange = (county: County | null) => {
    setSelectedCounty(county);
    clearError('county');

    if (county) {
      setStudentDetails({
        ...studentDetails,
        county: county.id.toString(),
      });
    } else {
      setStudentDetails({
        ...studentDetails,
        county: '',
      });
    }
  };

  // Handle county search input changes
  const handleCountySearchChange = (query: string) => {
    setCountySearchInput(query);
  };

  // Handle school selection
  const handleSchoolChange = (schoolId: number | null) => {
    clearError('school_id');
    clearError('high_school');

    const schoolIdString = schoolId ? schoolId.toString() : '';

    setStudentDetails({
      ...studentDetails,
      school_id: schoolIdString,
      high_school: '', // Clear the text field as we're using the school_id now
    });
  };

  const handleInterestSelect = (interests: Interest[]) => {
    // Clear error for this field
    clearError('career_interests');

    setSelectedInterests(interests);
    setStudentDetails({
      ...studentDetails,
      career_interests: interests.map(i => i.id.toString()),
    });
  };

  const handleAvatarClick = () => {
    // Open the UploadAvatarModal component
    open(
      <UploadAvatarModal
        onSave={async (file: File) => {
          // Save the file for form submission instead of uploading immediately
          setAvatarFile(file);
          const url = URL.createObjectURL(file);
          setAvatarUrl(url);
          setStudentDetails({ ...studentDetails, profile_photo_url: url });
          return Promise.resolve({ success: true }); // Return success for compatibility
        }}
        onSuccess={result => {
          // Modal will close automatically on success
        }}
        onError={error => {
          console.error('Avatar upload error:', error);
          // Error will be displayed in the modal
        }}
      />,
      '2xl'
    );
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    clearAllErrors();

    // Ensure all required fields are present
    const requiredFields = [
      'state',
      'county',
      'school_id',
      'graduation_year',
      'current_gpa',
      'current_class_rank',
      'gender',
      'height',
      'weight',
      // 'career_interests', // Not required
    ] as const;

    const missingFields = requiredFields.filter(field => !studentDetails[field]);

    if (missingFields.length > 0) {
      setErrors({
        message: 'Please fill in all required fields',
        errors: missingFields.reduce(
          (acc, field) => ({
            ...acc,
            [field]: ['This field is required'],
          }),
          {}
        ),
      });
      return;
    }

    // Create a FormData object to handle file uploads
    const formData = new FormData();

    // Add all the regular fields to the FormData, excluding client-side only URLs or stale photo strings
    Object.entries(studentDetails).forEach(([key, value]) => {
      if (key === 'profile_photo_url') {
        // Exclude profile_photo_url, it's for client-side preview
        return;
      }
      // Exclude 'profile_photo' as well if it's coming from studentDetails;
      // it should only be added from avatarFile (the actual File object).
      if (key === 'profile_photo') {
        return;
      }

      if (value !== null && value !== undefined) {
        if (Array.isArray(value)) {
          // Handle arrays (like career_interests)
          value.forEach(item => formData.append(`${key}[]`, String(item)));
        } else {
          formData.append(key, String(value));
        }
      }
    });

    // Add the avatar file (File object) if we have one
    if (avatarFile) {
      formData.append('profile_photo', avatarFile);
    }
    // If avatarFile is null (e.g., after navigation and no re-upload),
    // 'profile_photo' will not be appended to FormData. This is correct.

    // Submit the FormData
    submitDetails.mutate(formData as any);
  };

  // Handle form submission - use onKeyDown to prevent Enter key from submitting the form
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Prevent Enter key from submitting the form
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-6">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">
          Step 2: Verify your Details
        </h1>

        <p className="text-base md:text-body-lg text-gray-500">
          Complete as many of these details as you&apos;re able. Each additional piece of
          information goes a long way toward qualifying you for awards and scholarships.
        </p>
      </div>

      {/* Avatar upload section */}
      <div className="py-4">
        <Avatar
          src={avatarUrl}
          firstName={accountInfo.first_name}
          lastName={accountInfo.last_name}
          size="xl"
          isUploadState={!avatarUrl}
          onClick={handleAvatarClick}
        />
      </div>

      <form className="space-y-6" onSubmit={handleSubmit} onKeyDown={handleKeyDown}>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SelectInput
              label="state"
              id="state"
              value={studentDetails.state || ''}
              onChange={handleChange('state')}
              options={stateOptions}
              isFailedValidation={Boolean(fieldErrors['state'])}
              description={fieldErrors['state']}
              aria-invalid={Boolean(fieldErrors['state'])}
              aria-errormessage={fieldErrors['state'] ? 'state-error' : undefined}
            />

            <ComboboxInput
              label="county"
              value={selectedCounty}
              onChange={handleCountyChange}
              options={counties}
              disabled={!studentDetails.state}
              placeholder={studentDetails.state ? 'Search for your county' : 'Select a state first'}
              onSearchChange={handleCountySearchChange}
              isLoading={isLoadingCounties}
              loadingText="Loading counties..."
              emptyText={'No counties found, try a different search term'}
              isFailedValidation={Boolean(fieldErrors['county'])}
              description={fieldErrors['county']}
              displayValue={county => county?.name || ''}
            />
          </div>

          <SchoolComboboxInput
            label="High School"
            value={studentDetails.school_id ? parseInt(studentDetails.school_id) : null}
            onChange={handleSchoolChange}
            countyId={selectedCounty?.id}
            stateCode={studentDetails.state as StateCode}
            disabled={!studentDetails.state || !studentDetails.county}
            placeholder={
              !studentDetails.state || !studentDetails.county
                ? 'Select a state and county first'
                : 'Search for your high school'
            }
            isFailedValidation={
              Boolean(fieldErrors['school_id']) || Boolean(fieldErrors['high_school'])
            }
            description={fieldErrors['school_id'] || fieldErrors['high_school']}
          />

          <SelectInput
            label="Graduation Year"
            id="graduationYear"
            value={studentDetails.graduation_year || ''}
            onChange={handleChange('graduationYear')}
            options={GRADUATION_YEARS}
            isFailedValidation={Boolean(fieldErrors['graduation_year'])}
            description={fieldErrors['graduation_year']}
            aria-invalid={Boolean(fieldErrors['graduation_year'])}
            aria-errormessage={fieldErrors['graduation_year'] ? 'graduation-year-error' : undefined}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SiteInput
              label="Current GPA"
              id="currentGpa"
              value={studentDetails.current_gpa || ''}
              onChange={handleChange('currentGpa')}
              isFailedValidation={Boolean(fieldErrors['current_gpa'])}
              description={fieldErrors['current_gpa']}
              aria-invalid={Boolean(fieldErrors['current_gpa'])}
              aria-errormessage={fieldErrors['current_gpa'] ? 'current-gpa-error' : undefined}
            />

            <SiteInput
              label="Current Class Rank"
              id="currentClassRank"
              value={studentDetails.current_class_rank || ''}
              onChange={handleChange('currentClassRank')}
              isFailedValidation={Boolean(fieldErrors['current_class_rank'])}
              description={fieldErrors['current_class_rank']}
              aria-invalid={Boolean(fieldErrors['current_class_rank'])}
              aria-errormessage={
                fieldErrors['current_class_rank'] ? 'current-class-rank-error' : undefined
              }
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <SelectInput
              label="Gender"
              id="gender"
              value={studentDetails.gender || ''}
              onChange={handleChange('gender')}
              options={GENDER_OPTIONS}
              isFailedValidation={Boolean(fieldErrors['gender'])}
              description={fieldErrors['gender']}
              aria-invalid={Boolean(fieldErrors['gender'])}
              aria-errormessage={fieldErrors['gender'] ? 'gender-error' : undefined}
            />

            <div>
              <label className="pa-eyebrow text-text-primary mb-2">HEIGHT</label>
              <div className="flex gap-2">
                <SiteInput
                  label="FEET"
                  hideLabel
                  type="number"
                  value={
                    studentDetails.height ? Math.floor(Number(studentDetails.height) / 12) : ''
                  }
                  onChange={e => {
                    const feet = parseInt(e.target.value || '0');
                    const inches = studentDetails.height ? Number(studentDetails.height) % 12 : 0;
                    setStudentDetails({
                      ...studentDetails,
                      height: (feet * 12 + inches).toString(),
                    });
                    clearError('height');
                  }}
                  min={3}
                  max={8}
                  placeholder="ft"
                  className="w-20"
                  isFailedValidation={Boolean(fieldErrors['height'])}
                />
                <SiteInput
                  label="INCHES"
                  hideLabel
                  type="number"
                  value={studentDetails.height ? Number(studentDetails.height) % 12 : ''}
                  onChange={e => {
                    const inches = parseInt(e.target.value || '0');
                    const feet = studentDetails.height
                      ? Math.floor(Number(studentDetails.height) / 12)
                      : 0;
                    setStudentDetails({
                      ...studentDetails,
                      height: (feet * 12 + inches).toString(),
                    });
                    clearError('height');
                  }}
                  min={0}
                  max={11}
                  placeholder="in"
                  className="w-20"
                  isFailedValidation={Boolean(fieldErrors['height'])}
                />
              </div>
              {fieldErrors['height'] && (
                <p className="mt-1 text-sm text-red-600" id="height-error">
                  {fieldErrors['height']}
                </p>
              )}
            </div>

            <SiteInput
              label="Weight"
              id="weight"
              value={studentDetails.weight || ''}
              onChange={handleChange('weight')}
              isFailedValidation={Boolean(fieldErrors['weight'])}
              placeholder="160 lbs."
              description={fieldErrors['weight']}
              aria-invalid={Boolean(fieldErrors['weight'])}
              aria-errormessage={fieldErrors['weight'] ? 'weight-error' : undefined}
            />
          </div>

          <div className="pb-4">
            <OnboardingInterestSelector
              selectedInterests={selectedInterests}
              onSelect={handleInterestSelect}
              label="Career Interests"
            />
            {fieldErrors['career_interests'] && (
              <p className="mt-1 text-sm text-red-600" id="career-interests-error">
                {fieldErrors['career_interests']}
              </p>
            )}
          </div>

          <SocialLinksAccordion
            formData={studentDetails as Partial<ProfileDetails>}
            onChange={handleChange}
          />
        </div>

        <div className="flex justify-between pt-6">
          <Button
            onClick={() => {
              setCurrentStep('account_info');
              previousStep();
            }}
            variant="text"
            color="blue"
            icon={faArrowLeft}
            iconPosition="left"
            type="button"
          >
            Back
          </Button>

          <Button
            type="submit"
            color="blue"
            size="small"
            className="px-6"
            icon={faArrowRight}
            disabled={submitDetails.isPending}
          >
            {submitDetails.isPending ? 'Saving...' : 'Next Step'}
          </Button>
        </div>
      </form>
    </div>
  );
};
