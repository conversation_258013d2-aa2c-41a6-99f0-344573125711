'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { faArrowLeft, faArrowRight } from '@fortawesome/pro-regular-svg-icons';
import { isAxiosError } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import Button from '@/components/shared/Button';
import { DynamicListEditor } from '@/components/shared/dynamic-list';
import type { DynamicListItem } from '@/components/shared/dynamic-list/types';
import SiteInput from '@/components/shared/form/SiteInput';
import { Textarea } from '@/components/shared/form/Textarea';
import ConfirmationDialog from '@/components/wizard/Steps/ConfirmationDialog';
import { useADOnboarding } from '@/hooks/useADOnboarding';
import type {
  ADOnboardingStepResponse,
  ADSchoolSuccessesPayload,
  SuccessItemStructure as ADSuccessItem,
} from '@/services/aDOnboarding.service';
import { useADOnboardingStore } from '@/stores/aDOnboardingStore';
import { useModalStore } from '@/stores/modal.store';
import { useWizardStore } from '@/stores/wizardStore';

// Adapter type for DynamicListEditor, matching ADSuccessItem structure
interface SuccessItemWithId extends Omit<ADSuccessItem, 'order'>, DynamicListItem {
  id: string; // DynamicListEditor requires a string id
}

interface ADSuccessStepProps {
  heading?: string;
  subHeading?: string;
  initialData?: {
    // Align with store structure
    successes: ADSuccessItem[];
  };
}

export const ADSuccessStep = ({
  heading = 'Step 5: List School Successes', // This step number might need adjustment based on actual flow
  subHeading = 'Highlight any significant wins or successes that your school has had.',
  initialData,
}: ADSuccessStepProps) => {
  const { previousStep, nextStep } = useWizardStore(); // Removed goToStep
  const {
    setCurrentStep,
    schoolSuccesses, // Use schoolSuccesses from store
    setSchoolSuccesses, // Use setSchoolSuccesses from store
    errors: apiErrors,
    setErrors: setApiErrors,
    clearErrors: clearApiErrors,
    reset: resetADOnboardingStore,
  } = useADOnboardingStore();
  const { open, close } = useModalStore();
  const router = useRouter(); // Import and use useRouter from next/navigation

  const [localValidationError, setLocalValidationError] = useState<string | null>(null);

  const [successItems, setSuccessItems] = useState<SuccessItemWithId[]>(() => {
    const itemsFromStore = schoolSuccesses.successes || [];
    const itemsFromProp = initialData?.successes || [];
    const initialItemsToUse = itemsFromStore.length > 0 ? itemsFromStore : itemsFromProp;
    return initialItemsToUse.map(
      (item: ADSuccessItem): SuccessItemWithId => ({
        id: (item as any).id?.toString() || uuidv4(),
        name: item.name,
        date_range: item.date_range,
        description: item.description,
      })
    );
  });

  // Effect to update local state if store changes externally
  useEffect(() => {
    const itemsFromStore = schoolSuccesses.successes || [];
    // Basic check to prevent overwriting user edits if component re-initializes or store updates
    if (
      itemsFromStore.length > 0 &&
      successItems.length === 0 &&
      JSON.stringify(itemsFromStore) !==
        JSON.stringify(
          successItems.map(s => ({
            name: s.name,
            date_range: s.date_range,
            description: s.description,
          }))
        )
    ) {
      setSuccessItems(
        itemsFromStore.map(
          (item: ADSuccessItem): SuccessItemWithId => ({
            id: (item as any).id?.toString() || uuidv4(),
            name: item.name,
            date_range: item.date_range,
            description: item.description,
          })
        )
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [schoolSuccesses.successes]); // Dependency: only on the successes array from store

  const handleMutationError = (error: unknown) => {
    if (isAxiosError(error) && error.response?.data) {
      setApiErrors(error.response.data);
      const errorMsg =
        error.response.data.message || 'An error occurred during the final steps of onboarding.';
      setLocalValidationError(errorMsg);
    } else {
      setLocalValidationError('An unexpected error occurred. Please try again.');
    }
  };

  const { submitADSchoolSuccesses, completeADOnboarding } = useADOnboarding({
    // Removed global onSuccess and onError, will be handled in .mutate() calls
  });

  const handleAdd = () => {
    const newSuccess: SuccessItemWithId = {
      id: uuidv4(),
      name: '',
      date_range: '',
      description: '',
    };
    setSuccessItems(prev => [...prev, newSuccess]);
    if (localValidationError) setLocalValidationError(null);
    if (apiErrors) clearApiErrors();
  };

  const handleRemove = (id: string) => {
    setSuccessItems(prev => prev.filter(item => item.id !== id));
  };

  const handleUpdate = (id: string, updates: Partial<Omit<SuccessItemWithId, 'id'>>) => {
    setSuccessItems(prev => prev.map(item => (item.id === id ? { ...item, ...updates } : item)));
    if (localValidationError) setLocalValidationError(null);
    if (apiErrors) clearApiErrors();
  };

  const handleReorder = (reorderedItems: SuccessItemWithId[]) => {
    setSuccessItems(reorderedItems);
  };

  const validateItems = (): boolean => {
    const hasIncompleteItems = successItems.some(
      item =>
        item.name.trim() === '' || item.date_range.trim() === '' || item.description.trim() === ''
    );
    if (hasIncompleteItems) {
      setLocalValidationError(
        'Please fill in all fields for each school success, or remove incomplete ones.'
      );
      return false;
    }
    setLocalValidationError(null);
    return true;
  };

  const handleSubmit = async (e?: React.FormEvent<HTMLFormElement>) => {
    e?.preventDefault();
    setLocalValidationError(null);
    clearApiErrors();

    const validSuccesses = successItems.filter(
      item => item.name.trim() && item.date_range.trim() && item.description.trim()
    );

    if (validSuccesses.length === 0 && successItems.length > 0) {
      setLocalValidationError(
        'Please complete at least one school success or remove all items to skip.'
      );
      return;
    }

    if (successItems.length > 0 && !validateItems()) return;

    const apiItems: ADSuccessItem[] = validSuccesses.map((item, index) => ({
      name: item.name,
      date_range: item.date_range,
      description: item.description,
      order: index,
    }));

    setSchoolSuccesses({ successes: apiItems });
    submitADSchoolSuccesses.mutate(
      { successes: apiItems },
      {
        onSuccess: dataFromSubmitSuccesses => {
          // After successfully submitting school successes, open confirmation dialog
          if (
            dataFromSubmitSuccesses &&
            dataFromSubmitSuccesses.data &&
            dataFromSubmitSuccesses.data.current_step === 'school_successes' &&
            !dataFromSubmitSuccesses.data.next_step
          ) {
            // Open confirmation dialog instead of immediately calling completeADOnboarding
            open(
              <ConfirmationDialog
                title="Awesome!"
                description="Thanks for adding your details. You can always edit these later."
                handleSubmit={() => {
                  close(); // Close modal immediately
                  completeADOnboarding.mutate(undefined, {
                    onSuccess: completionResponse => {
                      // Modal is already closed
                      resetADOnboardingStore();
                      if (completionResponse.data.redirect) {
                        router.push(completionResponse.data.redirect);
                      }
                    },
                    onError: err => {
                      handleMutationError(err);
                      // Modal is already closed. Error is handled by handleMutationError.
                    },
                  });
                }}
                handleClose={close}
              />
            );
          } else {
            // Handle unexpected response from submitADSchoolSuccesses if needed
            setLocalValidationError(
              'Could not finalize onboarding. Please try again or contact support.'
            );
          }
        },
        onError: error => {
          // Error from submitADSchoolSuccesses specifically
          handleMutationError(error);
          setLocalValidationError(
            'Failed to save school successes. Please check for errors and try again.'
          );
        },
      }
    );
  };

  const errorDisplay = localValidationError;

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-6">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">{heading}</h1>
        <p className="text-base md:text-body-lg text-gray-500">{subHeading}</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <DynamicListEditor<SuccessItemWithId>
          items={successItems}
          onAdd={handleAdd}
          onRemove={handleRemove}
          onUpdate={handleUpdate}
          onReorder={handleReorder}
          getTitle={item => item.name || 'New School Success'}
          renderFields={(item: SuccessItemWithId) => (
            <>
              <SiteInput
                label="Success/Achievement Name"
                value={item.name}
                onChange={e => handleUpdate(item.id, { name: e.target.value })}
                placeholder="e.g., State Champions, Mens Basketball"
              />
              <SiteInput
                label="Date or Range"
                type="text"
                value={item.date_range}
                onChange={e => handleUpdate(item.id, { date_range: e.target.value })}
                placeholder="e.g., 2022-2023"
              />
              <Textarea
                label="Description"
                value={item.description}
                onChange={e => handleUpdate(item.id, { description: e.target.value })}
                placeholder="Briefly describe this success"
                rows={4}
              />
            </>
          )}
          addButtonText="Add Another School Success"
        />

        {errorDisplay && <div className="text-sm text-red-500 mt-2">{errorDisplay}</div>}

        <div className="flex justify-between pt-6">
          <Button
            onClick={() => {
              previousStep();
            }}
            variant="text"
            color="blue"
            icon={faArrowLeft}
            iconPosition="left"
            type="button"
          >
            Back
          </Button>
          <Button
            type="submit"
            color="blue"
            size="small"
            className="px-6"
            icon={faArrowRight}
            disabled={submitADSchoolSuccesses.isPending}
          >
            {submitADSchoolSuccesses.isPending ? 'Saving...' : 'Finish Onboarding'}
          </Button>
        </div>
      </form>
    </div>
  );
};
