import React, { useEffect, useState } from 'react';
import { isAxiosError } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { DynamicListEditor } from '@/components/shared/dynamic-list';
import type { DynamicListItem } from '@/components/shared/dynamic-list/types';
import SiteInput from '@/components/shared/form/SiteInput';
import { Textarea } from '@/components/shared/form/Textarea';
import StepsNavigation from '@/components/wizard/Steps/StepsNavigation';
import { useADOnboarding } from '@/hooks/useADOnboarding';
import type {
  ADCommunityInvolvementPayload,
  InvolvementItemStructure as ADInvolvementItem,
  ADOnboardingStepResponse,
} from '@/services/aDOnboarding.service';
import { useADOnboardingStore } from '@/stores/aDOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';

// Adapter type for DynamicListEditor, ensuring it has an 'id' and matches ADInvolvementItem structure
interface InvolvementItemWithId extends Omit<ADInvolvementItem, 'order'>, DynamicListItem {
  id: string; // DynamicListEditor requires a string id
}

interface ADInvolvementStepProps {
  // Renamed from InvolvementStepProps for clarity
  heading?: string;
  subHeading?: string;
  initialData?: {
    // initialData should align with the store structure for this step
    items: ADInvolvementItem[];
  };
}

export const ADInvolvementStep = ({
  heading = 'Step 4: List your School / Community Involvement',
  subHeading = 'Highlight your school achievements and ways you&apos;ve positively engaged with your community.',
  initialData,
}: ADInvolvementStepProps) => {
  const { nextStep, previousStep } = useWizardStore(); // Added previousStep
  const {
    setCurrentStep,
    communityInvolvement, // Use communityInvolvement from store
    setCommunityInvolvement, // Use setCommunityInvolvement from store
    errors: apiErrors, // For displaying API errors
    setErrors: setApiErrors,
    clearErrors: clearApiErrors,
  } = useADOnboardingStore();

  const [localValidationError, setLocalValidationError] = useState<string | null>(null);

  // Initialize involvement items from store or initialData prop
  const [involvements, setInvolvements] = useState<InvolvementItemWithId[]>(() => {
    const itemsFromStore = communityInvolvement.items || [];
    const itemsFromProp = initialData?.items || [];
    // Prioritize store data if available
    const initialItemsToUse = itemsFromStore.length > 0 ? itemsFromStore : itemsFromProp;
    return initialItemsToUse.map(
      (item: ADInvolvementItem): InvolvementItemWithId => ({
        // Explicitly type item
        id: (item as any).id?.toString() || uuidv4(), // Handle if item already has an id, else generate
        name: item.name,
        date_range: item.date_range,
        description: item.description,
      })
    );
  });

  // Effect to update local state if store changes externally (e.g. initial load after hydration)
  useEffect(() => {
    const itemsFromStore = communityInvolvement.items || [];
    if (itemsFromStore.length > 0 && involvements.length === 0) {
      // Basic check to prevent overwriting user edits
      setInvolvements(
        itemsFromStore.map(
          (item: ADInvolvementItem): InvolvementItemWithId => ({
            id: (item as any).id?.toString() || uuidv4(),
            name: item.name,
            date_range: item.date_range,
            description: item.description,
          })
        )
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [communityInvolvement.items]); // Only depend on communityInvolvement.items

  const { submitADCommunityInvolvement } = useADOnboarding({
    onSuccess: (response: ADOnboardingStepResponse | { redirect?: string }) => {
      if ('current_step' in response && response.current_step) {
        setCurrentStep(response.current_step);
        clearApiErrors();
        setLocalValidationError(null);
        if (response.next_step) {
          nextStep();
        } else {
          console.log('AD Onboarding completed via Community Involvement step.');
        }
      }
    },
    onError: (error: unknown) => {
      if (isAxiosError(error) && error.response?.data) {
        setApiErrors(error.response.data);
        const errorMsg =
          error.response.data.message || 'An error occurred while saving involvement data.';
        setLocalValidationError(errorMsg);
      } else {
        setLocalValidationError('An unexpected error occurred. Please try again.');
      }
    },
  });

  const handleAdd = () => {
    const newInvolvement: InvolvementItemWithId = {
      id: uuidv4(),
      name: '',
      date_range: '',
      description: '',
    };
    setInvolvements(prev => [...prev, newInvolvement]);
    if (localValidationError) setLocalValidationError(null);
    if (apiErrors) clearApiErrors();
  };

  const handleRemove = (id: string) => {
    setInvolvements(prev => prev.filter(item => item.id !== id));
  };

  const handleUpdate = (id: string, updates: Partial<Omit<InvolvementItemWithId, 'id'>>) => {
    setInvolvements(prev => prev.map(item => (item.id === id ? { ...item, ...updates } : item)));
    if (localValidationError) setLocalValidationError(null);
    if (apiErrors) clearApiErrors();
  };

  const handleReorder = (reorderedItems: InvolvementItemWithId[]) => {
    setInvolvements(reorderedItems);
  };

  const validateItems = (): boolean => {
    const hasIncompleteItems = involvements.some(
      item =>
        item.name.trim() === '' || item.date_range.trim() === '' || item.description.trim() === ''
    );
    if (hasIncompleteItems) {
      setLocalValidationError(
        'Please fill in all fields for each involvement item, or remove incomplete ones.'
      );
      return false;
    }
    setLocalValidationError(null);
    return true;
  };

  const handleSubmit = async (e?: React.FormEvent<HTMLFormElement>) => {
    e?.preventDefault();
    setLocalValidationError(null);
    clearApiErrors();

    const validInvolvements = involvements.filter(
      item => item.name.trim() && item.date_range.trim() && item.description.trim()
    );

    if (validInvolvements.length === 0 && involvements.length > 0) {
      setLocalValidationError(
        'Please complete at least one involvement item or remove all items to skip.'
      );
      return;
    }
    if (involvements.length > 0 && !validateItems()) return;

    const apiItems: ADInvolvementItem[] = validInvolvements.map((item, index) => ({
      name: item.name,
      date_range: item.date_range,
      description: item.description,
      order: index,
    }));

    setCommunityInvolvement({ items: apiItems });
    submitADCommunityInvolvement.mutate({ items: apiItems });
  };

  const errorDisplay = localValidationError;

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-6">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">{heading}</h1>
        <p className="text-base md:text-body-lg text-gray-500">{subHeading}</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <DynamicListEditor<InvolvementItemWithId>
          items={involvements}
          onAdd={handleAdd}
          onRemove={handleRemove}
          onUpdate={handleUpdate}
          onReorder={handleReorder}
          getTitle={item => item.name || 'New Involvement'}
          renderFields={(item: InvolvementItemWithId) => (
            <>
              <SiteInput
                label="Activity Name"
                value={item.name}
                onChange={e => handleUpdate(item.id, { name: e.target.value })}
                placeholder="e.g., School Play Director, Food Bank Volunteer"
              />
              <SiteInput
                label="Date or Range"
                type="text"
                value={item.date_range}
                onChange={e => handleUpdate(item.id, { date_range: e.target.value })}
                placeholder="e.g., 2022-2023, Spring 2023"
              />
              <Textarea
                label="Description"
                value={item.description}
                onChange={e => handleUpdate(item.id, { description: e.target.value })}
                placeholder="Briefly describe this involvement"
                rows={4}
              />
            </>
          )}
          addButtonText="Add Another Involvement"
        />

        {errorDisplay && <div className="text-sm text-red-500 mt-2">{errorDisplay}</div>}

        <StepsNavigation
          backStep="bio" // Previous step is ADBioStep
          isSubmitting={submitADCommunityInvolvement.isPending}
          // showSkip={true} // This is the last data step, skip might mean finish with no involvement
          submitLabel={submitADCommunityInvolvement.isPending ? 'Saving...' : 'Next Step'} // Changed from 'Finish Onboarding'
        />
      </form>
    </div>
  );
};
