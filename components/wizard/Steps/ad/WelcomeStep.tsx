import { useState } from 'react';
import {
  faArrowRight,
  faBadgeCheck,
  faBriefcase,
  faGraduationCap,
  faUserCircle,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { isAxiosError } from 'axios';
import Button from '@/components/shared/Button';
import { useADOnboarding } from '@/hooks/useADOnboarding';
import type { ADOnboardingStepResponse } from '@/services/aDOnboarding.service';
import { useADOnboardingStore } from '@/stores/aDOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';

export const WelcomeStep = () => {
  const { nextStep } = useWizardStore();
  const { setCurrentStep, setAccountInfo, setErrors, clearErrors } = useADOnboardingStore();
  const [localApiMessage, setLocalApiMessage] = useState<string | null>(null);

  const { startADOnboarding } = useADOnboarding({
    onSuccess: (response: ADOnboardingStepResponse | { redirect?: string }) => {
      if ('current_step' in response && response.current_step) {
        setCurrentStep(response.current_step);
        if (response.prefill) {
          setAccountInfo(response.prefill);
        }
        clearErrors();
        setLocalApiMessage(null);
        nextStep();
      } else {
        console.error('Unexpected response from startADOnboarding', response);
        setLocalApiMessage('Failed to start onboarding. Unexpected response.');
      }
    },
    onError: (error: unknown) => {
      if (isAxiosError(error) && error.response?.data) {
        setErrors(error.response.data);
        setLocalApiMessage(
          error.response.data.message || 'An unexpected error occurred starting onboarding.'
        );
      } else {
        setLocalApiMessage('An unexpected error occurred. Please try again.');
      }
    },
  });

  const handleStart = () => {
    clearErrors();
    setLocalApiMessage(null);
    startADOnboarding.mutate();
  };

  const features = [
    {
      icon: faGraduationCap,
      title: 'Manage teams with access to X Factor character development resources',
    },
    {
      icon: faBadgeCheck,
      title: 'Track Positive Athlete nominations in your school',
    },
    {
      icon: faBriefcase,
      title: 'Monitor students&apos; learning engagement across teams and groups',
    },
    {
      icon: faUserCircle,
      title: 'Build a personal profile and highlight school accomplishments',
    },
  ];

  return (
    <div className="flex flex-col gap-6 md:gap-10">
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-text-primary">
          Welcome to the Positive Athlete App!
        </h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {features.map((feature, index) => (
          <div key={index} className="rounded-2xl bg-surface-secondary px-4 py-6 space-y-2 lg:p-6">
            <FontAwesomeIcon icon={feature.icon} className="!size-8 text-brand-red" />
            <p className="text-text-secondary text-sm md:text-base">{feature.title}</p>
          </div>
        ))}
      </div>

      <p className="text-text-secondary">Set up your account and build your profile!</p>

      {localApiMessage && <div className="text-sm text-red-500 mt-2">{localApiMessage}</div>}

      <div className="flex justify-end pt-4">
        <Button
          onClick={handleStart}
          color="blue"
          size="small"
          icon={faArrowRight}
          className="px-6"
          disabled={startADOnboarding.isPending}
        >
          {startADOnboarding.isPending ? 'Starting...' : "Let's Go!"}
        </Button>
      </div>
    </div>
  );
};
