import React, { useEffect, useState } from 'react';
import { faArrowLeft, faArrowRight } from '@fortawesome/pro-light-svg-icons'; // Assuming these are used in StepsNavigation
import { isAxiosError } from 'axios';
import { WysiwygEditor } from '@/components/shared/form/WysiwygEditor';
import StepsNavigation from '@/components/wizard/Steps/StepsNavigation';
import { useADOnboarding } from '@/hooks/useADOnboarding';
import type { ADBioPayload, ADOnboardingStepResponse } from '@/services/aDOnboarding.service';
import { useADOnboardingStore } from '@/stores/aDOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';

interface ADBioStepProps {
  heading?: string;
  subHeading?: string;
  initialData?: {
    content?: any;
  };
}

export const ADBioStep = ({
  heading = 'Step 3: Write Your Bio',
  subHeading = 'Quickly give a sense for who you are with a bio that speaks to your interests, background, qualities, and skills.',
  initialData,
}: ADBioStepProps) => {
  const { nextStep, previousStep } = useWizardStore();
  const {
    setCurrentStep,
    bioData,
    setBioData,
    errors: apiErrors,
    setErrors: setApiErrors,
    clearErrors: clearApiErrors,
  } = useADOnboardingStore();

  const [content, setContent] = useState<string>(bioData.content || initialData?.content || '');
  const [localValidationError, setLocalValidationError] = useState<string | null>(null);

  const { submitADBio } = useADOnboarding({
    onSuccess: (response: ADOnboardingStepResponse | { redirect?: string }) => {
      if ('current_step' in response && response.current_step) {
        setCurrentStep(response.current_step);
        clearApiErrors();
        setLocalValidationError(null);
        nextStep();
      }
    },
    onError: (error: unknown) => {
      if (isAxiosError(error) && error.response?.data) {
        const responseData = error.response.data as {
          message?: string;
          errors?: Record<string, string[]>;
        };
        setApiErrors({ errors: responseData.errors || {}, message: responseData.message });
        const contentErrorMessages = responseData.errors?.content
          ? responseData.errors.content.join(', ')
          : null;
        const errorMsg =
          responseData.message ||
          contentErrorMessages ||
          'An error occurred while saving your bio.';
        setLocalValidationError(errorMsg);
      } else {
        setLocalValidationError('An unexpected error occurred. Please try again.');
      }
    },
  });

  useEffect(() => {
    if (bioData.content !== content) {
      setBioData({ content });
    }
  }, [content, bioData.content, setBioData]);

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    if (localValidationError) setLocalValidationError(null);
    if (apiErrors?.content || (apiErrors && Object.keys(apiErrors || {}).length > 0)) {
      clearApiErrors();
    }
  };

  const handleSubmit = async (e?: React.FormEvent<HTMLFormElement>) => {
    e?.preventDefault();
    setLocalValidationError(null);
    clearApiErrors();

    if (!content.trim()) {
      setLocalValidationError('Bio content cannot be empty.');
      return;
    }

    const payload: ADBioPayload = {
      content,
    };

    submitADBio.mutate(payload);
  };

  const contentApiError = apiErrors?.content ? apiErrors.content.join(', ') : undefined;
  const errorDisplay = localValidationError || contentApiError;

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-6">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">{heading}</h1>
        <p className="text-base md:text-body-lg text-gray-500">{subHeading}</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <WysiwygEditor
          value={content}
          onChange={handleContentChange}
          placeholder="Your bio here..."
        />

        {errorDisplay && <div className="text-sm text-red-500 mt-2">{errorDisplay}</div>}

        <StepsNavigation
          backStep="details"
          isSubmitting={submitADBio.isPending}
          submitLabel="Next Step"
        />
      </form>
    </div>
  );
};
