import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  faArrowLeft,
  faArrowRight,
  faEnvelope,
  faMobileButton,
} from '@fortawesome/pro-regular-svg-icons';
import { isAxiosError } from 'axios';
import Button from '@/components/shared/Button';
import PasswordInput from '@/components/shared/form/PasswordInput';
import SelectInput from '@/components/shared/form/SelectInput';
import SiteInput from '@/components/shared/form/SiteInput';
import { useADOnboarding } from '@/hooks/useADOnboarding';
import { useFormErrors } from '@/hooks/useFormErrors';
import type {
  ADAccountInfoPayload,
  ADOnboardingStepResponse,
} from '@/services/aDOnboarding.service';
import { useADOnboardingStore } from '@/stores/aDOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';
import { STATE_OPTIONS } from '@/utils/constants/states';

interface AccountInfoStepProps {
  initialData?: {
    first_name?: string;
    last_name?: string;
    email?: string;
    phone?: string;
  };
}

interface FieldError {
  field: string;
  message: string;
}

interface ValidationErrors {
  message: string;
  errors: Record<string, string[]>;
}

export const ADAccountInfoStep = ({ initialData }: AccountInfoStepProps) => {
  const router = useRouter();
  const {
    setCurrentStep,
    accountInfo,
    setAccountInfo,
    errors: apiErrors,
    setErrors: setApiErrors,
    clearErrors: clearApiErrors,
  } = useADOnboardingStore();
  const { nextStep, previousStep } = useWizardStore();
  const [localFieldErrors, setLocalFieldErrors] = useState<Record<string, string[]>>({});
  const [localApiMessage, setLocalApiMessage] = useState<string | null>(null);

  const { submitADAccountInfo } = useADOnboarding({
    onSuccess: (response: ADOnboardingStepResponse | { redirect?: string }) => {
      if ('current_step' in response && response.current_step) {
        setCurrentStep(response.current_step);
        clearApiErrors();
        setLocalFieldErrors({});
        setLocalApiMessage(null);
        nextStep();
      }
    },
    onError: (error: unknown) => {
      if (isAxiosError(error) && error.response?.data) {
        setApiErrors(error.response.data);
        setLocalApiMessage(
          error.response.data.message || 'An error occurred. Please check your input.'
        );
      } else {
        setLocalApiMessage('An unexpected error occurred. Please try again.');
      }
    },
  });

  useEffect(() => {
    if (initialData && !accountInfo.first_name && !accountInfo.last_name) {
      setAccountInfo({
        first_name: initialData.first_name,
        last_name: initialData.last_name,
        email: initialData.email,
        phone: initialData.phone,
      });
    }
  }, [initialData, setAccountInfo, accountInfo.first_name, accountInfo.last_name]);

  useEffect(() => {
    return () => {
      setLocalFieldErrors({});
    };
  }, []);

  const handleChange =
    (field: keyof ADAccountInfoPayload | 'confirmPassword') =>
    (value: string | React.ChangeEvent<HTMLInputElement>) => {
      const newValue = typeof value === 'string' ? value : value.target.value;

      setLocalFieldErrors(prev => {
        const { [field as string]: _, ...rest } = prev;
        return rest;
      });

      if (localApiMessage) setLocalApiMessage(null);

      if (field === 'confirmPassword') {
        // Handle confirmPassword locally if needed, not part of accountInfo store slice directly
      } else {
        if (apiErrors?.errors?.[field as any]) {
          clearApiErrors();
        }

        setAccountInfo({
          ...accountInfo,
          [field as keyof ADAccountInfoPayload]: newValue,
        });
      }
    };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLocalFieldErrors({});
    setLocalApiMessage(null);
    clearApiErrors();

    const currentLocalValErrors: Record<string, string[]> = {};
    if (!accountInfo.first_name?.trim())
      currentLocalValErrors.first_name = ['First name is required'];
    if (!accountInfo.last_name?.trim()) currentLocalValErrors.last_name = ['Last name is required'];
    if (!accountInfo.email?.trim()) currentLocalValErrors.email = ['Email is required'];
    else if (!/\S+@\S+\.\S+/.test(accountInfo.email))
      currentLocalValErrors.email = ['Valid email is required'];
    if (!accountInfo.password) currentLocalValErrors.password = ['Password is required'];
    else if (accountInfo.password.length < 6)
      currentLocalValErrors.password = ['Password must be at least 6 characters'];

    if (Object.keys(currentLocalValErrors).length > 0) {
      setLocalFieldErrors(currentLocalValErrors);
      return;
    }

    const payload: ADAccountInfoPayload = {
      first_name: accountInfo.first_name || '',
      last_name: accountInfo.last_name || '',
      email: accountInfo.email || '',
      phone: accountInfo.phone || '',
      password: accountInfo.password || '',
      notification_email: accountInfo.notification_email || '',
    };
    submitADAccountInfo.mutate(payload);
  };

  const displayFieldErrors = (fieldName: keyof ADAccountInfoPayload): string | undefined => {
    const local = localFieldErrors[fieldName as string];
    const api = apiErrors?.errors?.[fieldName as any];
    let allErrors: string[] = [];
    if (local) allErrors = [...allErrors, ...local];

    if (api && Array.isArray(api)) {
      allErrors = [...allErrors, ...api];
    } else if (api) {
      allErrors.push(String(api));
    }

    return allErrors.length > 0 ? allErrors.join(', ') : undefined;
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-6">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">
          Step 1: Confirm your Account Info
        </h1>

        <p className="text-base md:text-body-lg text-gray-500">
          These details will be used for your account and notifications.
        </p>
      </div>

      <form className="space-y-6" onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SiteInput
              label="First Name"
              id="first_name"
              type="text"
              value={accountInfo.first_name || ''}
              onChange={handleChange('first_name')}
              isFailedValidation={Boolean(displayFieldErrors('first_name'))}
              description={displayFieldErrors('first_name')}
              required
            />

            <SiteInput
              label="Last Name"
              id="last_name"
              type="text"
              value={accountInfo.last_name || ''}
              onChange={handleChange('last_name')}
              isFailedValidation={Boolean(displayFieldErrors('last_name'))}
              description={displayFieldErrors('last_name')}
              required
            />
          </div>

          <SiteInput
            label="Account Email"
            id="email"
            type="email"
            value={accountInfo.email || ''}
            onChange={handleChange('email')}
            icon={faEnvelope}
            isFailedValidation={Boolean(displayFieldErrors('email'))}
            description={displayFieldErrors('email')}
            required
          />

          {/* <SiteInput
            label="Notification Email"
            id="notification_email"
            type="email"
            value={accountInfo.notification_email || ''}
            onChange={handleChange('notification_email')}
            icon={faEnvelope}
            isFailedValidation={Boolean(displayFieldErrors('notification_email'))}
            description={displayFieldErrors('notification_email')}
          /> */}

          <SiteInput
            label="Phone"
            id="phone"
            type="tel"
            value={accountInfo.phone || ''}
            onChange={handleChange('phone')}
            icon={faMobileButton}
            isFailedValidation={Boolean(displayFieldErrors('phone'))}
            description={displayFieldErrors('phone')}
            required
          />

          <PasswordInput
            label="Set a Password"
            id="password"
            value={accountInfo.password || ''}
            onChange={handleChange('password')}
            isFailedValidation={Boolean(displayFieldErrors('password'))}
            description={
              displayFieldErrors('password') || 'Password must be at least 6 characters long'
            }
            required
          />
        </div>

        {localApiMessage && <div className="text-sm text-red-500 mt-2 mb-4">{localApiMessage}</div>}

        <div className="flex justify-between pt-6">
          <Button
            onClick={() => {
              setCurrentStep('start');
              previousStep();
            }}
            variant="text"
            color="blue"
            icon={faArrowLeft}
            iconPosition="left"
            type="button"
          >
            Back
          </Button>

          <Button
            type="submit"
            color="blue"
            size="small"
            icon={faArrowRight}
            disabled={submitADAccountInfo.isPending}
          >
            {submitADAccountInfo.isPending ? 'Saving...' : 'Next Step'}
          </Button>
        </div>
      </form>
    </div>
  );
};
