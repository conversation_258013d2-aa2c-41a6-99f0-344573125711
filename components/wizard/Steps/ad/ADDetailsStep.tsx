import React, { useEffect, useState } from 'react';
import { faArrowLeft, faArrowRight } from '@fortawesome/pro-light-svg-icons';
import { isAxiosError } from 'axios';
import { EditDetailsModalSocialLinks as SocialLinksAccordion } from '@/components/profile/modals/EditDetailsModalSocialLinks';
import { UploadAvatarModal } from '@/components/profile/modals/UploadAvatarModal';
import Avatar from '@/components/shared/Avatar';
import Button from '@/components/shared/Button';
import ComboboxInput from '@/components/shared/form/ComboboxInput';
import SchoolComboboxInput from '@/components/shared/form/SchoolComboboxInput';
import SelectInput from '@/components/shared/form/SelectInput';
import type { SelectOption } from '@/components/shared/form/SelectInput';
import SiteInput from '@/components/shared/form/SiteInput';
import { useADOnboarding } from '@/hooks/useADOnboarding';
import { useCounties } from '@/hooks/useCounties';
import { useFormErrors } from '@/hooks/useFormErrors';
import type {
  ADDetailsFormData,
  ADDetailsPayload,
  ADOnboardingStepResponse,
} from '@/services/aDOnboarding.service';
import type { County } from '@/services/county.service';
import type { Interest } from '@/services/interest.service';
import { ProfileDetails } from '@/services/positive-athlete-profile.service';
import type { StudentDetailsPayload } from '@/services/positiveAthleteOnboarding.service';
import type { School } from '@/services/school.service';
import { useADOnboardingStore } from '@/stores/aDOnboardingStore';
import { useModalStore } from '@/stores/modal.store';
import { useWizardStore } from '@/stores/wizardStore';
import { STATE_OPTIONS, type StateCode } from '@/utils/constants/states';
import { OnboardingInterestSelector } from '../OnboardingInterestSelector';

// Extend StudentDetailsPayload to include profile_photo
interface ExtendedStudentDetails extends Partial<StudentDetailsPayload> {
  profile_photo?: string | null;
}

// Define a more specific type for the data this step manages, aligning with ADDetailsPayload fields
interface ADDetailsStepDataFromStore {
  title?: string;
  state?: string;
  county?: string;
  school_id?: number;
  twitter?: string;
  instagram?: string;
  facebook?: string;
  hudl?: string;
  custom_link?: string;
  profile_photo_url?: string | null;
}

interface ADDetailsStepProps {
  initialData?: Partial<ADDetailsStepDataFromStore>;
}

interface ValidationErrors {
  message: string;
  errors: Record<string, string[]>;
}

// Placeholder options - replace with actual data
const GRADUATION_YEARS = Array.from({ length: 10 }, (_, i) => {
  const year = new Date().getFullYear() + i;
  return { value: year.toString(), label: year.toString() };
});

const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
  { value: 'prefer_not_to_say', label: 'Prefer not to say' },
];

const AD_TITLE_OPTIONS: SelectOption[] = [
  { value: 'Athletic Director', label: 'Athletic Director' },
  { value: 'Assistant Athletic Director', label: 'Assistant Athletic Director' },
  { value: 'Athletic Secretary', label: 'Athletic Secretary' },
  { value: 'Other', label: 'Other' }, // Consider if 'Other' needs a text input
];

export const ADDetailsStep = ({ initialData }: ADDetailsStepProps) => {
  const {
    setCurrentStep,
    detailsData,
    setDetailsData,
    accountInfo,
    errors: apiErrors,
    setErrors: setApiErrors,
    clearErrors: clearApiErrors,
  } = useADOnboardingStore();
  const { nextStep, previousStep } = useWizardStore();
  const { open } = useModalStore();
  const [localFieldErrors, setLocalFieldErrors] = useState<Record<string, string[]>>({});
  const [localApiErrorMessages, setLocalApiErrorMessages] = useState<string | null>(null);

  const {
    counties,
    isSearching: isLoadingCounties,
    setSearchInput: setCountySearchInput,
    setState: setCountyState,
  } = useCounties();
  const [selectedCounty, setSelectedCounty] = useState<County | null>(null);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(
    detailsData.profile_photo_url || undefined
  );

  useEffect(() => {
    setAvatarUrl(detailsData.profile_photo_url || undefined);
  }, [detailsData.profile_photo_url]);

  const stateOptions = [...STATE_OPTIONS] as SelectOption[];

  const { submitADDetails } = useADOnboarding({
    onSuccess: (response: ADOnboardingStepResponse | { redirect?: string }) => {
      if ('current_step' in response && response.current_step) {
        setCurrentStep(response.current_step);
        clearApiErrors();
        setLocalFieldErrors({});
        setLocalApiErrorMessages(null);
        nextStep();
      }
    },
    onError: (error: unknown) => {
      if (isAxiosError(error) && error.response?.data) {
        setApiErrors(error.response.data);
        setLocalApiErrorMessages(
          error.response.data.message || 'An error occurred. Please check the fields.'
        );
      } else {
        setLocalApiErrorMessages('An unexpected error occurred. Please try again.');
      }
    },
  });

  useEffect(() => {
    if (initialData && !detailsData.title) {
      setDetailsData({
        title: initialData.title,
        state: initialData.state,
        county: initialData.county,
        school_id: initialData.school_id,
        twitter: initialData.twitter,
        instagram: initialData.instagram,
        facebook: initialData.facebook,
        hudl: initialData.hudl,
        custom_link: initialData.custom_link,
        profile_photo_url: initialData.profile_photo_url,
      });
    }
  }, [initialData, detailsData.title, setDetailsData]);

  useEffect(() => {
    if (detailsData.state) {
      setCountyState(detailsData.state as StateCode);
      setCountySearchInput('');
    } else {
      setCountyState(null);
      setSelectedCounty(null);
    }
  }, [detailsData.state, setCountyState, setCountySearchInput]);

  useEffect(() => {
    if (detailsData.county && counties.length > 0) {
      const county = counties.find(c => c.id.toString() === detailsData.county);
      if (county) setSelectedCounty(county);
      else setSelectedCounty(null);
    }
  }, [counties, detailsData.county]);

  useEffect(() => {
    return () => {
      setLocalFieldErrors({});
    };
  }, []);

  const clearLocalFieldError = (field: keyof ADDetailsPayload | string) => {
    setLocalFieldErrors(prev => {
      const { [field as string]: _, ...rest } = prev;
      return rest;
    });
  };

  const componentHandleChange =
    (field: keyof ADDetailsPayload | string) =>
    (value: string | React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const newValue = typeof value === 'string' ? value : value.target.value;
      clearLocalFieldError(field);
      if (localApiErrorMessages) setLocalApiErrorMessages(null);
      if (apiErrors?.errors?.[field as any]) clearApiErrors();

      if (field === 'state') {
        setDetailsData({ ...detailsData, [field]: newValue, county: '', school_id: undefined });
        setSelectedCounty(null);
      } else if (field === 'county') {
        setDetailsData({ ...detailsData, [field]: newValue, school_id: undefined });
      } else {
        setDetailsData({ ...detailsData, [field as keyof ADDetailsPayload]: newValue });
      }
    };

  const handleCountyChange = (countyObj: County | null) => {
    setSelectedCounty(countyObj);
    clearLocalFieldError('county');
    if (localApiErrorMessages) setLocalApiErrorMessages(null);
    if (apiErrors?.errors?.['county' as any]) clearApiErrors();
    setDetailsData({
      ...detailsData,
      county: countyObj ? countyObj.id.toString() : '',
      school_id: undefined,
    });
  };

  const handleSchoolChange = (schoolId: number | null) => {
    clearLocalFieldError('school_id');
    if (localApiErrorMessages) setLocalApiErrorMessages(null);
    if (apiErrors?.errors?.['school_id' as any]) clearApiErrors();
    setDetailsData({ ...detailsData, school_id: schoolId || undefined });
  };

  const handleAvatarClick = () => {
    open(
      <UploadAvatarModal
        onSave={async (file: File) => {
          // Save the file for form submission instead of uploading immediately
          setAvatarFile(file);
          const url = URL.createObjectURL(file);
          setAvatarUrl(url);
          setDetailsData({ ...detailsData, profile_photo_url: url });
          return Promise.resolve({ success: true }); // Return success for compatibility
        }}
        onSuccess={result => {
          // Modal will close automatically on success
        }}
        onError={error => {
          console.error('Avatar upload error:', error);
          // Error will be displayed in the modal
        }}
      />,
      '2xl'
    );
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLocalFieldErrors({});
    setLocalApiErrorMessages(null);
    clearApiErrors();

    const currentLocalValErrors: Record<string, string[]> = {};
    if (!detailsData.title?.trim()) currentLocalValErrors.title = ['Title is required'];
    if (!detailsData.state) currentLocalValErrors.state = ['State is required'];
    if (!detailsData.county) currentLocalValErrors.county = ['County is required'];
    if (!detailsData.school_id) currentLocalValErrors.school_id = ['School is required'];

    if (Object.keys(currentLocalValErrors).length > 0) {
      setLocalFieldErrors(currentLocalValErrors);
      return;
    }

    const formData = new FormData() as ADDetailsFormData;
    Object.entries(detailsData).forEach(([key, value]) => {
      if (key === 'profile_photo_url' || key === 'content') return;
      if (value !== null && value !== undefined && value !== '') {
        formData.append(key as keyof ADDetailsPayload, String(value));
      }
    });
    if (avatarFile) {
      formData.append('profile_photo', avatarFile);
    }

    submitADDetails.mutate(formData);
  };

  const displayFieldErrors = (fieldName: keyof ADDetailsPayload | string): string | undefined => {
    const local = localFieldErrors[fieldName as string];
    const api = apiErrors?.errors?.[fieldName as any];
    let allErrors: string[] = [];
    if (local) allErrors = [...allErrors, ...local];
    if (api && Array.isArray(api)) allErrors = [...allErrors, ...api];
    else if (api) allErrors.push(String(api));
    return allErrors.length > 0 ? allErrors.join(', ') : undefined;
  };

  const { school_id, ...detailsDataForSocial } = detailsData;
  const socialLinkAccordionData: Partial<ProfileDetails> = {
    ...detailsDataForSocial,
    school_id: detailsData.school_id,
  };

  const handleSocialLinksAccordionChange =
    (field: keyof ProfileDetails) => (event: React.ChangeEvent<HTMLInputElement>) => {
      componentHandleChange(field as string)(event.target.value);
    };

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-6">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">
          Step 2: Add Your Details
        </h1>
        <p className="text-base md:text-body-lg text-gray-500">
          Details you enter below will be visible to your athletes and community.
        </p>
      </div>

      <div className="py-4">
        <Avatar
          src={avatarUrl}
          firstName={accountInfo.first_name}
          lastName={accountInfo.last_name}
          size="xl"
          isUploadState={!avatarUrl}
          onClick={handleAvatarClick}
        />
      </div>

      <form className="space-y-6" onSubmit={handleSubmit}>
        {localApiErrorMessages && (
          <div className="text-sm text-red-500 mt-2 mb-4">{localApiErrorMessages}</div>
        )}
        <SelectInput
          label="Your Title"
          id="title"
          value={detailsData.title || ''}
          onChange={value => componentHandleChange('title')(value as string)}
          options={AD_TITLE_OPTIONS}
          isFailedValidation={Boolean(displayFieldErrors('title'))}
          description={displayFieldErrors('title')}
          required
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <SelectInput
            label="State"
            id="state"
            value={detailsData.state || ''}
            onChange={value => componentHandleChange('state')(value as string)}
            options={stateOptions}
            isFailedValidation={Boolean(displayFieldErrors('state'))}
            description={displayFieldErrors('state')}
            required
          />
          <ComboboxInput<any>
            label="County"
            value={selectedCounty || undefined}
            onChange={handleCountyChange}
            options={counties.map(c => ({ ...c, value: c.id.toString(), label: c.name }))}
            disabled={!detailsData.state}
            placeholder={detailsData.state ? 'Search for your county' : 'Select a state first'}
            onSearchChange={setCountySearchInput}
            isLoading={isLoadingCounties}
            loadingText="Loading counties..."
            emptyText={'No counties found, try a different search term'}
            isFailedValidation={Boolean(displayFieldErrors('county'))}
            description={displayFieldErrors('county')}
            displayValue={c => c?.name || ''}
          />
        </div>

        <SchoolComboboxInput
          label="High School"
          value={detailsData.school_id || null}
          onChange={handleSchoolChange}
          countyId={selectedCounty?.id}
          stateCode={detailsData.state as StateCode}
          disabled={!detailsData.state || !detailsData.county}
          placeholder={
            !detailsData.state || !detailsData.county
              ? 'Select a state and county first'
              : 'Search for your high school'
          }
          isFailedValidation={Boolean(displayFieldErrors('school_id'))}
          description={displayFieldErrors('school_id')}
        />

        <SocialLinksAccordion
          formData={socialLinkAccordionData}
          onChange={handleSocialLinksAccordionChange}
        />

        <div className="flex justify-between pt-6">
          <Button
            onClick={() => {
              setCurrentStep('account_info');
              previousStep();
            }}
            variant="text"
            color="blue"
            icon={faArrowLeft}
            iconPosition="left"
            type="button"
          >
            Back
          </Button>
          <Button
            type="submit"
            color="blue"
            size="small"
            className="px-6"
            icon={faArrowRight}
            disabled={submitADDetails.isPending}
          >
            {submitADDetails.isPending ? 'Saving...' : 'Next Step'}
          </Button>
        </div>
      </form>
    </div>
  );
};
