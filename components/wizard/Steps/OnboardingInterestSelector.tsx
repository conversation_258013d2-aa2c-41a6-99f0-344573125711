'use client';

import React, { useEffect, useState } from 'react';
import { faCheck, faPlus, faSearch, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Transition } from '@headlessui/react';
import Button from '@/components/shared/Button';
import SiteInput from '@/components/shared/form/SiteInput';
import { useInterests } from '@/hooks/useInterests';
import type { Interest } from '@/services/interest.service';
import { getInterestIcon } from '@/utils/icons';

interface OnboardingInterestSelectorProps {
  selectedInterests: Interest[];
  onSelect: (interests: Interest[]) => void;
  label?: string;
}

export function OnboardingInterestSelector({
  selectedInterests,
  onSelect,
  label = 'CAREER INTERESTS',
}: OnboardingInterestSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [localSelectedInterests, setLocalSelectedInterests] =
    useState<Interest[]>(selectedInterests);
  const { interests, isLoading, setSearchInput } = useInterests();

  useEffect(() => {
    setLocalSelectedInterests(selectedInterests);
  }, [selectedInterests]);

  useEffect(() => {
    setSearchInput(query);
  }, [query, setSearchInput]);

  const handleSelect = (interest: Interest) => {
    if (!localSelectedInterests.find(i => i.id === interest.id)) {
      const newInterests = [...localSelectedInterests, interest];
      setLocalSelectedInterests(newInterests);
      onSelect(newInterests);
    }
  };

  const handleRemove = (interestId: number, e?: React.MouseEvent) => {
    e?.stopPropagation(); // Prevent opening the selector when removing
    e?.preventDefault(); // Prevent form submission
    const newInterests = localSelectedInterests.filter(i => i.id !== interestId);
    setLocalSelectedInterests(newInterests);
    onSelect(newInterests);
  };

  const handleFieldClick = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent form submission
    setIsOpen(!isOpen);
  };

  return (
    <div className="onboarding-interest-selector">
      {label && <h3 className="pa-eyebrow text-gray-700 mb-1">{label}</h3>}

      {/* Main Display Field */}
      <div>
        <div
          className="flex items-center justify-between min-h-[48px] px-4 py-2 bg-white border border-neutral-200 rounded-lg hover:border-neutral-300 cursor-pointer transition-colors"
          onClick={handleFieldClick}
        >
          <div className="flex gap-2 flex-wrap">
            {localSelectedInterests.length === 0 ? (
              <span className="text-neutral-500">Select your career interests</span>
            ) : (
              localSelectedInterests.map(interest => (
                <span
                  key={interest.id}
                  className="inline-flex items-center gap-2 px-3 py-1 bg-brand-red font-semibold text-white rounded text-sm"
                >
                  <FontAwesomeIcon icon={getInterestIcon(interest.icon)} className="h-3 w-3" />
                  {interest.name}
                  <button
                    type="button" // Explicitly set type to prevent form submission
                    onClick={e => handleRemove(interest.id, e)}
                    className="text-white hover:text-white/80"
                  >
                    <FontAwesomeIcon icon={faXmark} className="h-3 w-3" />
                  </button>
                </span>
              ))
            )}
          </div>
          <FontAwesomeIcon
            icon={isOpen ? faXmark : faPlus}
            className="text-neutral-500 h-4 w-4 flex-shrink-0"
          />
        </div>

        {/* Selector Panel */}
        <Transition
          show={isOpen}
          enter="transition-all duration-200 ease-out"
          enterFrom="opacity-0 -translate-y-2"
          enterTo="opacity-100 translate-y-0"
          leave="transition-all duration-200 ease-out"
          leaveFrom="opacity-100 translate-y-0"
          leaveTo="opacity-0 -translate-y-2"
        >
          <div className="mt-2">
            {/* Search Input */}
            <SiteInput
              label="Search"
              type="search"
              icon={faSearch}
              placeholder="Search career interests..."
              value={query}
              onChange={e => setQuery(e.target.value)}
              hideLabel
            />

            {/* Results */}
            <div className="mt-2 max-h-[240px] overflow-auto">
              {(() => {
                console.log(
                  '[OnboardingInterestSelector] Rendering results - isLoading:',
                  isLoading,
                  'interests:',
                  interests
                );
                if (isLoading) {
                  return <div className="p-4 text-center text-neutral-500">Loading...</div>;
                }
                if (interests.length === 0) {
                  return <div className="p-4 text-center text-neutral-500">No results found</div>;
                }
                return (
                  <div className="space-y-1">
                    {interests.map(interest => (
                      <button
                        type="button" // Explicitly set type to prevent form submission
                        key={interest.id}
                        onClick={() => handleSelect(interest)}
                        className={`w-full flex items-center gap-3 px-4 py-2 text-left hover:bg-neutral-50 ${
                          localSelectedInterests.find(i => i.id === interest.id)
                            ? 'text-brand-blue font-medium'
                            : 'text-neutral-700'
                        }`}
                      >
                        <FontAwesomeIcon
                          icon={getInterestIcon(interest.icon)}
                          className="h-4 w-4"
                        />
                        {interest.name}
                        {localSelectedInterests.find(i => i.id === interest.id) && (
                          <FontAwesomeIcon icon={faCheck} className="h-4 w-4 ml-auto" />
                        )}
                      </button>
                    ))}
                  </div>
                );
              })()}
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3 mt-4 pt-4 border-t border-neutral-200">
              <Button
                color="white"
                size="small"
                onClick={e => {
                  e.preventDefault(); // Prevent form submission
                  setIsOpen(false);
                }}
                type="button" // Explicitly set type to prevent form submission
              >
                Cancel
              </Button>
              <Button
                color="blue"
                size="small"
                onClick={e => {
                  e.preventDefault(); // Prevent form submission
                  setIsOpen(false);
                }}
                type="button" // Explicitly set type to prevent form submission
              >
                Done
              </Button>
            </div>
          </div>
        </Transition>
      </div>
    </div>
  );
}
