import React, { useState } from 'react';
import { faArrowLeft, faArrowRight } from '@fortawesome/pro-light-svg-icons';
import { isAxiosError } from 'axios';
import { Richtext } from '@/components/shared/blocks/Richtext';
import Button from '@/components/shared/Button';
import { WysiwygEditor } from '@/components/shared/form/WysiwygEditor';
import { usePositiveAthleteOnboarding } from '@/hooks/usePositiveAthleteOnboarding';
import { useModalStore } from '@/stores/modal.store';
import { usePositiveAthleteOnboardingStore } from '@/stores/positiveAthleteOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';
import { ConfirmationDialog } from './ConfirmationDialog';

interface StoryStepProps {
  initialData?: {
    content: string;
  };
  nominationName?: string;
  nominationTitle?: string;
  nominationDate?: string;
  nominationDetails?: any;
}

export const StoryStep = ({
  initialData,
  nominationName,
  nominationTitle,
  nominationDate,
  nominationDetails,
}: StoryStepProps) => {
  const { previousStep } = useWizardStore();
  const {
    setCurrentStep,
    story,
    setStory,
    accountInfo,
    sports,
    studentDetails,
    involvement,
    workExperience,
  } = usePositiveAthleteOnboardingStore();
  const { open, close } = useModalStore();
  const [content, setContent] = useState<string>(story.content || initialData?.content || '');
  const [validationError, setValidationError] = useState<string | null>(null);

  const { submitStory, completeOnboarding } = usePositiveAthleteOnboarding({
    onSuccess: (response: any) => {
      // First check for redirect (onboarding completion) - should NOT show confirmation dialog
      if ('redirect' in response && typeof response.redirect === 'string' && response.redirect) {
        // Redirect happens after completeOnboarding
        window.location.href = response.redirect;
      }
      // Then check for 'current_step' (story submission success) - SHOULD show confirmation dialog
      else if ('current_step' in response) {
        setCurrentStep(response.current_step);
        // Show confirmation dialog after successful story submission
        showConfirmationDialog();
      }
      // Optional: Add a log for unexpected response structures if needed
      // else {
      //   console.warn('Unexpected API response structure in onboarding onSuccess:', response);
      // }
    },
    onError: (error: unknown) => {
      console.error('Error in onboarding step:', error);
      if (isAxiosError(error) && error.response?.data) {
        console.error('API error details:', error.response.data);
        setValidationError(error.response.data.message || 'An error occurred');
      } else {
        setValidationError('An unexpected error occurred. Please try again.');
      }
    },
  });

  // Handler for WysiwygEditor changes
  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    setStory({ content: newContent }); // Update store on every change
    if (validationError) {
      setValidationError(null); // Clear validation error on input change
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Content is already up-to-date in the store due to handleContentChange
    // We still call setStory here to ensure the most recent local state (if any divergence) is captured before API call
    // but primarily, the store should be current.
    setStory({ content });
    submitStory.mutate({ content });
  };

  const saveStoryAndShowConfirmation = () => {
    // Content is already up-to-date in the store
    // Similar to handleSubmit, ensure store has the latest local state before API call.
    setStory({ content });
    submitStory.mutate({ content });
  };

  const showConfirmationDialog = () => {
    // Get primary sport if available
    const primarySport = sports.sports && sports.sports.length > 0 ? sports.sports[0].name : '';

    open(
      <ConfirmationDialog
        description={`Thanks for adding your details. You can always edit these later. Here's ${nominationName}'s nomination!`}
        handleSubmit={handleFinishOnboarding}
        handleClose={close}
      >
        <div className="bg-surface-secondary p-6 rounded-2xl mb-8">
          <div className="mb-4">
            {nominationName && (
              <h3 className="text-2xl font-bold text-text-primary">{nominationName}</h3>
            )}
            {nominationTitle && <p className="pa-eyebrow text-brand-red mt-2">{nominationTitle}</p>}
            {nominationDate && (
              <p className="text-sm font-bold text-text-secondary mt-2">{nominationDate}</p>
            )}
          </div>
          {nominationDetails && (
            <div className="mb-4">
              <Richtext content={nominationDetails} className="text-text-secondary" />
            </div>
          )}
        </div>
      </ConfirmationDialog>
    );
  };

  const handleFinishOnboarding = () => {
    // Close the modal first
    close();

    // Call completeOnboarding with the accountInfo
    if (accountInfo.first_name && accountInfo.last_name && accountInfo.email) {
      // Create the basic payload required by the API
      const payload = {
        // Account Info - only include fields expected by AccountInfoPayload
        first_name: accountInfo.first_name,
        last_name: accountInfo.last_name,
        email: accountInfo.email,
        phone: accountInfo.phone || '',
        street_address: accountInfo.street_address || '',
        unit: accountInfo.unit || '',
        city: accountInfo.city || '',
        state: accountInfo.state || '',
        zip_code: accountInfo.zip_code || '',
        password: accountInfo.password || '',
      };

      completeOnboarding.mutate(payload);
      // Note: redirection will happen in the onSuccess callback in usePositiveAthleteOnboarding
      // after storage has been cleared
    } else {
      setValidationError('Missing required account information. Please go back to step 1.');
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-6">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">
          Step 6: Write your Story
        </h1>

        <p className="text-base md:text-body-lg text-gray-500">
          Quickly give a sense for who you are with a bio that speaks to your interests, background,
          qualities, and skills.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <WysiwygEditor
          value={content}
          onChange={handleContentChange}
          placeholder="Your story here"
        />

        {validationError && <div className="text-sm text-red-500 mt-2">{validationError}</div>}

        <div className="flex justify-between pt-6">
          <Button
            onClick={() => {
              setCurrentStep('work_experience');
              previousStep();
            }}
            variant="text"
            color="blue"
            icon={faArrowLeft}
            iconPosition="left"
            type="button"
          >
            Back
          </Button>

          <Button
            type="button"
            color="blue"
            size="small"
            icon={faArrowRight}
            className="px-6"
            disabled={submitStory.isPending || completeOnboarding.isPending}
            onClick={saveStoryAndShowConfirmation}
          >
            {submitStory.isPending || completeOnboarding.isPending ? 'Saving...' : 'Finish'}
          </Button>
        </div>
      </form>
    </div>
  );
};
