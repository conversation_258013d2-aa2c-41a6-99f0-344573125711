import React, { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { faArrowLeft, faArrowRight } from '@fortawesome/pro-light-svg-icons';
import { faGlobe, faPlus, faSearch } from '@fortawesome/pro-regular-svg-icons';
import { faTrash } from '@fortawesome/pro-solid-svg-icons';
import { isAxiosError } from 'axios';
import { UploadAvatarModal } from '@/components/profile/modals/UploadAvatarModal';
import Avatar from '@/components/shared/Avatar';
import Button from '@/components/shared/Button';
import ComboboxInput from '@/components/shared/form/ComboboxInput';
import SiteInput from '@/components/shared/form/SiteInput';
import { WysiwygEditor } from '@/components/shared/form/WysiwygEditor';
import { useFormErrors } from '@/hooks/useFormErrors';
import { useSponsorOnboarding } from '@/hooks/useSponsorOnboarding';
import type {
  ApiErrorResponse,
  SponsorOrganizationInfoPayload,
} from '@/services/sponsorOnboarding.service';
import { useModalStore } from '@/stores/modal.store';
import { useSponsorOnboardingStore } from '@/stores/sponsorOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';
import type { OrganizationSearchResultDTO } from '@/types/organization';

// DEPRECATED: This step is no longer used.
export const SponsorDetailsStep = () => {
  // const router = useRouter();
  // const { previousStep, nextStep } = useWizardStore();
  // const { open } = useModalStore();
  // const { fieldErrors, setErrors, clearError, clearAllErrors } = useFormErrors();

  // // Use the sponsor onboarding store instead of local state
  // // const { organizationInfo, setOrganizationInfo } = useSponsorOnboardingStore();

  // // Destructure values from the store with fallbacks
  // const mode = organizationInfo.mode || 'search';
  // const selectedOrganization = organizationInfo.selectedOrganizationId
  //   ? ({
  //       id:
  //         typeof organizationInfo.selectedOrganizationId === 'string'
  //           ? parseInt(organizationInfo.selectedOrganizationId)
  //           : organizationInfo.selectedOrganizationId,
  //       name: organizationInfo.organization_name || '',
  //     } as OrganizationSearchResultDTO)
  //   : null;
  // const organizationName = organizationInfo.organization_name || '';
  // const organizationWebsite = organizationInfo.organization_website || '';
  // const organizationAbout = organizationInfo.organization_about || '';
  // const logoPreviewUrl = organizationInfo.logoPreviewUrl || null;
  // // We can't store the File object in the store, so we'll keep that in local state
  // const [logoFile, setLogoFile] = useState<File | null>(null);

  // const fallbackFirstName = 'Organization';
  // const fallbackLastName = 'Logo';

  // const {
  //   submitOrganizationInfo,
  //   completeOnboarding,
  //   setSearchQueryInput,
  //   searchResults,
  //   isSearching,
  //   searchError,
  // } = useSponsorOnboarding({});

  // useEffect(() => {
  //   return () => {
  //     clearAllErrors();
  //   };
  // }, [clearAllErrors]);

  // const handleModeChange = (newMode: 'search' | 'create') => {
  //   setOrganizationInfo({ mode: newMode });
  //   clearAllErrors();

  //   if (newMode === 'search') {
  //     setOrganizationInfo({
  //       organization_name: '',
  //       organization_website: '',
  //       organization_about: '',
  //       logoPreviewUrl: null,
  //     });
  //     setLogoFile(null);
  //   } else {
  //     setSearchQueryInput('');
  //     setOrganizationInfo({ selectedOrganizationId: null });
  //   }
  // };

  // const handleAvatarClick = () => {
  //   open(
  //     <UploadAvatarModal
  //       onSave={file => {
  //         setLogoFile(file);
  //         const url = URL.createObjectURL(file);
  //         setOrganizationInfo({ logoPreviewUrl: url });
  //         clearError('organization_logo');
  //         return () => URL.revokeObjectURL(url);
  //       }}
  //     />,
  //     '2xl'
  //   );
  // };

  // const handleSearchSelection = (org: OrganizationSearchResultDTO | null) => {
  //   if (org) {
  //     setOrganizationInfo({
  //       selectedOrganizationId: typeof org.id === 'string' ? parseInt(org.id) : org.id,
  //       organization_name: org.name,
  //     });
  //   } else {
  //     setOrganizationInfo({
  //       selectedOrganizationId: null,
  //       organization_name: '',
  //     });
  //   }
  //   clearError('organization_id');
  // };

  // const handleSubmitAndComplete = async (e: React.FormEvent<HTMLFormElement>) => {
  //   e.preventDefault();
  //   clearAllErrors();

  //   let payload: SponsorOrganizationInfoPayload | FormData;

  //   if (mode === 'search') {
  //     if (!selectedOrganization) {
  //       setErrors({
  //         message: 'Please select an organization or create a new one.',
  //         errors: { organization_id: ['Selection required.'] },
  //       });
  //       return;
  //     }
  //     payload = {
  //       organization_id: selectedOrganization.id,
  //       organization_name: selectedOrganization.name,
  //       organization_website: null,
  //       organization_about: null,
  //       organization_logo: null,
  //     };
  //   } else {
  //     if (!organizationName.trim()) {
  //       setErrors({
  //         message: 'Validation failed',
  //         errors: { organization_name: ['Organization name is required.'] },
  //       });
  //       return;
  //     }
  //     if (logoFile) {
  //       payload = new FormData();
  //       payload.append('organization_name', organizationName);
  //       payload.append('organization_logo', logoFile);
  //       if (organizationWebsite) payload.append('organization_website', organizationWebsite);
  //       if (organizationAbout) payload.append('organization_about', organizationAbout);
  //     } else {
  //       payload = {
  //         organization_name: organizationName,
  //         organization_website: organizationWebsite || null,
  //         organization_about: organizationAbout || null,
  //         organization_logo: null,
  //       };
  //     }
  //   }

  //   submitOrganizationInfo.mutate(payload, {
  //     onSuccess: response => {
  //       console.log('Organization Info Submitted Successfully:', response);
  //       completeOnboarding.mutate(undefined, {
  //         onSuccess: completeResponse => {
  //           console.log('Onboarding Completed Successfully:', completeResponse);
  //           // Redirect is handled by the router instead of window.location
  //           if (completeResponse.data.redirect) {
  //             router.push(completeResponse.data.redirect);
  //           }
  //         },
  //         onError: completeError => {
  //           console.error('Error completing onboarding:', completeError);

  //           // Check if it's a 409 Conflict (user exists but not an error)
  //           if (isAxiosError(completeError) && completeError.response?.status === 409) {
  //             // This is actually a success case - user already exists
  //             // We need to type-check the data more carefully
  //             const responseData = completeError.response.data as { redirect?: string };
  //             if (responseData.redirect) {
  //               router.push(responseData.redirect);
  //               return;
  //             }
  //           }

  //           // Handle actual errors
  //           let message = 'Failed to complete onboarding after saving details.';
  //           if (isAxiosError(completeError) && completeError.response?.data) {
  //             const errorData = completeError.response.data as ApiErrorResponse;
  //             if (errorData.error) {
  //               message = errorData.error;
  //             }
  //           }
  //           setErrors({ message, errors: { _general: [message] } });
  //         },
  //       });
  //     },
  //     onError: error => {
  //       console.error('Error submitting organization info:', error);
  //       if (isAxiosError(error) && error.response?.data) {
  //         // Check for new error format
  //         const errorData = error.response.data as ApiErrorResponse;
  //         if (errorData.error) {
  //           setErrors({
  //             message: errorData.error,
  //             errors: errorData.errors || {},
  //           });
  //         } else {
  //           // Fall back to old format or custom handling
  //           setErrors({
  //             message: 'Failed to save organization details',
  //             errors: {},
  //           });
  //         }
  //       } else {
  //         setErrors({
  //           message: 'An unexpected error occurred while saving organization details.',
  //           errors: {},
  //         });
  //       }
  //     },
  //   });
  // };

  // return (
  //   <div className="flex flex-col gap-6">
  //     <div className="space-y-6">
  //       <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">
  //         Link or Create your Organization
  //       </h1>
  //       <p className="text-base md:text-body-lg text-gray-500">
  //         Search for your organization below. If you don&apos;t find it, you can create a new
  //         profile for it.
  //       </p>
  //     </div>

  //     <div className="flex gap-2 border-b border-gray-200 pb-2 mb-4">
  //       <Button
  //         variant={mode === 'search' ? 'filled' : 'text'}
  //         color="blue"
  //         icon={faSearch}
  //         onClick={() => handleModeChange('search')}
  //         size="small"
  //       >
  //         Search Existing
  //       </Button>
  //       <Button
  //         variant={mode === 'create' ? 'filled' : 'text'}
  //         color="blue"
  //         icon={faPlus}
  //         onClick={() => handleModeChange('create')}
  //         size="small"
  //       >
  //         Create New
  //       </Button>
  //     </div>

  //     <form onSubmit={handleSubmitAndComplete} className="space-y-6">
  //       {mode === 'search' && (
  //         <div className="space-y-4">
  //           <ComboboxInput
  //             label="Search for your organization"
  //             value={selectedOrganization}
  //             onChange={handleSearchSelection}
  //             options={searchResults?.data || []}
  //             displayValue={(org: OrganizationSearchResultDTO | null) => org?.name || ''}
  //             onSearchChange={setSearchQueryInput}
  //             isLoading={isSearching}
  //             loadingText="Searching..."
  //             emptyText={
  //               !isSearching && searchResults?.data?.length === 0
  //                 ? 'No organizations found.'
  //                 : 'Type to search...'
  //             }
  //             placeholder="Enter organization name..."
  //             isFailedValidation={Boolean(fieldErrors['organization_id'])}
  //             description={
  //               fieldErrors['organization_id'] ||
  //               (searchError ? 'Error searching organizations' : undefined)
  //             }
  //           />
  //           {selectedOrganization && (
  //             <div className="p-3 bg-gray-50 rounded border border-gray-200">
  //               <p className="font-medium text-gray-700">Selected: {selectedOrganization.name}</p>
  //             </div>
  //           )}
  //         </div>
  //       )}

  //       {mode === 'create' && (
  //         <div className="space-y-6">
  //           <SiteInput
  //             label="Organization Name"
  //             id="organization_name"
  //             value={organizationName}
  //             onChange={e => {
  //               setOrganizationInfo({ organization_name: e.target.value });
  //               clearError('organization_name');
  //             }}
  //             isFailedValidation={Boolean(fieldErrors['organization_name'])}
  //             description={fieldErrors['organization_name']}
  //             required
  //           />
  //           <div className="py-4">
  //             <label className="block text-sm font-medium text-gray-700 mb-1">
  //               Organization Logo (Optional)
  //             </label>
  //             <Avatar
  //               src={logoPreviewUrl ?? undefined}
  //               firstName={fallbackFirstName}
  //               lastName={fallbackLastName}
  //               size="xl"
  //               isUploadState={!logoPreviewUrl}
  //               onClick={handleAvatarClick}
  //               placeholderContent={
  //                 <>
  //                   {' '}
  //                   Upload Organization <br /> Logo{' '}
  //                 </>
  //               }
  //             />
  //             {fieldErrors['organization_logo'] && (
  //               <p className="text-sm text-red-500 mt-1" id="logo-error">
  //                 {' '}
  //                 {fieldErrors['organization_logo']}{' '}
  //               </p>
  //             )}

  //             <Button
  //               variant="text"
  //               color="red"
  //               size="small"
  //               icon={faTrash}
  //               className="mt-2"
  //               onClick={() => setOrganizationInfo({ logoPreviewUrl: null })}
  //             >
  //               Delete
  //             </Button>
  //           </div>
  //           <WysiwygEditor
  //             label="About your organization"
  //             value={organizationAbout}
  //             onChange={value => {
  //               setOrganizationInfo({ organization_about: value });
  //               clearError('organization_about');
  //             }}
  //             placeholder="Provide details about your organization..."
  //           />
  //           <SiteInput
  //             type="url"
  //             label="Website URL (Optional)"
  //             id="organization_website"
  //             value={organizationWebsite}
  //             icon={faGlobe}
  //             onChange={e => {
  //               setOrganizationInfo({ organization_website: e.target.value });
  //               clearError('organization_website');
  //             }}
  //             isFailedValidation={Boolean(fieldErrors['organization_website'])}
  //             description={fieldErrors['organization_website']}
  //           />
  //         </div>
  //       )}

  //       {fieldErrors['_general'] && (
  //         <div className="text-sm text-red-500 mt-2">{fieldErrors['_general']}</div>
  //       )}
  //       {fieldErrors.message && Object.keys(fieldErrors.errors || {}).length === 0 && (
  //         <div className="text-sm text-red-500 mt-2">{fieldErrors.message}</div>
  //       )}

  //       <div className="flex justify-between pt-6">
  //         <Button
  //           onClick={previousStep}
  //           variant="text"
  //           color="blue"
  //           icon={faArrowLeft}
  //           iconPosition="left"
  //           type="button"
  //           disabled={submitOrganizationInfo.isPending || completeOnboarding.isPending}
  //         >
  //           Back
  //         </Button>
  //         <Button
  //           type="submit"
  //           color="blue"
  //           size="small"
  //           icon={faArrowRight}
  //           className="px-6"
  //           disabled={submitOrganizationInfo.isPending || completeOnboarding.isPending}
  //         >
  //           {submitOrganizationInfo.isPending || completeOnboarding.isPending
  //             ? 'Saving...'
  //             : 'Finish Onboarding'}
  //         </Button>
  //       </div>
  //     </form>
  //   </div>
  // );
  return <></>;
};
