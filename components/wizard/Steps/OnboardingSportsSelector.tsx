'use client';

import React, { useEffect, useRef, useState } from 'react';
import {
  Combobox,
  ComboboxOption,
  ComboboxOptions,
  ComboboxInput as HeadlessComboboxInput,
} from '@headlessui/react';
import debounce from 'lodash/debounce';
import { DynamicListEditor } from '@/components/shared/dynamic-list';
import type { DynamicListItem } from '@/components/shared/dynamic-list/types';
import SiteInput from '@/components/shared/form/SiteInput';
import { useSports } from '@/hooks/useSports';
import type { Sport } from '@/services/positive-athlete-profile.service';

/**
 * Represents a sport item in the editor, combining both platform and custom sports
 * with consistent order handling across both types.
 */
export interface SportItem extends DynamicListItem {
  name: string;
  icon?: string;
  isCustom: boolean;
  customName?: string;
  sportId?: number;
  order: number;
}

interface OnboardingSportSelectorProps {
  sports: SportItem[];
  onChange: (sports: SportItem[]) => void;
  className?: string;
}

export function OnboardingSportsSelector({
  sports,
  onChange,
  className = '',
}: OnboardingSportSelectorProps) {
  const [activeSearchId, setActiveSearchId] = useState<string | null>(null);
  const [inputValues, setInputValues] = useState<Record<string, string>>({});
  const [searchQuery, setSearchQuery] = useState('');

  // Use the sports hook at the top level of the component
  const { sports: searchResults, isLoading: isSearching, setSearchInput } = useSports();

  // Create a debounced search function
  const debouncedSearch = useRef(
    debounce((query: string) => {
      if (query.length < 2) {
        setSearchQuery('');
        return;
      }

      setSearchQuery(query);
      setSearchInput(query);
    }, 300)
  ).current;

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const handleAdd = (e?: React.MouseEvent) => {
    // Prevent form submission
    e?.preventDefault();

    const newSport: SportItem = {
      id: crypto.randomUUID(),
      name: '',
      isCustom: false,
      order: sports.length,
    };
    onChange([...sports, newSport]);
  };

  const handleRemove = (id: string) => {
    onChange(sports.filter(item => item.id !== id));
  };

  const handleUpdate = (id: string, updates: Partial<Omit<SportItem, 'id'>>) => {
    onChange(sports.map(item => (item.id === id ? { ...item, ...updates } : item)));
  };

  const handleReorder = (reorderedItems: DynamicListItem[]) => {
    onChange(
      reorderedItems.map((item, index) => {
        const sport = sports.find(s => s.id === item.id);
        if (!sport) return sport!;
        return { ...sport, order: index };
      })
    );
  };

  const handleInputChange = (id: string, value: string) => {
    setInputValues(prev => ({ ...prev, [id]: value }));
    setActiveSearchId(id);
    debouncedSearch(value);
  };

  const handleSportSelect = (id: string, selectedSport: Sport | null) => {
    if (!selectedSport) return;

    // Check if this platform sport is already added
    const isDuplicate = sports.some(
      item => !item.isCustom && item.sportId === selectedSport.id && item.id !== id
    );

    if (isDuplicate) {
      console.warn('You cannot add the same sport more than once.');
      return;
    }

    // Handle platform sport
    handleUpdate(id, {
      name: selectedSport.name,
      icon: selectedSport.icon,
      sportId: selectedSport.id,
      isCustom: false,
      customName: undefined,
    });

    // Clear input value and search state
    setInputValues(prev => ({ ...prev, [id]: '' }));
    setActiveSearchId(null);
    setSearchQuery('');
  };

  return (
    <div className={className}>
      <DynamicListEditor<SportItem>
        items={sports}
        onAdd={handleAdd}
        onRemove={handleRemove}
        onUpdate={handleUpdate}
        onReorder={handleReorder}
        getTitle={(item: SportItem) => `SPORT ${sports.indexOf(item) + 1}`}
        disableRemove={(item: SportItem) => sports.length === 1}
        renderFields={(item: SportItem) => (
          <div className="flex items-center gap-4">
            <Combobox
              as="div"
              className="flex-1 relative"
              value={null}
              onChange={(sport: Sport | null) => handleSportSelect(item.id, sport)}
            >
              <HeadlessComboboxInput
                label="Search for a sport..."
                as={SiteInput}
                value={inputValues[item.id] || item.name || ''}
                onChange={e => handleInputChange(item.id, e.target.value)}
                placeholder="Search for a sport..."
                className="w-full"
                hideLabel
              />
              {activeSearchId === item.id && (
                <ComboboxOptions className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black/5 overflow-auto focus:outline-none sm:text-sm">
                  {isSearching ? (
                    <div className="px-4 py-2 text-sm text-neutral-500">Searching...</div>
                  ) : searchResults.length === 0 && !inputValues[item.id] ? (
                    <div className="px-4 py-2 text-sm text-neutral-500">
                      Start typing to search...
                    </div>
                  ) : searchResults.length === 0 && inputValues[item.id]?.length >= 2 ? (
                    <div className="px-4 py-2 text-sm text-neutral-500">
                      No matching sports found
                    </div>
                  ) : (
                    <>
                      {/* Platform Sports */}
                      <div className="px-4 py-2 text-xs font-medium text-neutral-500">SPORTS</div>
                      {searchResults.map((sport: Sport) => (
                        <ComboboxOption
                          key={sport.id}
                          value={sport}
                          className={({ active }) =>
                            `relative cursor-default select-none py-2 pl-3 pr-9 ${
                              active ? 'bg-neutral-100 text-neutral-900' : 'text-neutral-700'
                            }`
                          }
                        >
                          {sport.name}
                        </ComboboxOption>
                      ))}

                      {/* Information about custom sports */}
                      <div className="border-t border-gray-200 px-4 py-2 text-xs text-neutral-500 italic">
                        You can add custom sports after completing registration.
                      </div>
                    </>
                  )}
                </ComboboxOptions>
              )}
            </Combobox>
          </div>
        )}
        addButtonText="Add Another Sport"
      />
    </div>
  );
}
