import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { faArrowRight, faEnvelope, faMobileButton } from '@fortawesome/pro-regular-svg-icons';
import { isAxiosError } from 'axios';
import Button from '@/components/shared/Button';
import PasswordInput from '@/components/shared/form/PasswordInput';
import SiteInput from '@/components/shared/form/SiteInput';
import { useFormErrors } from '@/hooks/useFormErrors';
import { useParentOnboarding } from '@/hooks/useParentOnboarding';
import type {
  ApiErrorResponse,
  ParentAccountInfoPayload,
} from '@/services/parentOnboarding.service';
import { useParentOnboardingStore } from '@/stores/parentOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';

interface ParentAccountInfoStepProps {
  initialData?: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };
}

export const ParentAccountInfoStep = ({ initialData }: ParentAccountInfoStepProps) => {
  const router = useRouter();
  const { nextStep } = useWizardStore();
  const { fieldErrors, setErrors, clearError, clearAllErrors } = useFormErrors();

  // Use the parent onboarding store instead of local state
  const { accountInfo, setAccountInfo } = useParentOnboardingStore();

  const { submitAccountInfo, clearStoreData } = useParentOnboarding({
    onSuccess: response => {
      console.log('Account Info Submitted:', response);
      // Clear stores before redirecting
      clearStoreData();
      // Redirect to login page after successful account creation
      router.push('/login');
    },
    onError: (error: unknown) => {
      console.error('Error submitting account info:', error);
      if (isAxiosError(error) && error.response?.data) {
        const errorData = error.response.data as ApiErrorResponse;
        if (errorData.error) {
          setErrors({
            message: errorData.error,
            errors: errorData.errors || {},
          });
        } else {
          // Handle legacy format
          setErrors({
            message: 'Failed to save account details',
            errors: {},
          });
        }
      } else {
        console.error('An unexpected error occurred:', error);
        setErrors({
          message: 'An unexpected error occurred',
          errors: {},
        });
      }
    },
  });

  useEffect(() => {
    // Initialize from initialData only if store is empty
    if (initialData && !accountInfo.first_name && !accountInfo.last_name) {
      setAccountInfo({
        first_name: initialData.firstName || '',
        last_name: initialData.lastName || '',
        email: initialData.email || '',
        phone: initialData.phone || '',
      });
    }
  }, [initialData, setAccountInfo, accountInfo.first_name, accountInfo.last_name]);

  useEffect(() => {
    return () => {
      clearAllErrors();
    };
  }, [clearAllErrors]);

  const handleChange =
    (field: keyof ParentAccountInfoPayload) =>
    (value: string | React.ChangeEvent<HTMLInputElement>) => {
      const newValue = typeof value === 'string' ? value : value.target.value;

      clearError(field);

      // Update the store instead of local state
      setAccountInfo({
        [field]: newValue,
      });
    };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    clearAllErrors();

    if (!accountInfo.first_name) {
      setErrors({
        message: 'Validation failed',
        errors: { first_name: ['First name is required.'] },
      });
      return;
    }
    if (!accountInfo.last_name) {
      setErrors({
        message: 'Validation failed',
        errors: { last_name: ['Last name is required.'] },
      });
      return;
    }
    if (!accountInfo.email) {
      setErrors({ message: 'Validation failed', errors: { email: ['Email is required.'] } });
      return;
    }
    if (!accountInfo.password || accountInfo.password.length < 8) {
      setErrors({
        message: 'Validation failed',
        errors: {
          password: ['Password must be at least 8 characters long'],
        },
      });
      return;
    }

    const payload: ParentAccountInfoPayload = {
      first_name: accountInfo.first_name || '',
      last_name: accountInfo.last_name || '',
      email: accountInfo.email || '',
      phone: accountInfo.phone || '',
      password: accountInfo.password || '',
    };

    submitAccountInfo.mutate(payload);
  };

  const isSubmitting = submitAccountInfo.isPending;

  return (
    <div className="flex flex-col gap-6">
      <div className="space-y-6">
        <h1 className="text-2xl md:text-display-md font-semibold text-gray-900">
          Welcome to the Positive Athlete App!
        </h1>

        <p className="text-base md:text-body-lg text-gray-500">
          Let&apos;s finish creating your parent account. These details will not be visible to
          anyone else.
        </p>
      </div>

      {fieldErrors.message && (
        <div className="p-3 bg-error-50 border border-error-300 rounded text-error-700">
          {fieldErrors.message}
        </div>
      )}

      <form className="space-y-6" onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SiteInput
              label="First Name"
              id="first_name"
              type="text"
              value={accountInfo.first_name || ''}
              onChange={handleChange('first_name')}
              isFailedValidation={Boolean(fieldErrors['first_name'])}
              description={fieldErrors['first_name']}
              aria-invalid={Boolean(fieldErrors['first_name'])}
              aria-errormessage={fieldErrors['first_name'] ? 'first_name-error' : undefined}
              required
            />

            <SiteInput
              label="Last Name"
              id="last_name"
              type="text"
              value={accountInfo.last_name || ''}
              onChange={handleChange('last_name')}
              isFailedValidation={Boolean(fieldErrors['last_name'])}
              description={fieldErrors['last_name']}
              aria-invalid={Boolean(fieldErrors['last_name'])}
              aria-errormessage={fieldErrors['last_name'] ? 'last_name-error' : undefined}
              required
            />
          </div>

          <SiteInput
            label="Email"
            id="email"
            type="email"
            value={accountInfo.email || ''}
            onChange={handleChange('email')}
            icon={faEnvelope}
            isFailedValidation={Boolean(fieldErrors['email'])}
            description={fieldErrors['email']}
            aria-invalid={Boolean(fieldErrors['email'])}
            aria-errormessage={fieldErrors['email'] ? 'email-error' : undefined}
            required
          />

          <SiteInput
            label="Phone"
            id="phone"
            type="tel"
            value={accountInfo.phone || ''}
            onChange={handleChange('phone')}
            icon={faMobileButton}
            isFailedValidation={Boolean(fieldErrors['phone'])}
            description={fieldErrors['phone']}
            aria-invalid={Boolean(fieldErrors['phone'])}
            aria-errormessage={fieldErrors['phone'] ? 'phone-error' : undefined}
          />

          <div className="relative">
            <PasswordInput
              label="Set a Password"
              id="password"
              value={accountInfo.password || ''}
              onChange={handleChange('password')}
              isFailedValidation={Boolean(fieldErrors['password'])}
              description={fieldErrors['password'] || 'Password must be at least 8 characters long'}
              aria-invalid={Boolean(fieldErrors['password'])}
              aria-errormessage={fieldErrors['password'] ? 'password-error' : undefined}
              required
            />
          </div>
        </div>

        {fieldErrors['_general'] && (
          <div className="text-sm text-red-500 mt-2">{fieldErrors['_general']}</div>
        )}

        <div className="flex justify-between pt-6">
          <div />

          <Button
            type="submit"
            color="blue"
            size="small"
            icon={faArrowRight}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Creating your account...' : 'Complete Registration'}
          </Button>
        </div>
      </form>
    </div>
  );
};
