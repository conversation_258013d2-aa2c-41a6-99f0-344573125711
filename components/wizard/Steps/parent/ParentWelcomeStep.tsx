import {
  faArrowRight,
  faBadgeCheck,
  faBriefcase,
  faDiploma,
  faGraduationCap,
  faShareNodes,
  faUserCircle,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { isAxiosError } from 'axios';
import Button from '@/components/shared/Button';
import { useParentOnboarding } from '@/hooks/useParentOnboarding';
import type { ParentOnboardingStepResponse } from '@/services/parentOnboarding.service';
import { useParentOnboardingStore } from '@/stores/parentOnboardingStore';
import { useWizardStore } from '@/stores/wizardStore';

export const ParentWelcomeStep = () => {
  const { nextStep } = useWizardStore();
  const { setAccountInfo } = useParentOnboardingStore();

  // Start parent onboarding process
  const { startOnboarding, athleteName } = useParentOnboarding({
    onSuccess: (response: ParentOnboardingStepResponse) => {
      // If there's prefill data, set it in the store
      if (response.prefill) {
        setAccountInfo(response.prefill);
      }

      // Move to the next step (account info)
      nextStep();
    },
    onError: (error: unknown) => {
      console.error('Error starting parent onboarding:', error);
      if (isAxiosError(error)) {
        console.error('API Error:', error.response?.data);
      }
    },
  });

  const handleStart = () => {
    // Call the intro endpoint
    startOnboarding.mutate();
  };

  const features = [
    {
      icon: faDiploma,
      title: 'Qualify for regional and state awards and scholarships',
    },
    {
      icon: faGraduationCap,
      title: 'Browse a Library of hundreds of character development resources',
    },
    {
      icon: faBadgeCheck,
      title: 'Earn leadership certifications',
    },
    {
      icon: faBriefcase,
      title: 'Access exclusive workforce and education opportunities',
    },
    {
      icon: faShareNodes,
      title: 'Connect with other Positive Athletes and alumni',
    },
    {
      icon: faUserCircle,
      title: 'Build a public profile with their story and accomplishments',
    },
  ];

  return (
    <div className="flex flex-col gap-6 md:gap-10">
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-text-primary">
          {athleteName} has been nominated as a Positive Athlete
        </h1>
      </div>

      <p className="text-text-secondary">
        Positive Athlete is a recognition program that celebrates high character, high school
        student-athletes! As a nominee, {athleteName} now has access to the Positive Athlete app
        where they can:
      </p>

      <div className="grid grid-cols-2 xl:grid-cols-3 gap-4">
        {features.map((feature, index) => (
          <div key={index} className="rounded-2xl bg-surface-secondary px-4 py-6 space-y-2 lg:p-6">
            <FontAwesomeIcon icon={feature.icon} className="!size-8 text-brand-red" />
            <p className="text-text-secondary">{feature.title}</p>
          </div>
        ))}
      </div>

      <p className="text-text-secondary">
        Set up your parent/guardian account for read-only visibility of {athleteName}&apos;s
        Positive Athlete account and messages.
      </p>

      <div className="flex justify-end">
        <Button
          onClick={handleStart}
          color="blue"
          size="small"
          icon={faArrowRight}
          className="px-6"
          disabled={startOnboarding.isPending}
        >
          {startOnboarding.isPending ? 'Starting...' : 'Let&apos;s Go!'}
        </Button>
      </div>
    </div>
  );
};
