import React, { ReactNode, useEffect } from 'react';
import { useWizardStore } from '../../stores/wizardStore';
import { WizardProgress } from './WizardProgress';

interface WizardProps {
  children: ReactNode;
}

interface WizardStepProps {
  children: ReactNode;
}

export function WizardStep({ children }: WizardStepProps) {
  return <>{children}</>;
}

export function Wizard({ children }: WizardProps) {
  const { currentStep, initialize, isInitialized, setTotalSteps } = useWizardStore();

  // Only initialize if not already initialized
  useEffect(() => {
    if (!isInitialized) {
      initialize();
    }
    // Set total steps based on children length
    const validSteps = React.Children.toArray(children);
    setTotalSteps(validSteps.length);
  }, [initialize, isInitialized, children, setTotalSteps]);

  // Validate current step is within bounds
  const validSteps = React.Children.toArray(children);
  const safeCurrentStep = Math.min(currentStep, validSteps.length - 1);

  if (!isInitialized) {
    return null; // or a loading spinner
  }

  return (
    <div className="bg-surface-primary">
      <div className="w-full mx-auto max-w-3xl">
        <WizardProgress className="mb-8" />
        {validSteps[safeCurrentStep]}
      </div>
    </div>
  );
}
