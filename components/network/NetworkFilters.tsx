'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { RefinementList, useClearRefinements, useSortBy } from 'react-instantsearch';
import { useSearchParams as useNextSearchParams } from 'next/navigation';
import { faSliders, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Field, Label, Radio, RadioGroup } from '@headlessui/react';
import Accordion from '@/components/shared/interactive/Accordion';
import { useAuth } from '@/hooks/useAuth';
import { AccordionSection, useAccordionStore } from '@/stores/accordionState.store';
import { ProfileTypes } from '@/stores/auth.store';

// Define sort options
const SORT_OPTIONS = [
  { label: 'Relevance', value: 'users' },
  { label: 'Name A-Z', value: 'users:first_name:asc' },
  { label: 'Name Z-A', value: 'users:first_name:desc' },
  { label: 'Graduation Year (Newest)', value: 'users:graduation_year:desc' },
  { label: 'Graduation Year (Oldest)', value: 'users:graduation_year:asc' },
];

// Utility function to format filter labels for display
const formatFilterLabel = (attribute: string, value: string): string => {
  // Handle specific attribute types
  switch (attribute) {
    case 'profile_type':
      // Format profile types
      if (value === 'positive_athlete') return 'Athlete';
      if (value === 'positive_coach') return 'Coach';
      if (value === 'athletics_director') return 'Athletics Director';
      if (value === 'parent') return 'Parent';
      if (value === 'professional') return 'Professional';
      return value.replace('_', ' ').replace(/\b\w/g, char => char.toUpperCase());

    case 'graduation_year':
      // Return graduation year as is
      return value;

    case 'state_code':
      // Return state code as is
      return value;

    case 'career_interests':
      // Return career interest as is
      return value;

    case 'life_stage':
      // Life stage values are already in display name format in the search index
      return value;

    default:
      // For other attributes, just return the value as is
      return value;
  }
};

// Custom sort component using HeadlessUI RadioGroup
function SortByRadioGroup() {
  const { refine, currentRefinement } = useSortBy({
    items: SORT_OPTIONS,
  });

  const [selectedSort, setSelectedSort] = useState(currentRefinement);

  // Update the selected sort when the refinement changes
  // But only if they're different to prevent loops
  useEffect(() => {
    if (selectedSort !== currentRefinement) {
      setSelectedSort(currentRefinement);
    }
  }, [currentRefinement, selectedSort]);

  const handleSortChange = (value: string) => {
    // Don't update if the value is already selected
    if (value === selectedSort) return;

    setSelectedSort(value);
    refine(value);
  };

  return (
    <RadioGroup value={selectedSort} onChange={handleSortChange} className="space-y-2">
      {SORT_OPTIONS.map(option => (
        <Field key={option.value}>
          <Radio
            value={option.value}
            className={({ checked }) => `
              relative flex cursor-pointer rounded-lg px-2 py-1 focus:outline-none
              ${checked ? 'text-text-primary' : 'text-text-secondary'}
            `}
          >
            {({ checked }) => (
              <div className="flex w-full items-center">
                <div className="flex-shrink-0 mr-2">
                  <div
                    className={`
                      w-4 h-4 rounded-full border flex items-center justify-center
                      ${checked ? 'border-brand-red' : 'border-gray-300'}
                    `}
                  >
                    {checked && <div className="w-2 h-2 rounded-full bg-brand-red" />}
                  </div>
                </div>
                <Label className="text-sm">{option.label}</Label>
              </div>
            )}
          </Radio>
        </Field>
      ))}
    </RadioGroup>
  );
}

// Convenience wrapper for RefinementList with our styling
function FilterRefinementList({
  attribute,
  title,
  operator = 'or',
  limit = 10,
}: {
  attribute: string;
  title: string;
  operator?: 'or' | 'and';
  limit?: number;
}) {
  // Use a properly typed selector
  const isOpen = useAccordionStore(
    (state: { isAccordionOpen: (section: AccordionSection, title: string) => boolean }) =>
      state.isAccordionOpen(AccordionSection.NETWORK, title)
  );

  // Memoize the toggle function to avoid recreating it on each render
  const toggle = useCallback(() => {
    useAccordionStore.getState().toggleAccordion(AccordionSection.NETWORK, title);
  }, [title]);

  // Check if there are any refinements for this attribute in the URL
  const nextSearchParams = useNextSearchParams();
  const hasRefinements = React.useMemo(() => {
    // Check URL params for any refinements on this attribute
    // The URL parameter format for refinements follows the pattern: refinementList[attribute][0]=value
    const paramPrefix = `refinementList[${attribute}]`;

    // Safely iterate through URL params to find any matching this attribute
    if (nextSearchParams) {
      // Convert URLSearchParams to entries array that we can iterate through
      const entries = Array.from(nextSearchParams.entries());
      for (const entry of entries) {
        const [key, value] = entry;
        if (key.startsWith(paramPrefix) && value) {
          return true;
        }
      }
    }

    return false;
  }, [attribute, nextSearchParams]);

  // If we find refinements, open this accordion
  React.useEffect(() => {
    if (hasRefinements && !isOpen) {
      toggle();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasRefinements]); // Remove isOpen and toggle from deps to prevent loops

  // Transform function to format the display labels
  const transformItems = React.useCallback(
    (items: any[]) => {
      return items.map(item => ({
        ...item,
        // Keep the original value for filtering but add a formatted label for display
        label: formatFilterLabel(attribute, item.label),
      }));
    },
    [attribute]
  );

  return (
    <Accordion title={title} defaultOpen={isOpen} onToggle={toggle}>
      <RefinementList
        attribute={attribute}
        operator={operator}
        sortBy={['count:desc']}
        limit={limit}
        transformItems={transformItems}
        classNames={{
          list: 'space-y-2',
          count: 'ml-1 bg-gray-100 text-gray-600 text-xs rounded-full px-2 py-0.5',
          selectedItem: 'font-semibold text-text-primary',
          label: 'flex items-center cursor-pointer text-text-secondary',
          checkbox:
            'mr-2 rounded-sm border-gray-300 text-brand-red accent-brand-red focus:ring-brand-red',
        }}
      />
    </Accordion>
  );
}

export function NetworkFilters() {
  const { refine: clearRefinements } = useClearRefinements();
  const { profileType } = useAuth();
  const nextSearchParams = useNextSearchParams();

  // Reference the reset function directly with proper typing
  const resetSection = useAccordionStore(
    (state: { resetSection: (section: AccordionSection) => void }) => state.resetSection
  );

  // Check if sorting parameters exist in URL
  const hasSortRefinement = React.useMemo(() => {
    return nextSearchParams?.has('sortBy') || false;
  }, [nextSearchParams]);

  const isSponsor = profileType === ProfileTypes.SPONSOR;
  const isCoach = profileType === ProfileTypes.POSITIVE_COACH;
  const isCollegeAthlete = profileType === ProfileTypes.COLLEGE_ATHLETE;
  const isProfessional = profileType === ProfileTypes.PROFESSIONAL;

  const handleClearAll = () => {
    clearRefinements();
    resetSection(AccordionSection.NETWORK); // Reset all accordions to default state when clearing filters
  };

  return (
    <div className="divide-y divide-gray-200">
      <div className="px-4 py-3 flex justify-between items-center">
        <div className="flex items-center gap-2">
          <FontAwesomeIcon icon={faSliders} className="size-4 text-brand-red" aria-hidden="true" />
          <h2 className="pa-eyebrow text-brand-red">SORT & FILTER</h2>
        </div>

        <button
          type="button"
          onClick={handleClearAll}
          className="pa-eyebrow text-brand-red"
          aria-label="Clear All"
        >
          <span className="sr-only">Clear All</span>
          <FontAwesomeIcon icon={faXmark} className="size-4 mr-1" aria-hidden="true" />
          Clear
        </button>
      </div>

      {/* Sort by accordion is always defaultOpen and not managed by the store */}
      <Accordion title="Sort by" defaultOpen={true}>
        <SortByRadioGroup />
      </Accordion>

      {/* Using the store to manage filter accordion open/close state */}
      {!isSponsor && <FilterRefinementList attribute="profile_type" title="User Type" />}
      {/* Life Stage filter only visible to coaches, college athletes, and professionals */}
      {(isCoach || isCollegeAthlete || isProfessional) && (
        <FilterRefinementList attribute="life_stage" title="Life Stage" />
      )}
      <FilterRefinementList attribute="career_interests" title="Career Interest" limit={20} />
      <FilterRefinementList attribute="sports" title="Sport" limit={20} />
      <FilterRefinementList attribute="state_code" title="State" />
      <FilterRefinementList attribute="graduation_year" title="High School Graduation Year" />
    </div>
  );
}
