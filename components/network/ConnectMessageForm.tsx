import React, { useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import ModalFormContainer from '@/components/shared/form/ModalFormContainer';
import { Textarea } from '@/components/shared/form/Textarea';
import { NetworkingService, UserBasicData } from '@/services/networking.service';
import { useModalStore } from '@/stores/modal.store';

interface ConnectMessageFormProps {
  recipient: UserBasicData;
}

export function ConnectMessageForm({ recipient }: ConnectMessageFormProps) {
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { close } = useModalStore();

  const handleSave = async () => {
    if (!message.trim()) {
      setError('Please enter a message');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await NetworkingService.connectWithMessage({
        recipientId: recipient.id.toString(),
        message: message.trim(),
      });

      close();

      // TODO: We don't currently support redirecting to a specific conversation
      // We should just throw a toast and let the user know that the message was sent
      // if (response.conversation?.exists) {
      //   router.push(`/messages/${recipient.id}`);
      // }
    } catch (error: any) {
      console.error('Error sending connection request:', error);

      // Handle specific error cases
      if (error.response?.data?.message) {
        // Use the specific error message from the server
        setError(error.response.data.message);
      } else if (error.message.includes('rejected connection')) {
        setError('Cannot send message due to rejected connection request.');
      } else if (error.message.includes('blocking restrictions')) {
        setError('Cannot send message because one of you has blocked the other.');
      } else {
        setError('Failed to send message. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ModalFormContainer
      title={`Connect with ${recipient.firstName} ${recipient.lastName}`}
      description={`Send a message to ${recipient.firstName} to start a conversation.`}
      isLoading={isSubmitting}
      error={error}
      handleSave={handleSave}
      handleClose={close}
      saveButtonText="Send Message"
      allowSave={message.trim().length > 0}
    >
      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 relative rounded-full overflow-hidden">
            <Image
              src={recipient.profileImageUrl || '/mock/football-avatar.jpg'}
              alt={`${recipient.firstName} ${recipient.lastName}`}
              className="object-cover"
              fill
            />
          </div>
          <div>
            <h3 className="font-semibold text-lg">
              {recipient.firstName} {recipient.lastName}
            </h3>
          </div>
        </div>

        <div className="mt-4">
          <Textarea
            label="Send a message to connect"
            rows={4}
            value={message}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setMessage(e.target.value)}
            placeholder={`Hi ${recipient.firstName}, I'd like to connect with you...`}
            required
            hideLabel
          />
        </div>
      </div>
    </ModalFormContainer>
  );
}
