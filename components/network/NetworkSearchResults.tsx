'use client';

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { Configure, InfiniteHits, useSearchBox } from 'react-instantsearch';
import { faSliders, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import AthleteCard, { CareerInterest } from '@/components/shared/cards/AthleteCard';
import SearchInput from '@/components/shared/form/SearchInput';
import { getAllUsers, testMeiliSearchConnection, USERS_INDEX } from '@/config/users-meilisearch';
import { useAuth } from '@/hooks/useAuth';
import { useConnectionPermissions } from '@/hooks/useConnectionPermissions';
import { NetworkingService, UserBasicData } from '@/services/networking.service';
import { ProfileTypes } from '@/stores/auth.store';
import { useModalStore } from '@/stores/modal.store';
import { ConnectMessageForm } from './ConnectMessageForm';

interface SearchResult {
  id: string;
  first_name: string;
  last_name: string;
  graduation_year: string;
  state: string;
  high_school: string;
  profile_image_url: string;
  career_interests: string[];
  class_of: string;
  profile_type: string;
  state_code: string;
  has_achievements: boolean;
}

// MeiliSearch SearchBox component that uses the existing SearchInput component
function MeilisearchBox() {
  const { query, refine } = useSearchBox();
  const [inputValue, setInputValue] = useState(query);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    refine(newValue);
  };

  // Sync the query value when it changes from outside
  useEffect(() => {
    setInputValue(query);
  }, [query]);

  return (
    <SearchInput
      value={inputValue}
      placeholder="Search character traits, athletes, or topics..."
      onChange={handleChange}
    />
  );
}

// Custom hit component for rendering athlete cards
function AthleteHit({ hit }: { hit: SearchResult }) {
  const { open } = useModalStore();
  const checkCanConnect = useConnectionPermissions();

  const handleConnect = (user: SearchResult) => {
    // Check if the current user can connect with the target user
    const canConnect = checkCanConnect(user.profile_type);

    if (!canConnect) {
      return;
    }

    const recipient: UserBasicData = {
      id: parseInt(user.id),
      firstName: user.first_name,
      lastName: user.last_name,
      profileImageUrl: user.profile_image_url,
    };

    open(<ConnectMessageForm recipient={recipient} />, 'md');
  };

  // Determine if the current user can connect with this user
  const canConnect = checkCanConnect(hit.profile_type);

  const careerInterests: CareerInterest[] = (hit.career_interests || []).map(interest => ({
    name: interest,
    count: 1,
  }));

  return (
    // TODO: Add has_achievements prop and pass in correct value from backend
    <AthleteCard
      key={hit.id}
      id={hit.id}
      name={`${hit.first_name} ${hit.last_name}`}
      graduationYear={hit.graduation_year || ''}
      location={hit.state || ''}
      highSchool={hit.high_school || ''}
      avatar={hit.profile_image_url}
      careerInterests={careerInterests}
      onConnect={() => handleConnect(hit)}
      canConnect={canConnect}
      hasAchievements={hit.has_achievements ?? false}
    />
  );
}

export function NetworkSearchResults({
  handleMobileFiltersOpen,
}: {
  handleMobileFiltersOpen: () => void;
}) {
  const [connectionStatus, setConnectionStatus] = useState<{
    tested: boolean;
    success?: boolean;
    indexExists?: boolean;
    error?: string;
  }>({ tested: false });
  const [isLoading, setIsLoading] = useState(true);
  const { user, profileType } = useAuth();

  const isSponsor = profileType === ProfileTypes.SPONSOR;

  // Test connection to Meilisearch
  useEffect(() => {
    const testConnection = async () => {
      const result = await testMeiliSearchConnection();

      if (result.success) {
        try {
          // Skip trying to configure the index settings - this requires admin permissions
          // Just try to fetch initial data
          const usersResult = await getAllUsers(20);

          if (usersResult.success && usersResult.data) {
            setConnectionStatus({
              tested: true,
              success: true,
              indexExists: true,
            });
          } else {
            // Index might not exist yet
            setConnectionStatus({
              tested: true,
              success: true,
              indexExists: false,
              error: 'The users index does not exist yet. Please run the rebuild index command.',
            });
          }
        } catch (error: any) {
          // Handle index not found or other errors
          setConnectionStatus({
            tested: true,
            success: true,
            indexExists: false,
            error: error.message || 'Failed to access the users index',
          });
        }
      } else {
        setConnectionStatus({
          tested: true,
          success: false,
          error: 'Failed to connect to the search service',
        });
      }

      setIsLoading(false);
    };

    testConnection();
  }, []);

  if (!connectionStatus.tested) {
    return <div className="text-center py-8">Connecting to search service...</div>;
  }

  if (connectionStatus.tested && !connectionStatus.success) {
    return (
      <div className="text-center py-8 text-red-500">
        Unable to connect to search service. Please try again later.
      </div>
    );
  }

  if (connectionStatus.tested && connectionStatus.success && !connectionStatus.indexExists) {
    return (
      <div className="space-y-6">
        <div className="flex items-center flex-wrap gap-4 mb-10">
          <div className="flex-1">
            <MeilisearchBox />
          </div>

          {/* Mobile Filters Drawer Trigger */}
          <button
            type="button"
            className="text-brand-red lg:hidden"
            aria-label="Open Filters Drawer"
            onClick={handleMobileFiltersOpen}
          >
            <span className="sr-only">Open Filters Drawer</span>
            <FontAwesomeIcon icon={faSliders} className="!size-6" aria-hidden="true" />
          </button>
        </div>

        <div className="text-center py-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">Search Index Not Ready</h3>
          <p className="text-yellow-700">
            The user search index is not available yet. Please run the following command to create
            it:
          </p>
          <div className="mt-4 bg-gray-800 text-white p-3 rounded text-sm font-mono">
            php artisan networking:rebuild-index
          </div>
          <p className="mt-4 text-sm text-yellow-600">
            This is a one-time setup step. Once the index is created, you&#39;ll be able to search
            for users.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center flex-wrap gap-4 mb-10">
        <div className="flex-1">
          <MeilisearchBox />
        </div>

        {/* Mobile Filters Drawer Trigger */}
        <button
          type="button"
          className="text-brand-red lg:hidden"
          aria-label="Open Filters Drawer"
          onClick={handleMobileFiltersOpen}
        >
          <span className="sr-only">Open Filters Drawer</span>
          <FontAwesomeIcon icon={faSliders} className="!size-6" aria-hidden="true" />
        </button>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 gap-8">
          <div className={`animate-pulse bg-gray-200 rounded-3xl w-full delay-100 h-20`} />
          <div className={`animate-pulse bg-gray-200 rounded-3xl w-full delay-300 h-20`} />
          <div className={`animate-pulse bg-gray-200 rounded-3xl w-full delay-100 h-20`} />
          <div className={`animate-pulse bg-gray-200 rounded-3xl w-full delay-200 h-20`} />
        </div>
      ) : (
        <div className="space-y-4">
          <InfiniteHits
            hitComponent={AthleteHit}
            classNames={{
              list: 'space-y-4',
              item: 'mb-0',
              loadMore:
                'text-sm px-4 py-2 rounded-lg bg-brand-blue flex justify-center mt-8 text-white font-semibold',
              disabledLoadMore:
                'pointer-events-none !opacity-10 transition-opacity duration-300 ease-in-out',
            }}
            showPrevious={false}
            translations={{
              showMoreButtonText: 'Load More Users',
            }}
          />
        </div>
      )}
    </div>
  );
}
