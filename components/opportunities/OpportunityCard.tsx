'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { faArrowRight, faChevronDown } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/react';
import clsx from 'clsx';
import type { BaseHit } from 'instantsearch.js';
import OpportunityActions from '@/components/opportunities/OpportunityActions';
import OpportunityBookmark from '@/components/opportunities/OpportunityBookmark';
import Richtext from '@/components/shared/blocks/Richtext';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import Tag, { CAREER_TAG_ICONS } from '@/components/shared/Tag';
import { useAuth } from '@/hooks/useAuth';
import { useOpportunities } from '@/hooks/useOpportunities';
import { Icon<PERSON><PERSON> } from '@/lib/fontawesome';
import { ProfileTypes } from '@/stores/auth.store';
import { useOpportunityImpressions } from '@/stores/opportunityImpressions.store';
import { MeiliSearchOpportunity } from '@/types/opportunity';

// Helper function to format location type
const formatLocationType = (locationType: string | undefined | null): string => {
  if (!locationType) return '';
  // Simple capitalization for 'onsite', 'remote', 'hybrid'
  return locationType.charAt(0).toUpperCase() + locationType.slice(1);
};

// Generic image for opportunities
const DEFAULT_IMAGE = '/images/positive-athlete-icon.svg';

// Map opportunity types to learning types for display
const TYPE_TO_LEARNING_TYPE: Record<string, string> = {
  education: 'Degree Programs',
  job: 'Job Opportunity',
  internship: 'Internships',
  scholarship: 'Scholarship',
  mentorship: 'Mentorship Program',
};

// Base opportunity interface that both Meilisearch and regular opportunities share
export interface BaseOpportunity {
  id: number | string;
  title: string;
  description: string;
  details?: string;
  location?: string;
  city?: string;
  state_code: string;
  location_type: string;
  location_display?: string;
  type?: string;
  subtype?: string;
  organization_name: string;
  organization_logo: string;
  // Deprecated: industries array of strings - will be removed in future
  industries?: string[];
  // Interests array (can be either string[] or objects with name/icon)
  interests?: (string | { id: number; name: string; icon: string })[];
  term?: string;
  qualifications?: string;
  responsibilities?: string;
  benefits?: string;
  handleToggleListed?: (checked: boolean) => void;
  handleDuplicate?: () => void;
  handleDelete?: () => void;
  listed?: boolean;
  isProcessingToggle?: boolean;
  isProcessingDuplicate?: boolean;
  isProcessingDelete?: boolean;
}

// Props for direct usage (Dashboard, Postings)
interface DirectOpportunityProps {
  opportunity: BaseOpportunity;
  hit?: never;
}

// Props for Meilisearch usage (Opportunities)
interface MeilisearchOpportunityProps {
  opportunity?: never;
  hit: BaseHit & BaseOpportunity;
}

export type OpportunityCardProps = DirectOpportunityProps | MeilisearchOpportunityProps;

// Type for tag display data that can handle both string and IconDefinition icons
interface TagDisplay {
  label: string;
  icon: IconDefinition | IconKey | string;
}

export default function OpportunityCard({
  opportunity: propOpportunity,
  hit,
}: OpportunityCardProps) {
  const { profileType } = useAuth();
  const { isOpportunityBookmarked, toggleBookmark, bookmarkedIds, isToggling } = useOpportunities();
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const { trackClick } = useOpportunityImpressions();

  // Update bookmark state when bookmarkedIds changes
  useEffect(() => {
    if (propOpportunity || hit) {
      const opportunity = (propOpportunity || hit) as BaseOpportunity;
      setIsBookmarked(isOpportunityBookmarked(opportunity.id.toString()));
    }
  }, [bookmarkedIds, isOpportunityBookmarked, propOpportunity, hit]);

  // Track global toggling state for this specific opportunity
  useEffect(() => {
    if (!isToggling) {
      setIsProcessing(false);
    }
  }, [isToggling]);

  // Ensure we have a valid opportunity object
  if (!propOpportunity && !hit) {
    return null;
  }

  // Use either the direct opportunity or the hit data
  const opportunity = (propOpportunity || hit) as BaseOpportunity;
  const isSponsor = profileType === ProfileTypes.SPONSOR;
  const isParentProfile = profileType === ProfileTypes.PARENT;
  // Handle bookmark toggle with visual feedback
  const handleToggleBookmark = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsProcessing(true);
    toggleBookmark(opportunity.id.toString());
  };

  // Get primary interest/industry for display
  const primaryInterest =
    opportunity.interests && opportunity.interests[0]
      ? typeof opportunity.interests[0] === 'string'
        ? opportunity.interests[0]
        : opportunity.interests[0].name
      : 'Education';

  // Map opportunity type to learning type
  const learningType =
    TYPE_TO_LEARNING_TYPE[opportunity.type || opportunity.subtype || ''] || 'Opportunity';

  // Format term for display
  const formattedTerm = opportunity.term
    ? opportunity.term.replace('<', 'Less than ').replace('>', 'More than ').replace('_', ' ')
    : 'Not specified';

  // Create tags from interests array of objects
  const tagsToDisplay: TagDisplay[] =
    opportunity.interests?.map(interest => {
      const interestName = typeof interest === 'string' ? interest : interest.name;
      // Use an empty string as fallback for the icon if it's null
      const interestIcon = typeof interest === 'string' ? '' : interest.icon || '';

      return {
        label: interestName,
        icon: interestIcon,
      };
    }) || [];

  // If no interests found, provide empty array (industries are deprecated)
  if (tagsToDisplay.length === 0) {
    // No fallback needed as industries are deprecated
  }

  // These handlers will be called by OpportunityActions
  const handleToggleListed = (checked: boolean) => {
    if (opportunity.handleToggleListed) {
      // Use the handler provided in the opportunity object
      opportunity.handleToggleListed(checked);
    } else {
      // console.log('Toggle Listed/Unlisted:', checked);
    }
  };

  const handleDuplicate = () => {
    if (opportunity.handleDuplicate) {
      // Use the handler provided in the opportunity object
      opportunity.handleDuplicate();
    } else {
      // console.log('Duplicate opportunity:', opportunity.id);
    }
  };

  const handleDelete = () => {
    if (opportunity.handleDelete) {
      // Use the handler provided in the opportunity object
      opportunity.handleDelete();
    } else {
      // console.log('Delete opportunity:', opportunity.id);
    }
  };

  // Format location string based on available data
  const locationString =
    opportunity.location_display ||
    (opportunity.city && opportunity.state_code
      ? `${opportunity.city}, ${opportunity.state_code}`
      : opportunity.state_code);

  // Create a cleaner display string for the template that handles empty locations
  const displayLocation = locationString
    ? `${isSponsor ? formatLocationType(opportunity.location_type) : opportunity.organization_name}${locationString ? ' • ' + locationString : ''}`
    : isSponsor
      ? formatLocationType(opportunity.location_type)
      : opportunity.organization_name;

  return (
    <Disclosure as={React.Fragment}>
      {({ open }) => (
        <Card
          elevation="card"
          roundedCorners="2xl"
          className={clsx(isSponsor && 'overflow-visible')}
          noPadding
        >
          <div className="bg-white grid grid-cols-4 gap-4 px-6 py-5 items-center relative rounded-2xl lg:grid-cols-12 xl:gap-6">
            {/* Organization Logo, Learning Type, Title, Organization Name, Location */}
            <div
              className={clsx(
                'col-span-3 items-center gap-4 xl:flex',
                isSponsor ? 'lg:col-span-6 2xl:col-span-5' : 'lg:col-span-6'
              )}
            >
              <Image
                src={opportunity.organization_logo || DEFAULT_IMAGE}
                alt={opportunity.organization_name || opportunity.title}
                width={0}
                height={0}
                className="w-full max-w-12 h-auto object-contain object-center mb-4 xl:mb-0"
              />

              <div className="flex flex-col flex-1 gap-1">
                <span className="pa-eyebrow text-brand-red">{learningType}</span>
                <h3 className="text-xl/none font-bold text-text-primary">{opportunity.title}</h3>
                <p className="text-sm text-text-secondary">{displayLocation}</p>
              </div>
            </div>

            {/* Term */}
            <div className="hidden lg:block lg:col-span-2">
              <span className="block text-text-secondary text-sm">Term</span>
              <span className="block text-text-primary text-base font-bold">{formattedTerm}</span>
            </div>

            {/* Industry */}
            <div
              className={clsx(
                'hidden flex-col gap-2 relative overflow-hidden lg:flex',
                isSponsor ? 'lg:col-span-3 2xl:col-span-2' : 'lg:col-span-3'
              )}
            >
              <span className="text-text-secondary text-sm">Interest</span>

              <div className="flex flex-wrap gap-2 pointer-events-none">
                {tagsToDisplay
                  ?.slice(0, 2)
                  ?.map((tag, index) => (
                    <Tag key={`${tag.label}-${index}`} label={tag.label} icon={tag.icon} />
                  ))}
                {tagsToDisplay?.length > 2 && <Tag label={`+${tagsToDisplay.length - 2}`} />}
              </div>
            </div>

            <div
              className={clsx(
                'flex flex-col items-end justify-end gap-4 bg-white',
                'lg:flex-row lg:flex-wrap lg:items-center',
                isSponsor ? 'lg:col-span-1 2xl:col-span-3' : 'lg:col-span-1'
              )}
            >
              {!isSponsor ? (
                <>
                  {!isParentProfile && (
                    <OpportunityBookmark
                      handleToggleBookmark={handleToggleBookmark}
                      isProcessing={isProcessing}
                      isBookmarked={isBookmarked}
                    />
                  )}
                </>
              ) : (
                <OpportunityActions
                  id={opportunity.id.toString()}
                  listed={opportunity.listed ?? false}
                  handleToggleListed={handleToggleListed}
                  handleDuplicate={handleDuplicate}
                  handleDelete={handleDelete}
                  isProcessingToggle={opportunity.isProcessingToggle}
                  isProcessingDuplicate={opportunity.isProcessingDuplicate}
                  isProcessingDelete={opportunity.isProcessingDelete}
                />
              )}

              <DisclosureButton className="group text-text-secondary hover:text-text-primary transition-colors">
                <span className="sr-only">Toggle details</span>
                <FontAwesomeIcon
                  icon={faChevronDown}
                  className="size-4 group-data-[open]:-rotate-180 transition-transform"
                  aria-hidden="true"
                />
              </DisclosureButton>
            </div>
          </div>

          <DisclosurePanel>
            <div className="border-t border-stroke-weak py-4 px-8 bg-surface-secondary rounded-b-2xl">
              <div className="flex flex-col lg:flex-row w-full gap-4 lg:gap-8">
                <div className="flex lg:hidden gap-4">
                  <div className="block space-y-2">
                    <span className="block text-text-secondary text-sm">Term</span>
                    <span className="block text-text-primary text-base font-bold">
                      {formattedTerm}
                    </span>
                  </div>

                  <div className="block space-y-2">
                    <span className="block text-text-secondary text-sm">Interest</span>

                    <div className="flex flex-wrap gap-2 pointer-events-none pb-4">
                      {tagsToDisplay.length > 0 ? (
                        tagsToDisplay.map((tag, index) => (
                          <Tag key={`${tag.label}-${index}`} label={tag.label} icon={tag.icon} />
                        ))
                      ) : (
                        <div className="text-text-secondary">No interests found</div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex-1">
                  <h4 className="text-sm font-bold mb-4">Description</h4>
                  <Richtext
                    content={opportunity.description || ''}
                    className="prose-sm text-text-secondary"
                  />
                </div>

                <div className="flex-1">
                  <h4 className="text-sm font-bold mb-4">Details</h4>

                  <Richtext
                    content={
                      opportunity.qualifications ||
                      opportunity.responsibilities ||
                      opportunity.benefits ||
                      opportunity.details ||
                      ''
                    }
                    className="prose-sm text-text-secondary"
                  />
                </div>
              </div>

              {opportunity.id && (
                <>
                  {isSponsor ? (
                    <Button
                      href={`/postings/${opportunity.id}`}
                      size="small"
                      icon={faArrowRight}
                      iconPosition="right"
                      className="mt-4"
                    >
                      Edit Posting
                    </Button>
                  ) : (
                    <Button
                      href={`/opportunities/${opportunity.id}`}
                      size="small"
                      icon={faArrowRight}
                      iconPosition="right"
                      className="mt-4"
                      onClick={() => trackClick(opportunity.id.toString())}
                    >
                      Learn More
                    </Button>
                  )}
                </>
              )}
            </div>
          </DisclosurePanel>
        </Card>
      )}
    </Disclosure>
  );
}
