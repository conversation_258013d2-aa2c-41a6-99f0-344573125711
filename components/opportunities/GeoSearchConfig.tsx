'use client';

import React, { useEffect } from 'react';
import { Configure, useInstantSearch } from 'react-instantsearch';
import { useGeolocationContext } from '@/context/GeolocationContext';

interface GeoSearchConfigProps {
  enabled: boolean;
}

/**
 * Component to handle geo-based search configuration for proximity sorting
 * When enabled and user location is available, it configures the search
 * to sort by proximity to the user's location
 */
export function GeoSearchConfig({ enabled }: GeoSearchConfigProps) {
  const { position, loading, error } = useGeolocationContext();
  const { setIndexUiState } = useInstantSearch();

  useEffect(() => {
    // If proximity sorting is enabled and we have user's location
    if (enabled && position && !loading && !error) {
      // Update aroundLatLng parameter for geo search
      setIndexUiState(prevState => ({
        ...prevState,
        configure: {
          ...prevState.configure,
          aroundLatLng: `${position.lat}, ${position.lng}`,
        },
      }));
    }
  }, [enabled, position, loading, error, setIndexUiState]);

  // If proximity is enabled and we have the user's location,
  // set the aroundLatLng parameter for geo search
  if (enabled && position && !loading && !error) {
    return (
      <Configure
        aroundLatLng={`${position.lat}, ${position.lng}`}
        // You can adjust these parameters based on your needs
        aroundRadius={100000} // 100km in meters
      />
    );
  }

  // If proximity is not enabled or we don't have location, don't modify the search
  return null;
}

export default GeoSearchConfig;
