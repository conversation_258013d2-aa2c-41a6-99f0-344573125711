'use client';

import React, { useEffect, useState } from 'react';
import { useSearchBox } from 'react-instantsearch';
import SearchInput from '@/components/shared/form/SearchInput';

/**
 * MeiliSearch SearchBox component that integrates with InstantSearch
 * and uses the existing SearchInput component
 */
export const OpportunitiesMeilisearchBox: React.FC = () => {
  const { query, refine } = useSearchBox();
  const [inputValue, setInputValue] = useState(query);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    refine(newValue);
  };

  // Sync the query value when it changes from outside
  useEffect(() => {
    setInputValue(query);
  }, [query]);

  return (
    <SearchInput
      value={inputValue}
      placeholder="Search opportunities by location, industry, or organization..."
      onChange={handleChange}
    />
  );
};

export default OpportunitiesMeilisearchBox;
