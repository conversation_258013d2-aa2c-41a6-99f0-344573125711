'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { SwiperSlide } from 'swiper/react';
import Button from '@/components/shared/Button';

// import CardCarousel from '@/components/shared/interactive/CardCarousel';

const LazyCardCarousel = dynamic(() => import('@/components/shared/interactive/CardCarousel'), {
  ssr: false,
});

// Define the sponsor interface
export interface Sponsor {
  id: string;
  label: string;
  logoUrl: string;
  coverImageUrl: string;
  tagline: string;
  ctaText: string;
  ctaUrl: string;
}

interface OpportunitiesSponsoredCarouselCardProps {
  sponsor: Sponsor;
  autoplay?: boolean;
}

const OpportunitiesSponsoredCarouselCard: React.FC<OpportunitiesSponsoredCarouselCardProps> = ({
  sponsor,
}) => {
  return (
    <figure className="relative min-h-[320px] overflow-hidden rounded-4xl flex items-center">
      {/* Content */}
      <figcaption className="relative z-20 h-full w-full flex flex-col justify-center px-4 py-6 lg:p-8">
        <div className="flex flex-col gap-4 max-w-2xl">
          <div className="pa-eyebrow text-brand-red">{sponsor.label || 'SPONSORED'}</div>

          {/* Logo */}
          {sponsor.logoUrl && (
            <div className="w-48 h-24 relative mb-2">
              <Image
                src={sponsor.logoUrl}
                alt={sponsor.tagline}
                fill
                className="object-contain object-left"
                unoptimized
              />
            </div>
          )}

          {/* Tagline */}
          <span className="font-medium text-white leading-tight text-pretty">
            {sponsor.tagline}
          </span>

          {/* CTA Button */}
          <div>
            <Button href={sponsor.ctaUrl} color="red" size="small" icon={faArrowRight}>
              {sponsor.ctaText || 'Learn More'}
            </Button>
          </div>
        </div>
      </figcaption>

      {/* Background Image */}
      {sponsor.coverImageUrl ? (
        <Image
          src={sponsor.coverImageUrl}
          alt={sponsor.tagline}
          fill
          unoptimized
          sizes="100vw"
          className="object-cover"
          priority
        />
      ) : (
        <div className="absolute inset-0 bg-gray-200 z-10" />
      )}

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-black/80 to-black/40 z-10" />
    </figure>
  );
};

interface OpportunitiesSponsoredCarouselProps {
  sponsors: Sponsor[];
  autoplay?: boolean;
}

export function OpportunitiesSponsoredCarousel({
  sponsors,
  autoplay = false,
}: OpportunitiesSponsoredCarouselProps) {
  // Don't render if no sponsors
  if (!sponsors || sponsors.length === 0) {
    return null;
  }

  // If only one sponsor, render it directly without carousel
  if (sponsors.length === 1) {
    return (
      <div className="w-full max-w-full">
        <OpportunitiesSponsoredCarouselCard sponsor={sponsors[0]} />
      </div>
    );
  }

  // Otherwise render the carousel with multiple sponsors
  return (
    <div className="w-full max-w-full">
      <LazyCardCarousel autoplay={autoplay}>
        {sponsors.map((sponsor, index) => (
          <SwiperSlide key={`opportunity-slide-${sponsor.id}-${index}`} className="!h-auto">
            <OpportunitiesSponsoredCarouselCard sponsor={sponsor} />
          </SwiperSlide>
        ))}
      </LazyCardCarousel>
    </div>
  );
}
