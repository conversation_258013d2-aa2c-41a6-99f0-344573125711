'use client';

import React, { useEffect, useState } from 'react';
import {
  RefinementList,
  useClearRefinements,
  useInstantSearch,
  useMenu,
  useRefinementList,
  useSortBy,
} from 'react-instantsearch';
import { faSliders, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Field, Label, Radio, RadioGroup } from '@headlessui/react';
import GeoSearchConfig from '@/components/opportunities/GeoSearchConfig';
import Accordion from '@/components/shared/interactive/Accordion';
import { useGeolocationContext } from '@/context/GeolocationContext';
import { useOpportunities } from '@/hooks/useOpportunities';

// Define sort options
const SORT_OPTIONS = [
  { label: 'Relevance', value: 'opportunities' },
  { label: 'Proximity', value: 'opportunities_proximity' },
];

// Utility function to format filter labels for display
const formatFilterLabel = (attribute: string, value: string): string => {
  // Handle specific attribute types
  switch (attribute) {
    case 'subtype': // Learning Type
      // Replace underscores with spaces and capitalize each word
      return value
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

    case 'term':
      // Format terms like "1-2_years" to "1-2 Years"
      if (value === '<1_year') return 'Less than 1 Year';
      if (value === '1-2_years') return '1-2 Years';
      if (value === '3-4_years') return '3-4 Years';
      if (value === '>4_years') return 'More than 4 Years';
      if (value === 'indefinite') return 'Indefinite';
      return value.replace('_', ' ').replace(/\b\w/g, char => char.toUpperCase());

    case 'location_type':
      // Capitalize location types
      return value.charAt(0).toUpperCase() + value.slice(1);

    default:
      // For other attributes, just return the value as is
      return value;
  }
};

// Convenience wrapper for RefinementList with our styling
function FilterRefinementList({
  attribute,
  title,
  operator = 'or',
  limit = 10,
}: {
  attribute: string;
  title: string;
  operator?: 'or' | 'and';
  limit?: number;
}) {
  // Transform function to format the display labels
  // Memoize the function to prevent unnecessary re-renders
  const transformItems = React.useCallback(
    (items: any[]) => {
      return items.map(item => ({
        ...item,
        // Keep the original value for filtering but add a formatted label for display
        label: formatFilterLabel(attribute, item.label),
      }));
    },
    [attribute]
  );

  return (
    <Accordion title={title}>
      <RefinementList
        attribute={attribute}
        operator={operator}
        sortBy={['count:desc']}
        limit={limit}
        transformItems={transformItems}
        classNames={{
          list: 'space-y-2',
          count: 'ml-1 bg-gray-100 text-gray-600 text-xs rounded-full px-2 py-0.5',
          selectedItem: 'font-semibold text-text-primary',
          label: 'flex items-center cursor-pointer text-text-secondary',
          checkbox:
            'mr-2 rounded-sm border-gray-300 text-brand-red accent-brand-red focus:ring-brand-red',
        }}
      />
    </Accordion>
  );
}

// Create a hidden RefinementList component for bookmarked IDs
function HiddenRefinementList({ attribute }: { attribute: string }) {
  const { items, refine } = useRefinementList({ attribute });

  // This component doesn't render anything visible
  return null;
}

// Custom filter for bookmarked opportunities
function BookmarkedFilter() {
  const { bookmarkedIds } = useOpportunities();
  const { refine: clearRefinements } = useClearRefinements();
  const { setIndexUiState, indexUiState, refresh } = useInstantSearch();
  const [isActive, setIsActive] = useState(false);

  // Toggle the bookmarked filter
  const toggleBookmarkedFilter = () => {
    const newIsActive = !isActive;
    setIsActive(newIsActive);

    if (!newIsActive) {
      // If deactivating, remove the filter
      // Only clear the ID refinement, not all refinements
      setIndexUiState(state => {
        const { refinementList } = state;
        if (refinementList && refinementList.id) {
          const { id, ...restRefinements } = refinementList;
          return {
            ...state,
            refinementList: restRefinements,
          };
        }
        return state;
      });
    } else {
      // If activating, apply filter for bookmarked IDs
      if (bookmarkedIds && bookmarkedIds.length > 0) {
        // Use a different approach to apply the filter
        // First, clear any existing ID refinements
        setIndexUiState(state => {
          const { refinementList } = state;
          const newRefinementList = { ...refinementList };

          // Remove any existing id refinement
          if (newRefinementList && newRefinementList.id) {
            delete newRefinementList.id;
          }

          return {
            ...state,
            refinementList: newRefinementList,
          };
        });

        // Then apply the new refinements in a separate update
        setTimeout(() => {
          setIndexUiState(state => ({
            ...state,
            refinementList: {
              ...state.refinementList,
              // Convert IDs to strings if they aren't already
              id: bookmarkedIds.map(id => id.toString()),
            },
          }));

          // Force a refresh
          refresh();
        }, 0);
      }
    }
  };

  return (
    <div className="px-4 py-3">
      <h3 className="text-sm font-semibold text-gray-900 mb-3">Bookmarked</h3>
      <div className="flex items-center">
        <label
          htmlFor="bookmarked-filter"
          className={`font-normal ${isActive ? 'text-text-primary' : 'text-text-secondary'}`}
        >
          <input
            type="checkbox"
            id="bookmarked-filter"
            checked={isActive}
            onChange={toggleBookmarkedFilter}
            disabled={!bookmarkedIds || bookmarkedIds.length === 0}
            className="mr-2 rounded-sm border-gray-300 text-brand-red accent-brand-red focus:ring-brand-red"
          />
          Show Bookmarked Only
          {bookmarkedIds && bookmarkedIds.length > 0 && (
            <span className="ml-1 bg-gray-100 text-gray-600 text-xs rounded-full px-2 py-0.5">
              {bookmarkedIds.length}
            </span>
          )}
        </label>
      </div>
    </div>
  );
}

// Custom menu for opportunity type
function OpportunityTypeMenu() {
  const { items, refine } = useMenu({ attribute: 'type' });

  // Make sure we have education and employment options
  // If the items from the index don't include them, we'll add them manually
  const options = [
    { label: 'Education', value: 'education', isRefined: false, count: 0 },
    { label: 'Employment', value: 'employment', isRefined: false, count: 0 },
  ];

  // Update with actual data if available
  items.forEach(item => {
    const optionIndex = options.findIndex(opt => opt.value === item.value);
    if (optionIndex >= 0) {
      options[optionIndex].isRefined = item.isRefined;
      // Don't update the count as it might be causing issues
      // options[optionIndex].count = item.count;
    }
  });

  // Default to Education if nothing is selected
  if (!options.some(option => option.isRefined)) {
    options[0].isRefined = true;
  }

  const handleClick = (value: string) => {
    // Only refine if clicking on a different option than the currently selected one
    if (!options.find(option => option.value === value)?.isRefined) {
      refine(value);
    }
  };

  return (
    <div className="flex gap-3 w-full">
      {options.map(option => (
        <button
          key={option.value}
          type="button"
          className={`flex-1 py-2 px-2 text-sm font-semibold rounded-lg cursor-pointer ${
            option.isRefined
              ? 'bg-brand-red text-white border-brand-red'
              : 'bg-white text-gray-700 border border-transparent'
          }`}
          onClick={() => handleClick(option.value)}
        >
          {option.label}
          {/* Remove the count display for now */}
        </button>
      ))}
    </div>
  );
}

// Custom sort component using HeadlessUI RadioGroup
function SortByRadioGroup() {
  const { refine, currentRefinement } = useSortBy({
    items: [
      { label: 'Relevance', value: 'opportunities' },
      { label: 'Proximity', value: 'opportunities_proximity' },
    ],
  });

  const { bookmarkedIds } = useOpportunities();
  const { setIndexUiState } = useInstantSearch();
  const [selectedSort, setSelectedSort] = useState(currentRefinement);
  const [isProximityEnabled, setIsProximityEnabled] = useState(
    currentRefinement === 'opportunities_proximity'
  );
  const { position, loading, error } = useGeolocationContext();

  // Update the selected sort when the refinement changes
  // But only if they're different to prevent loops
  useEffect(() => {
    if (selectedSort !== currentRefinement) {
      setSelectedSort(currentRefinement);
      setIsProximityEnabled(currentRefinement === 'opportunities_proximity');
    }
  }, [currentRefinement, selectedSort]);

  const handleSortChange = (value: string) => {
    // Don't update if the value is already selected
    if (value === selectedSort) return;

    // If selecting proximity sort, check if we have geolocation
    if (value === 'opportunities_proximity') {
      // If we don't have position yet and geolocation is not loading, request it
      if (!position && !loading && !error) {
        // Prompt user to allow location access if they haven't already
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(
            () => {
              // Successfully got location, continue with sort change
              setSelectedSort(value);
              setIsProximityEnabled(true);
              refine(value);
            },
            geoError => {
              // Handle geolocation error - maybe fallback to default sort
              console.error('Geolocation error:', geoError);
              alert(
                'Unable to get your location for proximity-based sorting. Please allow location access and try again.'
              );
            }
          );
          return; // Exit early, we'll handle the sort change in the callback
        } else {
          // Browser doesn't support geolocation
          alert(
            'Your browser does not support geolocation, which is required for proximity-based sorting.'
          );
          return;
        }
      }
    }

    setSelectedSort(value);
    setIsProximityEnabled(value === 'opportunities_proximity');

    // Use the standard refine method for all sort options
    refine(value);
  };

  return (
    <>
      <RadioGroup value={selectedSort} onChange={handleSortChange} className="space-y-2">
        {SORT_OPTIONS.map(option => (
          <Field key={option.value}>
            <Radio
              value={option.value}
              className={({ checked }) => `
              relative flex cursor-pointer rounded-lg px-2 py-1 focus:outline-none
              ${checked ? 'text-text-primary' : 'text-text-secondary'}
              ${option.value === 'opportunities:is_featured:desc' && (!bookmarkedIds || bookmarkedIds.length === 0) ? 'opacity-50' : ''}
              ${option.value === 'opportunities_proximity' && !position && !loading ? 'opacity-80' : ''}
            `}
              disabled={
                (option.value === 'opportunities:is_featured:desc' &&
                  (!bookmarkedIds || bookmarkedIds.length === 0)) ||
                (option.value === 'opportunities_proximity' && error !== null)
              }
            >
              {({ checked }) => (
                <div className="flex w-full items-center">
                  <div className="flex-shrink-0 mr-2">
                    <div
                      className={`
                    w-4 h-4 rounded-full border flex items-center justify-center
                    ${checked ? 'border-brand-red' : 'border-gray-300'}
                  `}
                    >
                      {checked && <div className="w-2 h-2 rounded-full bg-brand-red" />}
                    </div>
                  </div>
                  <Label className="text-sm">
                    {option.label}
                    {option.value === 'opportunities:is_featured:desc' && bookmarkedIds && (
                      <span className="ml-1 text-xs text-gray-500">({bookmarkedIds.length})</span>
                    )}
                    {option.value === 'opportunities_proximity' && loading && (
                      <span className="ml-1 text-xs text-gray-500">(Loading location...)</span>
                    )}
                    {option.value === 'opportunities_proximity' && error && (
                      <span className="ml-1 text-xs text-text-error">(Location unavailable)</span>
                    )}
                  </Label>
                </div>
              )}
            </Radio>
          </Field>
        ))}
      </RadioGroup>

      {/* Include the GeoSearchConfig component to handle proximity sorting */}
      <GeoSearchConfig enabled={isProximityEnabled} />
    </>
  );
}

export function OpportunitiesFilters() {
  const { refine: clearRefinements } = useClearRefinements();

  const handleClearAll = () => {
    clearRefinements();
  };

  return (
    <div className="divide-y divide-gray-200">
      {/* Add the hidden RefinementList for 'id' attribute */}
      <div style={{ display: 'none' }}>
        <RefinementList attribute="id" />
      </div>

      <div className="px-4 py-3 flex justify-between items-center !border-t-0">
        <div className="flex items-center gap-2">
          <FontAwesomeIcon icon={faSliders} className="size-4 text-brand-red" aria-hidden="true" />
          <h2 className="pa-eyebrow text-brand-red">SORT & FILTER</h2>
        </div>

        <button
          type="button"
          onClick={handleClearAll}
          className="pa-eyebrow text-brand-red"
          aria-label="Clear All"
        >
          <span className="sr-only">Clear All</span>
          <FontAwesomeIcon icon={faXmark} className="size-4 mr-1" aria-hidden="true" />
          Clear
        </button>
      </div>

      <BookmarkedFilter />

      {/* Sorting */}
      <Accordion title="Sort by" defaultOpen>
        <SortByRadioGroup />
      </Accordion>

      {/* Opportunity Type - Mutually exclusive selection */}
      <div className="px-4 py-3">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">Opportunity Type</h3>
        <div className="shadow-search-input rounded-lg px-2 py-2">
          <OpportunityTypeMenu />
        </div>
      </div>

      {/* Learning Type */}
      <FilterRefinementList attribute="subtype" title="Learning Type" />

      {/* Industries */}
      <FilterRefinementList attribute="industries" title="Industry" />

      {/* Location Type */}
      <FilterRefinementList attribute="location_type" title="Location Type" />

      {/* Term */}
      <FilterRefinementList attribute="term" title="Term" />
    </div>
  );
}
