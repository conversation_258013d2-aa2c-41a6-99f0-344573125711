import React from 'react';
import Link from 'next/link';
import {
  faCopy,
  faEllipsis,
  faPencil,
  faToggleOff,
  faToggleOn,
  faTrash,
} from '@fortawesome/pro-regular-svg-icons';
import { faSpinner } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';
import Toggle from '@/components/shared/form/Toggle';

interface OpportunityActionsProps {
  id: string | number;
  listed: boolean;
  handleToggleListed: (checked: boolean) => void;
  handleDuplicate: () => void;
  handleDelete: () => void;
  isProcessingToggle?: boolean;
  isProcessingDuplicate?: boolean;
  isProcessingDelete?: boolean;
}

const OpportunityActions: React.FC<OpportunityActionsProps> = ({
  id,
  listed,
  handleToggleListed,
  handleDuplicate,
  handleDelete,
  isProcessingToggle = false,
  isProcessingDuplicate = false,
  isProcessingDelete = false,
}) => {
  return (
    <>
      {/* Desktop Actions: Visible on 2xl screens */}
      <div className="hidden items-center gap-4 2xl:flex">
        <div className="flex items-center gap-2">
          <span className="text-text-secondary text-sm">{listed ? 'Listed' : 'Unlisted'}</span>
          <Toggle
            checked={listed}
            size="sm"
            onChange={handleToggleListed}
            disabled={isProcessingToggle}
          />
        </div>

        <Link
          href={`/postings/${id}`}
          aria-label="Edit opportunity"
          className="text-text-secondary hover:text-text-primary transition-colors"
        >
          <span className="sr-only">Edit opportunity</span>
          <FontAwesomeIcon icon={faPencil} className="size-4" aria-hidden="true" />
        </Link>

        <button
          type="button"
          onClick={handleDuplicate}
          className="text-text-secondary hover:text-text-primary transition-colors"
          aria-label="Copy opportunity link"
          disabled={isProcessingDuplicate}
        >
          <span className="sr-only">Copy opportunity link</span>
          <FontAwesomeIcon icon={faCopy} className="size-4" aria-hidden="true" />
        </button>

        <button
          type="button"
          onClick={handleDelete}
          className="text-text-secondary hover:text-text-primary transition-colors"
          aria-label="Delete opportunity"
          disabled={isProcessingDelete}
        >
          <span className="sr-only">Delete opportunity</span>
          <FontAwesomeIcon icon={faTrash} className="size-4" aria-hidden="true" />
        </button>
      </div>

      {/* Mobile Actions: Visible on screens smaller than 2xl */}
      <div className="flex items-center gap-4 2xl:hidden">
        <Menu as="div" className="relative inline-block text-left">
          <div>
            <MenuButton
              disabled={isProcessingToggle || isProcessingDuplicate || isProcessingDelete}
            >
              <span className="sr-only">Open menu</span>
              <FontAwesomeIcon icon={faEllipsis} className="size-4" aria-hidden="true" />
            </MenuButton>
          </div>

          <MenuItems
            transition
            className="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
          >
            <div className="py-1">
              <MenuItem
                as="button"
                onClick={() => handleToggleListed(!listed)}
                className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm text-gray-700 data-[focus]:bg-gray-100 data-[focus]:text-gray-900 data-[focus]:outline-none"
                aria-label={listed ? 'Unlist Opportunity' : 'List Opportunity'}
                disabled={isProcessingToggle}
              >
                <FontAwesomeIcon
                  icon={listed ? faToggleOn : faToggleOff}
                  className="size-4"
                  aria-hidden="true"
                />
                {listed ? 'Unlist Opportunity' : 'List Opportunity'}
              </MenuItem>
              <MenuItem
                as={Link}
                href={`/postings/${id}`}
                className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 data-[focus]:bg-gray-100 data-[focus]:text-gray-900 data-[focus]:outline-none"
                aria-label="Edit opportunity"
              >
                <FontAwesomeIcon icon={faPencil} className="size-4" aria-hidden="true" />
                Edit Opportunity
              </MenuItem>
              <MenuItem
                as="button"
                onClick={handleDuplicate}
                className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm text-gray-700 data-[focus]:bg-gray-100 data-[focus]:text-gray-900 data-[focus]:outline-none"
                aria-label="Duplicate opportunity"
                disabled={isProcessingDuplicate}
              >
                <FontAwesomeIcon icon={faCopy} className="size-4" aria-hidden="true" />
                Duplicate Opportunity
              </MenuItem>
              <MenuItem
                as="button"
                onClick={handleDelete}
                className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm text-gray-700 data-[focus]:bg-gray-100 data-[focus]:text-gray-900 data-[focus]:outline-none"
                aria-label="Delete opportunity"
                disabled={isProcessingDelete}
              >
                <FontAwesomeIcon icon={faTrash} className="size-4" aria-hidden="true" />
                Delete Opportunity
              </MenuItem>
            </div>
          </MenuItems>
        </Menu>
      </div>
    </>
  );
};

export default OpportunityActions;
