'use client';

import React from 'react';
import { faTrash, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from '@/components/shared/Button';

interface SponsorDeleteDialogProps {
  id: string | number;
  handleDelete: (id: string | number) => void;
  close: () => void;
  title?: string;
  message?: string;
}

export default function SponsorDeleteDialog({
  id,
  handleDelete,
  close,
  title = 'Delete Posting?',
  message = "If you'd prefer to hide this posting from Positive Athletes without deleting it altogether, you can set it to “Unlisted.”",
}: SponsorDeleteDialogProps) {
  return (
    <div className="p-6 bg-white space-y-4">
      <div className="block space-y-1 relative pr-4">
        <h2 className="text-2xl font-bold">{title}</h2>
        <p className="text-text-secondary">{message}</p>

        <button
          type="button"
          className="absolute top-0 right-0 z-[1]"
          onClick={close}
          aria-label="Close"
        >
          <span className="sr-only">Close</span>
          <FontAwesomeIcon icon={faXmark} aria-hidden="true" />
        </button>
      </div>

      <div className="flex items-center flex-wrap gap-4 pt-4">
        <Button color="red" icon={faTrash} iconPosition="right" onClick={() => handleDelete(id)}>
          Delete Posting
        </Button>

        <Button
          variant="filled"
          color="white"
          icon={faXmark}
          iconPosition="right"
          className="text-brand-blue"
          onClick={close}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
}
