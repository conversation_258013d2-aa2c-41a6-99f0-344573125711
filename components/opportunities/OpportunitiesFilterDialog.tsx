'use client';

import React from 'react';
import { faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Dialog, DialogBackdrop, DialogPanel, TransitionChild } from '@headlessui/react';
import { OpportunitiesFilters } from '@/components/opportunities/OpportunitiesFilters';

interface OpportunitiesFilterDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Mobile filter dialog component for opportunities
 */
export const OpportunitiesFilterDialog: React.FC<OpportunitiesFilterDialogProps> = ({
  isOpen,
  onClose,
}) => {
  return (
    <Dialog open={isOpen} className="relative z-50" onClose={onClose} unmount={false}>
      <DialogBackdrop
        className="fixed inset-0 bg-black/30 transition-opacity duration-500 ease-in-out data-[closed]:opacity-0"
        transition
      />

      <div className="fixed inset-0 overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
            <DialogPanel
              className="pointer-events-auto relative w-screen max-w-md transform transition duration-500 ease-in-out data-[closed]:translate-x-full sm:duration-700"
              transition
            >
              <TransitionChild>
                <div className="absolute left-0 top-0 -ml-8 flex pr-2 pt-4 duration-500 ease-in-out data-[closed]:opacity-0 sm:-ml-10 sm:pr-4 z-50">
                  <button
                    type="button"
                    onClick={onClose}
                    className="relative rounded-md text-gray-200 hover:text-white focus:outline-none focus:ring-2 focus:ring-white"
                  >
                    <span className="absolute -inset-2.5" />
                    <span className="sr-only">Close panel</span>
                    <FontAwesomeIcon
                      icon={faXmark}
                      className="!size-5 text-current"
                      aria-hidden="true"
                    />
                  </button>
                </div>
              </TransitionChild>

              <div className="flex h-full flex-col overflow-y-scroll bg-white py-6 shadow-xl">
                <div className="px-4 sm:px-6">
                  <div className="flex-1">
                    <OpportunitiesFilters />
                  </div>
                  <div className="py-4 border-t border-gray-200 mt-2">
                    <button
                      type="button"
                      className="w-full py-2 px-4 bg-brand-red text-white font-semibold rounded-lg"
                      onClick={onClose}
                    >
                      Done
                    </button>
                  </div>
                </div>
                <div className="relative mt-6 flex-1 px-4 sm:px-6"></div>
              </div>

              <div className="flex items-center justify-between px-4 border-b border-gray-200 pb-4 mb-2">
                <div className="flex items-center gap-2"></div>
              </div>
            </DialogPanel>
          </div>
        </div>
      </div>
    </Dialog>
  );
};

export default OpportunitiesFilterDialog;
