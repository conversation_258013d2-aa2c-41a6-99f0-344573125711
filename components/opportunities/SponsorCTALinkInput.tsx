import React, { useEffect, useState } from 'react';
import TextDropdownInput from '@/components/shared/form/TextDropdownInput';

interface SponsorCTALinkInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  required?: boolean;
  isFailedValidation?: boolean;
  onValidationChange?: (isValid: boolean) => void;
}

export const SponsorCTALinkInput: React.FC<SponsorCTALinkInputProps> = ({
  value,
  onChange,
  required = false,
  isFailedValidation = false,
  onValidationChange,
}) => {
  const [linkType, setLinkType] = useState<'url' | 'email'>('url');
  const [validationError, setValidationError] = useState<string | null>(null);

  // Define dynamic placeholders based on linkType
  const placeholders = {
    url: 'e.g., https://website.com',
    email: 'e.g., <EMAIL>',
  };

  // Auto-detect if input is an email or URL
  const detectInputType = (input: string): 'url' | 'email' => {
    // Simple check for email (contains @ symbol)
    if (input.includes('@')) {
      return 'email';
    }
    return 'url';
  };

  // Pure validation functions (don't set state)
  const isValidUrl = (url: string): { isValid: boolean; message: string | null } => {
    if (!url) return { isValid: !required, message: required ? 'URL is required' : null };

    try {
      // Add protocol if missing
      let urlToTest = url;
      if (!url.match(/^https?:\/\//i)) {
        urlToTest = 'https://' + url;
      }

      // Use URL constructor to validate
      new URL(urlToTest);
      return { isValid: true, message: null };
    } catch (e) {
      return { isValid: false, message: 'Please enter a valid URL' };
    }
  };

  const isValidEmail = (email: string): { isValid: boolean; message: string | null } => {
    if (!email) return { isValid: !required, message: required ? 'Email is required' : null };
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(email);
    return {
      isValid,
      message: isValid ? null : 'Please enter a valid email address',
    };
  };

  // Combined validation for either email or URL
  const isValidEmailOrUrl = (value: string): { isValid: boolean; message: string | null } => {
    if (!value) return { isValid: !required, message: required ? 'This field is required' : null };

    // First check if it's an email
    if (value.includes('@')) {
      return isValidEmail(value);
    }

    // Otherwise treat as URL
    return isValidUrl(value);
  };

  // Validate based on current type and update state
  const validateLink = (value: string, type: 'url' | 'email' = linkType) => {
    // Use the combined validation to allow either email or URL
    const validation = isValidEmailOrUrl(value);
    setValidationError(validation.message);

    // Notify parent component of validation status
    if (onValidationChange) {
      onValidationChange(validation.isValid);
    }

    return validation.isValid;
  };

  // Validate on initial load and when value changes
  useEffect(() => {
    if (value) {
      // Auto-detect type from value
      const detectedType = detectInputType(value);

      // Only change the linkType if it's different from current
      if (detectedType !== linkType) {
        setLinkType(detectedType);
      }

      // Validate using the combined validation
      const validation = isValidEmailOrUrl(value);
      setValidationError(validation.message);

      // Notify parent component of validation status
      if (onValidationChange) {
        onValidationChange(validation.isValid);
      }
    } else {
      // Empty value is valid unless required
      const isValid = !required;
      setValidationError(required ? 'This field is required' : null);
      if (onValidationChange) {
        onValidationChange(isValid);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, required, onValidationChange, linkType]);

  // Handle link type change
  const handleLinkTypeChange = (type: string) => {
    const newType = type as 'url' | 'email';
    setLinkType(newType);

    // Always validate when type changes, even if the value hasn't changed
    validateLink(value, newType);
  };

  return (
    <TextDropdownInput
      label="Action Button Link"
      name="apply_url"
      value={value}
      onChange={onChange}
      options={[
        { value: 'url', label: 'URL' },
        { value: 'email', label: 'Email' },
      ]}
      isFailedValidation={!!validationError || isFailedValidation}
      description={
        validationError ||
        'Enter a URL or email address where interested athletes can apply or learn more'
      }
      onOptionSelect={handleLinkTypeChange}
      selectedOption={linkType}
      placeholder={placeholders[linkType]}
    />
  );
};

export default SponsorCTALinkInput;
