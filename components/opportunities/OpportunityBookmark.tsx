import React from 'react';
import { faBookmark as faBookmarkRegular } from '@fortawesome/pro-regular-svg-icons';
import { faBookmark as faBookmarkSolid } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';

interface OpportunityBookmarkProps {
  isProcessing: boolean;
  isBookmarked: boolean;
  handleToggleBookmark: (e: React.MouseEvent<HTMLButtonElement>) => void;
}

const OpportunityBookmark: React.FC<OpportunityBookmarkProps> = ({
  isProcessing,
  isBookmarked,
  handleToggleBookmark,
}) => {
  return (
    <button
      type="button"
      onClick={handleToggleBookmark}
      disabled={isProcessing}
      className={clsx(
        'text-text-secondary hover:text-text-primary transition-colors relative',
        isProcessing && 'opacity-70 cursor-not-allowed'
      )}
      aria-label={isBookmarked ? 'Remove from bookmarks' : 'Add to bookmarks'}
      title={isBookmarked ? 'Remove from bookmarks' : 'Add to bookmarks'}
    >
      <span className="sr-only">{isBookmarked ? 'Remove bookmark' : 'Bookmark'}</span>
      <FontAwesomeIcon
        icon={isBookmarked ? faBookmarkSolid : faBookmarkRegular}
        className={clsx(
          'size-4 transition-all duration-200',
          isBookmarked ? 'text-brand-blue' : 'text-text-secondary',
          isProcessing && 'animate-pulse'
        )}
        aria-hidden="true"
      />
      {isProcessing && (
        <span className="absolute -top-1 -right-1 flex h-2 w-2">
          <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-brand-red opacity-75"></span>
          <span className="relative inline-flex rounded-full h-2 w-2 bg-brand-red"></span>
        </span>
      )}
    </button>
  );
};

export default OpportunityBookmark;
