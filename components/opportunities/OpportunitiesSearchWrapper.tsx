'use client';

import React, { useEffect } from 'react';
import { useOpportunities } from '@/hooks/useOpportunities';

interface OpportunitiesSearchWrapperProps {
  children: React.ReactNode;
}

/**
 * Wrapper component to ensure bookmarks are loaded before rendering children
 */
export const OpportunitiesSearchWrapper: React.FC<OpportunitiesSearchWrapperProps> = ({
  children,
}) => {
  return <>{children}</>;
};

export default OpportunitiesSearchWrapper;
