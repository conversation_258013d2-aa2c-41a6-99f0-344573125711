[build]
  command = "npm run build"
  publish = ".next"

[[plugins]]
  package = "@netlify/plugin-nextjs"

[build.environment]
  NODE_VERSION = "20"
  NEXT_TELEMETRY_DISABLED = "1"

[[headers]]
  for = "/_next/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/images/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Handle client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Ensure static assets are properly served
[[redirects]]
  from = "/images/*"
  to = "/images/:splat"
  status = 200
