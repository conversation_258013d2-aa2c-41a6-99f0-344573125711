<?php

use App\Services\VariableSubstitutionService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

it('can substitute user variables with correct field names', function () {
    $service = new VariableSubstitutionService();
    
    $userData = [
        'first_name' => '<PERSON>',
        'last_name' => '<PERSON>',
        'email' => '<EMAIL>',
        'profile_type' => 'positive_athlete',
    ];
    
    $service->setDataSources(['user' => $userData]);
    
    $template = 'Hello {user.first_name} {user.last_name}! Your email is {user.email} and you are a {user.profile_type}.';
    $result = $service->processTemplate($template);
    
    expect($result)->toBe('Hello John <PERSON>! Your <NAME_EMAIL> and you are a positive_athlete.');
});

it('can substitute contact variables', function () {
    $service = new VariableSubstitutionService();
    
    $contactData = [
        'first_name' => '<PERSON>',
        'last_name' => '<PERSON>',
        'email' => '<EMAIL>',
        'type' => 'positive_coach',
    ];
    
    $service->setDataSources(['contact' => $contactData]);
    
    $template = 'Dear {contact.first_name} {contact.last_name}, we have you listed as a {contact.type}.';
    $result = $service->processTemplate($template);
    
    expect($result)->toBe('Dear Emily Davis, we have you listed as a positive_coach.');
});

it('can substitute nominee variables', function () {
    $service = new VariableSubstitutionService();
    
    $nomineeData = [
        'first_name' => 'Sarah',
        'last_name' => 'Johnson',
        'sport' => 'Basketball',
        'school_name' => 'Example High School',
    ];
    
    $service->setDataSources(['nominee' => $nomineeData]);
    
    $template = 'Congratulations {nominee.first_name} {nominee.last_name} from {nominee.school_name} for your excellence in {nominee.sport}!';
    $result = $service->processTemplate($template);
    
    expect($result)->toBe('Congratulations Sarah Johnson from Example High School for your excellence in Basketball!');
});

it('can substitute system variables', function () {
    $service = new VariableSubstitutionService();
    
    $template = 'Welcome to {system.app_name}! Today is {system.current_date}.';
    $result = $service->processTemplate($template);
    
    expect($result)->toContain('Welcome to');
    expect($result)->toContain('Today is');
});

it('returns organized variables by context', function () {
    $service = new VariableSubstitutionService();
    $contexts = $service->getVariablesByContext();
    
    expect($contexts)->toHaveKey('user');
    expect($contexts)->toHaveKey('contact');
    expect($contexts)->toHaveKey('nominee');
    expect($contexts)->toHaveKey('nominator');
    expect($contexts)->toHaveKey('system');
    
    expect($contexts['user']['variables'])->toHaveKey('user.first_name');
    expect($contexts['user']['variables'])->toHaveKey('user.last_name');
    expect($contexts['contact']['variables'])->toHaveKey('contact.first_name');
    expect($contexts['nominee']['variables'])->toHaveKey('nominee.sport');
});

it('creates sample data with correct field names', function () {
    $service = new VariableSubstitutionService();
    $sampleData = $service->createSampleData();
    
    expect($sampleData['user'])->toHaveKey('first_name');
    expect($sampleData['user'])->toHaveKey('last_name');
    expect($sampleData['user'])->toHaveKey('profile_type');
    
    expect($sampleData['contact'])->toHaveKey('first_name');
    expect($sampleData['contact'])->toHaveKey('type');
    
    expect($sampleData['nominee'])->toHaveKey('first_name');
    expect($sampleData['nominee'])->toHaveKey('sport');
    expect($sampleData['nominee'])->toHaveKey('school_name');
});
