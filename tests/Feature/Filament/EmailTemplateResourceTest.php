<?php

namespace Tests\Feature\Filament;

use App\Filament\Resources\EmailTemplateResource;
use App\Models\EmailTemplate;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class EmailTemplateResourceTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin role if it doesn't exist
        Role::firstOrCreate(['name' => 'admin']);

        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        $this->adminUser->assignRole('admin');
    }

    public function test_can_render_email_template_index_page()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(EmailTemplateResource::getUrl('index'));

        $response->assertSuccessful();
    }

    public function test_can_render_email_template_create_page()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get(EmailTemplateResource::getUrl('create'));

        $response->assertSuccessful();
    }

    public function test_can_create_email_template()
    {
        $this->actingAs($this->adminUser);

        $templateData = [
            'name' => 'Welcome Email',
            'subject' => 'Welcome {user.firstname}!',
            'preview_text' => 'Welcome to {system.app_name}',
            'body' => '<p>Hello {user.firstname} {user.lastname},</p><p>Welcome to our platform!</p>',
            'is_active' => true,
        ];

        $this->post(EmailTemplateResource::getUrl('create'), $templateData);

        $this->assertDatabaseHas('email_templates', [
            'name' => 'Welcome Email',
            'subject' => 'Welcome {user.firstname}!',
            'created_by' => $this->adminUser->id,
        ]);
    }

    public function test_can_view_email_template()
    {
        $this->actingAs($this->adminUser);

        $template = EmailTemplate::factory()->create([
            'created_by' => $this->adminUser->id,
        ]);

        $response = $this->get(EmailTemplateResource::getUrl('view', ['record' => $template]));

        $response->assertSuccessful();
    }

    public function test_can_edit_email_template()
    {
        $this->actingAs($this->adminUser);

        $template = EmailTemplate::factory()->create([
            'created_by' => $this->adminUser->id,
        ]);

        $response = $this->get(EmailTemplateResource::getUrl('edit', ['record' => $template]));

        $response->assertSuccessful();
    }

    public function test_email_template_shows_variables_in_view()
    {
        $this->actingAs($this->adminUser);

        $template = EmailTemplate::factory()->create([
            'subject' => 'Hello {user.firstname}',
            'body' => 'Welcome {user.firstname} {user.lastname} to {system.app_name}!',
            'created_by' => $this->adminUser->id,
        ]);

        $variables = $template->getVariables();

        $this->assertContains('user.firstname', $variables);
        $this->assertContains('user.lastname', $variables);
        $this->assertContains('system.app_name', $variables);
    }
}
