<?php

use App\Filament\Resources\EmailTemplateResource;
use App\Models\EmailTemplate;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Spatie\Permission\Models\Role;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create admin role if it doesn't exist
    Role::firstOrCreate(['name' => 'admin']);

    $this->adminUser = User::factory()->create([
        'email' => '<EMAIL>',
    ]);

    $this->adminUser->assignRole('admin');
});

it('can render email template index page', function () {
    $this->actingAs($this->adminUser);

    $response = $this->get(EmailTemplateResource::getUrl('index'));

    $response->assertSuccessful();
});

it('can render email template create page', function () {
    $this->actingAs($this->adminUser);

    $response = $this->get(EmailTemplateResource::getUrl('create'));

    $response->assertSuccessful();
});

it('can create email template', function () {
    $this->actingAs($this->adminUser);

    $templateData = [
        'name' => 'Welcome Email',
        'subject' => 'Welcome {user.firstname}!',
        'preview_text' => 'Welcome to {system.app_name}',
        'body' => '<p>Hello {user.firstname} {user.lastname},</p><p>Welcome to our platform!</p>',
        'is_active' => true,
        'created_by' => $this->adminUser->id,
    ];

    Livewire::test(EmailTemplateResource\Pages\CreateEmailTemplate::class)
        ->fillForm($templateData)
        ->call('create')
        ->assertHasNoFormErrors();

    $this->assertDatabaseHas('email_templates', [
        'name' => 'Welcome Email',
        'subject' => 'Welcome {user.firstname}!',
        'created_by' => $this->adminUser->id,
    ]);
});

it('can view email template', function () {
    $this->actingAs($this->adminUser);

    $template = EmailTemplate::factory()->create([
        'created_by' => $this->adminUser->id,
    ]);

    $response = $this->get(EmailTemplateResource::getUrl('view', ['record' => $template]));

    $response->assertSuccessful();
});

it('can edit email template', function () {
    $this->actingAs($this->adminUser);

    $template = EmailTemplate::factory()->create([
        'created_by' => $this->adminUser->id,
    ]);

    $response = $this->get(EmailTemplateResource::getUrl('edit', ['record' => $template]));

    $response->assertSuccessful();
});

it('shows variables in email template view', function () {
    $this->actingAs($this->adminUser);

    $template = EmailTemplate::factory()->create([
        'subject' => 'Hello {user.firstname}',
        'body' => 'Welcome {user.firstname} {user.lastname} to {system.app_name}!',
        'created_by' => $this->adminUser->id,
    ]);

    $variables = $template->getVariables();

    expect($variables)->toContain('user.firstname');
    expect($variables)->toContain('user.lastname');
    expect($variables)->toContain('system.app_name');
});
