import type { Config } from 'tailwindcss';

export default {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './views/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    fontFamily: {
      sans: ['var(--font-figtree)', 'system-ui', 'sans-serif'],
      oxanium: ['var(--font-oxanium)', 'system-ui', 'sans-serif'],
    },
    extend: {
      spacing: {
        120: '120px',
        160: '160px',
      },
      fontSize: {
        // Display styles
        'display-xl': ['45px', { lineHeight: '1.2', letterSpacing: '-0.02em' }],
        'display-lg': ['36px', { lineHeight: '1.2', letterSpacing: '-0.02em' }],
        'display-md': ['30px', { lineHeight: '1.2', letterSpacing: '-0.02em' }],
        'display-sm': ['24px', { lineHeight: '1.2', letterSpacing: '-0.02em' }],
        'display-xs': ['20px', { lineHeight: '1.2', letterSpacing: '-0.02em' }],

        // Heading styles
        'heading-2xl': ['32px', { lineHeight: '1.3', letterSpacing: '-0.01em' }],
        'heading-xl': ['28px', { lineHeight: '1.3', letterSpacing: '-0.01em' }],
        'heading-lg': ['24px', { lineHeight: '1.3', letterSpacing: '-0.01em' }],
        'heading-md': ['20px', { lineHeight: '1.3', letterSpacing: '-0.01em' }],
        'heading-sm': ['16px', { lineHeight: '1.3', letterSpacing: '-0.01em' }],
        'heading-xs': ['14px', { lineHeight: '1.3', letterSpacing: '-0.01em' }],

        // Body styles - Updated to match design system
        'text-2xl': ['24px', { lineHeight: '1.5', letterSpacing: '0' }], // New largest text size
        'text-xl': ['20px', { lineHeight: '1.5', letterSpacing: '0' }], // Previously body-xl
        'text-lg': ['18px', { lineHeight: '1.5', letterSpacing: '0' }], // New intermediate size
        'text-md': ['16px', { lineHeight: '1.5', letterSpacing: '0' }], // Standard text size
        'text-sm': ['14px', { lineHeight: '1.5', letterSpacing: '0' }], // Small text
        'text-xs': ['12px', { lineHeight: '1.5', letterSpacing: '0' }], // Extra small text

        // Label styles - Updated to match design system
        'label-xl': ['16px', { lineHeight: '1.2', letterSpacing: '0.02em', fontWeight: '600' }],
        'label-lg': ['14px', { lineHeight: '1.2', letterSpacing: '0.02em', fontWeight: '600' }],
        'label-md': ['12px', { lineHeight: '1.2', letterSpacing: '0.02em', fontWeight: '600' }],
        'label-sm': ['11px', { lineHeight: '1.2', letterSpacing: '0.02em', fontWeight: '600' }],
        'label-xs': ['10px', { lineHeight: '1.2', letterSpacing: '0.02em', fontWeight: '600' }],

        // Caption styles - New addition from design system
        'caption-lg': ['14px', { lineHeight: '1.4', letterSpacing: '0.01em' }],
        'caption-md': ['12px', { lineHeight: '1.4', letterSpacing: '0.01em' }],
        'caption-sm': ['11px', { lineHeight: '1.4', letterSpacing: '0.01em' }],
      },
      fontWeight: {
        regular: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
        heavy: '800', // Added for extra bold text
      },
      borderRadius: {
        '4xl': '2rem',
      },
      boxShadow: {
        'avatar-outer':
          '0px 4px 4px rgba(255, 255, 255, 0.85), 0px 10px 16px rgba(0, 0, 0, 0.05), 0px -3px 4px rgba(0, 0, 0, 0.1), inset 0px 4px 6px rgba(0,0,0, 0.1)',
        card: '0px 8px 20px 0px rgba(0, 0, 0, 0.04)',
        'neumorphism-inset-small':
          '0px -3px 4px 0px rgba(0,0,0,0.10), 0px 10px 16px 0px rgba(0,0,0,0.05), 0px 4px 4px 0px rgba(255,255,255,1.00), inset 0px 4px 6px 0px rgba(0,0,0,0.10)',
        'search-input':
          '0px 4px 4px rgb(255 255 255), 0px 10px 16px rgb(0 0 0 / 0.05), 0px -3px 4px rgb(0 0 0 / 0.1), inset 0px 4px 6px rgb(0 0 0 / 0.1)',
        sidebar: '4px 0px 20px 0px rgba(0, 0, 0, 0.05)',
        header: '0px 8px 20px 0px rgba(0, 0, 0, 0.04)',
      },
      colors: {
        // Brand Colors
        brand: {
          blue: '#002855', // Bold Blue
          red: '#ce0d2f', // Passion Red
          white: '#FFFFFF', // Winning White
          grey: '#C3C3C3', // Gameday Grey
        },
        // Text Colors
        text: {
          primary: '#383838', // Grey 7
          secondary: '#777D80', // Grey 5
          'primary-reverse': '#FFFFFF', // White
          'secondary-reverse': '#D7DCDE', // Grey 3
          red: '#ce0d2f', // Passion Red
          blue: '#002855', // Bold Blue
        },
        // Surface Colors
        surface: {
          primary: '#FFFFFF', // White
          secondary: '#F6F6F6', // Grey 1
          tertiary: '#EDEFF0', // Grey 2
        },
        // Stroke Colors
        stroke: {
          weak: '#EDEFF0', // Grey 2
          strong: '#A5ABAD', // Grey 4
        },
        // Greyscale
        grey: {
          1: '#F6F6F6',
          2: '#EDEFF0',
          3: '#D7DCDE',
          4: '#A5ABAD',
          5: '#777D80',
          6: '#414547',
          7: '#383838',
          8: '#060F11',
        },
        // Utility Colors
        utility: {
          success: '#249B00',
          'success-light': '#F9FFF7',
          alert: '#D59500',
          danger: '#920038',
          'danger-light': '#FFF7F7',
        },
        'brand-red': '#ce0d2f',
        'surface-secondary': '#F6F6F6',
        'grey-4': '#98A2B3',
      },
      maxWidth: {
        '8xl': '90rem',
      },
    },
  },
  plugins: [require('@tailwindcss/typography'), require('tailwind-scrollbar')],
} satisfies Config;
