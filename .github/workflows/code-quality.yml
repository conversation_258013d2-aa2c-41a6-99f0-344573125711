name: Code Quality Checks

on:
  # Run on all push events to any branch
  push:
    branches: ['**'] # matches all branches
  # Also run on PRs to main and develop
  pull_request:
    branches: [main, develop]

jobs:
  code-quality:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: TypeScript Check
        run: npx tsc --noEmit
