# .cursorrules for Next.js Project

# Technologies: Tai<PERSON>wind CSS, Next.js (App Router), TypeScript, Axios, TanStack Query, Zustand, Headless UI, Zod

# Key Focus: TypeScript Best Practices, OOP with SOLID Principles, and Modular Code

# IMPORTANT

- <PERSON>VER UPDATE THE LARAVEL PROJECT IN `/positive-athlete` unless the user includes "UPDATE_BACKEND" in the request. You should only update the client in `/positive-athlete-client`. Only reference the Laravel project to write code in the `/positive-athlete-client` project.
- We use SSR, so make sure you are building components that need client side rendering in a way that will work to avoid SSR hydration issues.
- Page Parameters in Next.js 15: All page parameters (params and searchParams) are Promises. Always:

  1. Type params as `type Params = Promise<{ paramName: string }>`
  2. Accept full props object: `props: { params: Params }`
  3. Await params before use: `const params = await props.params`
     Never destructure params directly from props or treat them as non-Promise values.

# CODE ORGANIZATION

- Follow feature-first organization:
  ```
  src/
    features/
      auth/
      profile/
      x-factor/
    shared/
    utils/
    hooks/
    types/
  ```

- Components should be organized by:
  - shared/ - Reusable across multiple features
  - features/ - Feature-specific components
  - layouts/ - Layout components
  - ui/ - Basic UI components

# TYPESCRIPT AND TYPE SAFETY

- Use TypeScript "strict" mode:
  - Avoid `any` whenever possible; prefer generics, interfaces, or `unknown` over `any`
  - Enable strict flags such as `strictNullChecks`, `noImplicitAny`, and `strictPropertyInitialization`
  - Define clear return types for functions and hooks unless inference is obvious and safe

- Implement proper type safety for:
  - API responses
  - Route parameters
  - Search parameters
  - Form data
  - Component props

- Ensure all code compiles and passes lint checks before PR merges:
  - Leverage ESLint + Prettier for consistent formatting and style
  - Use TypeScript's compiler checks as part of the CI process

# NEXT.JS APP ROUTER

- Organize the application using the Next.js App Router best practices:
  - Keep route files in `app/[route]/page.tsx` (or `.tsx` suffix for TypeScript)
  - Use `layout.tsx` for shared layouts. Keep layout code minimal and delegates specifics to child components
  - Co-locate client components (marked with `"use client"`) where interactivity is required
  - Use server components by default if no interactivity or state is needed

- Follow Next.js 14+ App Router conventions:
  - Group routes using (folder) convention
  - Use loading.tsx, error.tsx, and not-found.tsx for better UX
  - Implement proper layout inheritance
  - Handle dynamic routes with proper typing

# STATE MANAGEMENT

- Local State: React useState
- Server State: TanStack Query
- Global State: Zustand
- Form State: React Hook Form + Zod

# AXIOS (HTTP CLIENT)

- Axios usage:
  - Create a dedicated Axios instance with interceptors as needed (e.g., for auth tokens, error logging)
  - Keep the configuration in a single `api/axiosInstance.ts` (or similar)
  - Avoid scattering raw `axios.get()` or `axios.post()` calls everywhere—use reusable functions or custom hooks

- Consistency:
  - Ensure that all Axios calls return typed responses (e.g., `Promise<MyDataResponse>`)
  - Centralize error handling and logging within interceptors or shared utilities

# TANSTACK QUERY (DATA FETCHING)

- Usage:
  - Perform data fetching inside custom hooks (e.g., `useGetUsers()`, `useUpdateProfile()`) that wrap TanStack Query
  - Return typed data from hooks for clarity (e.g., `return useQuery<MyData>(...)`)
  - Use query keys carefully to avoid collisions and keep them consistent across the codebase

- Guidelines:
  - Keep query and mutation logic simple—abstract complex data transformations into helper functions
  - Use background revalidation features for real-time or near real-time data
  - Combine with Axios instance for consistent request/response handling

# ZUSTAND (GLOBAL STATE MANAGEMENT)

- Store usage:
  - Use Zustand for global states that do not belong in React Query caches (e.g., multi-step form states, user UI preferences)
  - Create dedicated slices in separate files (e.g., `authStore.ts`, `formStore.ts`) following best practices
  - Keep store logic minimal and typed (e.g., define store shapes via interfaces)
  - Use actions that clearly describe state transitions, following OOP or Redux-like patterns for clarity

- Best practices:
  - Avoid overloading a single global store with unrelated data
  - Keep derived or computed state in selectors or separate utility functions

# FORM HANDLING

- Use React Hook Form for complex forms:
  - Combine with Zod for schema validation
  - Create reusable form components
  - Implement proper error handling and display
  - Use controlled components only when necessary

- Form Best Practices:
  - Create type-safe form schemas
  - Implement proper client-side validation
  - Handle server-side validation errors
  - Use proper ARIA attributes for accessibility

# COMPONENT PATTERNS

- Implement proper component patterns:
  - Container/Presenter pattern
  - Render props when needed
  - Proper prop typing
  - Proper event handling

- Performance Optimization:
  - Use proper image optimization with next/image
  - Implement proper loading states
  - Use proper suspense boundaries
  - Implement proper error boundaries

# ACCESSIBILITY

- Follow WCAG 2.1 Level AA standards:
  - Implement proper heading hierarchy
  - Ensure proper color contrast
  - Provide alt text for images
  - Use semantic HTML elements
  - Implement proper keyboard navigation
  - Test with screen readers

# AUTHENTICATION

- Use proper route protection
- Handle auth state with session
- Implement proper redirects
- Type session data properly

# ERROR HANDLING

- Implement proper error boundaries
- Handle API errors consistently
- Implement proper error logging
- Show appropriate error messages

# INTERNATIONALIZATION

- Use proper i18n patterns
- Handle RTL layouts
- Implement proper date/time formatting
- Handle number formatting

# ENVIRONMENT CONFIGURATION

- Use proper environment variables
- Handle different environments
- Implement proper configuration management
- Handle secrets properly