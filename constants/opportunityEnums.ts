/**
 * Constants for opportunity types and related enums
 * Matched with backend enum values
 */

export const OPPORTUNITY_TYPES = {
  EDUCATION: 'education',
  EMPLOYMENT: 'employment',
} as const;

export type OpportunityType = (typeof OPPORTUNITY_TYPES)[keyof typeof OPPORTUNITY_TYPES];

// Employment subtypes
export const OPPORTUNITY_SUBTYPES = {
  // Educational Subtypes
  CONTINUING_EDUCATION: 'continuing_education',
  DEGREE_PROGRAM: 'degree_program',
  CERTIFICATE_PROGRAM: 'certificate_program',

  // Employment Subtypes
  FULL_TIME_JOB: 'full_time_job',
  PART_TIME_JOB: 'part_time_job',
  SHORT_TERM_CONTRACTING: 'short_term_contracting',
  INTERNSHIP: 'internship',
  APPRENTICESHIP: 'apprenticeship',
} as const;

export type OpportunitySubtype = (typeof OPPORTUNITY_SUBTYPES)[keyof typeof OPPORTUNITY_SUBTYPES];

// Location types
export const OPPORTUNITY_LOCATION_TYPES = {
  REMOTE: 'remote',
  HYBRID: 'hybrid',
  ONSITE: 'onsite',
} as const;

export type OpportunityLocationType =
  (typeof OPPORTUNITY_LOCATION_TYPES)[keyof typeof OPPORTUNITY_LOCATION_TYPES];

// Terms
export const OPPORTUNITY_TERMS = {
  INDEFINITE: 'indefinite',
  THREE_TO_FOUR_YEARS: '3-4_years',
  ONE_TO_TWO_YEARS: '1-2_years',
  LESS_THAN_ONE_YEAR: '<1_year',
} as const;

export type OpportunityTerm = (typeof OPPORTUNITY_TERMS)[keyof typeof OPPORTUNITY_TERMS];

// For form options
export const opportunityTypeOptions = [
  { value: OPPORTUNITY_TYPES.EMPLOYMENT, label: 'Employment' },
  { value: OPPORTUNITY_TYPES.EDUCATION, label: 'Education' },
];

export const opportunitySubtypeOptions = {
  [OPPORTUNITY_TYPES.EDUCATION]: [
    { value: OPPORTUNITY_SUBTYPES.CONTINUING_EDUCATION, label: 'Continuing Education' },
    { value: OPPORTUNITY_SUBTYPES.DEGREE_PROGRAM, label: 'Degree Program' },
    { value: OPPORTUNITY_SUBTYPES.CERTIFICATE_PROGRAM, label: 'Certificate Program' },
  ],
  [OPPORTUNITY_TYPES.EMPLOYMENT]: [
    { value: OPPORTUNITY_SUBTYPES.FULL_TIME_JOB, label: 'Full-Time' },
    { value: OPPORTUNITY_SUBTYPES.PART_TIME_JOB, label: 'Part-Time' },
    { value: OPPORTUNITY_SUBTYPES.SHORT_TERM_CONTRACTING, label: 'Short Term Contract' },
    { value: OPPORTUNITY_SUBTYPES.INTERNSHIP, label: 'Internship' },
    { value: OPPORTUNITY_SUBTYPES.APPRENTICESHIP, label: 'Apprenticeship' },
  ],
};

export const opportunityLocationTypeOptions = [
  { value: OPPORTUNITY_LOCATION_TYPES.HYBRID, label: 'Hybrid' },
  { value: OPPORTUNITY_LOCATION_TYPES.REMOTE, label: 'Remote' },
  { value: OPPORTUNITY_LOCATION_TYPES.ONSITE, label: 'On-site' },
];

export const opportunityTermOptions = [
  { value: OPPORTUNITY_TERMS.INDEFINITE, label: 'Indefinite/Long-term' },
  { value: OPPORTUNITY_TERMS.THREE_TO_FOUR_YEARS, label: '3-4 Years' },
  { value: OPPORTUNITY_TERMS.ONE_TO_TWO_YEARS, label: '1-2 Years' },
  { value: OPPORTUNITY_TERMS.LESS_THAN_ONE_YEAR, label: 'Less than 1 Year' },
];
