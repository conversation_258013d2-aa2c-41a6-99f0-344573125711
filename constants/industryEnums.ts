/**
 * Constants for industries
 */

export const INDUSTRY_IDS = {
  CONSTRUCTION: 1,
  HEALTHCARE: 2,
  TECHNOLOGY: 3,
  EDUCATION: 4,
  MANUFACTURING: 5,
  RETAIL: 6,
  FINANCE_BANKING: 7,
  ENERGY: 8,
  TRANSPORTATION_LOGISTICS: 9,
  HOSPITALITY: 10,
  CREATIVE_ARTS: 11,
  AGRICULTURE: 12,
  PROFESSIONAL_SERVICES: 13,
  SCIENCE_RESEARCH: 14,
  GOVERNMENT: 15,
  NONPROFIT: 16,
} as const;

export type IndustryId = (typeof INDUSTRY_IDS)[keyof typeof INDUSTRY_IDS];

export const INDUSTRY_NAMES = {
  CONSTRUCTION: 'Construction',
  HEALTHCARE: 'Healthcare',
  TECHNOLOGY: 'Technology',
  EDUCATION: 'Education',
  MANUFACTURING: 'Manufacturing',
  RETAIL: 'Retail',
  FINANCE_BANKING: 'Finance & Banking',
  ENERGY: 'Energy',
  TRANSPORTATION_LOGISTICS: 'Transportation & Logistics',
  HOSPITALITY: 'Hospitality',
  CREATIVE_ARTS: 'Creative Arts',
  AGRICULTURE: 'Agriculture',
  PROFESSIONAL_SERVICES: 'Professional Services',
  SCIENCE_RESEARCH: 'Science & Research',
  GOVERNMENT: 'Government',
  NONPROFIT: 'Nonprofit',
} as const;

export type IndustryName = (typeof INDUSTRY_NAMES)[keyof typeof INDUSTRY_NAMES];

// Industry mapping
export const INDUSTRY_ID_TO_NAME_MAP: Record<IndustryId, IndustryName> = {
  [INDUSTRY_IDS.CONSTRUCTION]: INDUSTRY_NAMES.CONSTRUCTION,
  [INDUSTRY_IDS.HEALTHCARE]: INDUSTRY_NAMES.HEALTHCARE,
  [INDUSTRY_IDS.TECHNOLOGY]: INDUSTRY_NAMES.TECHNOLOGY,
  [INDUSTRY_IDS.EDUCATION]: INDUSTRY_NAMES.EDUCATION,
  [INDUSTRY_IDS.MANUFACTURING]: INDUSTRY_NAMES.MANUFACTURING,
  [INDUSTRY_IDS.RETAIL]: INDUSTRY_NAMES.RETAIL,
  [INDUSTRY_IDS.FINANCE_BANKING]: INDUSTRY_NAMES.FINANCE_BANKING,
  [INDUSTRY_IDS.ENERGY]: INDUSTRY_NAMES.ENERGY,
  [INDUSTRY_IDS.TRANSPORTATION_LOGISTICS]: INDUSTRY_NAMES.TRANSPORTATION_LOGISTICS,
  [INDUSTRY_IDS.HOSPITALITY]: INDUSTRY_NAMES.HOSPITALITY,
  [INDUSTRY_IDS.CREATIVE_ARTS]: INDUSTRY_NAMES.CREATIVE_ARTS,
  [INDUSTRY_IDS.AGRICULTURE]: INDUSTRY_NAMES.AGRICULTURE,
  [INDUSTRY_IDS.PROFESSIONAL_SERVICES]: INDUSTRY_NAMES.PROFESSIONAL_SERVICES,
  [INDUSTRY_IDS.SCIENCE_RESEARCH]: INDUSTRY_NAMES.SCIENCE_RESEARCH,
  [INDUSTRY_IDS.GOVERNMENT]: INDUSTRY_NAMES.GOVERNMENT,
  [INDUSTRY_IDS.NONPROFIT]: INDUSTRY_NAMES.NONPROFIT,
};

// Industry name to ID mapping
export const INDUSTRY_NAME_TO_ID_MAP: Record<IndustryName, IndustryId> = {
  [INDUSTRY_NAMES.CONSTRUCTION]: INDUSTRY_IDS.CONSTRUCTION,
  [INDUSTRY_NAMES.HEALTHCARE]: INDUSTRY_IDS.HEALTHCARE,
  [INDUSTRY_NAMES.TECHNOLOGY]: INDUSTRY_IDS.TECHNOLOGY,
  [INDUSTRY_NAMES.EDUCATION]: INDUSTRY_IDS.EDUCATION,
  [INDUSTRY_NAMES.MANUFACTURING]: INDUSTRY_IDS.MANUFACTURING,
  [INDUSTRY_NAMES.RETAIL]: INDUSTRY_IDS.RETAIL,
  [INDUSTRY_NAMES.FINANCE_BANKING]: INDUSTRY_IDS.FINANCE_BANKING,
  [INDUSTRY_NAMES.ENERGY]: INDUSTRY_IDS.ENERGY,
  [INDUSTRY_NAMES.TRANSPORTATION_LOGISTICS]: INDUSTRY_IDS.TRANSPORTATION_LOGISTICS,
  [INDUSTRY_NAMES.HOSPITALITY]: INDUSTRY_IDS.HOSPITALITY,
  [INDUSTRY_NAMES.CREATIVE_ARTS]: INDUSTRY_IDS.CREATIVE_ARTS,
  [INDUSTRY_NAMES.AGRICULTURE]: INDUSTRY_IDS.AGRICULTURE,
  [INDUSTRY_NAMES.PROFESSIONAL_SERVICES]: INDUSTRY_IDS.PROFESSIONAL_SERVICES,
  [INDUSTRY_NAMES.SCIENCE_RESEARCH]: INDUSTRY_IDS.SCIENCE_RESEARCH,
  [INDUSTRY_NAMES.GOVERNMENT]: INDUSTRY_IDS.GOVERNMENT,
  [INDUSTRY_NAMES.NONPROFIT]: INDUSTRY_IDS.NONPROFIT,
};

// Industry options for dropdowns
export const industryOptions = Object.entries(INDUSTRY_NAME_TO_ID_MAP).map(([name, id]) => ({
  value: name,
  label: name,
  id,
}));
