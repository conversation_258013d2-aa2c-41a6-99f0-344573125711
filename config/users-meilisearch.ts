import { instantMeiliSearch } from '@meilisearch/instant-meilisearch';
import { MeiliSearch } from 'meilisearch';

const MEILISEARCH_HOST = process.env.NEXT_PUBLIC_MEILISEARCH_HOST || 'http://localhost:7700';
const MEILISEARCH_API_KEY = process.env.NEXT_PUBLIC_MEILISEARCH_API_KEY || '';

// Create a direct MeiliSearch client for testing connection
export const meiliSearchClient = new MeiliSearch({
  host: MEILISEARCH_HOST,
  apiKey: MEILISEARCH_API_KEY,
});

export const USERS_INDEX = 'users';

/**
 * Test connection to MeiliSearch
 * This function checks if the MeiliSearch server is running and accessible
 */
export const testMeiliSearchConnection = async () => {
  try {
    const health = await meiliSearchClient.health();
    console.log('MeiliSearch connection successful:', health);
    return { success: true, data: health };
  } catch (error) {
    console.error('MeiliSearch connection failed:', error);
    return { success: false, error };
  }
};

/**
 * Search users
 *
 * Searchable attributes:
 * - first_name
 * - last_name
 * - high_school
 * - career_interests
 * - state
 */
export const searchUsers = async (query: string) => {
  try {
    const results = await meiliSearchClient.index(USERS_INDEX).search(query);
    console.log('MeiliSearch users search results:', results);
    return { success: true, data: results };
  } catch (error) {
    console.error('MeiliSearch users search failed:', error);
    return { success: false, error };
  }
};

/**
 * Search with filters
 *
 * Available filterable attributes:
 * - profile_type
 * - graduation_year
 * - state_code
 * - career_interests
 * - id (for excluding blocked users)
 *
 * Example filters:
 * - 'profile_type = "positive_athlete"'
 * - 'graduation_year = 2025'
 * - 'state_code = "NY"'
 * - 'career_interests = "Engineering"'
 */
export const searchWithFilters = async (query: string, filters: string) => {
  try {
    const results = await meiliSearchClient.index(USERS_INDEX).search(query, {
      filter: filters,
    });
    console.log('MeiliSearch filtered users search results:', results);
    return { success: true, data: results };
  } catch (error) {
    console.error('MeiliSearch filtered users search failed:', error);
    return { success: false, error };
  }
};

/**
 * Get all users
 *
 * This function retrieves documents from the users index using search
 * instead of direct document access, which requires fewer permissions
 */
export const getAllUsers = async (limit = 20) => {
  try {
    // Using search with empty query instead of getDocuments
    const results = await meiliSearchClient.index(USERS_INDEX).search('', {
      limit,
    });
    console.log('All users:', results);
    return { success: true, data: results };
  } catch (error) {
    console.error('Failed to get all users:', error);
    return { success: false, error };
  }
};

/**
 * Configure the Meilisearch index settings
 * This function sets up the necessary configuration for the users index
 * Call this function once during your app initialization
 */
export const configureUsersIndex = async () => {
  try {
    // Instead of trying to update settings, just log that we're using the public API key
    console.log('Using public API key for Meilisearch - skipping index configuration');

    // Note: Index configuration should be done on the backend side
    return { success: true };
  } catch (error) {
    console.error('Failed to log Meilisearch configuration info:', error);
    return { success: false, error };
  }
};

// Create an InstantMeiliSearch client for React InstantSearch
const { searchClient } = instantMeiliSearch(MEILISEARCH_HOST, MEILISEARCH_API_KEY, {
  primaryKey: 'id',
  // Add MeiliSearch client options
  finitePagination: true,
  keepZeroFacets: true,
});

// Export the searchClient for use with React InstantSearch
export { searchClient };

/**
 * Create a filter to exclude a user by ID
 *
 * @param userId The ID of the user to exclude
 * @returns A filter string in MeiliSearch format
 */
export const createUserExclusionFilter = (userId: number | string | null | undefined): string => {
  if (!userId) return '';
  return `NOT id = ${userId}`;
};
