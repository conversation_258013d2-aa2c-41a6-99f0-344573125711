import { instantMeiliSearch } from '@meilisearch/instant-meilisearch';
import type { InstantMeiliSearchInstance } from '@meilisearch/instant-meilisearch';
import { MeiliSearch } from 'meilisearch';

const MEILISEARCH_HOST = process.env.NEXT_PUBLIC_MEILISEARCH_HOST || 'http://localhost:7700';
const MEILISEARCH_API_KEY = process.env.NEXT_PUBLIC_MEILISEARCH_API_KEY || '';

// Create a direct MeiliSearch client for testing connection
export const meiliSearchClient = new MeiliSearch({
  host: MEILISEARCH_HOST,
  apiKey: MEILISEARCH_API_KEY,
});

/**
 * Test connection to MeiliSearch
 * This function checks if the MeiliSearch server is running and accessible
 */
export const testMeiliSearchConnection = async () => {
  try {
    const health = await meiliSearchClient.health();
    console.log('MeiliSearch connection successful:', health);
    return { success: true, data: health };
  } catch (error) {
    console.error('MeiliSearch connection failed:', error);
    return { success: false, error };
  }
};

/**
 * Search opportunities
 *
 * Searchable attributes:
 * - title
 * - description
 * - location
 * - city
 * - state_code
 * - details
 * - qualifications
 * - responsibilities
 * - benefits
 * - organization_name
 * - industries
 */
export const searchOpportunities = async (query: string) => {
  try {
    const results = await meiliSearchClient.index(OPPORTUNITIES_INDEX).search(query);
    console.log('MeiliSearch search results:', results);
    return { success: true, data: results };
  } catch (error) {
    console.error('MeiliSearch search failed:', error);
    return { success: false, error };
  }
};

/**
 * Search with filters
 *
 * Available filterable attributes:
 * - type (e.g., "education")
 * - subtype
 * - status (e.g., "active")
 * - state_code (e.g., "NY")
 * - location_type (e.g., "onsite")
 * - term (e.g., "1-2_years")
 * - is_featured (boolean)
 * - industries (array of strings)
 * - id (for bookmarking filter)
 *
 * Example filters:
 * - 'type = "education"'
 * - 'status = "active"'
 * - 'is_featured = true'
 * - 'industries = "Education"'
 * - 'type = "education" AND state_code = "NY"'
 * - 'id = 47'
 */
export const searchWithFilters = async (query: string, filters: string) => {
  try {
    const results = await meiliSearchClient.index(OPPORTUNITIES_INDEX).search(query, {
      filter: filters,
    });
    console.log('MeiliSearch filtered search results:', results);
    return { success: true, data: results };
  } catch (error) {
    console.error('MeiliSearch filtered search failed:', error);
    return { success: false, error };
  }
};

/**
 * Search with sorting
 *
 * Available sortable attributes:
 * - created_at
 * - updated_at
 * - is_featured
 *
 * Example sort parameters:
 * - ['created_at:asc']
 * - ['created_at:desc']
 * - ['is_featured:desc']
 * - ['is_featured:desc', 'created_at:desc']
 */
export const searchWithSorting = async (query: string, sortBy: string[]) => {
  try {
    const results = await meiliSearchClient.index(OPPORTUNITIES_INDEX).search(query, {
      sort: sortBy,
    });
    console.log('MeiliSearch sorted search results:', results);
    return { success: true, data: results };
  } catch (error) {
    console.error('MeiliSearch sorted search failed:', error);
    return { success: false, error };
  }
};

/**
 * Get all opportunities
 *
 * This function retrieves documents from the opportunities index using search
 * instead of direct document access, which requires fewer permissions
 */
export const getAllOpportunities = async (limit = 20) => {
  try {
    // Using search with empty query instead of getDocuments
    const results = await meiliSearchClient.index(OPPORTUNITIES_INDEX).search('', {
      limit,
    });
    console.log('All opportunities:', results);
    return { success: true, data: results };
  } catch (error) {
    console.error('Failed to get all opportunities:', error);
    return { success: false, error };
  }
};

// Create an InstantMeiliSearch client for React InstantSearch
const { searchClient } = instantMeiliSearch(MEILISEARCH_HOST, MEILISEARCH_API_KEY, {
  primaryKey: 'id',
  // Add MeiliSearch client options
  finitePagination: true,
  keepZeroFacets: true,
});

// Export the searchClient for use with React InstantSearch
export { searchClient };
export const OPPORTUNITIES_INDEX = 'opportunities';
