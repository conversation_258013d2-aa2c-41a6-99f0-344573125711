import type { Module, Test as ModuleTest } from '@/types/module';
import type { Answer, Question, Test as QuizTest } from '@/types/quiz';

export const mockTest: QuizTest = {
  id: 1,
  moduleId: 1,
  type: 'quiz',
  questions: [
    {
      id: 1,
      testId: 1,
      question: 'What is the key to maintaining focus during high-pressure situations?',
      type: 'multiple_choice',
      answers: [
        {
          id: 1,
          questionId: 1,
          answer: 'Deep breathing and visualization techniques',
          isCorrect: true,
        },
        {
          id: 2,
          questionId: 1,
          answer: 'Thinking about past failures',
          isCorrect: false,
        },
        {
          id: 3,
          questionId: 1,
          answer: 'Ignoring the pressure completely',
          isCorrect: false,
        },
        {
          id: 4,
          questionId: 1,
          answer: 'Focusing on the outcome rather than the process',
          isCorrect: false,
        },
      ],
    },
    {
      id: 2,
      testId: 1,
      question: 'Which of the following best describes a growth mindset?',
      type: 'multiple_choice',
      answers: [
        {
          id: 5,
          questionId: 2,
          answer: 'Believing that abilities are fixed and cannot be improved',
          isCorrect: false,
        },
        {
          id: 6,
          questionId: 2,
          answer: 'Viewing challenges as opportunities to learn and grow',
          isCorrect: true,
        },
        {
          id: 7,
          questionId: 2,
          answer: 'Avoiding difficult situations to maintain confidence',
          isCorrect: false,
        },
        {
          id: 8,
          questionId: 2,
          answer: 'Only focusing on areas where you already excel',
          isCorrect: false,
        },
      ],
    },
    {
      id: 3,
      testId: 1,
      question: 'What is the most effective way to build mental toughness?',
      type: 'multiple_choice',
      answers: [
        {
          id: 9,
          questionId: 3,
          answer: 'Avoiding challenging situations',
          isCorrect: false,
        },
        {
          id: 10,
          questionId: 3,
          answer: 'Only practicing when you feel motivated',
          isCorrect: false,
        },
        {
          id: 11,
          questionId: 3,
          answer: 'Regular practice and gradual exposure to challenges',
          isCorrect: true,
        },
        {
          id: 12,
          questionId: 3,
          answer: 'Comparing yourself to others constantly',
          isCorrect: false,
        },
      ],
    },
  ],
};

export const mockModule: Module = {
  id: 1,
  title: 'Focus',
  category: 'Mental Training',
  description:
    'Learn key strategies for maintaining focus under pressure and developing mental toughness.',
  content: {
    videoUrl: 'https://example.com/video.mp4',
  },
  hasQuiz: true,
  completed: false,
  duration: '30 minutes',
  locked: false,
  test: {
    ...mockTest,
    status: 'not_started',
    score: null,
    attempts: [],
  } as ModuleTest,
};
