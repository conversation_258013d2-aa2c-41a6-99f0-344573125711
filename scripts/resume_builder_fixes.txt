# PRD: Resume Builder Functionality Fixes

<context>
# Overview
This document outlines the requirements for addressing bugs and aligning functionality within the Positive Athlete application's resume builder tool. The primary goal is to ensure a smooth, intuitive, and error-free user experience when creating and managing resumes, specifically focusing on profile section updates, avatar management, and reset functionalities.

# Core Features (to be Addressed)
1.  **Fix "Save Profile Section" Functionality:**
    *   **What it does:** Allows users to save changes made to their profile section, including text content and avatar image.
    *   **Current Issue:** The "Save" button is incorrectly calling a "reset" endpoint, leading to errors and preventing data from being saved correctly.
    *   **Desired Behavior:** The "Save" button should call the appropriate backend endpoint to update the profile section content and avatar.
2.  **Fix/Implement "Reset Avatar" Functionality:**
    *   **What it does:** Allows users to reset their resume's avatar.
    *   **Current Issue:** The backend endpoint for resetting the avatar (`POST /api/v1/resumes/{resumeId}/avatar/reset`) is failing due to an undefined method in `ResumeService`.
    *   **Desired Behavior:** The backend service method should be implemented/fixed so the endpoint functions as intended, or the endpoint should be re-evaluated if its purpose overlaps with profile section updates.
3.  **Ensure "Reset to Profile" (Per Section) Works Correctly:**
    *   **What it does:** A button within each resume section that resets the fields of that specific section to match the user's main profile data.
    *   **Current Issue:** Needs verification that it correctly fetches profile data for the section and updates the section's content without errors.
    *   **Desired Behavior:** Each section's "Reset to Profile" button should accurately revert that section's data to the corresponding data from the user's primary profile.
4.  **Ensure "Reset All" (All Sections) Works Correctly:**
    *   **What it does:** A global button that resets all resume sections to match the user's main profile data.
    *   **Clarified Behavior:** This action should effectively trigger the "Reset to Profile" functionality for every section in the resume.
    *   **Current Issue:** Needs verification that it correctly iterates through all sections and applies the "Reset to Profile" logic.
    *   **Desired Behavior:** The "Reset All" button should make each resume section mirror the user's primary profile data.
5.  **Investigate and Fix Data Fetching Issues (404s):**
    *   **What it does:** Initial loading of resume and avatar data when the user accesses the resume builder.
    *   **Current Issue:** Network logs show 404 errors for `GET /api/v1/resumes` and `GET /api/v1/resumes/{id}/avatar`, suggesting data might not be loading correctly.
    *   **Desired Behavior:** Resume and avatar data should load successfully if available, or handle gracefully if not found (e.g. for a new user).

# User Experience
Currently, users may experience frustration due to save actions failing, reset buttons not working, and potentially missing data on load. The desired user experience is seamless and reliable, where users can confidently edit, save, and reset their resume sections without encountering errors or unexpected behavior.
</context>

<PRD>
# Technical Architecture (Changes/Fixes)

## Frontend:
1.  **"Save Profile Section" Button:**
    *   Modify the click handler to make a `PUT` request to `/api/v1/resumes/{resumeId}/sections/profile`.
    *   Ensure the request body is `multipart/form-data` and includes the section content (JSON object) and the avatar image file (if updated).
2.  **"Reset to Profile" Button (Per Section):**
    *   Verify that this button, when clicked for a section (e.g., 'education'), triggers an update to that section (e.g., `PUT /api/v1/resumes/{resumeId}/sections/education`) with content fetched/derived from the user's main profile for that specific section type.
    *   If frontend state management is used, ensure it correctly reverts to profile data and syncs with the backend if necessary.
3.  **"Reset All" Button:**
    *   Ensure this button iterates through all available resume sections.
    *   For each section, trigger the same logic as its individual "Reset to Profile" button.
4.  **Avatar Reset Logic:**
    *   If the `POST .../avatar/reset` endpoint is maintained for a specific "reset avatar to system default" action (distinct from profile section update), ensure any frontend button explicitly for this purpose calls it. Otherwise, avatar changes should primarily go through the profile section update.
5.  **Data Fetching (on Load):**
    *   Review API calls made when the resume builder loads.
    *   Correct the URLs or handling for `GET /api/v1/resumes` and `GET /api/v1/resumes/{id}/avatar` to prevent 404s when data is expected. Ensure proper error handling if data legitimately doesn't exist.

## Backend:
1.  **`ResumeService::resetAvatar()`:**
    *   Locate `App\Services\ResumeService`.
    *   Implement or fix the `resetAvatar(Resume $resume)` method. This might involve removing the current avatar media associated with the resume or setting it to a predefined default.
    *   This is critical for the `POST /api/v1/resumes/{resumeId}/avatar/reset` endpoint in `ResumeController.php` (line 260) to function.
2.  **"Reset to Profile" Logic (Per Section):**
    *   The `PUT /api/v1/resumes/{resumeId}/sections/{type}` endpoint (handled by `ResumeSectionController::update`) is used for general section updates.
    *   Ensure this endpoint can correctly process data that represents a "reset to profile" state for a given section. This means the frontend would send the profile's data for that section as the update payload.
3.  **Data Fetching (404s):**
    *   Review `ResumeService::getUserResume()`: Ensure it correctly fetches the authenticated user's resume. If 404s are unexpected, check data seeding or user context.
    *   Review `ResumeController::getAvatar()`: Ensure `$resume->getFirstMedia('avatar')` logic is sound. If 404s are unexpected, check if avatars are correctly associated or if the resume ID is valid.

# Development Roadmap

## Phase 1: Critical Bug Fixes & Core Save Functionality
1.  **Backend:** Implement/Fix `App\Services\ResumeService::resetAvatar()` method.
    *   *Verify:* `POST /api/v1/resumes/{resumeId}/avatar/reset` no longer returns a 500 error.
2.  **Frontend:** Correct the "Save" button action for the "Profile" section.
    *   *Action:* Ensure it sends a `PUT` request to `/api/v1/resumes/{resumeId}/sections/profile` with `multipart/form-data` containing section content and avatar file.
    *   *Verify:* Profile text and avatar image can be successfully updated and saved.

## Phase 2: Reset Functionality Alignment
1.  **Frontend & Backend:** Validate and align "Reset to Profile" (per section).
    *   *Action (Frontend):* Ensure clicking "Reset to Profile" for a section (e.g., 'education') correctly sends the user's main profile data for 'education' to `PUT /api/v1/resumes/{resumeId}/sections/education`.
    *   *Action (Backend):* Ensure `ResumeSectionController::update` handles this payload correctly.
    *   *Verify:* Each section can be individually reset to match profile data.
2.  **Frontend:** Implement/Verify "Reset All" functionality.
    *   *Action:* Ensure it triggers the "Reset to Profile" logic for all resume sections.
    *   *Verify:* All sections are reset to match profile data.

## Phase 3: Data Fetching & Polish
1.  **Frontend & Backend:** Investigate and resolve 404 errors for `GET /api/v1/resumes` and `GET /api/v1/resumes/{id}/avatar`.
    *   *Action (Backend):* Review service logic and controller responses.
    *   *Action (Frontend):* Review initial data loading calls.
    *   *Verify:* Resume data and avatar load correctly, or appropriate UI is shown if no data exists.
2.  **Testing:** Conduct thorough end-to-end testing of all resume builder save, update, and reset functionalities across different sections and scenarios.

# Logical Dependency Chain
1.  **Fix `ResumeService::resetAvatar()` (Backend):** Unblocks any frontend part (even if miswired) hitting this endpoint.
2.  **Fix Frontend "Save Profile Section" (Frontend):** Addresses the most critical user-facing bug where saving profile changes fails.
3.  **Align/Fix "Reset to Profile" (Per Section) (Frontend/Backend):** Ensures core reset logic works at a granular level.
4.  **Align/Fix "Reset All" (Frontend):** Builds upon the per-section reset.
5.  **Resolve Data Fetching 404s (Frontend/Backend):** Improves initial page load and data consistency.

# Risks and Mitigations
1.  **Risk:** Complexity in frontend state management for resume sections.
    *   **Mitigation:** Utilize browser developer tools for step-by-step debugging of state changes and API calls. Isolate components if possible.
2.  **Risk:** Ambiguity in the source of "profile data" for resets if it's not directly from the user's existing main profile (e.g., system-wide defaults).
    *   **Mitigation:** For this scope, assume "reset to profile" means resetting to the values stored in the user's primary profile record. If system defaults are needed, this would be a new feature requirement.
3.  **Risk:** The 404 errors on `GET` requests might indicate underlying issues with data seeding, user authentication context for service layers, or incorrect assumptions about data existence.
    *   **Mitigation:** Systematically check the data flow: route -> controller -> service -> model -> database for the failing GET requests. Verify user context and data existence in the database.

# Appendix
*   Reference `ResumeController.php` and `ResumeSectionController.php` for backend endpoint definitions.
*   Refer to browser network logs for current frontend behavior and error details.
</PRD>
