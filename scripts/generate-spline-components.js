const fs = require('fs');
const path = require('path');

// Badge data with name and URL mapping
const badges = [
  {
    name: 'Rookie',
    points: 1,
    unachieved: 'https://prod.spline.design/8CgEnmxCFEW2MgWU/scene.splinecode',
    achieved: 'https://prod.spline.design/uhdYHBpKStE9D527/scene.splinecode',
  },
  {
    name: 'JV',
    points: 5,
    unachieved: 'https://prod.spline.design/r3QwkYd5pY3uFurn/scene.splinecode',
    achieved: 'https://prod.spline.design/cJitc3i6dpnpOlnR/scene.splinecode',
  },
  {
    name: 'Varsity',
    points: 15,
    unachieved: 'https://prod.spline.design/34Nr4-jb6IhhpHzk/scene.splinecode',
    achieved: 'https://prod.spline.design/iSvuGEzFsqwnkZ-r/scene.splinecode',
  },
  {
    name: 'Starter',
    points: 25,
    unachieved: 'https://prod.spline.design/uKCsiipqkGGxpOgI/scene.splinecode',
    achieved: 'https://prod.spline.design/R35GZvR2rLjYqO1v/scene.splinecode',
  },
  {
    name: 'Playmaker',
    points: 35,
    unachieved: 'https://prod.spline.design/jJe5La-6BiathdxO/scene.splinecode',
    achieved: 'https://prod.spline.design/6Kl4URx9MK3WVjVp/scene.splinecode',
  },
  {
    name: 'ViceCaptain',
    points: 50,
    unachieved: 'https://prod.spline.design/CazYZJgf1YtkNKo5/scene.splinecode',
    achieved: 'https://prod.spline.design/GttNcYiAnjM4CMb0/scene.splinecode',
  },
  {
    name: 'Captain',
    points: 65,
    unachieved: 'https://prod.spline.design/ZhUhglL5DFmpbe-L/scene.splinecode',
    achieved: 'https://prod.spline.design/8pLYCBcbFzlIOAfa/scene.splinecode',
  },
  {
    name: 'MVP',
    points: 80,
    unachieved: 'https://prod.spline.design/LOKWt6QtNCSXu1qk/scene.splinecode',
    achieved: 'https://prod.spline.design/tCMEgi6NoVKV2DKG/scene.splinecode',
  },
  {
    name: 'AllState',
    points: 100,
    unachieved: 'https://prod.spline.design/tiQAbXCDX-4XEnHi/scene.splinecode',
    achieved: 'https://prod.spline.design/k2x3vQinkPLxHJjG/scene.splinecode',
  },
  {
    name: 'Champion',
    points: 125,
    unachieved: 'https://prod.spline.design/facIbYGUu4HEW3qc/scene.splinecode',
    achieved: 'https://prod.spline.design/8omWaawon80HptYH/scene.splinecode',
  },
  {
    name: 'Legend',
    points: 150,
    unachieved: 'https://prod.spline.design/GB-e9HGLKe3YDeQ7/scene.splinecode',
    achieved: 'https://prod.spline.design/e6vIounYUOoCnhwz/scene.splinecode',
  },
  {
    name: 'HallOfFame',
    points: 175,
    unachieved: 'https://prod.spline.design/OyCqtSb6hqCpYjzp/scene.splinecode',
    achieved: 'https://prod.spline.design/ked4JLCvVLqqDhyF/scene.splinecode',
  },
  {
    name: 'GOATLevel1',
    points: 200,
    unachieved: 'https://prod.spline.design/iwwCaSHnySx0uEAo/scene.splinecode',
    achieved: 'https://prod.spline.design/OciUrGBbIoBPpRYa/scene.splinecode',
  },
  {
    name: 'GOATLevel2',
    points: 225,
    unachieved: 'https://prod.spline.design/ZBQXtEMNrQXEvsVv/scene.splinecode',
    achieved: 'https://prod.spline.design/fYxSG80JfZwi5dlP/scene.splinecode',
  },
  {
    name: 'GOATLevel3',
    points: 250,
    unachieved: 'https://prod.spline.design/hMhYTP63XOd5Qnv5/scene.splinecode',
    achieved: 'https://prod.spline.design/pbS6xLUT4FDZK6BC/scene.splinecode',
  },
  {
    name: 'GOATLevel4',
    points: 275,
    unachieved: 'https://prod.spline.design/E7qlND7MbRqxHd26/scene.splinecode',
    achieved: 'https://prod.spline.design/kjNZwmjFshQQuma5/scene.splinecode',
  },
  {
    name: 'GOATLevel5',
    points: 300,
    unachieved: 'https://prod.spline.design/AqtPdY112XTTRjTf/scene.splinecode',
    achieved: 'https://prod.spline.design/fh4KgfHo99UN-Qyf/scene.splinecode',
  },
];

// Sports data with name and URL mapping
const sports = [
  {
    name: 'Soccer',
    card: 'https://prod.spline.design/Nb-D19XEXlx-zf5Y/scene.splinecode',
    background: 'https://prod.spline.design/jsBa46Q547W0PSX0/scene.splinecode',
  },
  {
    name: 'Football',
    card: 'https://prod.spline.design/AcM3jmj-SGraDKyA/scene.splinecode',
    background: 'https://prod.spline.design/wnv4JnS7195E1Dsn/scene.splinecode',
  },
  {
    name: 'Baseball',
    card: 'https://prod.spline.design/qnpB31FdnJaCxX-Y/scene.splinecode',
    background: 'https://prod.spline.design/iAw9gv59Nt4dUu0P/scene.splinecode',
  },
  {
    name: 'IceHockey',
    card: 'https://prod.spline.design/OLoC3MY7HbAboOvu/scene.splinecode',
    background: 'https://prod.spline.design/2TBnGT9CNLAp0XtM/scene.splinecode',
  },
  {
    name: 'Basketball',
    card: 'https://prod.spline.design/pzIFHMu4OlZ16K2J/scene.splinecode',
    background: 'https://prod.spline.design/VbdD59LUsUFic7Jq/scene.splinecode',
  },
  {
    name: 'TrackAndField',
    card: 'https://prod.spline.design/qPhFYdmwMt2bc7mp/scene.splinecode',
    background: 'https://prod.spline.design/nYuSnfAnrv4kGH12/scene.splinecode',
  },
  {
    name: 'CrossCountry',
    card: 'https://prod.spline.design/qPhFYdmwMt2bc7mp/scene.splinecode',
    background: 'https://prod.spline.design/nYuSnfAnrv4kGH12/scene.splinecode',
  },
  {
    name: 'Golf',
    card: 'https://prod.spline.design/83-kfQUsC6loESZX/scene.splinecode',
    background: 'https://prod.spline.design/Fthiaz8zbzTyPqF6/scene.splinecode',
  },
  {
    name: 'Weightlifting',
    card: 'https://prod.spline.design/vJ7u-Pfrwsjsdgp6/scene.splinecode',
    background: 'https://prod.spline.design/I9hj3JulRRxJGjh4/scene.splinecode',
  },
  {
    name: 'TableTennis',
    card: 'https://prod.spline.design/l9G02cIxOuWUnXV3/scene.splinecode',
    background: 'https://prod.spline.design/HETm5A-eG3P2WAFL/scene.splinecode',
  },
  {
    name: 'ESports',
    card: 'https://prod.spline.design/0PoQVZ2f9LaBfWJW/scene.splinecode',
    background: 'https://prod.spline.design/w1azqvwIeJcBDIym/scene.splinecode',
  },
  {
    name: 'Tennis',
    card: 'https://prod.spline.design/KPFYeB3vzGFw34h2/scene.splinecode',
    background: 'https://prod.spline.design/lmpjPEMIsV8wZlVN/scene.splinecode',
  },
  {
    name: 'Equestrian',
    card: 'https://prod.spline.design/H0ny6OmEnj9lQQOa/scene.splinecode',
    background: 'https://prod.spline.design/icL8WcD2ev1TaF8M/scene.splinecode',
  },
  {
    name: 'Badminton',
    card: 'https://prod.spline.design/AFyOl0XEvWW0Vm0n/scene.splinecode',
    background: 'https://prod.spline.design/37Ef1Ur5RH0YySdH/scene.splinecode',
  },
  {
    name: 'FieldHockey',
    card: 'https://prod.spline.design/NZwTRVvMisk0mEsf/scene.splinecode',
    background: 'https://prod.spline.design/ecX9AoQmuW0R9cIU/scene.splinecode',
  },
  {
    name: 'Shooting',
    card: 'https://prod.spline.design/gyOlNUWlchEv70B5/scene.splinecode',
    background: 'https://prod.spline.design/keOAZtwrQWtZMN7d/scene.splinecode',
  },
  {
    name: 'Softball',
    card: 'https://prod.spline.design/qnpB31FdnJaCxX-Y/scene.splinecode',
    background: 'https://prod.spline.design/iAw9gv59Nt4dUu0P/scene.splinecode',
  },
  {
    name: 'FlagFootball',
    card: 'https://prod.spline.design/AcM3jmj-SGraDKyA/scene.splinecode',
    background: 'https://prod.spline.design/wnv4JnS7195E1Dsn/scene.splinecode',
  },
  {
    name: 'Handball',
    card: 'https://prod.spline.design/Nb-D19XEXlx-zf5Y/scene.splinecode',
    background: 'https://prod.spline.design/jsBa46Q547W0PSX0/scene.splinecode',
  },
  {
    name: 'SkiingSnowboarding',
    card: 'https://prod.spline.design/yLmjsT0awzXLesFp/scene.splinecode',
    background: 'https://prod.spline.design/5-94OO3jxThaV2Lc/scene.splinecode',
  },
  {
    name: 'Bowling',
    card: 'https://prod.spline.design/HMmD3vZXPIFtreqw/scene.splinecode',
    background: 'https://prod.spline.design/2vdGy51Wqzgj9fbQ/scene.splinecode',
  },
  {
    name: 'Volleyball',
    card: 'https://prod.spline.design/mWJwH4PXzFssn-MS/scene.splinecode',
    background: 'https://prod.spline.design/xzchuPfuI6wyMkzH/scene.splinecode',
  },
  {
    name: 'WaterPolo',
    card: 'https://prod.spline.design/mWJwH4PXzFssn-MS/scene.splinecode',
    background: 'https://prod.spline.design/xzchuPfuI6wyMkzH/scene.splinecode',
  },
  {
    name: 'Cricket',
    card: 'https://prod.spline.design/cJMYJkZzEP26DqTR/scene.splinecode',
    background: 'https://prod.spline.design/6gx7CS7R2p7QJ6x7/scene.splinecode',
  },
  {
    name: 'Rugby',
    card: 'https://prod.spline.design/kSyAg1caZrHK0rVn/scene.splinecode',
    background: 'https://prod.spline.design/K6yLqcPUgtNQ237S/scene.splinecode',
  },
  {
    name: 'Lacrosse',
    card: 'https://prod.spline.design/2ZlNAfkHJxgBMKNK/scene.splinecode',
    background: 'https://prod.spline.design/ld4Al5sri2mnLGTz/scene.splinecode',
  },
  {
    name: 'Other',
    card: 'https://prod.spline.design/BNJAsY6R6ACjg-di/scene.splinecode',
    background: 'https://prod.spline.design/8q6qingLeeSwApCw/scene.splinecode',
  },
];

// Template for badge components
const badgeTemplate = (name, url, isAchieved) => `'use client';

import SplineViewer from '../SplineViewer';
import type { SplineViewerProps } from '../types';

type BadgeProps = Omit<SplineViewerProps, 'scene'>;

export default function Badge${name}${isAchieved ? 'Achieved' : 'Unachieved'}(props: BadgeProps) {
  return (
    <SplineViewer scene="${url}" {...props} />
  );
}
`;

// Template for sports components
const sportsTemplate = (name, url, isCard) => `'use client';

import SplineViewer from '../SplineViewer';
import type { SplineViewerProps } from '../types';

type SportsProps = Omit<SplineViewerProps, 'scene'>;

export default function Sports${name}${isCard ? 'Card' : 'Background'}(props: SportsProps) {
  return (
    <SplineViewer scene="${url}" {...props} />
  );
}
`;

// Create badge components
const badgesDir = path.join(__dirname, '../components/shared/spline/badges');

if (!fs.existsSync(badgesDir)) {
  fs.mkdirSync(badgesDir, { recursive: true });
}

badges.forEach(badge => {
  // Create unachieved badge
  fs.writeFileSync(
    path.join(badgesDir, `Badge${badge.name}Unachieved.tsx`),
    badgeTemplate(badge.name, badge.unachieved, false)
  );

  // Create achieved badge
  fs.writeFileSync(
    path.join(badgesDir, `Badge${badge.name}Achieved.tsx`),
    badgeTemplate(badge.name, badge.achieved, true)
  );
});

// Create sports components
const sportsDir = path.join(__dirname, '../components/shared/spline/sports');

if (!fs.existsSync(sportsDir)) {
  fs.mkdirSync(sportsDir, { recursive: true });
}

sports.forEach(sport => {
  // Create card
  fs.writeFileSync(
    path.join(sportsDir, `Sports${sport.name}Card.tsx`),
    sportsTemplate(sport.name, sport.card, true)
  );

  // Create background
  fs.writeFileSync(
    path.join(sportsDir, `Sports${sport.name}Background.tsx`),
    sportsTemplate(sport.name, sport.background, false)
  );
});

console.log('All Spline components have been generated successfully!');
