<context>
# Overview
This document outlines the requirements for a feature that enables the Positive Athlete web application's Resume Builder to generate a PDF, send it to the server for storage, and make it accessible for download by the mobile application. This feature will ensure that a user always has one consolidated resume PDF, whether it's generated by our builder or uploaded externally.

# Core Features
1.  **Client-Side PDF Generation and Transmission:**
    *   **What it does:** The existing client-side PDF generation capabilities of the `ResumePreview` component will be leveraged. After generation, the PDF will be transmitted as a file to a new server-side endpoint.
    *   **Why it's important:** Allows the mobile app to access resumes created with the web's Resume Builder, which currently only exist transiently on the client. Ensures a consistent experience across platforms.
    *   **How it works at a high level:**
        *   The `ResumePreview.tsx` component will use `html2pdf.js` to generate the PDF blob/file.
        *   A new function will be added to post this PDF file to a dedicated backend API endpoint.
        *   The endpoint will handle receiving the file and storing it.

2.  **Server-Side PDF Storage:**
    *   **What it does:** The backend will store the received PDF file, associating it with the specific user.
    *   **Why it's important:** Provides a persistent storage location for the generated PDF, making it retrievable by other clients (like the mobile app) and ensuring data isn't lost.
    *   **How it works at a high level:**
        *   A new API endpoint (e.g., `/api/v1/users/resume/pdf`) will accept a POST request with the PDF file.
        *   The service layer will handle saving the file to a designated storage (e.g., S3 bucket, local file system based on environment).
        *   The user's record in the database will be updated to reference this stored PDF (e.g., a URL or path to the file).
        *   This endpoint will replace any existing PDF associated with the user (generated or externally uploaded).

3.  **PDF Update Triggers:**
    *   **What it does:** Defines the events in the frontend that trigger the generation and server-side update of the resume PDF.
    *   **Why it's important:** Ensures the stored PDF is kept up-to-date with the user's latest changes in the Resume Builder.
    *   **How it works at a high level:**
        *   The PDF generation and upload to the server should be triggered automatically after any successful "save" operation within the Resume Builder sections (e.g., when education, experience, summary, etc., is updated and saved by the user).
        *   Consider a debounce mechanism or a slight delay to avoid excessive PDF regeneration if the user makes rapid changes.
        *   A manual "Refresh PDF" or "Sync PDF" button could also be considered if auto-sync proves too complex or resource-intensive initially, but the primary goal is automatic updates on save.

4.  **Unified PDF Management:**
    *   **What it does:** Ensures that a user has only one active resume PDF accessible through the system, regardless of whether it was created via the Resume Builder or uploaded externally.
    *   **Why it's important:** Prevents confusion and ensures the mobile app (and other services) always retrieves the single, definitive resume PDF for the user.
    *   **How it works at a high level:**
        *   When the Resume Builder generates and saves a PDF to the server, it will overwrite any previously uploaded external PDF for that user.
        *   Similarly, if a user uploads an external PDF (through the existing separate upload mechanism), it will become the active PDF, and any previously builder-generated PDF link for that user should be considered stale or overwritten. The backend logic for external uploads might need to be adjusted to ensure it updates the same user PDF reference.
        *   The mobile app will fetch the PDF from a single, consistent endpoint that always serves the currently active PDF.

# User Experience
*   **User Personas:**
    *   Student Athlete using the web Resume Builder.
    *   Student Athlete using the mobile app to view/download their resume.
    *   Positive Athlete Staff potentially needing access to student resumes.
*   **Key User Flows:**
    1.  **Web User - Resume Creation/Update:**
        *   User edits content in the Resume Builder.
        *   User saves changes to a section.
        *   *Behind the scenes:* PDF is regenerated and sent to the server, overwriting the previous version.
        *   User clicks "Download PDF" in `ResumePreview` - this still downloads directly from the client-side generation.
    2.  **Mobile User - Resume Access:**
        *   User navigates to their profile/resume section in the mobile app.
        *   Mobile app calls an API endpoint to get the URL of the user's current resume PDF (or directly downloads it).
        *   User can view/download the PDF.
*   **UI/UX Considerations:**
    *   The client-side PDF generation and server upload should be largely transparent to the web user after a save action.
    *   Clear feedback (e.g., a subtle loading indicator or a success message) might be needed if the upload process takes noticeable time.
    *   No changes are immediately envisaged for the `ResumePreview` component's "Download PDF" button, which will continue its current client-side download behavior. The server sync is an *additional* background process.

</context>
<PRD>
# Technical Architecture
*   **System Components:**
    *   **Frontend (Next.js):**
        *   `ResumePreview.tsx`: Modified to include logic for sending the generated PDF to the backend.
        *   New service/utility function for handling the API request to upload the PDF.
    *   **Backend (Laravel API):**
        *   New Controller (e.g., `UserResumePdfController`) with a method to handle PDF uploads.
        *   New Service (e.g., `UserResumePdfService`) to manage business logic (file storage, database updates).
        *   New Repository if direct database interactions for PDF metadata are complex, otherwise existing `UserRepository` might be sufficient.
        *   New API Route (e.g., `POST /users/resume/pdf`).
        *   Endpoint for mobile app to fetch PDF URL/file (e.g., `GET /users/resume/pdf`).
    *   **Storage:** Cloud storage (e.g., AWS S3) is recommended for scalability and reliability. Local storage for development.
*   **Data Models:**
    *   `users` table: Add a nullable column like `resume_pdf_path` (string) or `resume_pdf_url` (string) to store the location of the user's active resume PDF.
*   **APIs and Integrations:**
    *   Internal API for PDF upload from web client to server.
    *   Internal API for mobile client to retrieve PDF.
*   **Infrastructure Requirements:**
    *   File storage solution (S3 or similar).
    *   Appropriate server-side configuration for file uploads (max file size, MIME type validation).

# Development Roadmap
*   **Phase 1: MVP (Core Functionality)**
    1.  **Backend:**
        *   Create new API endpoint (`POST /api/v1/users/resume/pdf`) to receive a PDF file.
        *   Implement service logic to save the uploaded PDF to a designated storage (configurable for local dev vs. cloud).
        *   Update the `users` table with a new column (`resume_pdf_path` or `resume_pdf_url`).
        *   Logic to update this column when a new PDF is uploaded, ensuring only one PDF reference per user.
        *   Create API endpoint (`GET /api/v1/users/resume/pdf`) for mobile app to retrieve the current PDF (e.g., returns a signed URL or streams the file).
    2.  **Frontend:**
        *   Modify `ResumePreview.tsx` or related services.
        *   After `html2pdf.js` generates the PDF, convert it to a `File` or `Blob` object.
        *   Create a function to send this file to the new backend POST endpoint.
        *   Identify the primary "save" event/callback in the Resume Builder flow (e.g., when a user saves a section like "Education", "Experience", "Summary").
        *   Integrate the PDF generation and upload process to trigger automatically after this primary save event.
    3.  **Testing:**
        *   Unit tests for backend service logic (file handling, DB updates).
        *   Feature tests for the new API endpoints.
        *   Manual frontend testing: ensure PDF is generated and sent to the server upon saving resume content.
        *   Test retrieval via the GET endpoint.

*   **Future Enhancements (Post-MVP):**
    1.  **Improved UX:** Add visual feedback (loading/success states) during the PDF upload process in the web app.
    2.  **Optimization:** Implement debouncing or a more sophisticated trigger mechanism if auto-sync on every save causes performance issues or too frequent updates.
    3.  **Admin Interface:** Potentially allow admins to view/manage user-generated PDFs.
    4.  **Versioning (Optional):** If business requirements change to need historical versions, this would be a significant addition. (Current scope: only one PDF per user).
    5.  **Error Handling:** Robust error handling and reporting for PDF generation, transmission, and storage failures.

# Logical Dependency Chain
1.  **Backend Foundation:**
    *   Database migration for the new `users` table column.
    *   Implement the PDF storage service (uploading part).
    *   Implement the `POST /api/v1/users/resume/pdf` endpoint.
2.  **Frontend Integration:**
    *   Develop the client-side logic in `ResumePreview.tsx` (or helper service) to generate and send the PDF.
    *   Hook this logic into the main "save" event(s) of the Resume Builder.
3.  **Backend Retrieval & Mobile Access:**
    *   Implement the `GET /api/v1/users/resume/pdf` endpoint.
    *   Mobile team can then integrate this endpoint.
4.  **External Upload Consideration:**
    *   Review and potentially adjust the existing external PDF upload mechanism to ensure it updates the same `resume_pdf_path/url` field in the `users` table, thereby replacing any builder-generated PDF. This ensures the "single source of truth" for the user's resume.
5.  **Testing and Refinement:**
    *   Thorough end-to-end testing of both flows (builder-generated and external upload).
    *   Refine based on feedback and identified issues.

# Risks and Mitigations
*   **Technical Challenges:**
    *   **Client-side PDF generation consistency:** Ensuring `html2pdf.js` renders consistently across browsers and content variations. (Mitigation: Thorough testing with diverse resume content. Current library is already in use, so this is a known factor).
    *   **Large PDF file sizes:** Users with very long resumes might generate large PDFs. (Mitigation: Backend validation for max file size. Consider PDF optimization libraries on the server if this becomes an issue, though likely out of scope for MVP).
    *   **Authentication/Authorization:** Ensuring endpoints are properly secured. (Mitigation: Apply standard Laravel Sanctum authentication and authorization middleware).
*   **Figuring out the MVP that we can build upon:**
    *   The defined Phase 1 focuses on the core path: generate, send, store, retrieve. (Mitigation: Stick to this core for MVP. Defer UX niceties or advanced features).
*   **Resource Constraints:**
    *   Development time for both frontend and backend. (Mitigation: Clear task breakdown from this PRD. Prioritize MVP).
*   **External Upload Conflict:**
    *   Ensuring the existing external PDF upload logic correctly interacts with this new system to maintain a single PDF per user. (Mitigation: Allocate specific development time to review and adapt the external upload flow as part of the dependency chain).
*   **Triggering PDF Sync:**
    *   Identifying all relevant "save" events in the multi-part resume builder that should trigger a PDF sync. (Mitigation: Careful code review of the Resume Builder's state management and save handlers. Start with one primary save event and expand if necessary).

# Appendix
*   **Reference Component:** `positive-athlete-client/components/profile/positive-athlete/ResumeBuilder/ResumePreview.tsx`
*   **Client-side PDF Library:** `html2pdf.js`
*   **Existing External PDF Upload:** Details of this existing mechanism should be reviewed to ensure seamless integration.
</PRD> 