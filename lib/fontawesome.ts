import { config } from '@fortawesome/fontawesome-svg-core';
import '@fortawesome/fontawesome-svg-core/styles.css';
// Add icons to Font Awesome library
import { library } from '@fortawesome/fontawesome-svg-core';
// Import icons from Pro Light
import {
  faChevronCircleLeft,
  faChevronCircleRight,
  faChevronRight as faChevronRightThin,
  faThumbtack as faPinSolid,
} from '@fortawesome/pro-light-svg-icons';
// Import icons from Pro Regular
import {
  faBold,
  faCheck,
  faChevronDown,
  faChevronLeft,
  faChevronRight,
  faGauge as faDashboard,
  faEdit,
  faEllipsisH,
  faHandshake,
  faItalic,
  faListUl,
  faLock,
  faMessage,
  faNetworkWired as faNetwork,
  faBriefcase as faOpportunities,
  faThumbtack as faPinRegular,
  faPlus,
  faUser as faProfile,
  faSearch,
  faShare,
  faSliders,
  faTimes,
  faToggleOn,
  faTrash,
  faUnderline,
  faUnlock,
  faXmark,
} from '@fortawesome/pro-regular-svg-icons';
import {
  faArrowRight,
  faBaseballBatBall,
  faBasketball,
  faBellConcierge,
  faBuilding,
  faBuildingColumns,
  faBusinessTime,
  faCameraMovie,
  faCar,
  faChalkboardTeacher,
  faChartMixed,
  faCode,
  faCog,
  faCogs,
  faDumbbell,
  faFlask,
  faFootball,
  faGavel,
  faGraduationCap,
  faHandcuffs,
  faHardHat,
  faHeart,
  faHeartPulse,
  faHexagonVerticalNft,
  faHospital,
  faIndustry,
  faJetFighter,
  faLaptopCode,
  faLeaf,
  faLightbulb,
  faMegaphone,
  faMessageDollar,
  faMessages,
  faMicroscope,
  faMobile,
  faMoneyCheckDollar,
  faPaintBrush,
  faPallet,
  faPeople,
  faPlane,
  faQuestion,
  faRulerTriangle,
  faScaleBalanced,
  faScrewdriverWrench,
  faStethoscope,
  faToolbox,
  faTruck,
  faUserDoctor,
  faUserTie,
  faUtensils,
  faVolleyball,
  faWhistle,
  faWrench,
} from '@fortawesome/pro-solid-svg-icons';

library.add(
  // Pro Regular icons
  faDashboard,
  faProfile,
  faNetwork,
  faOpportunities,
  faMessage,
  faSearch,
  faSliders,
  faXmark,
  faChevronDown,
  faChevronLeft,
  faChevronRight,
  faEdit,
  faShare,
  faToggleOn,
  faEllipsisH,
  faPinRegular,
  faHandshake,
  faLock,
  faUnlock,
  faCheck,
  faTimes,
  faBold,
  faItalic,
  faUnderline,
  faListUl,
  faTrash,
  faPlus,
  // Pro Light icons
  faPinSolid,
  faChevronRightThin,
  faChevronCircleLeft,
  faChevronCircleRight,
  // Pro Solid icons
  faArrowRight,
  faBaseballBatBall,
  faBasketball,
  faBellConcierge,
  faBuilding,
  faBuildingColumns,
  faBusinessTime,
  faCameraMovie,
  faCar,
  faChalkboardTeacher,
  faChartMixed,
  faCode,
  faCog,
  faCogs,
  faDumbbell,
  faFlask,
  faFootball,
  faGavel,
  faGraduationCap,
  faHandcuffs,
  faHardHat,
  faHeart,
  faHeartPulse,
  faHexagonVerticalNft,
  faHospital,
  faIndustry,
  faJetFighter,
  faLaptopCode,
  faLeaf,
  faLightbulb,
  faMegaphone,
  faMessageDollar,
  faMessages,
  faMicroscope,
  faMobile,
  faMoneyCheckDollar,
  faPaintBrush,
  faPallet,
  faPeople,
  faPlane,
  faQuestion,
  faRulerTriangle,
  faScaleBalanced,
  faScrewdriverWrench,
  faStethoscope,
  faToolbox,
  faTruck,
  faUserDoctor,
  faUserTie,
  faUtensils,
  faVolleyball,
  faWhistle,
  faWrench
);

// Prevent Font Awesome from adding its CSS since we did it manually above
config.autoAddCss = false;

// Export all icons for direct usage
export {
  faDashboard,
  faProfile,
  faNetwork,
  faOpportunities,
  faMessage,
  faSearch,
  faSliders,
  faXmark,
  faChevronDown,
  faChevronLeft,
  faChevronRight,
  faEdit,
  faShare,
  faToggleOn,
  faEllipsisH,
  faPinRegular,
  faPinSolid,
  faChevronRightThin,
  faHandshake,
  faChevronCircleLeft,
  faChevronCircleRight,
  faLock,
  faUnlock,
  faCheck,
  faTimes,
  faBold,
  faItalic,
  faUnderline,
  faListUl,
  faTrash,
  faPlus,
  faArrowRight,
  faBaseballBatBall,
  faBasketball,
  faBellConcierge,
  faBuilding,
  faBuildingColumns,
  faBusinessTime,
  faCameraMovie,
  faCar,
  faChalkboardTeacher,
  faChartMixed,
  faCode,
  faCog,
  faCogs,
  faDumbbell,
  faFlask,
  faFootball,
  faGavel,
  faGraduationCap,
  faHandcuffs,
  faHardHat,
  faHeart,
  faHeartPulse,
  faHexagonVerticalNft,
  faHospital,
  faIndustry,
  faJetFighter,
  faLaptopCode,
  faLeaf,
  faLightbulb,
  faMegaphone,
  faMessageDollar,
  faMessages,
  faMicroscope,
  faMobile,
  faMoneyCheckDollar,
  faPaintBrush,
  faPallet,
  faPeople,
  faPlane,
  faQuestion,
  faRulerTriangle,
  faScaleBalanced,
  faScrewdriverWrench,
  faStethoscope,
  faToolbox,
  faTruck,
  faUserDoctor,
  faUserTie,
  faUtensils,
  faVolleyball,
  faWhistle,
  faWrench,
};

// Export a map for easier reference with FontAwesome shorthand names
export const ICON_MAP = {
  'fa-arrow-right': faArrowRight,
  'fa-baseball-bat-ball': faBaseballBatBall,
  'fa-basketball': faBasketball,
  'fa-bell-concierge': faBellConcierge,
  'fa-building': faBuilding,
  'fa-building-columns': faBuildingColumns,
  'fa-business-time': faBusinessTime,
  'fa-camera-movie': faCameraMovie,
  'fa-car': faCar,
  'fa-chalkboard-teacher': faChalkboardTeacher,
  'fa-chart-mixed': faChartMixed,
  'fa-chevron-down': faChevronDown,
  'fa-code': faCode,
  'fa-cog': faCog,
  'fa-cogs': faCogs,
  'fa-dumbbell': faDumbbell,
  'fa-flask': faFlask,
  'fa-football': faFootball,
  'fa-gavel': faGavel,
  'fa-graduation-cap': faGraduationCap,
  'fa-handcuffs': faHandcuffs,
  'fa-hard-hat': faHardHat,
  'fa-heart': faHeart,
  'fa-heart-pulse': faHeartPulse,
  'fa-hexagon-vertical-nft': faHexagonVerticalNft,
  'fa-hospital': faHospital,
  'fa-industry': faIndustry,
  'fa-jet-fighter': faJetFighter,
  'fa-laptop-code': faLaptopCode,
  'fa-leaf': faLeaf,
  'fa-lightbulb': faLightbulb,
  'fa-megaphone': faMegaphone,
  'fa-message-dollar': faMessageDollar,
  'fa-messages': faMessages,
  'fa-microscope': faMicroscope,
  'fa-mobile': faMobile,
  'fa-money-check-dollar': faMoneyCheckDollar,
  'fa-paint-brush': faPaintBrush,
  'fa-pallet': faPallet,
  'fa-people': faPeople,
  'fa-plane': faPlane,
  'fa-question': faQuestion,
  'fa-ruler-triangle': faRulerTriangle,
  'fa-scale-balanced': faScaleBalanced,
  'fa-screwdriver-wrench': faScrewdriverWrench,
  'fa-stethoscope': faStethoscope,
  'fa-toolbox': faToolbox,
  'fa-truck': faTruck,
  'fa-user-doctor': faUserDoctor,
  'fa-user-tie': faUserTie,
  'fa-utensils': faUtensils,
  'fa-volleyball': faVolleyball,
  'fa-whistle': faWhistle,
  'fa-wrench': faWrench,
};

// Create a type for icon keys to help with type safety
export type IconKey = keyof typeof ICON_MAP;
