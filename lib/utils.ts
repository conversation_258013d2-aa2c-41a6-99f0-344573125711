import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const META_TITLE = 'Positive Athlete | Scholarship & Leadership Development Program';
export const META_DESCRIPTION =
  'Positive Athlete is a national recognition, leadership, and career development program for high school student-athletes. Student-Athletes are nominated to join the team by their coaches, counselors, parents, and mentors for their success both on and off the field. Our nominees are high character leaders who may have overcome difficult circumstances, given back to their schools and communities in a significant way, or have an infectious positive attitude that lifts everyone around them.';
export const OG_IMAGE = '/images/meta/og-image_1200x675.jpg';

export const getCanonicalUrl = (path: string) => {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://positiveathlete.org';

  return `${baseUrl}${path}`;
};
