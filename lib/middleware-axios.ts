import axios from 'axios';

// Create a custom instance for middleware
const middlewareAxios = axios.create({
  validateStatus: status => status < 500, // Don't throw for 401/403
  timeout: 5000, // 5 second timeout
});

// Add response interceptor for better error handling
middlewareAxios.interceptors.response.use(
  response => response,
  error => {
    console.error('Middleware API Error:', {
      url: error.config?.url,
      status: error.response?.status,
      message: error.message,
    });
    return Promise.reject(error);
  }
);

export default middlewareAxios;
