// This is a basic implementation - you'll want to replace this
// with your actual authentication logic

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'athlete' | 'coach';
}

export interface Session {
  user: User;
  token: string;
}

// Mock user data for development
const MOCK_USER: User = {
  id: '1',
  email: '<EMAIL>',
  name: '<PERSON><PERSON>',
  role: 'athlete',
};

export async function getSession(): Promise<Session | null> {
  // Always return a mock session for development
  return {
    user: MOCK_USER,
    token: 'mock-token',
  };
}

export async function login(email: string, password: string): Promise<Session> {
  // In a real implementation, we would validate credentials here
  console.log(`Attempting login for ${email} with password length: ${password.length}`);

  return {
    user: {
      ...MOCK_USER,
      email, // Use the provided email
    },
    token: 'mock-token',
  };
}

export async function logout(): Promise<void> {
  // No-op for now
}
