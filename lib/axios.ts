import { useRouter } from 'next/navigation';
import axios, { AxiosError, InternalAxiosRequestConfig } from 'axios';

// Type definitions
declare module 'axios' {
  export interface AxiosRequestConfig {
    requiresCsrf?: boolean;
    skipAuthRedirect?: boolean;
  }
}

interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
  skipAuthRedirect?: boolean;
}

// Axios instance configuration
const instance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    'X-Requested-With': 'XMLHttpRequest',
    'Content-Type': 'application/json',
  },
  withCredentials: true,
  withXSRFToken: true,
});

// CSRF token management
export const csrf = async () => {
  try {
    await instance.get('/sanctum/csrf-cookie');
  } catch (error) {
    console.error('CSRF fetch error:', error);
    throw error;
  }
};

// Alias for csrf function to maintain backward compatibility
export const resetCsrf = csrf;

// Session management
const storeRedirectPath = () => {
  if (typeof window !== 'undefined') {
    const currentPath = window.location.pathname + window.location.search;
    sessionStorage.setItem('redirectAfterLogin', currentPath);
  }
};

// Navigation handling
let navigate: (path: string) => Promise<void>;

export const setNavigationHandler = (router: ReturnType<typeof useRouter>) => {
  navigate = async (path: string) => {
    await Promise.resolve(router.push(path));
  };
};

// Request interceptor for CSRF
instance.interceptors.request.use(
  async (config: ExtendedAxiosRequestConfig) => {
    if (config.requiresCsrf && config.method !== 'get') {
      await csrf();
    }
    return config;
  },
  error => Promise.reject(error)
);

// Response interceptor for auth and CSRF handling
instance.interceptors.response.use(
  response => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as ExtendedAxiosRequestConfig;

    if (originalRequest && error.response?.status === 419 && !originalRequest._retry) {
      originalRequest._retry = true;
      await csrf();
      return instance(originalRequest);
    }

    if (error.response?.status === 401 && !originalRequest?.skipAuthRedirect) {
      storeRedirectPath();

      if (navigate) {
        try {
          await navigate('/logout');
        } catch (navError) {
          console.error('Navigation error:', navError);
          window.location.href = '/logout';
        }
      } else {
        window.location.href = '/logout';
      }
    }

    return Promise.reject(error);
  }
);

export default instance;
export const isAxiosError = axios.isAxiosError;
