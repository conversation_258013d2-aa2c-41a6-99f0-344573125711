'use client';

import { use, useCallback, useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import { isAxiosError } from 'axios';
import LoadingScreen from '@/components/loading/LoadingScreen';
import SystemInviteService from '@/services/systemInvite.service';
import { usePositiveAthleteOnboardingStore } from '@/stores/positiveAthleteOnboardingStore';
import { hasHydrated, useSystemInviteStore } from '@/stores/systemInviteStore';

// API call status
type ApiStatus = 'idle' | 'loading' | 'success' | 'error';

export default function InvitePage({
  params: paramsPromise,
}: {
  params: Promise<{ token: string }>;
}) {
  const router = useRouter();
  const { inviteData, setInviteData, clearInviteData } = useSystemInviteStore();
  const resetOnboardingStore = usePositiveAthleteOnboardingStore(state => state.reset);
  const params = use(paramsPromise);

  // Refs to track if operations have already been performed
  const hasFetchedRef = useRef(false);
  const hasRedirectedRef = useRef(false);

  // State to track store hydration
  const [isStoreHydrated, setIsStoreHydrated] = useState(false);

  // State to track API call status
  const [apiStatus, setApiStatus] = useState<ApiStatus>('idle');

  // State to temporarily store API response before we process it
  const [apiResponse, setApiResponse] = useState<any>(null);

  // State to track errors
  const [error, setError] = useState<string | null>(null);

  // 1. Check hydration status - using a callback ref pattern to avoid polling
  const checkHydration = useCallback(() => {
    const hydrated = hasHydrated();
    if (hydrated !== isStoreHydrated) {
      setIsStoreHydrated(hydrated);
    }
    return hydrated;
  }, [isStoreHydrated]);

  useEffect(() => {
    // Check immediately on mount
    if (!checkHydration()) {
      // If not hydrated, set up a single recheck after a short delay
      const timer = setTimeout(checkHydration, 100);
      return () => clearTimeout(timer);
    }
  }, [checkHydration]);

  // 2. Fetch invite data - memoized to prevent duplicate calls
  const fetchInviteData = useCallback(async () => {
    // Only fetch once per component lifecycle
    if (hasFetchedRef.current) {
      return;
    }

    // Don't refetch if already in progress
    if (apiStatus === 'loading') {
      return;
    }

    // Don't refetch if we already have data for this token
    if (apiStatus === 'success' && apiResponse?.invite?.data?.token === params.token) {
      return;
    }

    setApiStatus('loading');

    try {
      hasFetchedRef.current = true; // Mark as fetched before the await to prevent race conditions
      const response = await SystemInviteService.getInvite(params.token);
      setApiResponse(response);
      setApiStatus('success');
      setError(null);
    } catch (error) {
      setApiStatus('error');

      if (isAxiosError(error)) {
        setError(`Failed to fetch invite: ${error.response?.status} ${error.message}`);
      } else if (error instanceof Error) {
        setError(`Failed to fetch invite: ${error.message}`);
      } else {
        setError('Failed to fetch invite: Unknown error');
      }
    }
  }, [params.token, apiStatus, apiResponse]);

  // Call fetch once on mount
  useEffect(() => {
    fetchInviteData();
  }, [fetchInviteData]);

  // 3. Process results only when BOTH hydration AND API call are complete
  const processResults = useCallback(() => {
    // Prevent multiple redirects
    if (hasRedirectedRef.current) {
      return;
    }

    // Wait until both processes are complete
    if (!isStoreHydrated) {
      return;
    }

    if (apiStatus === 'loading' || apiStatus === 'idle') {
      return;
    }

    if (apiStatus === 'error') {
      clearInviteData();
      resetOnboardingStore();
      hasRedirectedRef.current = true;
      router.push('/');
      return;
    }

    if (!apiResponse) {
      return;
    }

    // Both processes are complete and API call was successful

    // Check if we're switching to a different invite
    const currentToken = inviteData?.invite?.data?.token;

    if (currentToken && currentToken !== params.token) {
      clearInviteData();
      resetOnboardingStore();
    }

    // Update the store with the API response
    setInviteData(apiResponse);

    // Route to the appropriate wizard based on type
    const inviteType = apiResponse.invite.type;

    // Mark as redirected before actually redirecting
    hasRedirectedRef.current = true;

    switch (inviteType) {
      case 'positive_athlete':
        router.push('/onboarding/positive-athlete');
        break;
      case 'positive_coach':
        router.push('/onboarding/positive-coach');
        break;
      case 'sponsor':
        router.push('/onboarding/sponsor');
        break;
      case 'parent':
        router.push('/onboarding/parent');
        break;
      case 'alumni':
        router.push('/onboarding/alumni');
        break;
      case 'athletics_director':
        router.push('/onboarding/athletics-director');
        break;
      default:
        router.push('/'); // Redirect to home for unsupported types
    }
  }, [
    isStoreHydrated,
    apiStatus,
    apiResponse,
    inviteData,
    clearInviteData,
    resetOnboardingStore,
    router,
    params.token,
    setInviteData,
  ]);

  // Process results whenever dependencies change
  useEffect(() => {
    processResults();
  }, [processResults]);

  // Render appropriate loading state based on current status
  if (apiStatus === 'error') {
    return (
      <LoadingScreen
        content={
          <div className="text-center">
            <h1 className="text-xl font-semibold mb-2">Error</h1>
            <p className="text-red-600">{error || 'Failed to process your invite.'}</p>
            <p className="mt-4 text-gray-600">Redirecting you to the home page...</p>
          </div>
        }
      />
    );
  }

  if (!isStoreHydrated) {
    return (
      <LoadingScreen
        content={
          <div className="text-center">
            <h1 className="text-xl font-semibold mb-2">Preparing your account...</h1>
            <p className="text-gray-600">Initializing your profile data.</p>
          </div>
        }
      />
    );
  }

  if (apiStatus === 'loading' || apiStatus === 'idle') {
    return (
      <LoadingScreen
        content={
          <div className="text-center">
            <h1 className="text-xl font-semibold mb-2">Loading your invite...</h1>
            <p className="text-gray-600">Please wait while we fetch your profile information.</p>
          </div>
        }
      />
    );
  }

  // Default loading screen when processing/redirecting
  return (
    <LoadingScreen
      content={
        <div className="text-center">
          <h1 className="text-xl font-semibold mb-2">Setting up your onboarding...</h1>
          <p className="text-gray-600">You&apos;ll be redirected momentarily.</p>
        </div>
      }
    />
  );
}
