'use client';

import { useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  IconDashboard,
  IconMessages,
  IconNetwork,
  IconOpportunities,
  IconProfile,
} from '@/components/icons';
import { useAuthStore } from '@/stores/auth.store';
import { getPostLoginRedirect } from '@/utils/navigation';

interface NavItem {
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  badge?: number;
}

const navigationItems: NavItem[] = [
  { href: '/dashboard', icon: IconDashboard, label: 'Dashboard' },
  { href: '/profile', icon: IconProfile, label: 'My Profile' },
  { href: '/x-factor', icon: IconNetwork, label: 'X Factor' },
  { href: '/network', icon: IconNetwork, label: 'Network' },
  { href: '/opportunities', icon: IconOpportunities, label: 'Opportunities' },
  { href: '/messages', icon: IconMessages, label: 'Messages', badge: 1 },
];

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { user, profileType } = useAuthStore();

  useEffect(() => {
    if (user && profileType) {
      const correctPath = getPostLoginRedirect(profileType);
      if (window.location.pathname === '/dashboard' && correctPath !== '/dashboard') {
        router.replace(correctPath);
      }
    }
  }, [user, profileType, router]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Left Sidebar Navigation */}
      <nav className="fixed left-0 top-0 bottom-0 w-64 bg-white border-r border-gray-200 overflow-y-auto">
        <div className="p-6">
          <Link href="/dashboard">
            <Image
              src="/images/logo.png"
              alt="Positive Athlete"
              width={150}
              height={40}
              className="mb-8"
            />
          </Link>

          <div className="space-y-1">
            {navigationItems.map(item => (
              <Link
                key={item.href}
                href={item.href}
                className="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-50 group"
              >
                <item.icon className="w-5 h-5 mr-3 text-gray-400 group-hover:text-[#002B5C]" />
                <span className="flex-1">{item.label}</span>
                {item.badge && (
                  <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                    {item.badge}
                  </span>
                )}
              </Link>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content Area */}
      <main className="ml-64 min-h-screen">{children}</main>
    </div>
  );
}
