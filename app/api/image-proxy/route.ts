import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
  const imageUrl = req.nextUrl.searchParams.get('imageUrl');

  if (!imageUrl || typeof imageUrl !== 'string') {
    return NextResponse.json({ error: 'imageUrl query parameter is required.' }, { status: 400 });
  }

  try {
    const url = new URL(imageUrl);
    if (url.protocol !== 'http:' && url.protocol !== 'https:') {
      return NextResponse.json({ error: 'Invalid imageUrl protocol.' }, { status: 400 });
    }

    const imageResponse = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        // Avoid sending any client-side headers like cookies to the external image server
      },
    });

    if (!imageResponse.ok) {
      return NextResponse.json(
        {
          error: `Failed to fetch image from external source. Status: ${imageResponse.status}`,
          statusText: imageResponse.statusText,
        },
        { status: imageResponse.status }
      );
    }

    // Get the body as a ReadableStream
    const body = imageResponse.body;
    if (!body) {
      return NextResponse.json({ error: 'Image response body is null.' }, { status: 500 });
    }

    // Get headers from the original response to forward them
    const headers = new Headers();
    const contentType = imageResponse.headers.get('content-type');
    if (contentType) {
      headers.set('Content-Type', contentType);
    }
    const contentLength = imageResponse.headers.get('content-length');
    if (contentLength) {
      headers.set('Content-Length', contentLength);
    }

    // Stream the response back
    /* Original streaming approach
    return new NextResponse(body, {
      status: 200,
      statusText: 'OK',
      headers,
    });
    */

    // New approach: send the full buffer
    const imageBuffer = await imageResponse.arrayBuffer();
    return new NextResponse(imageBuffer, {
      status: 200,
      statusText: 'OK',
      headers, // Forwarding original Content-Type and Content-Length is important
    });
  } catch (error) {
    console.error('[IMAGE PROXY ERROR GET]:', error);
    if (error instanceof TypeError && error.message.includes('Invalid URL')) {
      return NextResponse.json({ error: 'Invalid imageUrl format.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal server error proxying image.' }, { status: 500 });
  }
}
