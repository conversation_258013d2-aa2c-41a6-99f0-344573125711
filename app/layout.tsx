import type { Metada<PERSON> } from 'next';
import { figtree, oxanium } from './fonts';
import './globals.css';
import ClientLayout from '../components/layouts/ClientLayout';

export const metadata: Metadata = {
  title: 'Positive Athlete',
  description: 'Empowering student athletes to achieve their full potential.',
  icons: {
    icon: [{ url: '/favicon.png', type: 'image/png' }],
    // apple: [{ url: '/apple-touch-icon.png' }],
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className={`${figtree.variable} ${oxanium.variable}`}>
      <body className="antialiased font-base font-sans">
        <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
  );
}
