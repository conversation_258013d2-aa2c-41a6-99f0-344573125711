@tailwind base;
@tailwind components;
@tailwind utilities;

@import '@/styles/grid.css';

:root {
  --background: #ffffff;
  --foreground: #383838;
}

body {
  color: var(--foreground);
  background: var(--background);
}

.pa-container {
  @apply w-full px-4 lg:px-10 xl:px-16 2xl:px-28;
}

.pa-container-wide {
  @apply w-full px-4 lg:px-10;
}

.pa-max-w {
  @apply mx-auto max-w-8xl;
}

.pa-spline-badge {
  @apply [&_div_canvas]:!size-[160px];
}

.pa-spline-badge-sports {
  @apply [&_div_canvas]:!size-[160px];
}

.pa-spline-sports-background {
  @apply [&_div_canvas]:!h-[280px] [&_div_canvas]:!w-[360px];
}

.pa-eyebrow {
  @apply block font-oxanium text-xs font-bold uppercase tracking-wider;
}

@layer utilities {
  /* Hide scrollbar but maintain scroll functionality */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
}
