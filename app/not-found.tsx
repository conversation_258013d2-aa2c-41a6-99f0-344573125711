import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { faArrowLeft } from '@fortawesome/pro-regular-svg-icons';
import Button from '@/components/shared/Button';

export default function NotFound() {
  return (
    <main className="relative grid min-h-screen place-items-center bg-surface-secondary px-6 py-24 sm:py-32 lg:px-8">
      <header className="fixed top-0 left-0 bg-white shadow-header h-20 flex items-center w-full z-40">
        <div className="pa-container flex items-center gap-x-4 justify-between px-4 h-full">
          <Link href="/dashboard" aria-label="Dashboard">
            <Image
              src="/images/positive-athlete.svg"
              alt="Positive Athlete"
              width={100}
              height={28}
              className="h-7 w-auto"
            />
          </Link>
        </div>
      </header>
      <section className="text-center">
        <p className="text-2xl font-bold font-oxanium text-brand-blue">404</p>
        <h1 className="mt-4 text-balance text-3xl font-semibold tracking-tight text-text-primary sm:text-5xl">
          Page Not Found
        </h1>
        <p className="mt-6 text-pretty text-lg font-medium text-text-secondary sm:text-xl/8">
          Sorry, we couldn&apos;t find the page you&apos;re looking for.
        </p>
        <div className="mt-10 flex items-center justify-center gap-x-6">
          <Button href="/" icon={faArrowLeft} iconPosition="left">
            Go Back Home
          </Button>
        </div>
      </section>
    </main>
  );
}
