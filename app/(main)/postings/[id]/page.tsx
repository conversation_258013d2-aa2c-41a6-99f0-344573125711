// app/(main)/postings/[id]/page.tsx

import { META_DESCRIPTION } from '@/lib/utils';
import PostingView from '@/views/postings/PostingView';

export const metadata = {
  title: 'Posting',
  description: META_DESCRIPTION,
};

type PageProps = {
  params: Promise<{ id: string }>;
  searchParams: Promise<Record<string, string | string[]>>;
};

export default async function Page({ params, searchParams }: PageProps) {
  const resolvedParams = await params;
  return <PostingView id={resolvedParams.id} />;
}
