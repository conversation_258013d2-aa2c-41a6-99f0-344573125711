'use server';

import { META_DESCRIPTION } from '@/lib/utils';
import XFactorModuleView from '@/views/x-factor/XFactorModuleView';

type MetadataProps = {
  params: Promise<{
    moduleId: string;
  }>;
};

// TODO: Add module title to metadata
export async function generateMetadata({ params }: MetadataProps) {
  return {
    title: 'X-Factor | Module',
    description: META_DESCRIPTION,
  };
}

type PageProps = {
  params: Promise<{
    moduleId: string;
  }>;
  searchParams: Promise<Record<string, string | string[]>>;
};

export default async function ModuleDetailPage({ params, searchParams }: PageProps) {
  return <XFactorModuleView />;
}
