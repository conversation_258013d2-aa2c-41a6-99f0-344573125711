'use server';

import { META_DESCRIPTION } from '@/lib/utils';
import XFactorCourseView from '@/views/x-factor/XFactorCourseView';

type MetadataProps = {
  params: Promise<{
    courseId: string;
  }>;
};

// TODO: Add course title to metadata
export async function generateMetadata({ params }: MetadataProps) {
  return {
    title: 'X-Factor | Course',
    description: META_DESCRIPTION,
  };
}

type PageProps = {
  params: Promise<{
    courseId: string;
  }>;
  searchParams: Promise<Record<string, string | string[]>>;
};

export default async function CoursePage({ params, searchParams }: PageProps) {
  return <XFactorCourseView />;
}
