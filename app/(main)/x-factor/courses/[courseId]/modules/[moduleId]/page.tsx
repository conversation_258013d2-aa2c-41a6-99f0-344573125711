'use server';

import { META_DESCRIPTION } from '@/lib/utils';
import XFactorCourseModuleView from '@/views/x-factor/XFactorCourseModuleView';

type MetadataProps = {
  params: Promise<{
    courseId: string;
    moduleId: string;
  }>;
};

export async function generateMetadata({ params }: MetadataProps) {
  return {
    title: 'X-Factor | Course Module',
    description: META_DESCRIPTION,
  };
}

type PageProps = {
  params: Promise<{
    courseId: string;
    moduleId: string;
  }>;
  searchParams: Promise<Record<string, string | string[]>>;
};

export default async function CourseModulePage({ params, searchParams }: PageProps) {
  return <XFactorCourseModuleView />;
}
