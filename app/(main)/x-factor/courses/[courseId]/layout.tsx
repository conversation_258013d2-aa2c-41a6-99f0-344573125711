'use client';

import { usePathname, useRouter } from 'next/navigation';
import { faArrowLeft } from '@fortawesome/pro-regular-svg-icons';
import Button from '@/components/shared/Button';
import { XFactorNavigation } from '@/components/x-factor/XFactorNavigation';

export default function CourseLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname() || '';

  // Check if we're on a module page by looking for /modules/ in the path
  const isModulePage = pathname.includes('/modules/');
  const courseId = pathname.split('/')[3]; // Get courseId from URL

  const handleBack = () => {
    if (isModulePage) {
      router.push(`/x-factor/courses/${courseId}`);
    } else {
      router.push('/x-factor?tab=browse');
    }
  };

  return (
    <div className="min-h-screen bg-[#F7F7F7]">
      {/* Navigation */}
      <XFactorNavigation usePageNavigation />

      {/* Header */}
      <div className="pa-container py-6 flex justify-between items-center">
        <Button
          color="blue"
          variant="text"
          size="large"
          icon={faArrowLeft}
          iconPosition="left"
          onClick={handleBack}
        >
          {isModulePage ? 'Back to Course' : 'Back to Browse'}
        </Button>

        {/* Timer portal target */}
        <div id="exam-timer-portal" />
      </div>

      {/* Main content */}
      <div className="pa-container pb-20">{children}</div>
    </div>
  );
}
