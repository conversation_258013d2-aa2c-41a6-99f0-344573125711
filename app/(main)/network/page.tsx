import { META_DESCRIPTION } from '@/lib/utils';
import NetworkView from '@/views/network/NetworkView';

export const metadata = {
  title: 'Network',
  description: 'Search for athletes and coaches in your network.',
};

type PageProps = {
  params: Promise<{ id: string }>;
  searchParams: Promise<Record<string, string | string[]>>;
};

export default function Page({ params, searchParams }: PageProps) {
  return <NetworkView />;
}
