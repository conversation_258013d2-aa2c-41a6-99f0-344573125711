import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { ResumeBuilderWrapper } from '@/components/profile/positive-athlete/ResumeBuilder/ResumeBuilderWrapper';
import { getSession } from '@/lib/auth';
import { getProfile } from '@/services/profile.service';

export const metadata: Metadata = {
  title: 'Resume Builder',
};

export default async function ResumeBuilderPage() {
  const session = await getSession();

  if (!session?.user.id) {
    notFound();
  }

  const response = await getProfile(parseInt(session.user.id, 10));

  if (!response?.data?.data) {
    notFound();
  }

  return (
    <div className="flex h-screen">
      {/* Left Panel - Resume Builder */}
      <div className="w-[500px] overflow-y-auto border-r border-gray-200 bg-gray-50">
        <ResumeBuilderWrapper />
      </div>

      {/* Right Panel - Resume Preview */}
      <div className="flex-1 overflow-y-auto bg-gray-100">
        <div className="p-8">{/* Resume preview content */}</div>
      </div>
    </div>
  );
}
