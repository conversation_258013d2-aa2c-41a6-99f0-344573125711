'use client';

import React, { useEffect } from 'react';
import clsx from 'clsx';
import {
  authenticatedADNavigationItems,
  authenticatedCoachNavigationItems,
  authenticatedNavigationItems,
  authenticatedParentNavigationItems,
  authenticatedSponsorNavigationItems,
  publicNavigationItems,
} from '@/components/navigation/constants';
import Header from '@/components/navigation/Header';
import Sidebar from '@/components/navigation/Sidebar';
import { useAuth } from '@/hooks/useAuth';
import { useRoutePermissions } from '@/hooks/useRoutePermissions';
import { MEDIA_QUERY_DESKTOP, useMediaQuery } from '@/hooks/utils/useMediaQuery';
import { ProfileType, ProfileTypes } from '@/stores/auth.store';
import { useSidebarStore } from '@/stores/useSidebarStore';

export default function MainLayout({ children }: { children: React.ReactNode }) {
  const { isExpanded } = useSidebarStore();
  const { isLoggedIn, profileType } = useAuth();
  const { hasPermissionForCurrentRoute } = useRoutePermissions();
  const isDesktop = useMediaQuery(MEDIA_QUERY_DESKTOP);

  // Verify the user has permission for the current route
  useEffect(() => {
    // This is just a safety check - the main protection happens in RouteGuard
    if (isLoggedIn && !hasPermissionForCurrentRoute()) {
      console.warn('User accessing a route they should not have permission for.');
    }
  }, [isLoggedIn, hasPermissionForCurrentRoute]);

  const getNavigationItems = () => {
    if (!isLoggedIn) return publicNavigationItems;

    switch (profileType) {
      case ProfileTypes.POSITIVE_COACH:
        return authenticatedCoachNavigationItems;
      case ProfileTypes.POSITIVE_ATHLETE:
        return authenticatedNavigationItems;
      case ProfileTypes.ATHLETICS_DIRECTOR:
        return authenticatedADNavigationItems;
      case ProfileTypes.SPONSOR:
        return authenticatedSponsorNavigationItems;
      case ProfileTypes.PARENT:
        return authenticatedParentNavigationItems;
      case ProfileTypes.UTILITY:
      case ProfileTypes.ADMIN:
      case ProfileTypes.COLLEGE_ATHLETE:
      case ProfileTypes.PROFESSIONAL:
      case ProfileTypes.TEAM_STUDENT:
      case ProfileTypes.TEAM_COACH:
      case ProfileTypes.ATHLETE:
      case ProfileTypes.COACH:
        return authenticatedNavigationItems;
      case null:
        return publicNavigationItems;
      default:
        return authenticatedNavigationItems;
    }
  };

  const navItems = getNavigationItems();

  const renderNavigation = () => {
    if (isLoggedIn) {
      if (isDesktop) {
        return <Sidebar navigationItems={navItems} />;
      }
      return <Header navigationItems={navItems} displayAdminAndLogout />;
    }
    return <Header navigationItems={publicNavigationItems} displayNavigationDesktop displayLogin />;
  };

  return (
    <div className="min-h-screen bg-surface-secondary">
      {renderNavigation()}

      <main
        className={clsx(
          'min-h-screen transition-all duration-300 ease-in-out lg:pt-0',
          isLoggedIn ? (isExpanded ? 'lg:ml-40' : 'lg:ml-20') : 'mx-auto',
          isLoggedIn ? 'pt-20' : 'mt-20'
        )}
      >
        {children}
      </main>
    </div>
  );
}
