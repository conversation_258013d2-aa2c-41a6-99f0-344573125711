// app/(main)/opportunities/[id]/page.tsx

import { META_DESCRIPTION } from '@/lib/utils';
import { opportunitiesService } from '@/services/opportunities.service';
import OpportunityView from '@/views/opportunities/OpportunityView';

// export async function generateMetadata({ params }: { params: { id: any } }) {
//   const opportunity = await opportunitiesService.getOpportunity(params.id);

//   const organizationName = opportunity.organization?.name || opportunity.organizationName || '';
//   const title = organizationName ? `${organizationName} | ${opportunity.title}` : opportunity.title;

//   const description = opportunity.description || 'Opportunity details available.';

//   return { title, description };
// }

// TODO: Wire up dynamic metadata
export const metadata = {
  title: 'Opportunity',
  description: META_DESCRIPTION,
};

type PageProps = {
  params: Promise<{ id: string }>;
  searchParams: Promise<Record<string, string | string[]>>;
};

export default async function Page({ params, searchParams }: PageProps) {
  const resolvedParams = await params;
  return <OpportunityView id={resolvedParams.id} />;
}
