'use server';

// app/(auth)/profile/public/page.tsx
import Link from 'next/link';
import { getCanonicalUrl, META_DESCRIPTION, OG_IMAGE } from '@/lib/utils';
import { PublicProfile, publicProfileService } from '@/services/public-profile.service';
import { ProfileTypes } from '@/stores/auth.store';
import PrivateProfileMessage from '@/views/profile/public/PublicProfileDefault';
import PublicProfileView from '@/views/profile/public/PublicProfileView';

const formatProfileType = (profileType: string): string => {
  return profileType
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

type MetadataProps = {
  params: Promise<{ id: string }>;
};

export async function generateMetadata({ params }: MetadataProps) {
  try {
    const resolvedParams = await params;

    // Add validation for the ID parameter
    if (!resolvedParams.id || typeof resolvedParams.id !== 'string') {
      console.warn('Invalid or missing ID parameter for generateMetadata');
      return {
        title: 'Positive Athlete',
        description: META_DESCRIPTION,
      };
    }

    const profile = await publicProfileService.getProfile(resolvedParams.id);

    // Default to ATHLETICS_DIRECTOR if profile_type is null (known issue with Athletic Directors)
    const profileType = profile.profile_type || ProfileTypes.ATHLETICS_DIRECTOR;
    const title = `${profile.first_name} ${profile.last_name} | ${formatProfileType(profileType)}`;
    const canonicalUrl = getCanonicalUrl(`/pa/${resolvedParams.id}`);
    const shareOgImage = getCanonicalUrl(OG_IMAGE);

    return {
      title,
      description: META_DESCRIPTION,
      alternates: {
        canonical: canonicalUrl,
      },
      openGraph: {
        url: canonicalUrl,
        images: [shareOgImage],
      },
    };
  } catch (error) {
    console.error('Error in generateMetadata:', error);

    // Return generic metadata for private profiles or errors
    return {
      title: 'Positive Athlete',
      description: META_DESCRIPTION,
      alternates: {
        canonical: getCanonicalUrl('/'),
      },
      openGraph: {
        url: getCanonicalUrl('/'),
        images: [getCanonicalUrl(OG_IMAGE)],
      },
    };
  }
}

type PageProps = {
  params: Promise<{ id: string }>;
  searchParams: Promise<Record<string, string | string[]>>;
};

export default async function Page({ params, searchParams }: PageProps) {
  try {
    const resolvedParams = await params;

    // Validate the ID parameter
    if (!resolvedParams.id || typeof resolvedParams.id !== 'string') {
      return <PrivateProfileMessage />;
    }

    // Try to fetch the profile to check if it's available for public viewing
    const profile = await publicProfileService.getProfile(resolvedParams.id);

    // If we successfully get profile data, render the public profile view
    if (profile) {
      return <PublicProfileView />;
    }

    // If no profile data, show private message
    return <PrivateProfileMessage />;
  } catch (error) {
    console.error('Error checking profile availability:', error);

    // If there's any error (including authorization errors), show private message
    return <PrivateProfileMessage />;
  }
}
