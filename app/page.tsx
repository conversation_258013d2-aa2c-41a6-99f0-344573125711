'use server';

// app/page.tsx
import { getCanonicalUrl, META_DESCRIPTION, META_TITLE, OG_IMAGE } from '@/lib/utils';
import HomeView from '@/views/home/<USER>';

export async function generateMetadata() {
  const canonicalUrl = getCanonicalUrl('/');
  const shareOgImage = getCanonicalUrl(OG_IMAGE);

  return {
    title: META_TITLE,
    description: META_DESCRIPTION,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      url: canonicalUrl,
      images: [shareOgImage],
    },
  };
}

export default async function Page() {
  return <HomeView />;
}
