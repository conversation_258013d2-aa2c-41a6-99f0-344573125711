'use client';

import { useEffect } from 'react';
import LoadingScreen from '@/components/loading/LoadingScreen';
import { useAuth } from '@/hooks/useAuth';

export default function LogoutPage() {
  const { logout } = useAuth();

  useEffect(() => {
    // Call logout when the component mounts
    logout();
  }, [logout]);

  return (
    <LoadingScreen
      content={
        <div className="text-center">
          <h1 className="text-2xl font-semibold mb-4">Logging out...</h1>
          <p className="text-gray-600">Please wait while we sign you out.</p>
        </div>
      }
    />
  );
}
