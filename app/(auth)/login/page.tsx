// app/(auth)/login/page.tsx

import { getCanonicalUrl, META_DESCRIPTION, META_TITLE, OG_IMAGE } from '@/lib/utils';
import LoginView from '@/views/auth/login/LoginView';

export async function generateMetadata() {
  const canonicalUrl = getCanonicalUrl('/login');
  const shareOgImage = getCanonicalUrl(OG_IMAGE);

  return {
    title: META_TITLE,
    description: META_DESCRIPTION,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      url: canonicalUrl,
      images: [shareOgImage],
    },
  };
}

export default async function Page() {
  return <LoginView />;
}
