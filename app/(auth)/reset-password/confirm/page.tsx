'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { faArrowLeft, faCheck, faLock } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { z } from 'zod';
import Button from '@/components/shared/Button';
import PasswordInput from '@/components/shared/form/PasswordInput';
import { useForgotPassword } from '@/hooks/useForgotPassword';

const resetPasswordSchema = z
  .object({
    password: z.string().min(8, 'Password must be at least 8 characters'),
    password_confirmation: z.string(),
  })
  .refine(data => data.password === data.password_confirmation, {
    message: "Passwords don't match",
    path: ['password_confirmation'],
  });

export default function ResetPasswordConfirmPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [password, setPassword] = useState('');
  const [passwordConfirmation, setPasswordConfirmation] = useState('');
  const [clientErrors, setClientErrors] = useState<Record<string, string>>({});
  const [redirectCountdown, setRedirectCountdown] = useState<number | null>(null);

  // Extract token and email from URL parameters
  const token = searchParams?.get('token');
  const email = searchParams?.get('email');

  const {
    resetPassword,
    isResettingPassword,
    resetPasswordError,
    resetPasswordSuccess,
    resetResetPassword,
  } = useForgotPassword();

  // Redirect if missing required parameters
  useEffect(() => {
    if (!token || !email) {
      router.push('/reset-password');
    }
  }, [token, email, router]);

  // Handle successful password reset with countdown redirect
  useEffect(() => {
    if (resetPasswordSuccess && redirectCountdown === null) {
      setRedirectCountdown(5);
      const interval = setInterval(() => {
        setRedirectCountdown(prev => {
          if (prev === null || prev <= 1) {
            clearInterval(interval);
            router.push('/login');
            return null;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [resetPasswordSuccess, redirectCountdown, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setClientErrors({});

    if (!token || !email) {
      return;
    }

    try {
      // Client-side validation
      resetPasswordSchema.parse({ password, password_confirmation: passwordConfirmation });

      // Submit password reset
      resetPassword({
        token,
        email,
        password,
        password_confirmation: passwordConfirmation,
      });
    } catch (error: unknown) {
      if (error instanceof z.ZodError) {
        const errorMap: Record<string, string> = {};
        error.errors.forEach(err => {
          if (err.path.length > 0) {
            errorMap[err.path[0] as string] = err.message;
          }
        });
        setClientErrors(errorMap);
      }
    }
  };

  // Show success state after successful password reset
  if (resetPasswordSuccess) {
    return (
      <div className="text-center">
        <div className="flex justify-center mb-6">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <FontAwesomeIcon icon={faCheck} className="w-8 h-8 text-green-600" />
          </div>
        </div>
        <h2 className="text-3xl font-extrabold text-gray-900 mb-4">Password Reset Successfully!</h2>
        <p className="text-gray-600 mb-8">
          Your password has been updated. You can now sign in with your new password.
        </p>

        {redirectCountdown && (
          <p className="text-sm text-gray-500 mb-4">
            Redirecting to login in {redirectCountdown} seconds...
          </p>
        )}

        <div className="space-y-3">
          <Button href="/login" size="small" className="mb-2">
            Go to Login
          </Button>
          <div>
            <Button href="/reset-password" variant="text" color="blue" className="text-sm">
              Reset Another Password
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Return early if missing parameters
  if (!token || !email) {
    return null;
  }

  // Helper function to parse backend validation errors
  const getBackendFieldErrors = (): Record<string, string> => {
    if (!resetPasswordError) return {};

    // Try to extract field-specific errors from the error
    const errorData = (resetPasswordError as any)?.response?.data;
    if (errorData?.errors) {
      const fieldErrors: Record<string, string> = {};
      Object.entries(errorData.errors).forEach(([field, messages]) => {
        if (Array.isArray(messages) && messages.length > 0) {
          fieldErrors[field] = messages[0];
        } else if (typeof messages === 'string') {
          fieldErrors[field] = messages;
        }
      });
      return fieldErrors;
    }

    return {};
  };

  // Get field-level errors (client-side + backend)
  const getFieldError = (field: string) => {
    const clientError = clientErrors[field];
    const backendErrors = getBackendFieldErrors();
    const backendError = backendErrors[field];

    // Prioritize client-side validation, then backend validation
    return clientError || backendError || undefined;
  };

  // Get general backend error message (if not field-specific)
  const getGeneralBackendError = () => {
    if (!resetPasswordError) return null;

    const backendFieldErrors = getBackendFieldErrors();
    const hasFieldErrors = Object.keys(backendFieldErrors).length > 0;

    // Only show general error if there are no field-specific errors
    if (!hasFieldErrors) {
      return resetPasswordError.message;
    }

    return null;
  };

  const generalBackendError = getGeneralBackendError();

  return (
    <div className="space-y-10">
      <Button
        href="/reset-password"
        variant="text"
        color="blue"
        icon={faArrowLeft}
        iconPosition="left"
        className="mb-4"
      >
        Back
      </Button>

      <div>
        <h2 className="text-3xl font-extrabold text-gray-900">Create New Password</h2>
        <p className="text-sm text-gray-600 mt-2">
          Enter your new password below. Make sure it&apos;s at least 8 characters long.
        </p>
      </div>

      {/* Display general backend errors */}
      {generalBackendError && (
        <div className="p-3 text-sm text-red-600 bg-red-50 rounded-lg">{generalBackendError}</div>
      )}

      <form className="space-y-6" onSubmit={handleSubmit}>
        <div>
          <PasswordInput
            label="New Password"
            id="password"
            required
            value={password}
            onChange={e => setPassword(e.target.value)}
            disabled={isResettingPassword}
            isFailedValidation={!!getFieldError('password')}
            description={getFieldError('password') || 'Password must be at least 8 characters'}
          />
        </div>

        <div>
          <PasswordInput
            label="Confirm New Password"
            id="password_confirmation"
            required
            value={passwordConfirmation}
            onChange={e => setPasswordConfirmation(e.target.value)}
            disabled={isResettingPassword}
            isFailedValidation={!!getFieldError('password_confirmation')}
            description={getFieldError('password_confirmation') || 'Enter the same password again'}
          />
        </div>

        <Button
          type="submit"
          size="small"
          icon={faLock}
          iconPosition="left"
          className="mb-2"
          disabled={isResettingPassword}
        >
          {isResettingPassword ? 'Updating Password...' : 'Update Password'}
        </Button>
      </form>

      <div className="text-center pt-4">
        <Link href="/login" className="text-sm font-medium text-[#002B5C] hover:text-[#002347]">
          Remember your password? Sign in
        </Link>
      </div>
    </div>
  );
}
