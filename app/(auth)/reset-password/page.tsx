'use client';

import { useState } from 'react';
import Link from 'next/link';
import { faArrowLeft, faEnvelope, faPaperPlane } from '@fortawesome/pro-regular-svg-icons';
import { z } from 'zod';
import Button from '@/components/shared/Button';
import SiteInput from '@/components/shared/form/SiteInput';
import { useForgotPassword } from '@/hooks/useForgotPassword';

const resetSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

export default function ResetPasswordPage() {
  const [email, setEmail] = useState('');
  const [clientError, setClientError] = useState('');

  const {
    requestPasswordReset,
    isRequestingPasswordReset,
    requestPasswordResetError,
    requestPasswordResetSuccess,
    resetRequestPasswordReset,
  } = useForgotPassword();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setClientError('');

    try {
      // Client-side validation
      resetSchema.parse({ email });

      // Make API call
      requestPasswordReset(email);
    } catch (error: unknown) {
      if (error instanceof z.ZodError) {
        setClientError(error.errors[0].message);
      }
    }
  };

  // Show success state after successful submission
  if (requestPasswordResetSuccess) {
    return (
      <div className="text-center">
        <h2 className="text-3xl font-extrabold text-gray-900 mb-4">Email Sent!</h2>
        <p className="text-gray-600 mb-8">
          Please check your email and follow the instructions to reset your password.
        </p>
        <Button
          href="/login"
          variant="text"
          color="blue"
          icon={faArrowLeft}
          iconPosition="left"
          className="mb-2"
        >
          Back to Login
        </Button>
      </div>
    );
  }

  // Get the error message to display
  const errorMessage = clientError || requestPasswordResetError?.message;

  return (
    <div className="space-y-10">
      <Button
        href="/login"
        variant="text"
        color="blue"
        icon={faArrowLeft}
        iconPosition="left"
        className="mb-4"
      >
        Back
      </Button>
      <h2 className="text-3xl font-extrabold text-gray-900">Reset your Password</h2>
      <p className="text-sm text-gray-600">
        Please enter your email address and we&apos;ll send you a reset link to change your
        password.
      </p>

      <form className="space-y-10" onSubmit={handleSubmit}>
        <div>
          <SiteInput
            label="Email"
            id="email"
            type="email"
            icon={faEnvelope}
            required
            value={email}
            onChange={e => setEmail(e.target.value)}
            disabled={isRequestingPasswordReset}
            isFailedValidation={!!errorMessage}
            description={errorMessage}
          />
        </div>

        <Button
          type="submit"
          size="small"
          icon={faPaperPlane}
          iconPosition="left"
          className="mb-2"
          disabled={isRequestingPasswordReset}
        >
          {isRequestingPasswordReset ? 'Sending...' : 'Send Password Reset Email'}
        </Button>
      </form>
    </div>
  );
}
