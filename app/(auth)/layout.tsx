'use client';

import Image from 'next/image';
import { publicNavigationItems } from '@/components/navigation/constants';
import Header from '@/components/navigation/Header';

export default function AuthLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Header with Navigation */}
      <Header
        navigationItems={publicNavigationItems}
        ctaLabel="Nominate"
        ctaHref="https://www.positiveathlete.org/nominate"
        displayNavigationDesktop
        displayLogin
      />

      {/* Main content */}
      <div className="flex-1 flex mt-16 relative">
        {/* Left side - Form content */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8 relative z-10">
          <div className="max-w-md w-full">{children}</div>
        </div>

        {/* Right side - Background image - now position fixed on desktop */}
        <div className="hidden lg:block lg:w-1/2 lg:fixed lg:top-0 lg:right-0 lg:bottom-0">
          <Image
            src={`${process.env.NEXT_PUBLIC_BASE_URL || ''}/images/auth-bg.jpg`}
            alt="Abstract background"
            fill
            sizes="50vw"
            quality={90}
            style={{ objectFit: 'cover' }}
            priority
          />
        </div>
      </div>
    </div>
  );
}
