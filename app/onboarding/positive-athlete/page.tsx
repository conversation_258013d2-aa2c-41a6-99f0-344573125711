'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { AccountInfoStep } from '@/components/wizard/Steps/AccountInfoStep';
import { ExperienceStep } from '@/components/wizard/Steps/ExperienceStep';
import { InvolvementStep } from '@/components/wizard/Steps/InvolvementStep';
import { SportsStep } from '@/components/wizard/Steps/SportsStep';
import { StoryStep } from '@/components/wizard/Steps/StoryStep';
import { StudentDetailsStep } from '@/components/wizard/Steps/StudentDetailsStep';
import { WelcomeStep } from '@/components/wizard/Steps/WelcomeStep';
import { Wizard, WizardStep } from '@/components/wizard/Wizard';
import { ProfileTypes } from '@/stores/auth.store';
import { hasHydrated, useSystemInviteStore } from '@/stores/systemInviteStore';

export default function Page() {
  const router = useRouter();
  const inviteData = useSystemInviteStore(state => state.inviteData);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(hasHydrated());
  }, []);

  useEffect(() => {
    if (!isHydrated) {
      return;
    }

    if (!inviteData?.invite || inviteData.invite.type !== ProfileTypes.POSITIVE_ATHLETE) {
      router.push('/');
      return;
    }
  }, [inviteData, router, isHydrated]);

  if (!isHydrated || !inviteData?.invite) {
    return null;
  }

  // Use type assertion to fix TypeScript error
  const nomination = (inviteData.invite.data as any).nomination;

  // console.log('inviteData', inviteData);
  // console.log('nomination', nomination);

  return (
    <Wizard>
      <WizardStep>
        <WelcomeStep
          nomineeName={`${nomination.first_name} ${nomination.last_name}`}
          nominatedBy={`${nomination.nominator_first_name} ${nomination.nominator_last_name}`}
        />
      </WizardStep>
      <WizardStep>
        <AccountInfoStep
          initialData={{
            firstName: nomination.first_name,
            lastName: nomination.last_name,
            email: nomination.email,
            phone: '', // Not provided in nomination data
            street: '',
            unit: '',
            city: '',
            state: '',
            zipCode: '',
          }}
        />
      </WizardStep>
      <WizardStep>
        <StudentDetailsStep />
      </WizardStep>
      <WizardStep>
        <SportsStep />
      </WizardStep>
      <WizardStep>
        <InvolvementStep />
      </WizardStep>
      <WizardStep>
        <ExperienceStep />
      </WizardStep>
      <WizardStep>
        <StoryStep
          nominationName={(inviteData.invite.data as any).nominator_name}
          nominationTitle={(inviteData.invite.data as any).relationship}
          nominationDate={format(
            new Date((inviteData.invite.data as any).created_at * 1000),
            'MM/dd/yy'
          )}
          nominationDetails={(inviteData.invite.data as any).note}
        />
      </WizardStep>
    </Wizard>
  );
}
