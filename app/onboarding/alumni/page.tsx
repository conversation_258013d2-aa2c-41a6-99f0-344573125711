'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { isAxiosError } from 'axios';
import { AlumniAccountInfoStep } from '@/components/wizard/Steps/alumni/AlumniAccountInfoStep';
import { AlumniWelcomeStep } from '@/components/wizard/Steps/alumni/AlumniWelcomeStep';
import { CollegeAthleteDetailsStep } from '@/components/wizard/Steps/alumni/CollegeAthleteDetailsStep';
import { ProfessionalDetailsStep } from '@/components/wizard/Steps/alumni/ProfessionalDetailsStep';
import { Wizard, WizardStep } from '@/components/wizard/Wizard';
import { useAlumniOnboarding } from '@/hooks/useAlumniOnboarding';
import type { AlumniOnboardingStepResponse } from '@/services/alumni-onboarding.service';
import { hasHydratedAlumni, useAlumniOnboardingStore } from '@/stores/alumniOnboardingStore';
import { ProfileTypes } from '@/stores/auth.store';
import {
  hasHydrated as hasHydratedSystemInvite,
  useSystemInviteStore,
} from '@/stores/systemInviteStore';
import { useWizardStore } from '@/stores/wizardStore';

export default function Page() {
  const router = useRouter();
  const inviteData = useSystemInviteStore(state => state.inviteData);
  const {
    currentStep: alumniCurrentStep,
    setCurrentStep: setAlumniCurrentStep,
    setLifeStageSelection,
    setAccountInfo,
    setErrors,
    lifeStageSelection,
  } = useAlumniOnboardingStore();
  const { setStep: setWizardStep, currentStep: wizardActiveStep } = useWizardStore();

  const [isClientHydrated, setIsClientHydrated] = useState(false);

  const inviteBaseData = useMemo(() => {
    if (!inviteData?.invite?.data) {
      return {
        first_name: undefined,
        last_name: undefined,
        email: undefined,
        phone: undefined,
        street_address: undefined,
        unit: undefined,
        city: undefined,
        state_code: undefined,
        zip: undefined,
      } as {
        [key: string]: any;
        first_name?: string;
        last_name?: string;
        email?: string;
        phone?: string;
        street_address?: string;
        unit?: string;
        city?: string;
        state_code?: string;
        zip?: string;
      };
    }
    return inviteData.invite.data as {
      first_name?: string;
      last_name?: string;
      email?: string;
      phone?: string;
      street_address?: string;
      unit?: string;
      city?: string;
      state_code?: string;
      zip?: string;
      [key: string]: any;
    };
  }, [inviteData]);

  const wizardSteps = useMemo(() => {
    const steps = [
      <WizardStep key="welcome">
        <AlumniWelcomeStep firstName={inviteBaseData.first_name} />
      </WizardStep>,
      <WizardStep key="account-info">
        <AlumniAccountInfoStep
          initialData={{
            firstName: inviteBaseData.first_name ?? '',
            lastName: inviteBaseData.last_name ?? '',
            email: inviteBaseData.email ?? '',
            phone: inviteBaseData.phone ?? '',
            street: inviteBaseData.street_address ?? '',
            unit: inviteBaseData.unit ?? '',
            city: inviteBaseData.city ?? '',
            state: inviteBaseData.state_code ?? '',
            zipCode: inviteBaseData.zip ?? '',
          }}
        />
      </WizardStep>,
    ];

    if (lifeStageSelection.intended_profile_type === 'college_athlete') {
      steps.push(
        <WizardStep key="college-details">
          <CollegeAthleteDetailsStep />
        </WizardStep>
      );
    } else if (lifeStageSelection.intended_profile_type === 'professional') {
      steps.push(
        <WizardStep key="professional-details">
          <ProfessionalDetailsStep />
        </WizardStep>
      );
    }
    return steps;
  }, [inviteBaseData, lifeStageSelection.intended_profile_type]);

  useEffect(() => {
    setIsClientHydrated(hasHydratedAlumni() && hasHydratedSystemInvite());
  }, []);

  const handleOnboardingSuccess = useCallback(
    (response: AlumniOnboardingStepResponse | { redirect: string }) => {
      if ('redirect' in response) {
        router.push(response.redirect);
      } else if (response.current_step) {
        setAlumniCurrentStep(response.current_step);
        if (response.prefill) {
          const { life_stage, intended_profile_type, ...accountAndOtherPrefill } =
            response.prefill as any;
          if (life_stage || intended_profile_type) {
            setLifeStageSelection({ life_stage, intended_profile_type });
          }
          setAccountInfo(accountAndOtherPrefill);
        }
      }
    },
    [setAlumniCurrentStep, router, setLifeStageSelection, setAccountInfo]
  );

  const handleOnboardingError = useCallback(
    (error: unknown) => {
      if (isAxiosError(error) && error.response?.data) {
        setErrors(error.response.data);
      } else {
        setErrors({ message: 'An unexpected error occurred.', errors: {} });
      }
    },
    [setErrors]
  );

  const { getNextStep } = useAlumniOnboarding({
    onSuccess: handleOnboardingSuccess,
    onError: handleOnboardingError,
  });

  useEffect(() => {
    if (!isClientHydrated) return;

    if (!inviteData?.invite || inviteData.invite.type !== ProfileTypes.ALUMNI) {
      router.push('/');
      return;
    }

    if (alumniCurrentStep === '' || alumniCurrentStep === 'life_stage_selection') {
      getNextStep.refetch();
    }
  }, [isClientHydrated, inviteData, router, alumniCurrentStep, getNextStep]);

  useEffect(() => {
    if (isClientHydrated) {
      let targetWizardStepIndex = 0;
      switch (alumniCurrentStep) {
        case 'life_stage_selection':
          targetWizardStepIndex = 0;
          break;
        case 'account_info':
          targetWizardStepIndex = 1;
          break;
        case 'college_details':
          targetWizardStepIndex = 2;
          break;
        case 'professional_details':
          targetWizardStepIndex =
            lifeStageSelection.intended_profile_type === 'college_athlete' ? 3 : 2;
          break;
        case 'completed':
          break;
        default:
          targetWizardStepIndex = 0;
      }
      if (wizardActiveStep !== targetWizardStepIndex) {
        setWizardStep(targetWizardStepIndex);
      }
    }
  }, [
    alumniCurrentStep,
    lifeStageSelection.intended_profile_type,
    setWizardStep,
    wizardActiveStep,
    isClientHydrated,
  ]);

  if (!isClientHydrated || !inviteData?.invite || getNextStep.isLoading) {
    return <div>Loading onboarding...</div>;
  }

  return <Wizard>{wizardSteps}</Wizard>;
}
