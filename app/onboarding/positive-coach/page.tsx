'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { CoachAccountInfoStep } from '@/components/wizard/Steps/coach/CoachAccountInfoStep';
import { CoachDetailsStep } from '@/components/wizard/Steps/coach/CoachDetailsStep';
import { CoachInvolvementStep } from '@/components/wizard/Steps/coach/CoachInvolvementStep';
import { CoachSportsStep } from '@/components/wizard/Steps/coach/CoachSportsStep';
import { CoachStoryStep } from '@/components/wizard/Steps/coach/CoachStoryStep';
import { CoachSuccessStep } from '@/components/wizard/Steps/coach/CoachSuccessStep';
import { WelcomeStep } from '@/components/wizard/Steps/coach/WelcomeStep';
import { Wizard, WizardStep } from '@/components/wizard/Wizard';
import { ProfileTypes } from '@/stores/auth.store';
import { hasHydrated, useSystemInviteStore } from '@/stores/systemInviteStore';

export default function Page() {
  const router = useRouter();
  const inviteData = useSystemInviteStore(state => state.inviteData);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(hasHydrated());
  }, []);

  useEffect(() => {
    if (!isHydrated) {
      return;
    }

    if (!inviteData?.invite || inviteData.invite.type !== ProfileTypes.POSITIVE_COACH) {
      router.push('/');
      return;
    }
  }, [inviteData, router, isHydrated]);

  if (!isHydrated || !inviteData?.invite) {
    return null;
  }

  // Use type assertion to fix TypeScript error
  const nomination = (inviteData.invite.data as any).nomination;

  return (
    <Wizard>
      <WizardStep>
        <WelcomeStep
          nomineeName={`${nomination.first_name} ${nomination.last_name}`}
          nominatedBy={`${nomination.nominator_first_name} ${nomination.nominator_last_name}`}
        />
      </WizardStep>

      <WizardStep>
        <CoachAccountInfoStep
          initialData={{
            firstName: nomination.first_name,
            lastName: nomination.last_name,
            email: nomination.email,
            phone: '', // Not provided in nomination data
            street: '',
            unit: '',
            city: '',
            state: '',
            zipCode: '',
          }}
        />
      </WizardStep>

      <WizardStep>
        <CoachDetailsStep />
      </WizardStep>

      <WizardStep>
        <CoachSportsStep
          heading="Step 3: Sports You Coach"
          subHeading="Add the sports that you coach. The sports you select will qualify you for regional and state Positive Athlete awards!"
        />
      </WizardStep>

      <WizardStep>
        <CoachInvolvementStep
          heading="Step 4: List your School / Community Involvement"
          subHeading="Highlight your school achievements and ways you've positively engaged with your community."
        />
      </WizardStep>

      <WizardStep>
        <CoachSuccessStep
          heading="Step 5: List Team Successes"
          subHeading="Highlight any significant wins or successes that your team or teams have had."
        />
      </WizardStep>

      <WizardStep>
        <CoachStoryStep
          nominationName={`${nomination.first_name} ${nomination.last_name}`}
          nominationTitle={`${nomination.relationship}`}
          nominationDate={format(new Date(nomination.created_at), 'MM/dd/yy')}
          nominationDetails={nomination.note}
        />
      </WizardStep>
    </Wizard>
  );
}
