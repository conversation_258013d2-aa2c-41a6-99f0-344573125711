'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { ADAccountInfoStep } from '@/components/wizard/Steps/ad/ADAccountInfoStep';
import { ADBioStep } from '@/components/wizard/Steps/ad/ADBioStep';
import { ADDetailsStep } from '@/components/wizard/Steps/ad/ADDetailsStep';
import { ADInvolvementStep } from '@/components/wizard/Steps/ad/ADInvolvementStep';
import { ADSuccessStep } from '@/components/wizard/Steps/ad/ADSuccessStep';
import { WelcomeStep } from '@/components/wizard/Steps/ad/WelcomeStep';
import { Wizard, WizardStep } from '@/components/wizard/Wizard';
import { ProfileTypes } from '@/stores/auth.store';
import { hasHydrated, useSystemInviteStore } from '@/stores/systemInviteStore';

export default function Page() {
  const router = useRouter();
  const inviteData = useSystemInviteStore(state => state.inviteData);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(hasHydrated());
  }, []);

  useEffect(() => {
    if (!isHydrated) {
      return;
    }

    if (!inviteData?.invite || inviteData.invite.type !== ProfileTypes.ATHLETICS_DIRECTOR) {
      router.push('/');
      return;
    }
  }, [inviteData, router, isHydrated]);

  if (!isHydrated || !inviteData?.invite) {
    return null;
  }

  // Use type assertion to fix TypeScript error
  const nomination = (inviteData.invite.data as any).nomination;

  return (
    <Wizard>
      <WizardStep>
        <WelcomeStep />
      </WizardStep>
      <WizardStep>
        <ADAccountInfoStep
          initialData={{
            first_name: '',
            last_name: '',
            email: '',
            phone: '',
          }}
        />
      </WizardStep>
      <WizardStep>
        <ADDetailsStep />
      </WizardStep>
      <WizardStep>
        <ADBioStep />
      </WizardStep>
      <WizardStep>
        <ADInvolvementStep />
      </WizardStep>
      <WizardStep>
        <ADSuccessStep />
      </WizardStep>
    </Wizard>
  );
}
