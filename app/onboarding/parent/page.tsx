'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { ParentAccountInfoStep } from '@/components/wizard/Steps/parent/ParentAccountInfoStep';
import { ParentWelcomeStep } from '@/components/wizard/Steps/parent/ParentWelcomeStep';
import { Wizard, WizardStep } from '@/components/wizard/Wizard';
import { ParentInviteData } from '@/services/parentOnboarding.service';
import { ProfileTypes } from '@/stores/auth.store';
import { hasHydrated, useSystemInviteStore } from '@/stores/systemInviteStore';

export default function Page() {
  const router = useRouter();
  const { inviteData } = useSystemInviteStore();
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(hasHydrated());
  }, []);

  useEffect(() => {
    if (!isHydrated) {
      return;
    }

    if (!inviteData?.invite || inviteData.invite.type !== 'parent') {
      router.push('/');
      return;
    }
  }, [inviteData, router, isHydrated]);

  if (!isHydrated || !inviteData?.invite) {
    return null;
  }

  // Access the parent data with appropriate type conversion
  const parentData = inviteData.invite.data as ParentInviteData;

  // Prepare initial data with sensible defaults
  const initialData = {
    firstName: parentData.first_name || '',
    lastName: parentData.last_name || '',
    email: parentData.email || '',
    phone: '',
  };

  return (
    <Wizard>
      <WizardStep>
        <ParentWelcomeStep />
      </WizardStep>
      <WizardStep>
        <ParentAccountInfoStep initialData={initialData} />
      </WizardStep>
    </Wizard>
  );
}
