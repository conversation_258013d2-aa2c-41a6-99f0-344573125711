'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { SponsorAccountInfoStep } from '@/components/wizard/Steps/sponsor/SponsorAccountInfoStep';
import { Wizard, WizardStep } from '@/components/wizard/Wizard';
import { BaseInviteData } from '@/services/systemInvite.service';
import { ProfileTypes } from '@/stores/auth.store';
import { hasHydrated, useSystemInviteStore } from '@/stores/systemInviteStore';

/**
 * Sponsor-specific invite data type
 * Extends the base invite data with sponsor-specific fields
 */
interface SponsorInviteData extends BaseInviteData {
  first_name: string;
  last_name: string;
  company_name: string;
}

export default function Page() {
  const router = useRouter();
  const inviteData = useSystemInviteStore(state => state.inviteData);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(hasHydrated());
  }, []);

  useEffect(() => {
    if (!isHydrated) {
      return;
    }

    if (!inviteData?.invite || inviteData.invite.type !== ProfileTypes.SPONSOR) {
      router.push('/');
      return;
    }
  }, [inviteData, router, isHydrated]);

  if (!isHydrated || !inviteData?.invite) {
    return null;
  }

  // Access the sponsor data with appropriate type conversion
  const sponsorData = inviteData.invite.data as SponsorInviteData;

  return (
    <Wizard>
      <WizardStep>
        <SponsorAccountInfoStep
          initialData={{
            firstName: sponsorData.first_name,
            lastName: sponsorData.last_name,
            email: sponsorData.email,
            phone: '',
          }}
        />
      </WizardStep>
    </Wizard>
  );
}
