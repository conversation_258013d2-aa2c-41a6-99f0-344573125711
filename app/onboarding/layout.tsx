import React from 'react';
import Image from 'next/image';

export default function OnboardingLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Main content */}
      <div className="flex-1 flex">
        {/* Left side - content */}
        <div className="px-4 py-8 w-full flex justify-center lg:w-1/2 lg:px-8">
          <div className="max-w-4xl w-full">
            <Image
              src="/images/logo.png"
              className="mb-10"
              alt="Positive Athlete Logo"
              width={120}
              height={32}
              priority
            />

            {children}
          </div>
        </div>

        {/* Right side - Background image */}
        <div className="hidden lg:block lg:w-1/2 relative">
          <Image
            src={`${process.env.NEXT_PUBLIC_BASE_URL || ''}/images/auth-bg.jpg`}
            alt="Abstract background"
            fill
            sizes="50vw"
            quality={90}
            style={{ objectFit: 'cover' }}
            priority
          />
        </div>
      </div>
    </div>
  );
}
