<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class VariableSubstitutionService
{
    /**
     * Default data sources for context resolution.
     */
    protected array $dataSources = [];

    /**
     * Cache for resolved variables during processing.
     */
    protected array $variableCache = [];

    /**
     * Available variable contexts and their database fields.
     * Using exact database column names for direct field access.
     */
    protected array $availableContexts = [
        'user' => [
            'id', 'first_name', 'last_name', 'email', 'phone', 'profile_type',
            'life_stage', 'graduation_year', 'gpa', 'class_rank', 'gender',
            'school_id', 'state_code', 'city', 'created_at', 'updated_at'
        ],
        'contact' => [
            'id', 'first_name', 'last_name', 'email', 'phone', 'type',
            'status', 'gender', 'graduation_year', 'created_at', 'updated_at'
        ],
        'nominee' => [
            'id', 'first_name', 'last_name', 'email', 'nominee_phone', 'sport',
            'sport_2', 'sport_3', 'grade', 'gender', 'school_name', 'school_id',
            'state_code', 'county', 'created_at'
        ],
        'nominator' => [
            'nominator_first_name', 'nominator_last_name', 'nominator_email',
            'nominator_phone', 'relationship'
        ],
        'system' => [
            'app_name', 'app_url', 'current_date', 'current_year'
        ]
    ];

    /**
     * Set the data sources for variable resolution.
     *
     * @param array $dataSources Associative array with context keys
     */
    public function setDataSources(array $dataSources): self
    {
        $this->dataSources = $dataSources;
        $this->variableCache = []; // Clear cache when data sources change
        return $this;
    }

    /**
     * Add a data source for a specific context.
     *
     * @param string $context The context key (user, nominee, nominator, etc.)
     * @param mixed $data The data object or array
     */
    public function addDataSource(string $context, $data): self
    {
        $this->dataSources[$context] = $data;
        unset($this->variableCache[$context]); // Clear cache for this context
        return $this;
    }

    /**
     * Process a template string by substituting all variables.
     *
     * @param string|null $template The template string to process
     * @param array $additionalData Additional data to merge with existing sources
     * @return string The processed template with variables substituted
     */
    public function processTemplate(?string $template, array $additionalData = []): string
    {
        if (empty($template)) {
            return '';
        }

        // Merge additional data with existing sources
        $workingData = array_merge($this->dataSources, $additionalData);

        return preg_replace_callback(
            '/\{([^}]+)\}/',
            function ($matches) use ($workingData) {
                return $this->resolveVariable($matches[1], $workingData);
            },
            $template
        );
    }

    /**
     * Extract all variables from a template string.
     *
     * @param string|null $template The template to analyze
     * @return array Array of unique variables found
     */
    public function extractVariables(?string $template): array
    {
        if (empty($template)) {
            return [];
        }

        preg_match_all('/\{([^}]+)\}/', $template, $matches);

        return array_unique($matches[1] ?? []);
    }

    /**
     * Get available variables for a given context.
     *
     * @param string|null $context The context to get variables for
     * @return array Array of available variable names
     */
    public function getAvailableVariables(?string $context = null): array
    {
        if ($context && isset($this->availableContexts[$context])) {
            return array_map(
                fn($field) => "{$context}.{$field}",
                $this->availableContexts[$context]
            );
        }

        // Return all available variables across all contexts
        $allVariables = [];
        foreach ($this->availableContexts as $contextName => $fields) {
            foreach ($fields as $field) {
                $allVariables[] = "{$contextName}.{$field}";
            }
        }

        return $allVariables;
    }

    /**
     * Validate that all variables in a template have available data sources.
     *
     * @param string|null $template The template to validate
     * @param array $additionalData Additional data to consider
     * @return array Array of missing variables
     */
    public function validateTemplate(?string $template, array $additionalData = []): array
    {
        $variables = $this->extractVariables($template);
        $workingData = array_merge($this->dataSources, $additionalData);
        $missing = [];

        foreach ($variables as $variable) {
            $value = $this->resolveVariable($variable, $workingData, false);
            if ($value === null) {
                $missing[] = $variable;
            }
        }

        return $missing;
    }

    /**
     * Resolve a single variable to its value.
     *
     * @param string $variable The variable name (e.g., 'user.firstname')
     * @param array $data The data sources to resolve from
     * @param bool $gracefulFallback Whether to return empty string for missing values
     * @return string|null The resolved value or null if not found
     */
    protected function resolveVariable(string $variable, array $data, bool $gracefulFallback = true): ?string
    {
        // Check cache first
        $cacheKey = md5($variable . serialize($data));
        if (isset($this->variableCache[$cacheKey])) {
            return $this->variableCache[$cacheKey];
        }

        $value = null;

        // Handle system variables
        if (Str::startsWith($variable, 'system.')) {
            $value = $this->resolveSystemVariable($variable);
        } else {
            // Handle data context variables (user.field, nominee.field, etc.)
            $value = data_get($data, $variable);

            // If not found directly, try to resolve from object properties
            if ($value === null) {
                $value = $this->resolveFromObject($variable, $data);
            }
        }

        // Convert to string for template usage
        if ($value !== null) {
            $value = $this->formatValue($value);
        } elseif ($gracefulFallback) {
            $value = '';
            Log::debug("Variable '{$variable}' not found, using empty string");
        }

        // Cache the resolved value
        $this->variableCache[$cacheKey] = $value;

        return $value;
    }

    /**
     * Resolve system variables.
     *
     * @param string $variable The system variable name
     * @return string|null The resolved value
     */
    protected function resolveSystemVariable(string $variable): ?string
    {
        return match ($variable) {
            'system.app_name' => config('app.name'),
            'system.app_url' => config('app.url'),
            'system.current_date' => now()->format('F j, Y'),
            'system.current_year' => (string) now()->year,
            default => null
        };
    }

    /**
     * Resolve variables from objects when direct data_get fails.
     *
     * @param string $variable The variable name
     * @param array $data The data sources
     * @return mixed The resolved value
     */
    protected function resolveFromObject(string $variable, array $data)
    {
        $parts = explode('.', $variable);
        if (count($parts) < 2) {
            return null;
        }

        $context = $parts[0];
        $field = $parts[1];

        $object = $data[$context] ?? null;
        if (!$object) {
            return null;
        }

        // Handle Eloquent models
        if (is_object($object) && method_exists($object, 'getAttribute')) {
            return $object->getAttribute($field);
        }

        // Handle arrays
        if (is_array($object)) {
            return $object[$field] ?? null;
        }

        // Handle objects with properties
        if (is_object($object) && property_exists($object, $field)) {
            return $object->{$field};
        }

        return null;
    }

    /**
     * Format a value for template output.
     *
     * @param mixed $value The value to format
     * @return string The formatted value
     */
    protected function formatValue($value): string
    {
        if (is_null($value)) {
            return '';
        }

        if (is_bool($value)) {
            return $value ? 'Yes' : 'No';
        }

        if ($value instanceof \Carbon\Carbon) {
            return $value->format('F j, Y');
        }

        return (string) $value;
    }

    /**
     * Create sample data for testing templates.
     *
     * @param string $context The context to create sample data for
     * @return array Sample data structure
     */
    public function createSampleData(string $context = 'all'): array
    {
        $sampleData = [];

        if ($context === 'all' || $context === 'user') {
            $sampleData['user'] = [
                'id' => 1,
                'first_name' => 'John',
                'last_name' => 'Smith',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'profile_type' => 'positive_athlete',
                'life_stage' => 'high_school',
                'graduation_year' => 2025,
                'gpa' => 3.8,
                'gender' => 'male',
                'city' => 'Springfield',
                'state_code' => 'IL',
                'created_at' => now()->format('F j, Y'),
                'updated_at' => now()->format('F j, Y'),
            ];
        }

        if ($context === 'all' || $context === 'contact') {
            $sampleData['contact'] = [
                'id' => 1,
                'first_name' => 'Emily',
                'last_name' => 'Davis',
                'email' => '<EMAIL>',
                'phone' => '(*************',
                'type' => 'positive_coach',
                'status' => 'active',
                'gender' => 'female',
                'graduation_year' => '2020',
                'created_at' => now()->format('F j, Y'),
                'updated_at' => now()->format('F j, Y'),
            ];
        }

        if ($context === 'all' || $context === 'nominee') {
            $sampleData['nominee'] = [
                'id' => 1,
                'first_name' => 'Sarah',
                'last_name' => 'Johnson',
                'email' => '<EMAIL>',
                'nominee_phone' => '(*************',
                'sport' => 'Basketball',
                'sport_2' => 'Track & Field',
                'sport_3' => null,
                'grade' => 11,
                'gender' => 'female',
                'school_name' => 'Example High School',
                'state_code' => 'CA',
                'county' => 'Los Angeles',
                'created_at' => now()->format('F j, Y'),
            ];
        }

        if ($context === 'all' || $context === 'nominator') {
            $sampleData['nominator'] = [
                'nominator_first_name' => 'Mike',
                'nominator_last_name' => 'Williams',
                'nominator_email' => '<EMAIL>',
                'nominator_phone' => '(*************',
                'relationship' => 'coach',
            ];
        }

        return $sampleData;
    }

    /**
     * Get available variable contexts and their fields.
     *
     * @return array
     */
    public function getAvailableContexts(): array
    {
        return $this->availableContexts;
    }

    /**
     * Get variables organized by context with user-friendly descriptions.
     *
     * @return array
     */
    public function getVariablesByContext(): array
    {
        return [
            'user' => [
                'label' => 'User Information',
                'description' => 'Variables for platform users (athletes, coaches, etc.)',
                'variables' => [
                    'user.first_name' => 'First name',
                    'user.last_name' => 'Last name',
                    'user.email' => 'Email address',
                    'user.phone' => 'Phone number',
                    'user.profile_type' => 'Profile type (positive_athlete, positive_coach, etc.)',
                    'user.life_stage' => 'Life stage (high_school, college, etc.)',
                    'user.graduation_year' => 'Graduation year',
                    'user.gpa' => 'GPA',
                    'user.gender' => 'Gender',
                    'user.city' => 'City',
                    'user.state_code' => 'State code',
                ]
            ],
            'contact' => [
                'label' => 'Contact Information',
                'description' => 'Variables for contacts and prospects',
                'variables' => [
                    'contact.first_name' => 'First name',
                    'contact.last_name' => 'Last name',
                    'contact.email' => 'Email address',
                    'contact.phone' => 'Phone number',
                    'contact.type' => 'Contact type',
                    'contact.status' => 'Contact status',
                    'contact.gender' => 'Gender',
                    'contact.graduation_year' => 'Graduation year',
                ]
            ],
            'nominee' => [
                'label' => 'Nominee Information',
                'description' => 'Variables for nomination recipients',
                'variables' => [
                    'nominee.first_name' => 'First name',
                    'nominee.last_name' => 'Last name',
                    'nominee.email' => 'Email address',
                    'nominee.nominee_phone' => 'Phone number',
                    'nominee.sport' => 'Primary sport',
                    'nominee.sport_2' => 'Secondary sport',
                    'nominee.sport_3' => 'Third sport',
                    'nominee.grade' => 'Grade level',
                    'nominee.gender' => 'Gender',
                    'nominee.school_name' => 'School name',
                    'nominee.state_code' => 'State code',
                    'nominee.county' => 'County',
                ]
            ],
            'nominator' => [
                'label' => 'Nominator Information',
                'description' => 'Variables for the person who made the nomination',
                'variables' => [
                    'nominator.nominator_first_name' => 'First name',
                    'nominator.nominator_last_name' => 'Last name',
                    'nominator.nominator_email' => 'Email address',
                    'nominator.nominator_phone' => 'Phone number',
                    'nominator.relationship' => 'Relationship to nominee',
                ]
            ],
            'system' => [
                'label' => 'System Information',
                'description' => 'System-generated variables',
                'variables' => [
                    'system.app_name' => 'Application name',
                    'system.app_url' => 'Application URL',
                    'system.current_date' => 'Current date',
                    'system.current_year' => 'Current year',
                ]
            ]
        ];
    }

    /**
     * Clear the variable cache.
     */
    public function clearCache(): void
    {
        $this->variableCache = [];
    }
}
