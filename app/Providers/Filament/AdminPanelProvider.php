<?php

namespace App\Providers\Filament;

use App\Filament\Admin\Pages\Admins\Admins;
use App\Filament\Admin\Pages\Advertisements\AdvertisementIndex;
use App\Filament\Admin\Pages\Athletes\AthleteIndex;
use App\Filament\Admin\Pages\Awards\AwardIndex;
use App\Filament\Admin\Pages\Coaches\CoachIndex;
use App\Filament\Admin\Pages\AthleticsDirectors\AthleticsDirectorIndex;
use App\Filament\Admin\Pages\Contacts\ContactIndex;
use App\Filament\Admin\Pages\Nominees\NomineeIndex;
use App\Filament\Admin\Pages\Scholarships\ScholarshipIndex;
use App\Filament\Admin\Pages\Sponsors\SponsorIndex;
use App\Filament\Admin\Pages\Tools\AthleticsDirectorImport;
use App\Filament\Admin\Pages\Tools\ContactsImport;
use App\Filament\Admin\Pages\Tools\NomineeImport;
use App\Filament\Admin\Pages\Tools\SchoolImport;
use App\Filament\Resources\EmailTemplateResource;
use App\Filament\Resources\ImportResource;
use App\Filament\Resources\InterestResource;
use App\Filament\Resources\TopicResource;
use App\Filament\Resources\OrganizationResource;
use App\Filament\Resources\ViewOnly\SchoolResource;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Illuminate\Support\Facades\Vite;
use Filament\Navigation\NavigationItem;
use App\Filament\Admin\Pages\XFactor\Modules;
use App\Filament\Resources\NomineeResource;
use Filament\Navigation\NavigationGroup;
use Filament\Navigation\NavigationBuilder;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('admin')
            ->path('admin')
            ->darkMode(false)
            ->colors([
                'primary' => [
                    50 => '#f6f7f9',
                    100 => '#edf0f3',
                    200 => '#d7dde4',
                    300 => '#b3bfcd',
                    400 => '#8799af',
                    500 => '#667892',
                    600 => '#002855', // brand blue
                    700 => '#001f44',
                    800 => '#001733',
                    900 => '#000e22',
                    950 => '#000711',
                ],
                'danger' => [
                    50 => '#fff0f3',
                    100 => '#ffe0e7',
                    200 => '#ffc1d0',
                    300 => '#ff92aa',
                    400 => '#ff547c',
                    500 => '#ce0d2f', // brand red
                    600 => '#b80929',
                    700 => '#920038', // utility danger
                    800 => '#7a0a2b',
                    900 => '#660d28',
                    950 => '#40010f',
                ],
                'gray' => [
                    50 => '#F9FAFB',
                    100 => '#F3F4F6',
                    200 => '#E5E7EB',
                    300 => '#D1D5DB',
                    400 => '#9CA3AF',
                    500 => '#6B7280',
                    600 => '#4B5563',
                    700 => '#374151',
                    800 => '#1F2937',
                    900 => '#111827',
                    950 => '#030712',
                ],
                'success' => [
                    50 => '#f9fff7', // utility success light
                    100 => '#f0fded',
                    200 => '#dcfbd3',
                    300 => '#bbf5ac',
                    400 => '#249b00', // utility success
                    500 => '#71e755',
                    600 => '#44b82d',
                    700 => '#378f25',
                    800 => '#2f7122',
                    900 => '#275d1e',
                    950 => '#10340c',
                ],
                'warning' => [
                    50 => '#FFF9E6',
                    100 => '#FFF3CC',
                    200 => '#FFE799',
                    300 => '#FFDB66',
                    400 => '#FFCF33',
                    500 => '#d59500', // utility alert
                    600 => '#CC8A00',
                    700 => '#B37A00',
                    800 => '#996A00',
                    900 => '#805A00',
                    950 => '#664800',
                ],
            ])
            ->font('Figtree')
            ->login()
            ->registration(false)
            ->passwordReset(false)
            ->emailVerification(false)
            ->authGuard('web')
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->tenant(null)
            ->databaseNotifications()
            ->resources([
                EmailTemplateResource::class,
                ImportResource::class,
                SchoolResource::class,
                OrganizationResource::class,
                InterestResource::class,
                TopicResource::class,
                NomineeResource::class,
            ])
            ->navigation(function (NavigationBuilder $builder): NavigationBuilder {
                return $builder
                    ->groups([
                        NavigationGroup::make('Users')
                            ->collapsible()
                            ->items([
                                NavigationItem::make('Positive Athletes')
                                    ->icon('heroicon-o-users')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.pages.athletes.*'))
                                    ->url(fn(): string => AthleteIndex::getUrl()),
                                NavigationItem::make('Positive Coaches')
                                    ->icon('heroicon-o-users')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.pages.coaches.*'))
                                    ->url(fn(): string => CoachIndex::getUrl()),
                                NavigationItem::make('Athletics Directors')
                                    ->icon('heroicon-o-academic-cap')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.pages.athletics-directors.*'))
                                    ->url(fn(): string => AthleticsDirectorIndex::getUrl()),
                                NavigationItem::make('Recruiters/Sponsors')
                                    ->icon('heroicon-o-building-office-2')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.pages.sponsors.*'))
                                    ->url(fn(): string => SponsorIndex::getUrl()),
                                NavigationItem::make('Contacts')
                                    ->icon('heroicon-o-user-group')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.pages.contacts.*'))
                                    ->url(fn(): string => ContactIndex::getUrl()),
                                NavigationItem::make('Admins')
                                    ->icon('heroicon-o-user-group')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.pages.admins.*'))
                                    ->url(fn(): string => Admins::getUrl()),

                            ]),
                    ])
                    ->groups([
                        NavigationGroup::make('')
                            ->collapsible()
                            ->items([
                                NavigationItem::make('X Factor')
                                    ->icon('heroicon-o-academic-cap')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.pages.x-factor.*'))
                                    ->url(fn(): string => Modules::getUrl()),
                                NavigationItem::make('Advertisements')
                                    ->icon('heroicon-o-rectangle-stack')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.pages.advertisements.*'))
                                    ->url(fn(): string => AdvertisementIndex::getUrl()),
                                NavigationItem::make('Scholarships')
                                    ->icon('heroicon-o-academic-cap')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.pages.scholarships.*'))
                                    ->url(fn(): string => ScholarshipIndex::getUrl()),
                                NavigationItem::make('Awards')
                                    ->icon('heroicon-o-trophy')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.pages.awards.*'))
                                    ->url(fn(): string => AwardIndex::getUrl()),
                            ]),
                    ])
                    ->groups([
                        NavigationGroup::make('Resources')
                            ->collapsible()
                            ->items([
                                NavigationItem::make('Nominees')
                                    ->icon('heroicon-o-star')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.resources.nominees.*'))
                                    ->url(fn(): string => NomineeResource::getUrl()),
                                NavigationItem::make('Schools')
                                    ->icon('heroicon-o-academic-cap')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.resources.view-only.schools.*'))
                                    ->url(fn(): string => SchoolResource::getUrl()),
                                NavigationItem::make('Organizations')
                                    ->icon('heroicon-o-building-office')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.resources.organizations.*'))
                                    ->url(fn(): string => OrganizationResource::getUrl()),
                                NavigationItem::make('Interests')
                                    ->icon('heroicon-o-heart')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.resources.interests.*'))
                                    ->url(fn(): string => InterestResource::getUrl()),
                                NavigationItem::make('Topics')
                                    ->icon('heroicon-o-tag')
                                    ->isActiveWhen(fn(): bool => request()->routeIs('filament.admin.resources.topics.*'))
                                    ->url(fn(): string => TopicResource::getUrl()),
                            ]),
                    ])
                    ->groups([
                        NavigationGroup::make('Tools')
                            ->collapsible()
                            ->collapsed(true)
                            ->items([
                                NavigationItem::make('School Import')
                                    ->icon('heroicon-o-building-library')
                                    ->url(fn(): string => SchoolImport::getUrl()),
                                NavigationItem::make('Nominee Import')
                                    ->icon('heroicon-o-users')
                                    ->url(fn(): string => NomineeImport::getUrl()),
                                NavigationItem::make('Athletics Director Import')
                                    ->icon('heroicon-o-users')
                                    ->url(fn(): string => AthleticsDirectorImport::getUrl()),
                                NavigationItem::make('Contacts Import')
                                    ->icon('heroicon-o-users')
                                    ->url(fn(): string => ContactsImport::getUrl()),
                            ]),

                        NavigationGroup::make('Reports')
                            ->collapsible()
                            ->collapsed(true)
                            ->items([
                                NavigationItem::make('Import Reports')
                                    ->icon('heroicon-o-document-chart-bar')
                                    ->url(fn(): string => ImportResource::getUrl()),
                            ]),
                    ]);
            })
            ->sidebarCollapsibleOnDesktop()
            ->discoverPages(in: app_path('Filament/Admin/Pages'), for: 'App\\Filament\\Admin\\Pages')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
            ])
            ->spa()
            ->maxContentWidth('full')
            ->default()
            ->brandName('Positive Athlete Admin')
            ->brandLogo(Vite::asset('resources/assets/svg/logo-default.svg'))
            ->favicon(Vite::asset('resources/assets/images/favicon.png'))
            ->viteTheme(['resources/js/filament/admin.js', 'resources/css/filament/admin/theme.css']);
    }
}
