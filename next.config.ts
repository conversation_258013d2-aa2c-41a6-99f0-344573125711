import type { NextConfig } from 'next';

// List of packages that should be treated as client-side only
const browserOnlyPackages = [
  '@splinetool/runtime',
  '@splinetool/react-spline',
  'echarts',
  'swiper',
  'swiper/react',
  'swiper/modules',
  'zustand',
  'zustand/middleware',
];

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'positive-athlete.test',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '8000',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '**',
        pathname: '/**',
      },
    ],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    unoptimized: true,
  },
  reactStrictMode: true,
  poweredByHeader: false,
  generateEtags: true,
  // Configure Turbopack (stable as of Next.js 15)
  turbopack: {
    resolveAlias: {
      // Provide browser-safe aliases for SSR
      'zustand/middleware': 'zustand/middleware',
    },
  },
  // Configure server external packages to prevent SSR bundling issues
  serverExternalPackages: ['zustand'],
  // Add webpack configuration for better file watching
  webpack: (config, { isServer, dev }) => {
    // Handle browser-only modules during SSR
    if (isServer) {
      // Configure externals
      const externals = Array.isArray(config.externals)
        ? config.externals
        : config.externals
          ? [config.externals]
          : [];

      // Add browser-only packages to externals
      config.externals = [
        ...externals,
        ...browserOnlyPackages.map(module => ({ [module]: 'commonjs ' + module })),
      ];
    }

    // Add watch options for better hot reloading in development
    if (dev) {
      config.watchOptions = {
        poll: 1000, // Check for changes every second
        aggregateTimeout: 300, // Delay before rebuilding
        ignored: /node_modules/,
      };
    }

    return config;
  },
};

export default nextConfig;
