// <PERSON>ript to fix the remaining badge files with special formatting
const fs = require('fs');
const path = require('path');

// Path to the badges directory
const badgesDir = path.join(__dirname, 'public', 'images', 'badges');

// Files to fix manually
const specialCases = [
  {
    oldName: '10 Champion - Achieved @1-1080x1080.png',
    newName: '10_champion_achieved.png',
  },
  {
    oldName: '10 Champion - Unachieved @1-1080x1080.png',
    newName: '10_champion_unachieved.png',
  },
  {
    oldName: '3 Varsity - Unachieved @1-1080x1080.png',
    newName: '03_varsity_unachieved.png',
  },
];

// Main function to rename files
const fixSpecialCases = () => {
  try {
    // Process each special case
    specialCases.forEach(({ oldName, newName }) => {
      const oldPath = path.join(badgesDir, oldName);
      const newPath = path.join(badgesDir, newName);

      // Check if the file exists before attempting to rename
      if (fs.existsSync(oldPath)) {
        // Rename the file
        fs.renameSync(oldPath, newPath);
        console.log(`Renamed: ${oldName} -> ${newName}`);
      } else {
        console.log(`File not found: ${oldName}`);
      }
    });

    console.log('Special cases fixed successfully!');
  } catch (error) {
    console.error('Error fixing special cases:', error);
  }
};

// Run the script
fixSpecialCases();
