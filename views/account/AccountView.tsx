'use client';

import { ChangeEvent, FormEvent, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  faEnvelope,
  faEye,
  faLock,
  faMobile,
  faTrash,
  faUserPlus,
  faXmark,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import AthleticsDirectorProfileForm from '@/components/account/AthleticsDirectorProfileForm';
import GeneralProfileForm from '@/components/account/GeneralProfileForm';
import ParentProfileForm from '@/components/account/ParentProfileForm';
import SponsorProfileForm from '@/components/account/SponsorProfileForm';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import { InfoCard } from '@/components/shared/cards/InfoCard';
import ModalFormContainer from '@/components/shared/form/ModalFormContainer';
import SiteInput from '@/components/shared/form/SiteInput';
import { useAccount } from '@/hooks/useAccount';
import { useAuth } from '@/hooks/useAuth';
import { useParentAccounts } from '@/hooks/useParentAccounts';
import {
  DeleteAccountRequest,
  ProfileData,
  UpdateAddressRequest,
  UpdatePasswordRequest,
  UpdateProfileRequest,
} from '@/services/account.service';
import { LinkParentRequest, ParentAccountData } from '@/services/positive-athlete-account.service';
import { ProfileType, ProfileTypes } from '@/stores/auth.store';
import { useModalStore } from '@/stores/modal.store';
import { useToastNotify } from '@/stores/toastNotify.store';

// Extended User type to handle the properties we need
interface UserWithProfile {
  email?: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  life_stage?: string;
  street_address?: string;
  unit?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  user_type?: string;
  role?: string;
  type?: string;
  profile_type?: string;
  profile_meta?: any; // TODO: Add type for profile meta
}

export default function AccountPage() {
  const router = useRouter();
  const { user, profileType } = useAuth();
  const { open, close } = useModalStore();
  const { toastNotify } = useToastNotify();
  const {
    profile,
    isLoadingProfile,
    address,
    isLoadingAddress,
    updateProfile,
    isUpdatingProfile,
    updateAddress,
    isUpdatingAddress,
    updatePassword,
    isUpdatingPassword,
    deleteAccount,
    isDeletingAccount,
  } = useAccount();

  // Cast user to our extended type for TypeScript
  const userWithProfile = user as UserWithProfile | null;

  // Check if user is a positive athlete (only check profile_type)
  const isPositiveAthlete =
    !!user && userWithProfile?.profile_type === ProfileTypes.POSITIVE_ATHLETE;
  const isPositiveCoach = !!user && userWithProfile?.profile_type === ProfileTypes.POSITIVE_COACH;
  const isParent = !!user && userWithProfile?.profile_type === ProfileTypes.PARENT;
  const isSponsor = !!user && userWithProfile?.profile_type === ProfileTypes.SPONSOR;
  const isAthleticsDirector =
    !!user && userWithProfile?.profile_type === ProfileTypes.ATHLETICS_DIRECTOR;

  // Always call hooks at the top level, regardless of conditions
  const {
    activeParents,
    pendingInvites,
    isLoadingParents,
    linkParent,
    isLinkingParent,
    linkParentError,
    unlinkParent,
    isUnlinkingParent,
    unlinkParentByEmail,
    isUnlinkingParentByEmail,
    resendInvite,
  } = useParentAccounts();

  // Form states
  const [profileData, setProfileData] = useState<UpdateProfileRequest & ProfileData>({
    first_name: profile?.first_name || '',
    last_name: profile?.last_name || '',
    email: profile?.email || '',
    notification_email: profile?.notification_email || null,
    phone: profile?.phone || '',
    life_stage: profile?.life_stage || '',
  });

  const [addressData, setAddressData] = useState<UpdateAddressRequest>({
    street_address_1: address?.street_address_1 || '',
    street_address_2: address?.street_address_2 || '',
    city: address?.city || '',
    state_code: address?.state_code || '',
    zip: address?.zip || '',
  });

  // Logo state for sponsor profile
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreviewUrl, setLogoPreviewUrl] = useState<string | null>(
    profile?.organization_logo || null
  );

  // Form validation states
  const [profileErrors, setProfileErrors] = useState<Record<string, string>>({});
  const [addressErrors, setAddressErrors] = useState<Record<string, string>>({});

  // Update form data when profile data changes
  useEffect(() => {
    if (profile) {
      setProfileData({
        first_name: profile.first_name || '',
        last_name: profile.last_name || '',
        email: profile.email || '',
        notification_email: profile.notification_email || null,
        phone: profile.phone || '',
        life_stage: profile.life_stage || '',
      });
    }
  }, [profile]);

  // Update form data when address data changes
  useEffect(() => {
    if (address) {
      setAddressData({
        street_address_1: address.street_address_1 || '',
        street_address_2: address.street_address_2 || '',
        city: address.city || '',
        state_code: address.state_code || '',
        zip: address.zip || '',
      });
    }
  }, [address]);

  // Input change handlers
  const handleProfileChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({ ...prev, [name]: value }));
  };

  const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setAddressData(prev => ({ ...prev, [name]: value }));
  };

  // Form submission handlers
  const handleSaveChanges = (e?: FormEvent) => {
    e?.preventDefault();

    // Validate profile form
    const errors: Record<string, string> = {};
    if (!profileData.first_name) errors.first_name = 'First name is required';
    if (!profileData.last_name) errors.last_name = 'Last name is required';
    if (!profileData.email) errors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(profileData.email)) errors.email = 'Email is invalid';

    setProfileErrors(errors);
    if (Object.keys(errors).length > 0) return;

    // Create a clean profile data object without empty strings, null or undefined values
    const cleanProfileData: UpdateProfileRequest = {};

    // Only add properties that have actual values
    if (profileData.first_name) cleanProfileData.first_name = profileData.first_name;
    if (profileData.last_name) cleanProfileData.last_name = profileData.last_name;
    if (profileData.phone && profileData.phone !== '') cleanProfileData.phone = profileData.phone;

    // Include email if it's provided and not empty
    if (profileData.email && profileData.email !== '') cleanProfileData.email = profileData.email;

    // Include notification_email if it's provided and not empty
    if (profileData.notification_email && profileData.notification_email !== '')
      cleanProfileData.notification_email = profileData.notification_email;

    // Check if the user's profile type should have a life_stage field
    const shouldHaveLifeStage = [
      ProfileTypes.POSITIVE_ATHLETE,
      ProfileTypes.COLLEGE_ATHLETE,
      ProfileTypes.PROFESSIONAL,
    ].includes(userWithProfile?.profile_type as any);

    // Only include life_stage for users who should have it
    if (shouldHaveLifeStage && profileData.life_stage && profileData.life_stage !== '') {
      // Only include life_stage if it's a valid value
      const validLifeStages = [
        'high_school_student',
        'high_school_graduate',
        'college_student',
        'college_graduate',
        'gap_year',
        'professional',
      ];
      if (validLifeStages.includes(profileData.life_stage)) {
        cleanProfileData.life_stage = profileData.life_stage;
      }
    }

    // Save profile data
    updateProfile(cleanProfileData, {
      onSuccess: () => {
        // If profile update is successful, also update address
        // Create clean address data
        const cleanAddressData: UpdateAddressRequest = {};

        // Only add properties that have actual values
        if (addressData.street_address_1 && addressData.street_address_1 !== '')
          cleanAddressData.street_address_1 = addressData.street_address_1;
        if (addressData.street_address_2 && addressData.street_address_2 !== '')
          cleanAddressData.street_address_2 = addressData.street_address_2;
        if (addressData.city && addressData.city !== '') cleanAddressData.city = addressData.city;
        if (addressData.state_code && addressData.state_code !== '')
          cleanAddressData.state_code = addressData.state_code;
        if (addressData.zip && addressData.zip !== '') cleanAddressData.zip = addressData.zip;

        updateAddress(cleanAddressData, {
          onSuccess: () => {
            console.log('Profile and address updated successfully');
            toastNotify('Profile updated successfully', 'success');
          },
          onError: error => {
            console.error('Error updating address:', error);
            toastNotify('Failed to update address information', 'error');
          },
        });
      },
      onError: error => {
        console.error('Error updating profile:', error);
        toastNotify('Failed to update profile information', 'error');
      },
    });
  };

  // Reset Password Form Component
  const ResetPasswordForm = () => {
    const [localPasswordData, setLocalPasswordData] = useState<UpdatePasswordRequest>({
      current_password: '',
      new_password: '',
      new_password_confirmation: '',
    });
    const [localErrors, setLocalErrors] = useState<Record<string, string>>({});
    const [backendError, setBackendError] = useState<string | null>(null);

    const handleLocalPasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setLocalPasswordData(prev => ({ ...prev, [name]: value }));
      // Clear backend error when user starts typing again
      if (backendError) setBackendError(null);
    };

    const handleSavePassword = () => {
      // Clear any previous backend errors
      setBackendError(null);

      // Validate form
      const errors: Record<string, string> = {};
      if (!localPasswordData.current_password)
        errors.current_password = 'Current password is required';
      if (!localPasswordData.new_password) errors.new_password = 'New password is required';
      else if (localPasswordData.new_password.length < 8)
        errors.new_password = 'Password must be at least 8 characters';

      if (!localPasswordData.new_password_confirmation)
        errors.new_password_confirmation = 'Please confirm your new password';
      else if (localPasswordData.new_password !== localPasswordData.new_password_confirmation)
        errors.new_password_confirmation = 'Passwords do not match';

      setLocalErrors(errors);
      if (Object.keys(errors).length > 0) return;

      updatePassword(localPasswordData, {
        onSuccess: () => {
          close();
          toastNotify('Password updated successfully', 'success');
        },
        onError: error => {
          // Log the entire error object for debugging
          console.error('Password update error:', error);

          // Type assertion for API error response
          const apiError = error as any;
          console.error('Error response data:', apiError.response?.data);

          // Handle backend validation errors
          if (apiError.response?.data?.message) {
            setBackendError(apiError.response.data.message);
            console.error('Error message:', apiError.response.data.message);
          } else if (apiError.response?.data?.errors) {
            // Handle Laravel validation errors format
            const validationErrors = apiError.response.data.errors;
            console.error('Validation errors:', validationErrors);

            // Log each validation error
            Object.entries(validationErrors).forEach(([field, messages]) => {
              console.error(`Field: ${field}, Errors:`, messages);
            });

            const firstError = Object.values(validationErrors)[0];
            setBackendError(Array.isArray(firstError) ? firstError[0] : String(firstError));
          } else {
            setBackendError('An error occurred while updating your password. Please try again.');
          }
        },
      });
    };

    return (
      <ModalFormContainer
        title="Reset Password"
        description="Enter your current password and a new password to reset."
        isLoading={isUpdatingPassword}
        error={null}
        handleSave={handleSavePassword}
        handleClose={close}
      >
        <div className="space-y-6">
          {backendError && (
            <div className="mb-4">
              <InfoCard type="error" message={backendError} />
            </div>
          )}

          <div>
            <SiteInput
              label="CURRENT PASSWORD"
              type="password"
              name="current_password"
              value={localPasswordData.current_password}
              onChange={handleLocalPasswordChange}
              icon={faLock}
            />
            {localErrors.current_password && (
              <p className="text-red-500 text-sm">{localErrors.current_password}</p>
            )}
          </div>
          <div>
            <SiteInput
              label="NEW PASSWORD"
              type="password"
              name="new_password"
              value={localPasswordData.new_password}
              onChange={handleLocalPasswordChange}
              icon={faLock}
            />
            {localErrors.new_password && (
              <p className="text-red-500 text-sm">{localErrors.new_password}</p>
            )}
          </div>
          <div>
            <SiteInput
              label="CONFIRM NEW PASSWORD"
              type="password"
              name="new_password_confirmation"
              value={localPasswordData.new_password_confirmation}
              onChange={handleLocalPasswordChange}
              icon={faLock}
            />
            {localErrors.new_password_confirmation && (
              <p className="text-red-500 text-sm">{localErrors.new_password_confirmation}</p>
            )}
          </div>
        </div>
      </ModalFormContainer>
    );
  };

  // Modal handlers
  const openResetPasswordModal = () => {
    open(<ResetPasswordForm />);
  };

  const openAddParentModal = () => {
    // Create a local component for the parent invitation form
    const AddParentForm = () => {
      const [localParentData, setLocalParentData] = useState<LinkParentRequest>({
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
      });
      const [localErrors, setLocalErrors] = useState<Record<string, string>>({});

      const handleLocalParentChange = (e: ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setLocalParentData(prev => ({ ...prev, [name]: value }));
      };

      const handleSaveParent = () => {
        // Validate form
        const errors: Record<string, string> = {};
        if (!localParentData.first_name) errors.first_name = 'First name is required';
        if (!localParentData.last_name) errors.last_name = 'Last name is required';
        if (!localParentData.email) errors.email = 'Email is required';

        setLocalErrors(errors);
        if (Object.keys(errors).length > 0 || !linkParent) return;

        linkParent(localParentData, {
          onSuccess: () => {
            close();
            toastNotify('Parent invitation sent successfully', 'success');
          },
          onError: error => {
            console.error(error);
            toastNotify('Failed to send parent invitation', 'error');
          },
        });
      };

      return (
        <ModalFormContainer
          title="Invite a Parent or Guardian"
          description="Parent accounts provide read-only visibility of your Positive Athlete account and messages."
          isLoading={isLinkingParent}
          error={null}
          handleSave={handleSaveParent}
          handleClose={close}
          saveButtonText="Send Invitation"
          saveButtonIcon={faEnvelope}
        >
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <SiteInput
                  label="FIRST NAME"
                  name="first_name"
                  value={localParentData.first_name}
                  onChange={handleLocalParentChange}
                />
                {localErrors.first_name && (
                  <p className="text-red-500 text-sm">{localErrors.first_name}</p>
                )}
              </div>
              <div>
                <SiteInput
                  label="LAST NAME"
                  name="last_name"
                  value={localParentData.last_name}
                  onChange={handleLocalParentChange}
                />
                {localErrors.last_name && (
                  <p className="text-red-500 text-sm">{localErrors.last_name}</p>
                )}
              </div>
            </div>
            <div>
              <SiteInput
                label="EMAIL"
                type="email"
                name="email"
                value={localParentData.email}
                onChange={handleLocalParentChange}
                icon={faEnvelope}
              />
              {localErrors.email && <p className="text-red-500 text-sm">{localErrors.email}</p>}
            </div>
            <SiteInput
              label="PHONE (OPTIONAL)"
              type="tel"
              name="phone"
              value={localParentData.phone || ''}
              onChange={handleLocalParentChange}
              icon={faMobile}
            />
          </div>
        </ModalFormContainer>
      );
    };

    open(<AddParentForm />);
  };

  const openRemoveParentModal = (parentId: number | null, parentEmail?: string) => {
    // Create a local component for the remove parent confirmation
    const RemoveParentForm = () => {
      const handleRemoveParent = () => {
        // If we have a valid parentId, use that
        if (parentId && parentId > 0 && unlinkParent) {
          unlinkParent(parentId, {
            onSuccess: () => {
              console.log('Successfully removed parent by ID');
              close();
              toastNotify('Parent account removed successfully', 'success');
            },
            onError: error => {
              console.error('Error removing parent by ID:', error);
              toastNotify('Failed to remove parent account', 'error');
            },
          });
        }
        // If we have an email but no valid ID, use the email method
        else if (parentEmail && unlinkParentByEmail) {
          unlinkParentByEmail(parentEmail, {
            onSuccess: () => {
              close();
              toastNotify('Parent invitation canceled successfully', 'success');
            },
            onError: error => {
              console.error('Error removing parent by email:', error);
              toastNotify('Failed to cancel parent invitation', 'error');
            },
          });
        } else {
          console.error('Cannot remove parent: missing both ID and email');
          toastNotify('Unable to process your request', 'error');
        }
      };

      return (
        <div className="relative p-8 bg-white">
          {/* Close Button */}
          <button
            type="button"
            className="absolute top-6 right-6 text-gray-500 hover:text-gray-700 transition-colors"
            onClick={close}
            aria-label="Close Modal"
          >
            <FontAwesomeIcon icon={faXmark} className="size-4" aria-hidden="true" />
          </button>

          {/* Header */}
          <div className="mb-4">
            <h2 className="text-xl font-bold text-gray-900">Remove Parent Account</h2>
            <p className="text-sm text-gray-600 mt-1">This action cannot be undone</p>
          </div>

          {/* Actions */}
          <div className="flex gap-4 mt-6">
            <Button
              color="red"
              size="small"
              onClick={handleRemoveParent}
              disabled={isUnlinkingParent || isUnlinkingParentByEmail}
              icon={faTrash}
              iconPosition="right"
              className="h-10 px-4 py-2 text-sm font-semibold"
            >
              {isUnlinkingParent || isUnlinkingParentByEmail ? 'Removing...' : 'Remove Account'}
            </Button>

            <Button
              color="white"
              size="small"
              onClick={close}
              disabled={isUnlinkingParent || isUnlinkingParentByEmail}
              icon={faXmark}
              iconPosition="right"
              className="h-10 px-4 py-2 text-sm font-semibold"
            >
              Cancel
            </Button>
          </div>
        </div>
      );
    };

    open(<RemoveParentForm />);
  };

  const openDeleteAccountModal = () => {
    // Create a local component for the delete account confirmation
    const DeleteAccountForm = () => {
      const [localDeleteAccountData, setLocalDeleteAccountData] = useState<DeleteAccountRequest>({
        password: '',
        confirmation: '',
      });
      const [confirmText, setConfirmText] = useState('');
      const [localErrors, setLocalErrors] = useState<Record<string, string>>({});
      const [serverError, setServerError] = useState<string | null>(null);
      const isConfirmed = confirmText === 'DELETE MY ACCOUNT';

      const handleLocalDeleteAccountChange = (e: ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setLocalDeleteAccountData(prev => ({ ...prev, [name]: value }));
        // Clear server error when user starts typing
        if (serverError) setServerError(null);
      };

      const handleConfirmTextChange = (e: ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setConfirmText(value);

        // If the user types the exact confirmation phrase, set it in the confirmation field
        if (value === 'DELETE MY ACCOUNT') {
          setLocalDeleteAccountData(prev => ({ ...prev, confirmation: 'DELETE MY ACCOUNT' }));
        } else {
          setLocalDeleteAccountData(prev => ({ ...prev, confirmation: prev.password }));
        }

        // Clear server error when user starts typing
        if (serverError) setServerError(null);
      };

      const handleDeleteAccount = () => {
        // Clear any previous errors
        setLocalErrors({});
        setServerError(null);

        // Validate form
        const errors: Record<string, string> = {};
        if (!localDeleteAccountData.password) errors.password = 'Password is required';
        if (
          localDeleteAccountData.password !== localDeleteAccountData.confirmation &&
          !isConfirmed
        ) {
          errors.confirmation = 'Passwords do not match';
        }
        if (!isConfirmed) errors.confirmText = 'Please type DELETE MY ACCOUNT to confirm';

        setLocalErrors(errors);
        if (Object.keys(errors).length > 0) return;

        // Ensure the confirmation field has the correct value before submitting
        const dataToSubmit = {
          ...localDeleteAccountData,
          confirmation: isConfirmed ? 'DELETE MY ACCOUNT' : localDeleteAccountData.confirmation,
        };

        deleteAccount(dataToSubmit, {
          onSuccess: () => {
            toastNotify('Your account has been deleted', 'info');
            router.push('/login');
          },
          onError: error => {
            console.error('Delete account error:', error);
            // Type assertion for API error response
            const apiError = error as any;

            if (apiError.response?.data?.message) {
              setServerError(apiError.response.data.message);
            } else if (apiError.response?.data?.errors) {
              // Handle Laravel validation errors format
              const validationErrors = apiError.response.data.errors;
              const firstError = Object.values(validationErrors)[0];
              setServerError(Array.isArray(firstError) ? firstError[0] : String(firstError));
            } else {
              setServerError('An error occurred while deleting your account. Please try again.');
            }
          },
        });
      };

      return (
        <div className="relative p-8 w-full">
          {/* Close Button */}
          <button
            type="button"
            className="absolute top-6 right-6 text-gray-500 hover:text-gray-700 transition-colors"
            onClick={close}
            aria-label="Close Modal"
          >
            <FontAwesomeIcon icon={faXmark} className="size-4" aria-hidden="true" />
          </button>

          {/* Header */}
          <div className="mb-6">
            <h2 className="text-xl font-bold text-gray-900">Delete Account?</h2>
            <p className="text-sm text-gray-600 mt-1">This action cannot be undone</p>
          </div>

          {/* Error display */}
          {serverError && (
            <div className="mb-6">
              <InfoCard type="error" message={serverError} />
            </div>
          )}

          {/* Form fields */}
          <div className="space-y-6">
            <div>
              <SiteInput
                label="ENTER YOUR PASSWORD"
                type="password"
                name="password"
                value={localDeleteAccountData.password}
                onChange={handleLocalDeleteAccountChange}
                icon={faLock}
              />
              {localErrors.password && (
                <p className="text-red-500 text-sm mt-1">{localErrors.password}</p>
              )}
            </div>

            <div>
              <SiteInput
                label="TYPE 'DELETE MY ACCOUNT' TO CONFIRM"
                name="confirmText"
                value={confirmText}
                onChange={handleConfirmTextChange}
              />
              {localErrors.confirmText && (
                <p className="text-red-500 text-sm mt-1">{localErrors.confirmText}</p>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-4 mt-6">
            <Button
              color="red"
              size="small"
              onClick={handleDeleteAccount}
              disabled={isDeletingAccount || !isConfirmed}
              icon={faTrash}
              iconPosition="right"
              className="h-10 px-4 py-2 text-sm font-semibold"
            >
              {isDeletingAccount ? 'Deleting...' : 'Delete Account'}
            </Button>

            <Button
              color="white"
              size="small"
              onClick={close}
              disabled={isDeletingAccount}
              icon={faXmark}
              iconPosition="right"
              className="h-10 px-4 py-2 text-sm font-semibold"
            >
              Cancel
            </Button>
          </div>
        </div>
      );
    };

    open(<DeleteAccountForm />);
  };

  const handleResendInvite = (invite: ParentAccountData) => {
    resendInvite(invite, {
      onSuccess: () => {
        console.log('Invitation resent successfully');
        toastNotify('Invitation resent successfully', 'success');
      },
      onError: error => {
        console.error('Failed to resend invitation', error);
        toastNotify('Failed to resend invitation', 'error');
      },
    });
  };

  return (
    <div className="max-w-3xl mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Account</h1>
        <Button
          color="blue"
          size="small"
          onClick={handleSaveChanges}
          disabled={isUpdatingProfile || isUpdatingAddress}
        >
          Save
        </Button>
      </div>

      {/* Loading state */}
      {(isLoadingProfile || isLoadingAddress) && (
        <div className="bg-white rounded-3xl shadow-sm p-8 mb-6 flex justify-center items-center">
          <p>Loading account information...</p>
        </div>
      )}

      {/* Parent Profile Form */}
      {!isLoadingProfile && !isLoadingAddress && isParent && (
        <ParentProfileForm
          profileData={profileData}
          profileErrors={profileErrors}
          handleProfileChange={handleProfileChange}
          setProfileData={setProfileData}
          openResetPasswordModal={openResetPasswordModal}
          profileEmail={profile?.email}
        />
      )}

      {/* Athletics Director Profile Form */}
      {!isLoadingProfile && !isLoadingAddress && isAthleticsDirector && (
        <AthleticsDirectorProfileForm
          profileData={profileData}
          profileErrors={profileErrors}
          handleProfileChange={handleProfileChange}
          setProfileData={setProfileData}
          openResetPasswordModal={openResetPasswordModal}
          profileEmail={profile?.email}
        />
      )}

      {/* Sponsor Profile Form */}
      {/* Show if not loading, isSponsor, and NOT isParent */}
      {!isLoadingProfile && !isLoadingAddress && isSponsor && !isParent && (
        <SponsorProfileForm
          profileData={{
            first_name: profileData.first_name,
            last_name: profileData.last_name,
            email: profileData.email,
            phone: profileData.phone || '',
          }}
          profileErrors={profileErrors}
          handleProfileChange={handleProfileChange}
          openResetPasswordModal={openResetPasswordModal}
          handleWysiwygChange={(name, value) => {
            setProfileData(prev => ({ ...prev, [name]: value }));
          }}
          profileEmail={profile?.email}
          logoFile={logoFile}
          setLogoFile={setLogoFile}
          logoPreviewUrl={logoPreviewUrl}
          setLogoPreviewUrl={setLogoPreviewUrl}
          clearError={(field: string) => setProfileErrors(prev => ({ ...prev, [field]: '' }))}
          fieldErrors={profileErrors}
        />
      )}

      {/* General Profile and Address Information */}
      {/* Show if not loading, and NOT isParent, and NOT isSponsor, and NOT isAthleticsDirector */}
      {!isLoadingProfile &&
        !isLoadingAddress &&
        !isParent &&
        !isSponsor &&
        !isAthleticsDirector && (
          <GeneralProfileForm
            profileData={profileData}
            profileErrors={profileErrors}
            addressData={addressData}
            addressErrors={addressErrors}
            handleProfileChange={handleProfileChange}
            handleAddressChange={handleAddressChange}
            setProfileData={setProfileData}
            setAddressData={setAddressData}
            openResetPasswordModal={openResetPasswordModal}
            profileEmail={profile?.email}
            profileType={userWithProfile?.profile_type as ProfileType}
          />
        )}

      {/* Parent Account Management (only for positive athletes) */}
      {isPositiveAthlete && (
        <Card elevation="sm" className="mt-10">
          <CardHeader title="PARENT ACCOUNT" titleIcon={faEye} className="mb-8" />

          <p className="text-gray-600 mb-6 text-sm">
            Parent accounts provide read-only visibility of your Positive Athlete account and
            messages.
          </p>

          {isLoadingParents ? (
            <div className="flex justify-center py-4">
              <p className="text-sm text-gray-500">Loading parent accounts...</p>
            </div>
          ) : activeParents.length > 0 || pendingInvites.length > 0 ? (
            <div className="space-y-6">
              {/* Active Parents */}
              {activeParents.length > 0 && (
                <div>
                  <div className="space-y-3">
                    {activeParents.map(parent => (
                      <div
                        key={parent.id}
                        className="flex items-center justify-between bg-[#F6F6F6] p-6 rounded-2xl"
                      >
                        <div className="flex items-center gap-4">
                          <div className="w-10 h-10 bg-[#D50032] rounded-full flex items-center justify-center text-white text-xs font-bold border border-gray-200">
                            {parent.first_name?.[0]?.toUpperCase() || ''}
                            {parent.last_name?.[0]?.toUpperCase() || ''}
                          </div>
                          <div>
                            <span className="font-bold text-gray-800 block text-base">
                              {parent.first_name} {parent.last_name}
                            </span>
                            <span className="text-gray-500 text-sm">{parent.email}</span>
                          </div>
                        </div>
                        <button
                          className="text-gray-500 hover:text-red-600 transition-colors"
                          onClick={() => {
                            console.log('Removing active parent with ID:', parent.id);
                            openRemoveParentModal(parent.id, parent.email);
                          }}
                          aria-label="Remove parent account"
                        >
                          <FontAwesomeIcon icon={faTrash} className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Pending Invites */}
              {pendingInvites.length > 0 && (
                <div>
                  <h3 className="text-sm font-semibold text-gray-700 mb-3">Pending Invitations</h3>
                  <div className="space-y-3">
                    {pendingInvites.map(invite => (
                      <div
                        key={invite.id}
                        className="flex items-center justify-between bg-[#F6F6F6] p-6 rounded-2xl"
                      >
                        <div className="flex items-center gap-4">
                          <div className="w-10 h-10 bg-gray-400 rounded-full flex items-center justify-center text-white text-xs font-bold border border-gray-200">
                            {invite.first_name?.[0]?.toUpperCase() || ''}
                            {invite.last_name?.[0]?.toUpperCase() || ''}
                          </div>
                          <div>
                            <span className="font-bold text-gray-800 block text-base">
                              {invite.first_name} {invite.last_name}
                            </span>
                            <span className="text-gray-500 text-sm">{invite.email}</span>
                            {invite.invitation_sent_at && (
                              <span className="text-gray-400 text-xs block">
                                Invited: {new Date(invite.invitation_sent_at).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <button
                            className="text-blue-500 hover:text-blue-700 transition-colors"
                            onClick={() => handleResendInvite(invite)}
                            aria-label="Resend invitation"
                            title="Resend invitation"
                          >
                            <FontAwesomeIcon icon={faEnvelope} className="h-4 w-4" />
                          </button>
                          <button
                            className="text-gray-500 hover:text-red-600 transition-colors"
                            onClick={() => {
                              console.log('Removing pending invite with ID:', invite.id);
                              openRemoveParentModal(invite.id, invite.email);
                            }}
                            aria-label="Cancel invitation"
                          >
                            <FontAwesomeIcon icon={faTrash} className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div>
              <Button
                color="blue"
                size="small"
                onClick={openAddParentModal}
                icon={faUserPlus}
                iconPosition="right"
                className="px-4 py-2 h-10 text-sm"
              >
                Add a Parent Account
              </Button>
            </div>
          )}
        </Card>
      )}

      {/* Delete Account Button */}
      <div className="flex justify-start mt-8 mb-6">
        <Button color="red" size="small" icon={faTrash} onClick={openDeleteAccountModal}>
          Delete Account
        </Button>
      </div>
    </div>
  );
}
