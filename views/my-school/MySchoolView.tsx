/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import React, { useCallback, useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { faArrowDownToBracket } from '@fortawesome/pro-regular-svg-icons';
import clsx from 'clsx';
import debounce from 'lodash/debounce';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import SearchInput from '@/components/shared/form/SearchInput';
import { useAuth } from '@/hooks/useAuth';
import { usePublicProfile } from '@/hooks/usePublicProfile';
import { MEDIA_QUERY_DESKTOP, useMediaQuery } from '@/hooks/utils/useMediaQuery';
import {
  FilterParams,
  SchoolNominee,
  SchoolNomineesResponse,
  schoolService,
} from '@/services/school.service';
import { fpoNotifications } from '@/views/dashboard/DashboardView';
import MySchoolCard from './MySchoolCard';
import MySchoolFilters from './MySchoolFilters';
import MySchoolFiltersPopover from './MySchoolFiltersPopover';
import MySchoolTable from './MySchoolTable';
import SkeletonLoader from './SkeletonLoader';

const LazyNotificationCarousel = dynamic(() => import('@/components/shared/NotificationCarousel'), {
  ssr: false,
});

const MySchoolView: React.FC = () => {
  const router = useRouter();
  const isDesktop = useMediaQuery(MEDIA_QUERY_DESKTOP);

  // State for filters and search
  const [filters, setFilters] = useState<FilterParams>({
    search: '',
    graduation_year: undefined,
    sport: '',
    gender: '',
    profile_type: '',
    page: 1,
    per_page: 10,
    sort_by: 'name',
    sort_direction: 'asc',
    include_winners: false,
  });
  const [currentSearchInput, setCurrentSearchInput] = useState(filters.search || '');
  const { user } = useAuth();
  const userIdAsString = user?.id ? String(user.id) : '';
  const { profile } = usePublicProfile(userIdAsString);

  // State for nominees data
  const [nomineesData, setNomineesData] = useState<SchoolNomineesResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isLinked = user?.profile_meta?.is_linked;

  // State for filter options
  const [gradYearOptions, setGradYearOptions] = useState<Array<{ label: string; value: string }>>([
    { label: 'All Grad Years', value: '' },
  ]);
  const [userTypeOptions, setUserTypeOptions] = useState<Array<{ label: string; value: string }>>([
    { label: 'All User Types', value: '' },
    { label: 'Positive Athletes', value: 'positive_athlete' },
    { label: 'Positive Coaches', value: 'positive_coach' },
  ]);
  const [sportOptions, setSportOptions] = useState<Array<{ label: string; value: string }>>([
    { label: 'All Sports', value: '' },
  ]);
  const [genderOptions, setGenderOptions] = useState<Array<{ label: string; value: string }>>([
    { label: 'All Genders', value: '' },
    { label: 'Male', value: 'Male' },
    { label: 'Female', value: 'Female' },
    { label: 'Other', value: 'Other' },
  ]);

  // Fetch nominees data
  const fetchNominees = async () => {
    try {
      setLoading(true);
      const currentPage = filters.page || 1; // Ensure page is a number
      const newData = await schoolService.getNominees({ ...filters, page: currentPage });

      setNomineesData(prevData => {
        if (currentPage > 1 && prevData) {
          return {
            data: [...prevData.data, ...newData.data],
            meta: newData.meta,
          };
        }
        return newData;
      });

      console.log('Nominees data (page):', currentPage, newData);

      if (currentPage === 1 && newData.data.length > 0) {
        const years = Array.from(
          new Set(
            newData.data.map(n => n.graduation_year).filter(year => year !== null) as number[]
          )
        ).sort((a, b) => b - a);

        setGradYearOptions([
          { label: 'All Grad Years', value: '' },
          ...years.map(year => ({ label: year.toString(), value: year.toString() })),
        ]);

        const sports = Array.from(
          new Set(newData.data.flatMap(n => n.sports.map(s => s.name)))
        ).sort();

        setSportOptions([
          { label: 'All Sports', value: '' },
          ...sports.map(sport => ({ label: sport, value: sport })),
        ]);
      }
    } catch (err) {
      console.error('Failed to fetch nominees:', err);
      setError('Failed to load school nominees. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch data when filters change
  useEffect(() => {
    fetchNominees();
  }, [filters]);

  // Sync currentSearchInput when filters.search changes externally
  useEffect(() => {
    setCurrentSearchInput(filters.search || '');
  }, [filters.search]);

  // Debounced search handler
  const debouncedSetSearch = useCallback(
    debounce((query: string) => {
      setFilters(prev => ({ ...prev, search: query, page: 1 }));
    }, 500), // 500ms debounce time
    []
  );

  // Handle search input changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentSearchInput(e.target.value);
    debouncedSetSearch(e.target.value);
  };

  // Handle filter changes
  const handleGradYearChange = (value: string) => {
    setFilters(prev => ({
      ...prev,
      graduation_year: value ? parseInt(value) : undefined,
      page: 1,
    }));
  };

  const handleUserTypeChange = (value: string) => {
    setFilters(prev => ({ ...prev, profile_type: value, page: 1 }));
  };

  const handleSportChange = (value: string) => {
    setFilters(prev => ({ ...prev, sport: value, page: 1 }));
  };

  const handleGenderChange = (value: string) => {
    setFilters(prev => ({ ...prev, gender: value, page: 1 }));
  };

  // Handle Load More
  const handleLoadMore = () => {
    setFilters(prev => ({ ...prev, page: (prev.page || 1) + 1 }));
  };

  // Handle sorting
  const handleSort = (field: string) => {
    setFilters(prev => ({
      ...prev,
      sort_by: field,
      sort_direction: prev.sort_by === field && prev.sort_direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  // Build filter props
  const tableFilterProps = {
    gradYearsOptions: gradYearOptions,
    gradYearsValue: filters.graduation_year?.toString() || '',
    onGradYearsChange: handleGradYearChange,

    userTypesOptions: userTypeOptions,
    userTypesValue: filters.profile_type || '',
    onUserTypesChange: handleUserTypeChange,

    teamsOptions: sportOptions,
    teamsValue: filters.sport || '',
    onTeamsChange: handleSportChange,

    genderOptions: genderOptions,
    genderValue: filters.gender || '',
    onGenderChange: handleGenderChange,

    includeWinners: Boolean(filters.include_winners),
    onIncludeWinnersChange: (value: boolean) => {
      console.log('Setting include_winners to:', value);
      setFilters(prev => ({ ...prev, include_winners: value, page: 1 }));
    },
  };

  // Download nominees data as CSV
  const handleDownload = () => {
    if (!nomineesData?.data.length) return;

    const escapeCsvCell = (value: string): string => {
      // If the value contains a comma, double quote, or newline,
      // it needs to be enclosed in double quotes.
      // Any double quote within the value should be escaped by doubling it.
      // Also check for carriage return \r along with newline \n.
      if (
        value.includes(',') ||
        value.includes('"') ||
        value.includes('\n') ||
        value.includes('\r')
      ) {
        const escapedValue = value.replace(/"/g, '""');
        return `"${escapedValue}"`;
      }
      return value;
    };

    // Create CSV content
    // Use the headers already updated by the user
    const currentHeaders = [
      'Name',
      'Year',
      'Sports',
      'Gender',
      'Nominator',
      'Date Nominated',
      'Profile Type',
    ];
    const headers = currentHeaders.map(escapeCsvCell);

    const rows = nomineesData.data.map(nominee => [
      escapeCsvCell(nominee.full_name),
      escapeCsvCell(nominee.graduation_year?.toString() || 'N/A'),
      escapeCsvCell(nominee.sports.map(s => s.name).join(', ')),
      escapeCsvCell(nominee.gender || 'N/A'),
      escapeCsvCell(nominee.nominations[0]?.nominator_name || 'Unknown'),
      escapeCsvCell(nominee.nominations[0]?.date_nominated || 'Unknown'),
      escapeCsvCell(nominee.profile_type || 'N/A'),
    ]);

    const csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `school-nominees-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!isLinked) {
    return (
      <section className="pa-container py-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          <h4 className="text-2xl font-medium">No School Linked</h4>
          <p>Contact Positive Athlete to claim or create a new school.</p>

          <Link
            href="/account"
            className="inline-block text-yellow-800 transition-colors mt-4 hover:text-yellow-900"
            aria-label="Go To Account"
          >
            Go To Account
          </Link>
        </div>
      </section>
    );
  }

  return (
    <section className="min-h-screen bg-surface-secondary py-4 lg:py-10">
      <div className="pa-container py-8 space-y-10">
        <div className="flex items-center flex-wrap gap-4 sm:justify-between">
          <h1 className="text-2xl lg:text-4xl font-bold">
            {profile?.school_name ? profile.school_name : 'My School'}
          </h1>

          <div className="w-full max-w-96">
            <SearchInput
              placeholder="Search users by name, year, or team..."
              value={currentSearchInput}
              onChange={handleSearchChange}
            />
          </div>
        </div>

        {/* Notification Carousel */}
        <LazyNotificationCarousel notifications={fpoNotifications} />

        <div className="space-y-4">
          <div className="flex items-center gap-4 flex-wrap w-full justify-between lg:justify-end">
            {isDesktop ? (
              <MySchoolFilters {...tableFilterProps} />
            ) : (
              <MySchoolFiltersPopover>
                <MySchoolFilters {...tableFilterProps} />
              </MySchoolFiltersPopover>
            )}

            <Button
              icon={faArrowDownToBracket}
              size="small"
              color="white"
              onClick={handleDownload}
              disabled={!nomineesData?.data.length}
            >
              Download
            </Button>
          </div>

          {/* Card */}
          <Card roundedCorners="3xl" noPadding>
            {loading ? (
              <SkeletonLoader />
            ) : error ? (
              <div className="text-center py-10 text-red-500">{error}</div>
            ) : !nomineesData?.data.length ? (
              <div className="text-center py-10">No nominees found</div>
            ) : (
              <div>
                {/* Conditionally render Table or Cards based on screen size */}
                {isDesktop ? (
                  <MySchoolTable nominees={nomineesData.data} />
                ) : (
                  <div className="space-y-4 p-4">
                    {nomineesData.data.map(nominee => (
                      <MySchoolCard key={nominee.id} nominee={nominee} />
                    ))}
                  </div>
                )}

                <div
                  className={clsx('mt-4 px-4 lg:px-6', {
                    'mb-4 lg:mb-8':
                      nomineesData && nomineesData.meta.current_page < nomineesData.meta.last_page,
                  })}
                >
                  {nomineesData && nomineesData.meta.current_page < nomineesData.meta.last_page && (
                    <Button onClick={handleLoadMore} disabled={loading}>
                      {loading && (filters.page || 1) > 1 ? 'Loading More...' : 'Load More'}
                    </Button>
                  )}
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>
    </section>
  );
};

export default MySchoolView;
