'use client';

import React, { useEffect, useMemo, useState } from 'react';
import {
  faChevronDown,
  faTrophy,
  faWreathLaurel,
  faXmark,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/react';
import Richtext from '@/components/shared/blocks/Richtext';
import { SchoolNominee } from '@/services/school.service';
import MySchoolVerifyRadio from './MySchoolVerfiyRadio';

interface Award {
  id: number | string;
  name: string;
  details?: string | null;
  verification_state?: string | null;
}

interface Scholarship {
  id: number | string;
  name: string;
  details?: string | null;
  verification_state?: string | null;
}

interface MySchoolDialogProps {
  nominee: SchoolNominee;
  handleClose: () => void;
}

interface VerificationStatuses {
  [key: string]: 'verified' | 'rejected' | null;
}

const MySchoolDialog: React.FC<MySchoolDialogProps> = ({ nominee, handleClose }) => {
  const [verificationStatuses, setVerificationStatuses] = useState<VerificationStatuses>({});

  const awardsArray: Award[] = useMemo(
    () =>
      nominee.awards
        ? Array.isArray(nominee.awards)
          ? (nominee.awards as Award[])
          : (Object.values(nominee.awards) as Award[])
        : [],
    [nominee.awards]
  );

  const scholarshipsArray: Scholarship[] = useMemo(
    () => (nominee.scholarships as Scholarship[]) || [],
    [nominee.scholarships]
  );

  useEffect(() => {
    const initialStatuses: VerificationStatuses = {};
    awardsArray.forEach(award => {
      const key = `award-${award.id}`;
      if (award.verification_state === 'verified' || award.verification_state === 'rejected') {
        initialStatuses[key] = award.verification_state;
      } else {
        initialStatuses[key] = null; // Default to null (unselected) for other states (pending, unverified, etc.)
      }
    });
    scholarshipsArray.forEach(scholarship => {
      const key = `scholarship-${scholarship.id}`;
      if (
        scholarship.verification_state === 'verified' ||
        scholarship.verification_state === 'rejected'
      ) {
        initialStatuses[key] = scholarship.verification_state;
      } else {
        initialStatuses[key] = null; // Default to null (unselected)
      }
    });
    setVerificationStatuses(initialStatuses);
  }, [awardsArray, scholarshipsArray]);

  const handleIndividualStatusChange = (
    itemType: 'award' | 'scholarship',
    itemId: number | string,
    newStatus: 'verified' | 'rejected' | null
  ) => {
    const key = `${itemType}-${itemId}`;
    setVerificationStatuses(prevStatuses => ({
      ...prevStatuses,
      [key]: newStatus,
    }));
    console.log(
      `Dialog: Status changed for ${itemType} ${itemId}: ${newStatus === null ? 'unverified' : newStatus}`
    );
  };

  return (
    <div className="relative bg-white p-4 lg:p-6">
      <button
        onClick={handleClose}
        className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 transition-colors z-10"
        aria-label="Close dialog"
      >
        <FontAwesomeIcon icon={faXmark} size="lg" />
      </button>

      <h2 className="text-2xl font-semibold mb-6 text-gray-800 border-b pb-3">
        Verify Awards & Scholarships: {nominee.full_name}
      </h2>

      {awardsArray.length > 0 && (
        <div className="mb-6">
          <h3 className="text-xl flex items-center gap-x-2 font-medium mb-3 text-gray-700">
            <FontAwesomeIcon icon={faTrophy} className="text-gray-400" aria-hidden="true" />
            Awards
          </h3>
          <div className="space-y-6">
            {awardsArray.map(award => {
              const currentStatusKey = `award-${award.id}`;
              const isUnverified = verificationStatuses[currentStatusKey] === null;
              return (
                <div key={currentStatusKey} className="p-4 border rounded-md bg-gray-50 shadow-sm">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-lg font-semibold text-primary">{award.name}</h4>
                    {isUnverified && (
                      <span className="text-sm text-gray-500 font-semibold ml-2">(Unverified)</span>
                    )}
                  </div>

                  {award.details && (
                    <Disclosure as="div" className="mt-2 mb-3">
                      {({ open }) => (
                        <>
                          <DisclosureButton className="flex items-center gap-x-4 w-full text-sm font-medium text-left text-text-primary hover:text-brand-red focus:outline-none focus-visible:ring focus-visible:ring-gray-300 focus-visible:ring-opacity-75 py-1">
                            <span>{open ? 'See Less' : 'View Details'}</span>
                            <FontAwesomeIcon
                              icon={faChevronDown}
                              className={`w-4 h-4 transform transition-transform duration-150 ${open ? '-rotate-180' : 'rotate-0'}`}
                              aria-hidden="true"
                            />
                          </DisclosureButton>
                          <DisclosurePanel className="mt-2">
                            <Richtext
                              content={award.details!}
                              className="text-text-secondary prose-sm"
                            />
                          </DisclosurePanel>
                        </>
                      )}
                    </Disclosure>
                  )}

                  <MySchoolVerifyRadio
                    itemId={award.id}
                    itemName={award.name}
                    itemType="award"
                    currentStatusInDialog={verificationStatuses[currentStatusKey] ?? null}
                    onStatusChange={handleIndividualStatusChange}
                  />
                </div>
              );
            })}
          </div>
        </div>
      )}

      {scholarshipsArray.length > 0 && (
        <div className="mb-6">
          <h3 className="text-xl font-medium mb-3 flex items-center gap-x-2 text-gray-700">
            <FontAwesomeIcon icon={faWreathLaurel} className="text-gray-400" aria-hidden="true" />
            Scholarships
          </h3>
          <div className="space-y-6">
            {scholarshipsArray.map(scholarship => {
              const currentStatusKey = `scholarship-${scholarship.id}`;
              const isUnverified = verificationStatuses[currentStatusKey] === null;
              return (
                <div key={currentStatusKey} className="p-4 border rounded-md bg-gray-50 shadow-sm">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-lg font-semibold text-primary">{scholarship.name}</h4>
                    {isUnverified && (
                      <span className="text-sm text-gray-500 font-semibold ml-2">(Unverified)</span>
                    )}
                  </div>

                  {scholarship.details && (
                    <Disclosure as="div" className="mt-2 mb-3">
                      {({ open }) => (
                        <>
                          <DisclosureButton className="flex items-center gap-x-4 w-full text-sm font-medium text-left text-text-primary hover:text-brand-red focus:outline-none focus-visible:ring focus-visible:ring-gray-300 focus-visible:ring-opacity-75 py-1">
                            <span>{open ? 'See Less' : 'View Details'}</span>
                            <FontAwesomeIcon
                              icon={faChevronDown}
                              className={`w-4 h-4 transform transition-transform duration-150 ${open ? '-rotate-180' : 'rotate-0'}`}
                              aria-hidden="true"
                            />
                          </DisclosureButton>
                          <DisclosurePanel className="mt-2">
                            <Richtext
                              content={scholarship.details!}
                              className="text-text-secondary prose-sm"
                            />
                          </DisclosurePanel>
                        </>
                      )}
                    </Disclosure>
                  )}

                  <MySchoolVerifyRadio
                    itemId={scholarship.id}
                    itemName={scholarship.name}
                    itemType="scholarship"
                    currentStatusInDialog={verificationStatuses[currentStatusKey] ?? null}
                    onStatusChange={handleIndividualStatusChange}
                  />
                </div>
              );
            })}
          </div>
        </div>
      )}

      {awardsArray.length === 0 && scholarshipsArray.length === 0 && (
        <p className="text-gray-600 text-center py-4">
          No awards or scholarships information available for verification.
        </p>
      )}

      <div className="mt-8 pt-4 border-t flex justify-end space-x-3">
        <button
          onClick={() => {
            console.log('Saving all statuses:', verificationStatuses);
            handleClose();
          }}
          type="button"
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light transition-colors"
        >
          Save & Close
        </button>
        <button
          onClick={handleClose}
          type="button"
          className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 transition-colors"
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default MySchoolDialog;
