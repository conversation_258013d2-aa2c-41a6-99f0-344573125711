'use client';

import React from 'react';
import Link from 'next/link';
import { faArrowRight, faArrowUpRightFromSquare } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { SportsDetails } from '@/components/profile/SportsDetails';
import Avatar from '@/components/shared/Avatar';
import Tag from '@/components/shared/Tag';
import { SchoolNominee, SchoolNomineeSport } from '@/services/school.service';
import { SportEntry } from '@/types/profile';

interface MySchoolCardProps {
  nominee: SchoolNominee;
}

const MySchoolCard: React.FC<MySchoolCardProps> = ({ nominee }) => {
  return (
    <div className="bg-white py-4 border-b border-gray-200 mb-2">
      <div className="flex gap-4 justify-between mb-4">
        <div className="flex items-center gap-x-2 gap-y-4">
          <Avatar
            src={nominee.profile_image || undefined}
            alt={nominee.full_name}
            firstName={nominee.first_name}
            lastName={nominee.last_name}
            size="md"
          />

          <div className="block">
            <h3 className="text-lg font-semibold text-gray-900">{nominee.full_name}</h3>
            <p className="text-sm text-gray-500">Grad Year: {nominee.graduation_year || 'N/A'}</p>
          </div>
        </div>

        <Link
          href={`/pa/${nominee.id}`}
          className="self-start text-text-secondary hover:text-text-primary"
          aria-label="Go to user profile"
          title="Go to user profile"
        >
          <span className="sr-only">Go to user profile</span>
          <FontAwesomeIcon icon={faArrowRight} aria-hidden="true" />
        </Link>
      </div>

      <div className="flex items-center flex-grow">
        <div className="ml-2 block space-y-2">
          <div className="text-sm text-gray-500 flex items-center gap-x-1 my-1">
            <span className="mr-1">Sport(s):</span>
            {nominee.sports && nominee.sports.length > 0 ? (
              <div className="flex items-center gap-x-2">
                <SportsDetails
                  sports={[
                    {
                      id: String(nominee.sports[0].id),
                      sport: nominee.sports[0].slug,
                      type: 'predefined',
                      customName: nominee.sports[0].name,
                    } as SportEntry,
                  ]}
                />
                {nominee.sports.length > 1 && <Tag label={`+${nominee.sports.length - 1}`} />}
              </div>
            ) : (
              <span>N/A</span>
            )}
          </div>
          <p className="text-sm text-gray-500 capitalize">Gender: {nominee.gender || 'N/A'}</p>
          <p className="text-sm text-gray-500">
            Nominator:{' '}
            {nominee.nominations && nominee.nominations.length > 0 ? (
              <span className="flex items-center gap-2">
                <span>{nominee.nominations[0]?.nominator_name || 'N/A'}</span>
                {nominee.nominations.length > 1 && (
                  <span className="p-1 bg-gray-100 rounded text-xs">
                    +{nominee.nominations.length - 1}
                  </span>
                )}
              </span>
            ) : (
              'N/A'
            )}
          </p>
          <p className="text-sm text-gray-500">
            Date Nominated: {nominee.created_at ? nominee.created_at : 'N/A'}
          </p>
          <p className="text-sm text-gray-500 capitalize">
            User Type: {nominee.profile_type?.replace('_', ' ') || 'N/A'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default MySchoolCard;
