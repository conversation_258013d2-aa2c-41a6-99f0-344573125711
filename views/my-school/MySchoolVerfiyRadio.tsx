'use client';

import React, { useEffect, useState } from 'react';
import { Field, Label, Radio, RadioGroup } from '@headlessui/react';
import clsx from 'clsx';

const verificationOptions = [
  { name: 'Verified', value: 'verified' as const },
  { name: 'Rejected', value: 'rejected' as const },
];

interface MySchoolVerifyRadioProps {
  itemId: string | number;
  itemName: string; // For aria-label, not displayed directly by this component
  itemType: 'award' | 'scholarship';
  // initialStatus comes from the parent dialog's central state for this item
  currentStatusInDialog: 'verified' | 'rejected' | null;
  onStatusChange: (
    itemType: 'award' | 'scholarship',
    itemId: string | number,
    newStatus: 'verified' | 'rejected' | null
  ) => void;
  disabled?: boolean;
}

const MySchoolVerifyRadio: React.FC<MySchoolVerifyRadioProps> = ({
  itemId,
  itemName,
  itemType,
  currentStatusInDialog, // Renamed from initialStatus for clarity
  onStatusChange,
  disabled = false,
}) => {
  // This component directly uses the status from the parent dialog.
  // No internal selectedValue state is needed here if the parent (MySchoolDialog)
  // is the source of truth for verificationStatuses and passes the specific item's status down.

  const handleChange = (newStatus: 'verified' | 'rejected' | null) => {
    onStatusChange(itemType, itemId, newStatus);
  };

  const isUnverified = currentStatusInDialog === null;

  return (
    <div className="mt-1">
      {' '}
      {/* Removed outer div and adjusted margin if needed */}
      {/* The (Unverified) text will be controlled by MySchoolDialog based on its central state */}
      <RadioGroup
        value={currentStatusInDialog ?? null} // Ensures controlled component, value comes from parent state
        onChange={handleChange}
        aria-label={`Verification status for ${itemName}`}
      >
        <Label className="sr-only">Status for {itemName}</Label>
        <div className="flex items-center space-x-4">
          {verificationOptions.map(option => (
            <Field key={option.value} className="flex items-center" disabled={disabled}>
              <Radio
                value={option.value}
                className={clsx(
                  'relative flex cursor-pointer focus:outline-none w-full transition-colors duration-200',
                  'bg-[#F7F7F7] hover:bg-[#F0F0F0] rounded-lg p-2'
                )}
              >
                {/* <span className="invisible size-2 rounded-full bg-white group-data-checked:visible" /> */}
                {({ checked }) => (
                  <div className="flex items-center w-full">
                    <div className="flex items-center h-4">
                      <span
                        className={clsx(
                          'h-4 w-4 rounded-full border flex items-center justify-center transition-colors duration-200',
                          'focus:outline-none',
                          checked ? 'border-[#D50032]' : 'border-[#A5ADB0]'
                        )}
                      >
                        {checked && (
                          <span className="h-2 w-2 rounded-full bg-[#D50032] transition-all duration-200" />
                        )}
                      </span>
                    </div>
                  </div>
                )}
              </Radio>
              <Label className="ml-1 text-sm text-gray-700 cursor-pointer">{option.name}</Label>
            </Field>
          ))}
          {!isUnverified && (
            <button
              type="button"
              onClick={() => handleChange(null)} // Calls handleChange with null to clear
              className="text-xs text-gray-500 hover:text-gray-700 underline"
            >
              Clear
            </button>
          )}
        </div>
      </RadioGroup>
    </div>
  );
};

export default MySchoolVerifyRadio;
