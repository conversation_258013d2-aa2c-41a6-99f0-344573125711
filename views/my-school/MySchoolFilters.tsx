import React from 'react';
import {
  faBasketball,
  faCalendar,
  faFilter,
  faMedal,
  faUser,
  faVenusMars,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Toggle from '@/components/shared/form/Toggle';
import TableFilterSelect from '@/views/my-school/TableFilterSelect';

interface FilterOption {
  label: string;
  value: string;
}

interface MySchoolFiltersProps {
  gradYearsOptions: FilterOption[];
  gradYearsValue: string;
  onGradYearsChange: (value: string) => void;

  userTypesOptions: FilterOption[];
  userTypesValue: string;
  onUserTypesChange: (value: string) => void;

  teamsOptions: FilterOption[];
  teamsValue: string;
  onTeamsChange: (value: string) => void;

  genderOptions: FilterOption[];
  genderValue: string;
  onGenderChange: (value: string) => void;

  includeWinners: boolean;
  onIncludeWinnersChange: (value: boolean) => void;
}

const MySchoolFilters: React.FC<MySchoolFiltersProps> = ({
  gradYearsOptions,
  gradYearsValue,
  onGradYearsChange,
  userTypesOptions,
  userTypesValue,
  onUserTypesChange,
  teamsOptions,
  teamsValue,
  onTeamsChange,
  genderOptions,
  genderValue,
  onGenderChange,
  includeWinners,
  onIncludeWinnersChange,
}) => {
  return (
    <React.Fragment>
      <TableFilterSelect
        label="All Grad Years"
        icon={faCalendar}
        options={gradYearsOptions}
        value={gradYearsValue}
        onChange={onGradYearsChange}
      />

      <TableFilterSelect
        label="All User Types"
        // icon={faUser}
        options={userTypesOptions}
        value={userTypesValue}
        onChange={onUserTypesChange}
      />

      <TableFilterSelect
        label="All Teams"
        // icon={faBasketball}
        options={teamsOptions}
        value={teamsValue}
        onChange={onTeamsChange}
      />

      <TableFilterSelect
        label="All Genders"
        // icon={faVenusMars}
        options={genderOptions}
        value={genderValue}
        onChange={onGenderChange}
      />

      {/* <div className="flex items-center gap-2">
        <Toggle checked={includeWinners} onChange={onIncludeWinnersChange} />
        <label className="text-sm text-gray-700 flex items-center gap-1">
          <FontAwesomeIcon icon={faMedal} className="fa-fw" />
          Winners Only
        </label>
      </div> */}
    </React.Fragment>
  );
};

export default MySchoolFilters;
