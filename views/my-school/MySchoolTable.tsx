'use client';

import React from 'react';
import Link from 'next/link';
import { faArrowRight, faArrowUpRightFromSquare } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { SportsDetails } from '@/components/profile/SportsDetails';
import Avatar from '@/components/shared/Avatar';
import Tag from '@/components/shared/Tag';
import { SchoolNominee, SchoolNomineeSport } from '@/services/school.service';
import { SportEntry } from '@/types/profile';

interface MySchoolTableProps {
  nominees: SchoolNominee[];
}

const MySchoolTable: React.FC<MySchoolTableProps> = ({ nominees }) => {
  if (!nominees || nominees.length === 0) {
    return <div className="text-center py-10">No nominees found.</div>;
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead>
          <tr>
            <th
              scope="col"
              className="pl-6 pr-4 pb-4 pt-8 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Athlete Name
            </th>
            <th
              scope="col"
              className="px-4 pb-4 pt-8 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Grad Year
            </th>
            <th
              scope="col"
              className="px-4 pb-4 pt-8 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Sport(s)
            </th>
            <th
              scope="col"
              className="px-4 pb-4 pt-8 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Gender
            </th>
            <th
              scope="col"
              className="px-4 pb-4 pt-8 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Nominator
            </th>
            <th
              scope="col"
              className="px-4 pb-4 pt-8 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Date Nominated
            </th>
            <th
              scope="col"
              className="px-4 pb-4 pt-8 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              User Type
            </th>
            <th scope="col" className="relative px-4 py-3">
              <span className="sr-only">View</span>
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {nominees.map(nominee => {
            return (
              <tr key={nominee.id}>
                <td className="pl-6 pr-4 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <Avatar
                        src={nominee.profile_image || undefined}
                        alt={nominee.full_name}
                        firstName={nominee.first_name}
                        lastName={nominee.last_name}
                        size="sm"
                      />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{nominee.full_name}</div>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{nominee.graduation_year || 'N/A'}</div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="flex items-center gap-x-2">
                    {nominee.sports && nominee.sports.length > 0 ? (
                      <>
                        <SportsDetails
                          sports={[
                            {
                              id: String(nominee.sports[0].id),
                              sport: nominee.sports[0].slug,
                              type: 'predefined',
                              customName: nominee.sports[0].name,
                            } as SportEntry,
                          ]}
                        />
                        {nominee.sports.length > 1 && (
                          <Tag label={`+${nominee.sports.length - 1}`} />
                        )}
                      </>
                    ) : (
                      <div className="text-sm text-gray-900">N/A</div>
                    )}
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 capitalize">{nominee.gender || 'N/A'}</div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {nominee.nominations && nominee.nominations.length > 0 ? (
                      <div className="flex items-center gap-2">
                        <span>{nominee.nominations[0]?.nominator_name || 'N/A'}</span>
                        {nominee.nominations.length > 1 && (
                          <span className="p-1 bg-gray-100 rounded text-xs">
                            +{nominee.nominations.length - 1}
                          </span>
                        )}
                      </div>
                    ) : (
                      'N/A'
                    )}
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {nominee.created_at ? nominee.created_at : 'N/A'}
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 capitalize">
                    {nominee.profile_type?.replace('_', ' ') || 'N/A'}
                  </div>
                </td>
                <td className="pl-4 pr-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <Link
                    href={`/pa/${nominee.id}`}
                    className="text-text-secondary hover:text-text-primary"
                    aria-label="Go to user profile"
                    title="Go to user profile"
                  >
                    <span className="sr-only">Go to user profile</span>
                    <FontAwesomeIcon icon={faArrowRight} aria-hidden="true" />
                  </Link>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default MySchoolTable;
