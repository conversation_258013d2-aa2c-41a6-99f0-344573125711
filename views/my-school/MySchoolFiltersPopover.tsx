import React from 'react';
import { faBarsFilter } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/react';

interface MySchoolFiltersPopoverProps {
  children: React.ReactNode;
}

const MySchoolFiltersPopover: React.FC<MySchoolFiltersPopoverProps> = ({ children }) => {
  return (
    <Popover className="relative">
      <PopoverButton className="flex items-center gap-x-2 py-2.5 rounded-lg text-sm font-semibold px-6 border-0 bg-white text-gray-900">
        Filters
        <FontAwesomeIcon icon={faBarsFilter} className="size-3" aria-hidden="true" />
      </PopoverButton>
      <PopoverPanel
        transition
        anchor="bottom start"
        className="bg-surface-secondary ring-1 mt-2 ring-gray-200 shadow-md rounded-lg flex flex-col py-4 px-2 gap-y-4 min-w-72 transition duration-200 ease-in-out [--anchor-gap:--spacing(4)] data-[closed]:-translate-y-1 data-[closed]:opacity-0"
        unmount={false}
      >
        {children}
      </PopoverPanel>
    </Popover>
  );
};

export default MySchoolFiltersPopover;
