const Skeleton = ({ className }: { className: string }) => (
  <div className={`animate-pulse bg-gray-200 rounded ${className}`} />
);

const SkeletonLoader = () => {
  return (
    <div className="w-full">
      {/* Desktop Table Layout - Hidden on mobile */}
      <div className="hidden md:block">
        {/* Table Header */}
        <div className="grid grid-cols-5 gap-4 mb-4 p-4 border-b">
          {Array.from({ length: 5 }).map((_, index) => (
            <Skeleton key={`header-${index}`} className="h-4 w-full" />
          ))}
        </div>

        {/* Table Rows */}
        {Array.from({ length: 6 }).map((_, rowIndex) => (
          <div
            key={`row-${rowIndex}`}
            className="grid grid-cols-5 gap-4 p-4 border-b border-gray-100"
          >
            {Array.from({ length: 5 }).map((_, colIndex) => (
              <Skeleton key={`cell-${rowIndex}-${colIndex}`} className="h-6 w-full" />
            ))}
          </div>
        ))}
      </div>

      {/* Mobile Card Layout - Hidden on desktop */}
      <div className="md:hidden grid grid-cols-1">
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={`card-${index}`} className="space-y-2">
            <Skeleton className="aspect-square w-full" />

            <Skeleton className="aspect-square w-full" />

            <Skeleton className="aspect-square w-full" />
          </div>
        ))}
      </div>
    </div>
  );
};

export default SkeletonLoader;
