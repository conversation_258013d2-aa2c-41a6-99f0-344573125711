'use client';

import { redirect } from 'next/navigation';
import { ADProfile } from '@/components/profile/athletics-director/ADProfi<PERSON>';
import { CollegeAthleteProfile } from '@/components/profile/college-athlete/CollegeAthleteProfile';
import { ParentProfile } from '@/components/profile/parent/ParentProfile';
import { PositiveAthleteProfile } from '@/components/profile/positive-athlete/PositiveAthleteProfile';
import { PositiveCoachProfile } from '@/components/profile/positive-coach/PositiveCoachProfile';
import { ProfessionalProfile } from '@/components/profile/professional/ProfessionalProfile';
import { ProfileType, ProfileTypes, useAuthStore } from '@/stores/auth.store';

export default function ProfilePage() {
  const { user, isAuthenticated, profileType } = useAuthStore();

  if (!isAuthenticated || !user) {
    redirect('/login');
  }

  // Type-safe switch statement
  switch (profileType) {
    case ProfileTypes.PARENT:
      return <ParentProfile />;
    case ProfileTypes.COLLEGE_ATHLETE:
      return <CollegeAthleteProfile />;
    case ProfileTypes.PROFESSIONAL:
      return <ProfessionalProfile />;
    case ProfileTypes.ATHLETICS_DIRECTOR:
      return <ADProfile />;
    case ProfileTypes.POSITIVE_COACH:
    case ProfileTypes.TEAM_COACH:
    case ProfileTypes.COACH:
      return <PositiveCoachProfile />;
    case ProfileTypes.POSITIVE_ATHLETE:
      return <PositiveAthleteProfile />;
    case ProfileTypes.SPONSOR:
    case ProfileTypes.UTILITY:
    case ProfileTypes.ADMIN:
    case ProfileTypes.TEAM_STUDENT:
    case ProfileTypes.ATHLETE:
      // Default to athlete profile for now
      // TODO: Create specific profile components for each type
      return <PositiveAthleteProfile />;
    default:
      return <PositiveAthleteProfile />;
  }
}
