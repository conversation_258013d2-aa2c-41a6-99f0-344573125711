'use client';

import React, { useState } from 'react';
import { useParams } from 'next/navigation';
import { TabGroup, TabPanel, TabPanels } from '@headlessui/react';
import { ConnectMessageForm } from '@/components/network/ConnectMessageForm';
import { BadgesAndCertifications } from '@/components/profile/sections/BadgesAndCertifications';
import { ADAbout } from '@/components/public-profile/athletics-director/<PERSON><PERSON>bo<PERSON>';
import { PositiveAthleteAbout } from '@/components/public-profile/positive-athlete/PositiveAthleteAbout';
import { PositiveCoachAbout } from '@/components/public-profile/positive-coach/PositiveCoachAbout';
import { PublicDetails } from '@/components/public-profile/sections/PublicDetails';
import { PublicNominationsAndEndorsements } from '@/components/public-profile/sections/PublicNominationsAndEndorsements';
import { PublicProfileHeader } from '@/components/public-profile/sections/PublicProfileHeader';
import { PublicSports } from '@/components/public-profile/sections/PublicSports';
import { AboutProps } from '@/components/public-profile/types';
import { useAuth } from '@/hooks/useAuth';
import { useNetworking } from '@/hooks/useNetworking';
import { usePublicProfile } from '@/hooks/usePublicProfile';
import { NetworkingService, UserBasicData } from '@/services/networking.service';
import type { ProfilePhoto } from '@/services/positive-athlete-profile.service';
import { ProfileTypes } from '@/stores/auth.store';
import { useModalStore } from '@/stores/modal.store';
import { getTabListItems } from '@/utils/profile-utils';

// Simple Skeleton component since we don't have access to the UI library
const Skeleton = ({ className }: { className: string }) => (
  <div className={`animate-pulse bg-gray-200 rounded ${className}`} />
);

export default function PublicProfileView() {
  const { isLoggedIn, profileType: userProfileType } = useAuth();
  const [activeTab, setActiveTab] = useState('about');
  const params = useParams();
  const id = params?.id as string;
  const { open } = useModalStore();
  const { useCanConnect } = useNetworking();

  const {
    profile,
    details,
    sports,
    story,
    involvements,
    avatar,
    photos,
    careerInterests,
    workExperiences,
    isLoading,
    profileType,
    hasError,
  } = usePublicProfile(id);

  // Use the canConnect check from our networking hook
  const { data: canConnect = false, isLoading: isCheckingConnection } = useCanConnect(Number(id));

  // Don't try to get tab list items until we have a valid profile type
  const profileTabsList = profileType ? getTabListItems(profileType) : [];

  // Convert activeTab string to index for Headless UI
  const getTabIndex = (tabId: string) => {
    const index = profileTabsList.findIndex(tab => tab.id === tabId);
    return index;
  };

  // Handle tab change from Headless UI (index) to our component (id)
  const handleTabChange = (index: number) => {
    if (profileTabsList[index]) {
      setActiveTab(profileTabsList[index].id);
    }
  };

  // Determine if message button should be shown
  const shouldShowMessageButton = isLoggedIn && canConnect && !isCheckingConnection;

  // Handle opening the message modal
  const handleOpenMessageModal = () => {
    if (!profile) return;

    const recipient: UserBasicData = {
      id: Number(id),
      firstName: profile.first_name,
      lastName: profile.last_name,
      profileImageUrl: avatar?.url || null,
    };

    open(<ConnectMessageForm recipient={recipient} />, 'md');
  };

  const selectedIndex = getTabIndex(activeTab);

  // Extract graduation year from details and convert to string if needed
  const graduationYear = details?.graduation_year ? String(details.graduation_year) : undefined;

  // Map public photos to ProfilePhoto type
  const mappedPhotos: ProfilePhoto[] =
    photos?.map(photo => ({
      id: String(photo.id), // Convert ID to string
      url: photo.url,
      thumbnail_url: photo.url, // Use the same URL for thumbnail if not available
      width: photo.width || 0,
      height: photo.height || 0,
      order: 0, // Default order
      focal_point: { x: 0.5, y: 0.5 }, // Default focal point
    })) || [];

  // Check if only details and sports data are available
  const hasOnlyDetailsAndSports =
    details &&
    sports &&
    sports.length > 0 &&
    (!story || !story.content || story.content === '') &&
    (!involvements || involvements.length === 0) &&
    (!workExperiences || workExperiences.length === 0);

  const renderAboutLayout = () => {
    if (hasOnlyDetailsAndSports) {
      // If only details and sports data are available, show them in a side-by-side layout
      return (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <PublicDetails details={details} profile={profile} careerInterests={careerInterests} />
          <PublicSports sports={sports} />
        </div>
      );
    }

    // Create a properly typed props object for our about components
    const profileProps: AboutProps = {
      details,
      profile,
      sports,
      story,
      involvements,
      workExperiences,
      careerInterests,
    };

    switch (profileType) {
      case ProfileTypes.POSITIVE_ATHLETE:
        return <PositiveAthleteAbout {...profileProps} />;
      case ProfileTypes.POSITIVE_COACH:
        return <PositiveCoachAbout {...profileProps} />;
      case ProfileTypes.ATHLETICS_DIRECTOR:
        return <ADAbout {...profileProps} />;
      default:
        return <PositiveAthleteAbout {...profileProps} />;
    }
  };

  if (isLoading || !profileType) {
    return (
      <div className="pa-container py-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-48 w-full" />
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="pa-container py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p className="font-medium">Error loading profile</p>
          <p>There was an error loading this profile. Please try again later.</p>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="pa-container py-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          <p className="font-medium">Profile not found</p>
          <p>The requested profile could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-surface-secondary">
      <TabGroup selectedIndex={selectedIndex} onChange={handleTabChange}>
        {/* Use PublicProfileHeader component */}
        <PublicProfileHeader
          profileId={Number(id)}
          firstName={profile.first_name}
          lastName={profile.last_name}
          avatarUrl={avatar?.url}
          photos={mappedPhotos}
          graduationYear={graduationYear}
          isLoading={false}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          profileType={profileType}
          showMessageButton={shouldShowMessageButton}
          onMessageClick={handleOpenMessageModal}
        />

        <TabPanels>
          {/* About Tab Panel */}
          <TabPanel>
            <div className="pa-container pb-20 bg-surface-secondary">{renderAboutLayout()}</div>
          </TabPanel>

          {/* Badges & Certifications Tab Panel */}
          <TabPanel>
            <div className="pa-container pb-20 bg-surface-secondary">
              <BadgesAndCertifications userId={Number(id)} isPublicView={true} />
            </div>
          </TabPanel>

          {/* Nominations & Endorsements Tab Panel */}
          <TabPanel>
            <div className="pa-container pb-20 bg-surface-secondary">
              <PublicNominationsAndEndorsements
                userId={Number(id)}
                displayEndorsementButton={true}
              />
            </div>
          </TabPanel>
        </TabPanels>
      </TabGroup>
    </div>
  );
}
