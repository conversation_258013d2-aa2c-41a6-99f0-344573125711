import React from 'react';
import { faArrowRight, faUserCircle } from '@fortawesome/pro-regular-svg-icons';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';

export default function PrivateProfileMessage() {
  return (
    <div className="bg-surface-secondary flex items-center justify-center lg:min-h-screen">
      <div className="pa-container py-16">
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader
              title="This profile is private."
              titleIcon={faUserCircle}
              className="mb-8"
            />

            <p className="text-gray-600 mb-8 text-base">
              The owner of this account hasn&apos;t activated a public page, or may have chosen to
              keep their details private. Feel free to explore other athletes and stories on
              Positive Athlete.
            </p>

            <Button href="/" icon={faArrowRight}>
              Back
            </Button>
          </Card>
        </div>
      </div>
    </div>
  );
}
