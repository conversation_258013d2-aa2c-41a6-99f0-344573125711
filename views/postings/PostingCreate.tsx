'use client';

import React from 'react';
import type { PostingFormData } from '@/stores/postingStore';
import { ValidationErrors } from '@/types/validation';
import PostingFormTemplate from './PostingFormTemplate';

interface PostingCreateProps {
  posting: any;
  onCancel: () => void;
  onSave: (formData: PostingFormData, shouldList?: boolean) => void;
  isLoadingOrganization?: boolean;
  errors?: ValidationErrors;
  onClearErrors?: () => void;
}

const PostingCreate: React.FC<PostingCreateProps> = ({
  posting,
  onCancel,
  onSave,
  isLoadingOrganization,
  errors,
  onClearErrors,
}) => {
  return (
    <PostingFormTemplate
      posting={posting}
      onCancel={onCancel}
      onSave={onSave}
      buttonText={{
        submit: 'Create and List Posting',
        saveOnly: 'Create Without Listing',
      }}
      isLoadingOrganization={isLoadingOrganization}
      isCreate={true}
      errors={errors}
      onClearErrors={onClearErrors}
    />
  );
};

export default PostingCreate;
