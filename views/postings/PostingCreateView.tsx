'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSponsorOpportunity } from '@/hooks/useSponsorOpportunity';
import { useSponsorOrganization } from '@/hooks/useSponsorOrganization';
import { OpportunityStatus } from '@/services/sponsorOpportunity.service';
import { usePostingStore } from '@/stores/postingStore';
import type { PostingFormData } from '@/stores/postingStore';
import { ValidationErrors } from '@/types/validation';
import PostingCreate from './PostingCreate';

// Define an extended version of PostingFormData that includes the additional fields we need
interface ExtendedPostingFormData extends PostingFormData {
  industryIds?: number[];
  organizationId?: number;
  visibleStartDate?: string | null;
  visibleEndDate?: string | null;
  organizationLogo?: File | string;
  organizationName?: string;
  organizationWebsite?: string;
  organizationAbout?: string;
  // Add visibility settings
  preferredGraduationYearStart?: number | null;
  preferredGraduationYearEnd?: number | null;
  preferred_states?: string[];
  interests: number[];
}

export default function PostingCreateView() {
  const router = useRouter();
  const { clearEditing } = usePostingStore();
  const [organizationId, setOrganizationId] = useState<number | null>(null);
  const [errors, setErrors] = useState<ValidationErrors>({ _general: [] });

  // Get the sponsor's organization
  const { getOrganization } = useSponsorOrganization();

  // Call the getOrganization query
  const { data: organizationData, isLoading: isLoadingOrganization, isError } = getOrganization();

  // Set the organization ID when the data is available
  useEffect(() => {
    if (organizationData && organizationData.id) {
      console.log('Organization data fetched:', organizationData);
      setOrganizationId(organizationData.id);
    }
  }, [organizationData]);

  // Log errors if any
  useEffect(() => {
    if (isError) {
      console.error('Error fetching organization data');
      setErrors({ _general: ['Unable to fetch organization data. Please try again later.'] });
    }
  }, [isError]);

  const { createOpportunity } = useSponsorOpportunity({
    onSuccess: (data, context) => {
      if (context === 'create') {
        // Clear any previous errors
        setErrors({ _general: [] });
        // Navigate to the new opportunity page after creation
        router.push('/postings');
      }
    },
    onError: (error: any) => {
      console.error('Error creating posting:', error);

      // Initialize the structured errors object
      const validationErrors: ValidationErrors = { _general: [] };

      // Handle different error formats
      if (error?.response?.data?.message) {
        // Handle Laravel API error format
        const errorMessage = error.response.data.message;

        // Check if it's a validation error with details
        if (error.response.data.errors) {
          // Directly use the Laravel validation error structure
          const errorDetails = error.response.data.errors;

          // Map Laravel error fields to our frontend fields
          Object.entries(errorDetails).forEach(([field, messages]) => {
            // Convert field names from backend to frontend format if needed
            const frontendField = mapBackendFieldToFrontend(field);
            validationErrors[frontendField] = messages as string[];
          });

          // Add the general message to _general
          validationErrors._general = [errorMessage];
        } else {
          validationErrors._general = [errorMessage];
        }
      } else if (typeof error === 'string') {
        validationErrors._general = [error];
      } else {
        validationErrors._general = ['An unexpected error occurred. Please try again.'];
      }

      setErrors(validationErrors);
    },
  });

  // Map backend field names to frontend field names
  const mapBackendFieldToFrontend = (backendField: string): string => {
    const mapping: Record<string, string> = {
      title: 'title',
      type: 'type',
      subtype: 'category',
      locationType: 'location_type',
      location: 'location',
      description: 'description',
      industryIds: 'industries',
      applyUrl: 'apply_url',
      // Add more mappings as needed
    };

    return mapping[backendField] || backendField;
  };

  const handleCancel = () => {
    router.back();
  };

  const createFormData = (data: ExtendedPostingFormData) => {
    const formData = new FormData();

    // Add all the regular fields
    Object.keys(data).forEach(key => {
      // Skip organizationLogo as we'll handle it separately
      if (key !== 'organizationLogo') {
        // Handle arrays (like industries)
        if (Array.isArray(data[key as keyof ExtendedPostingFormData])) {
          // For industries array, send as industries[]
          if (key === 'industries') {
            (data[key as keyof ExtendedPostingFormData] as any[]).forEach((industry: string) => {
              formData.append(`industries[]`, industry);
            });
          } else if (key === 'industryIds') {
            // Handle industryIds array specially
            (data.industryIds || []).forEach((id: number) => {
              formData.append(`industryIds[]`, id.toString());
            });
          } else if (key === 'preferred_states') {
            // Handle preferred_states array specially
            (data.preferred_states || []).forEach((state: string) => {
              formData.append(`preferredStates[]`, state);
            });
          } else {
            (data[key as keyof ExtendedPostingFormData] as any[]).forEach(
              (item: any, index: number) => {
                formData.append(`${key}[${index}]`, item);
              }
            );
          }
        } else if (
          data[key as keyof ExtendedPostingFormData] !== undefined &&
          data[key as keyof ExtendedPostingFormData] !== null
        ) {
          const value = data[key as keyof ExtendedPostingFormData];
          formData.append(key, value as string);
        }
      }
    });

    // Handle the organization logo if it's a File object
    if (data.organizationLogo instanceof File) {
      formData.append('organizationLogo', data.organizationLogo);
    }

    return formData;
  };

  const handleSave = async (formData: ExtendedPostingFormData, shouldList: boolean = false) => {
    try {
      // Don't proceed if we don't have an organization ID
      if (!organizationId) {
        console.error('Cannot create opportunity: No organization ID available');
        // TODO: Show error toast
        return;
      }

      const status = shouldList ? OpportunityStatus.LISTED : OpportunityStatus.UNLISTED;

      // Log the incoming form data to verify it contains industryIds
      console.log('Creating opportunity with form data:', formData);

      // Convert the form data to the format expected by the API
      // Use 'as any' to avoid TypeScript errors with property names that differ from our form data
      const opportunityData: any = {
        title: formData.title,
        type: formData.type,
        subtype: formData.category, // Backend expects 'subtype' for what our UI calls 'category'
        term: formData.term,
        locationType: formData.location_type, // Backend expects camelCase
        location: formData.location, // UUID/ID of the location
        description: formData.description,
        qualifications: formData.qualifications,
        responsibilities: formData.responsibilities,
        benefits: formData.benefits,
        details: formData.details,
        industries: formData.industries,
        // Include industryIds as this is what the backend validator expects
        industryIds: formData.industryIds || [],
        // Include interestIds for the backend validation
        interestIds: formData.interests || [],
        // Use the sponsor's organization ID
        organizationId: organizationId,
        // Schedule visibility dates
        visibleStartDate: formData.visibleStartDate,
        visibleEndDate: formData.visibleEndDate,
        // Backend expects applyUrl
        applyUrl: formData.apply_url,
        // Include organizationLogo if provided
        organizationLogo: formData.organization_logo || formData.organizationLogo,
        // Include visibility settings
        preferredGraduationYearStart: formData.preferredGraduationYearStart || null,
        preferredGraduationYearEnd: formData.preferredGraduationYearEnd || null,
        preferredStates: formData.preferred_states || [],
        status,
      };

      console.log('Prepared API payload:', {
        title: opportunityData.title,
        type: opportunityData.type,
        subtype: opportunityData.subtype,
        industryIds: opportunityData.industryIds,
        interestIds: opportunityData.interestIds,
        organizationId: opportunityData.organizationId,
        visibilitySettings: {
          preferredGraduationYearStart: opportunityData.preferredGraduationYearStart,
          preferredGraduationYearEnd: opportunityData.preferredGraduationYearEnd,
          preferredStates: opportunityData.preferredStates,
        },
      });

      // If we have a File object for organizationLogo, we need to use FormData
      if (opportunityData.organizationLogo instanceof File) {
        const formDataPayload = createFormData(opportunityData);

        // Add industryIds explicitly to formData
        if (opportunityData.industryIds && opportunityData.industryIds.length > 0) {
          // Clear any existing values first
          formDataPayload.delete('industryIds[]');

          // Add each industry ID separately with the correct array notation
          opportunityData.industryIds.forEach((id: number) => {
            formDataPayload.append('industryIds[]', id.toString());
          });
        }

        // Add interestIds explicitly to formData
        if (opportunityData.interestIds && opportunityData.interestIds.length > 0) {
          // Clear any existing values first
          formDataPayload.delete('interestIds[]');

          // Add each interest ID separately with the correct array notation
          opportunityData.interestIds.forEach((id: number) => {
            formDataPayload.append('interestIds[]', id.toString());
          });
        }

        // Add organizationId explicitly
        formDataPayload.set('organizationId', organizationId.toString());

        // Add visibility settings
        if (opportunityData.preferredGraduationYearStart !== null) {
          formDataPayload.set(
            'preferredGraduationYearStart',
            opportunityData.preferredGraduationYearStart
          );
        }

        if (opportunityData.preferredGraduationYearEnd !== null) {
          formDataPayload.set(
            'preferredGraduationYearEnd',
            opportunityData.preferredGraduationYearEnd
          );
        }

        // Handle preferred states array
        if (opportunityData.preferredStates && opportunityData.preferredStates.length > 0) {
          // Clear any existing values first
          formDataPayload.delete('preferredStates[]');

          // Add each state separately with the correct array notation
          opportunityData.preferredStates.forEach((state: string) => {
            formDataPayload.append('preferredStates[]', state);
          });
        }

        createOpportunity.mutate(formDataPayload as any);
      } else {
        // Regular JSON payload if no file upload
        createOpportunity.mutate(opportunityData as any);
      }
    } catch (error) {
      console.error('Error creating posting:', error);
      // TODO: Show error toast
    }
  };

  // Create empty default posting for a new posting form
  const emptyPosting = {
    title: '',
    type: '',
    category: '',
    location_type: '',
    location: '',
    description: '',
    qualifications: '',
    responsibilities: '',
    benefits: '',
    details: '',
    industries: [],
    interests: [],
    term: '',
    organizationId: organizationData?.id,
    organizationName: organizationData?.name || '',
    organizationLogo: organizationData?.logo_url || '',
    organizationWebsite: organizationData?.website || '',
    organizationAbout: organizationData?.about || '',
    visibleStartDate: null,
    visibleEndDate: null,
    apply_url: '',
    // Add visibility settings with default values
    preferredGraduationYearStart: null,
    preferredGraduationYearEnd: null,
    preferred_states: [],
  };

  return (
    <PostingCreate
      posting={emptyPosting}
      onCancel={handleCancel}
      onSave={(formData, shouldList) => {
        // Clear any previous errors before attempting to save
        setErrors({ _general: [] });
        // Cast the form data to our extended type that includes industryIds and organizationId
        handleSave(formData as ExtendedPostingFormData, shouldList);
      }}
      isLoadingOrganization={isLoadingOrganization}
      errors={errors}
      onClearErrors={() => setErrors({ _general: [] })}
    />
  );
}
