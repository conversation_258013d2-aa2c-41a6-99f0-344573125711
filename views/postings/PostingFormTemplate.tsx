'use client';

import React, { useEffect, useMemo, useRef, useState } from 'react';
import Link from 'next/link';
import {
  faArrowLeft,
  faArrowRight,
  faBadgeCheck,
  faBriefcase,
  faBuilding,
  faCheck,
  faEye,
  faHeart,
  faListCheck,
  faXmark,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useQueryClient } from '@tanstack/react-query';
import clsx from 'clsx';
import SponsorCTALinkInput from '@/components/opportunities/SponsorCTALinkInput';
import OrganizationCard from '@/components/organizations/OrganizationCard';
import { AvatarEditorFieldRef } from '@/components/shared/AvatarEditorField';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import DateRangePicker from '@/components/shared/form/DateRangePicker/DateRangePicker';
import { InterestsSelector } from '@/components/shared/form/InterestsSelector';
import LocationSearchSelect from '@/components/shared/form/LocationSearchSelect';
import SelectInput from '@/components/shared/form/SelectInput';
import SiteInput from '@/components/shared/form/SiteInput';
import StatesSelector from '@/components/shared/form/StatesSelector';
import Toggle from '@/components/shared/form/Toggle';
import { WysiwygEditor } from '@/components/shared/form/WysiwygEditor';
import {
  OPPORTUNITY_TYPES,
  opportunityLocationTypeOptions,
  opportunitySubtypeOptions,
  opportunityTermOptions,
  opportunityTypeOptions,
} from '@/constants/opportunityEnums';
import { useSponsorOpportunity } from '@/hooks/useSponsorOpportunity';
import { OpportunityData, OpportunityStatus } from '@/services/sponsorOpportunity.service';
import { useCareerInterestStore } from '@/stores/careerInterestStore';
import type { PostingFormData } from '@/stores/postingStore';
import { usePostingStore } from '@/stores/postingStore';
import { useToastNotify } from '@/stores/toastNotify.store';
import { ValidationErrors } from '@/types/validation';

// We'll create a local version of PostingFormData that uses numbers for interests
interface LocalPostingFormData extends Omit<PostingFormData, 'industries' | 'term'> {
  interests: number[];
  term: string | null;
  start_date?: string;
  end_date?: string;
  visibleStartDate?: string | null;
  visibleEndDate?: string | null;
  initialInterestData?: any[]; // Hold the original interest objects
  // Map organization fields
  organization_name: string;
  organization_website: string;
  organization_logo: string | File;
  // Preferred fields
  preferred_graduation_year: string;
  preferred_states: string[];
}

// Updated version of PostingFormData that allows null location
interface ExtendedPostingFormData
  extends Omit<PostingFormData, 'location' | 'industries' | 'term'> {
  location: string | null;
  term: string | null;
  start_date?: string;
  end_date?: string;
  visibleStartDate?: string | null;
  visibleEndDate?: string | null;
  organizationId?: number;
  organizationName?: string;
  organizationWebsite?: string;
  organizationLogo?: string | File;
  interestIds?: number[];
  interests: number[]; // Make this required, not optional
  preferredGraduationYearStart?: number | null;
  preferredGraduationYearEnd?: number | null;
  preferredStates?: string[];
}

interface PostingFormTemplateProps {
  posting: any; // TODO: Add proper type
  onCancel: () => void;
  onSave: (formData: PostingFormData, shouldList?: boolean) => void;
  buttonText?: {
    submit?: string;
    saveOnly?: string;
  };
  isLoadingOrganization?: boolean;
  isCreate?: boolean;
  errors?: ValidationErrors;
  onClearErrors?: () => void;
}

const PostingFormTemplate: React.FC<PostingFormTemplateProps> = ({
  posting,
  onCancel,
  onSave,
  buttonText = {
    submit: 'Save and List Posting',
    saveOnly: 'Or Save Without Listing',
  },
  isLoadingOrganization = false,
  isCreate = false,
  errors,
  onClearErrors,
}) => {
  const { careers } = useCareerInterestStore();
  const { toastNotify } = useToastNotify();
  const avatarEditorRef = useRef<AvatarEditorFieldRef>(null);
  const queryClient = useQueryClient();
  const { toggleOpportunityStatus } = useSponsorOpportunity({
    onSuccess: (data, context) => {
      if (context === 'toggleStatus') {
        const opportunityData = data as OpportunityData;
        setListed(opportunityData.status === OpportunityStatus.LISTED);

        // Show toast notification when status is toggled
        const statusMessage =
          opportunityData.status === OpportunityStatus.LISTED
            ? 'Posting successfully listed'
            : 'Posting successfully unlisted';
        toastNotify(statusMessage, 'success');

        // Invalidate both specific opportunity and opportunities list to ensure UI is updated
        if (opportunityData.id) {
          // Invalidate the specific opportunity
          queryClient.invalidateQueries({ queryKey: ['sponsorOpportunity', opportunityData.id] });
          queryClient.invalidateQueries({
            queryKey: ['opportunities', 'detail', String(opportunityData.id)],
          });

          // Invalidate the opportunities list
          queryClient.invalidateQueries({ queryKey: ['sponsorOpportunities'] });
          queryClient.invalidateQueries({ queryKey: ['opportunities', 'list'] });
        }
      }
    },
  });

  const [listed, setListed] = useState<boolean>(posting?.status === OpportunityStatus.LISTED);

  // Add useEffect to update listed state when posting changes
  useEffect(() => {
    if (posting && posting.status) {
      setListed(posting.status === OpportunityStatus.LISTED);
    }
  }, [posting]);

  const handleToggleListed = () => {
    if (posting?.id) {
      toggleOpportunityStatus.mutate(posting.id);
    } else {
      setListed(!listed);
    }
  };

  // Industry options for dropdown
  const industryOptions = careers.map(career => ({
    value: career.slug,
    label: career.label,
  }));

  // Local form state instead of using the store
  const [formData, setFormData] = useState<LocalPostingFormData>({
    title: '',
    type: '',
    category: '',
    location_type: '',
    location: '',
    location_display: '',
    description: '',
    qualifications: '',
    responsibilities: '',
    benefits: '',
    organization_name: '',
    details: '',
    interests: [], // Number[] in LocalPostingFormData
    industry: '',
    apply_url: '',
    action_button_label: '',
    term: null, // Default null for term
    organization_website: '',
    organization_logo: '',
    preferred_graduation_year: '',
    preferred_states: [],
    initialInterestData: [], // Initialize initialInterestData
  });

  // Create opportunity type flags for conditional rendering
  const isEmploymentOpportunity = formData.type === OPPORTUNITY_TYPES.EMPLOYMENT;
  const isEducationOpportunity = formData.type === OPPORTUNITY_TYPES.EDUCATION;

  // Local state for start and end years
  const [startYear, setStartYear] = useState<string>('');
  const [endYear, setEndYear] = useState<string>('');

  // Local state for schedule posting dates
  const [scheduleDates, setScheduleDates] = useState<{ startDate: string; endDate: string }>({
    startDate: '',
    endDate: '',
  });

  // Track original data for hasUnsavedChanges check
  const [originalData, setOriginalData] = useState<LocalPostingFormData | null>(null);

  // Track original date values separately to detect date picker changes
  const [originalDates, setOriginalDates] = useState<{ startDate: string; endDate: string }>({
    startDate: '',
    endDate: '',
  });

  // Keep track of link validation status
  const [isLinkValid, setIsLinkValid] = useState<boolean>(true);

  // Add state to track if a field has been touched/interacted with
  const [touchedFields, setTouchedFields] = useState<Record<string, boolean>>({});
  // Add state to track if form is submitting
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Helper to format date string from API (can be full ISO timestamp) to YYYY-MM-DD format
  const parseAPIDateToISO = (dateString: string): string => {
    if (!dateString) return '';

    try {
      // Parse the API date string and explicitly use UTC methods to avoid timezone shifts
      const date = new Date(dateString);

      // Extract date components using UTC methods
      const year = date.getUTCFullYear();
      const month = String(date.getUTCMonth() + 1).padStart(2, '0');
      const day = String(date.getUTCDate()).padStart(2, '0');

      // Format to YYYY-MM-DD
      const isoDate = `${year}-${month}-${day}`;

      return isoDate;
    } catch (error) {
      return '';
    }
  };

  // Helper to format ISO date (YYYY-MM-DD) to display format (MM/DD/YYYY)
  const formatDisplayDate = (isoDate: string): string => {
    if (!isoDate) return '';

    try {
      // Simple conversion assuming isoDate is in YYYY-MM-DD format
      const [year, month, day] = isoDate.split('-');
      if (!year || !month || !day) {
        console.error('Invalid ISO date format:', isoDate);
        return '';
      }

      const formatted = `${month}/${day}/${year}`;

      return formatted;
    } catch (error) {
      console.error('Error formatting date:', error, isoDate);
      return '';
    }
  };

  // Initialize form data when posting data is loaded
  useEffect(() => {
    if (posting) {
      // Save the original interest objects and extract IDs
      const originalInterests = posting.interests || [];

      // Extract and convert interests to numbers
      const interestsAsNumbers = originalInterests
        .map((i: any) => {
          if (typeof i === 'object' && i.id) {
            return Number(i.id);
          } else if (typeof i === 'number') {
            return i;
          } else if (typeof i === 'string' && !isNaN(Number(i))) {
            return Number(i);
          }
          return 0; // Fallback, should not happen
        })
        .filter((id: number) => id > 0);

      // Map API fields to form fields with proper logging of the mapping
      const initialFormData: LocalPostingFormData = {
        title: posting.title || '',
        type: posting.type || '',
        // Map subtype from backend to category in the frontend
        category: posting.subtype || posting.category || '',
        location_type: posting.location_type || posting.locationType || '',
        // Handle location fields: location should only be UUID, display value goes in location_display
        // Try all possible field names for the location UUID to ensure we capture it
        location:
          posting.location_coordinate_id ||
          posting.location_uuid ||
          posting.location_id ||
          posting.location ||
          '',
        location_display:
          posting.location_display ||
          posting.city_state ||
          (posting.city && posting.state_code ? `${posting.city}, ${posting.state_code}` : ''),
        description: posting.description || '',
        qualifications: posting.qualifications || '',
        responsibilities: posting.responsibilities || '',
        benefits: posting.benefits || '',
        organization_name: posting.organizationName || posting.organization_name || '',
        details: posting.details || '',
        // Use the converted interest IDs as numbers
        interests: interestsAsNumbers,
        // Store the original interest objects for display
        initialInterestData: originalInterests,
        industry: posting.industry || '',
        apply_url: posting.apply_url || posting.applyUrl || '',
        action_button_label: posting.action_button_label || '',
        // For term, only set a value if it exists in the posting data
        term: posting.term || null,
        organization_website: posting.organizationWebsite || '',
        organization_logo: posting.organizationLogo || '',
        preferred_graduation_year: posting.preferred_graduation_year || '', // Keep original format for comparison
        preferred_states: posting.preferredStates || [],
        // Add date fields from backend - convert to ISO format (YYYY-MM-DD)
        visibleStartDate: posting.visibleStartDate || null,
        visibleEndDate: posting.visibleEndDate || null,
        start_date: parseAPIDateToISO(posting.visibleStartDate || ''),
        end_date: parseAPIDateToISO(posting.visibleEndDate || ''),
      };

      // Set initial form data
      setFormData(initialFormData);
      setOriginalData({ ...initialFormData });

      // Initialize schedule dates if available with properly parsed ISO dates
      const startDateISO = parseAPIDateToISO(posting.visibleStartDate || '');
      const endDateISO = parseAPIDateToISO(posting.visibleEndDate || '');

      if (startDateISO || endDateISO) {
        const dates = { startDate: startDateISO, endDate: endDateISO };
        setScheduleDates(dates);
        setOriginalDates(dates);
      } else {
        // console.log('No date values found in posting data to initialize schedule dates');
      }

      // Initialize graduation year start/end fields from backend data
      // Use standardized camelCase field names
      if (posting.preferredGraduationYearStart !== undefined) {
        setStartYear(
          posting.preferredGraduationYearStart
            ? posting.preferredGraduationYearStart.toString()
            : ''
        );
        setEndYear(
          posting.preferredGraduationYearEnd ? posting.preferredGraduationYearEnd.toString() : ''
        );
      }
      // Fallback to the legacy field (preferred_graduation_year) if new fields are not available
      else if (posting.preferred_graduation_year) {
        const yearString = posting.preferred_graduation_year || '';
        if (yearString.includes(' - ')) {
          const [start, end] = yearString.split(' - ');
          setStartYear(start.trim());
          setEndYear(end.trim());
        } else if (yearString.trim()) {
          setStartYear(yearString.trim());
          setEndYear(''); // Ensure end year is cleared if only one year exists
        } else {
          setStartYear('');
          setEndYear('');
        }
      } else {
        setStartYear('');
        setEndYear('');
      }
    } else {
      // Initialize with default values for new postings
      const defaultFormData: LocalPostingFormData = {
        title: '',
        type: '',
        category: '',
        location_type: '',
        location: '',
        location_display: '',
        description: '',
        qualifications: '',
        responsibilities: '',
        benefits: '',
        organization_name: '',
        details: '',
        interests: [],
        industry: '',
        apply_url: '',
        action_button_label: '',
        term: null, // Default null for term
        organization_website: '',
        organization_logo: '',
        preferred_graduation_year: '',
        preferred_states: [],
        initialInterestData: [],
      };
      setFormData(defaultFormData);
      setOriginalData({ ...defaultFormData });
    }
  }, [posting, careers]);

  // Effect to update formData.preferred_graduation_year based on local year state
  useEffect(() => {
    let combinedYear = '';
    if (startYear) {
      combinedYear = startYear;
      if (endYear) {
        combinedYear += ` - ${endYear}`;
      }
    }

    // Update formData only if the combined value is different
    // This prevents unnecessary re-renders and state loops
    if (formData.preferred_graduation_year !== combinedYear) {
      setFormData(prev => ({
        ...prev,
        preferred_graduation_year: combinedYear,
      }));
    }
  }, [startYear, endYear, formData.preferred_graduation_year]); // Include formData.preferred_graduation_year to avoid infinite loops

  // Effect to update formData with schedule dates
  useEffect(() => {
    if (scheduleDates.startDate || scheduleDates.endDate) {
      setFormData(prev => {
        const updated = {
          ...prev,
          // Set both the frontend field names
          start_date: scheduleDates.startDate || '',
          end_date: scheduleDates.endDate || '',
          // And the backend field names
          visibleStartDate: scheduleDates.startDate || null,
          visibleEndDate: scheduleDates.endDate || null,
        };

        return updated;
      });
    }
  }, [scheduleDates]);

  // Update category when type changes
  useEffect(() => {
    // Clear category when type changes
    if (formData.type && formData.category) {
      let isValid = false;

      if (formData.type === OPPORTUNITY_TYPES.EMPLOYMENT) {
        isValid = opportunitySubtypeOptions[OPPORTUNITY_TYPES.EMPLOYMENT].some(
          opt => opt.value === formData.category
        );
      } else if (formData.type === OPPORTUNITY_TYPES.EDUCATION) {
        isValid = opportunitySubtypeOptions[OPPORTUNITY_TYPES.EDUCATION].some(
          opt => opt.value === formData.category
        );
      }

      if (!isValid) {
        setFormData(prev => ({
          ...prev,
          category: '',
        }));
      }
    }
  }, [formData.type]);

  // Memoized value to check if form has unsaved changes
  const hasUnsavedChanges = useMemo(() => {
    if (!originalData) return false;

    // First check if the dates have changed
    const datesChanged =
      scheduleDates.startDate !== originalDates.startDate ||
      scheduleDates.endDate !== originalDates.endDate;

    // If dates changed, we definitely have unsaved changes
    if (datesChanged) {
      return true;
    }

    // Otherwise check other fields
    return Object.keys(formData).some(key => {
      const k = key as keyof LocalPostingFormData;

      // Skip fields that shouldn't affect dirty state
      if (k === 'initialInterestData' || k === 'visibleStartDate' || k === 'visibleEndDate') {
        return false;
      }

      // Handle array comparisons
      if (Array.isArray(formData[k])) {
        // If lengths are different, they're definitely different
        if ((formData[k] as any[]).length !== (originalData[k] as any[]).length) {
          return true;
        }

        // Compare arrays element by element
        return JSON.stringify(formData[k]) !== JSON.stringify(originalData[k]);
      }

      // Handle file object cases
      if (
        k === 'organization_logo' &&
        (formData[k] instanceof File || originalData[k] instanceof File)
      ) {
        return true; // If either is a File object, consider it changed
      }

      // Handle regular fields
      return formData[k] !== originalData[k];
    });
  }, [formData, originalData, scheduleDates, originalDates]);

  // Update isFormValid to check required fields for new postings
  const isFormValid = useMemo(() => {
    // Check for required fields
    const titleValid = formData.title.trim() !== '';
    const typeValid = formData.type.trim() !== '';
    const descriptionValid = formData.description.trim() !== '';
    const interestsValid = formData.interests.length > 0;

    // Basic form validation for all required fields
    const requiredFieldsValid = titleValid && typeValid && descriptionValid && interestsValid;

    // For existing postings, also check if link is valid
    const basicValidation = requiredFieldsValid && isLinkValid;

    // For existing postings (non-create view), we also check for unsaved changes
    if (isCreate) {
      // For new posts, just check required fields
      return basicValidation;
    } else {
      // For existing posts, also check for unsaved changes
      return basicValidation && hasUnsavedChanges;
    }
  }, [
    formData.title,
    formData.type,
    formData.description,
    formData.interests,
    isLinkValid,
    hasUnsavedChanges,
    isCreate,
  ]);

  // Update handleInputChange to mark field as touched
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Mark the field as touched
    setTouchedFields(prev => ({
      ...prev,
      [name]: true,
    }));

    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Update handleSelectChange to mark field as touched
  const handleSelectChange =
    (name: keyof LocalPostingFormData) => (value: string | null, displayValue?: string) => {
      // Mark the field as touched
      setTouchedFields(prev => ({
        ...prev,
        [name]: true,
      }));

      if (name === 'location') {
        // If it's the location field, update both location and location_display
        setFormData(prev => ({
          ...prev,
          location: value || '', // Use empty string in form state to match form field type
          location_display: displayValue || '', // Use provided display value
        }));
      } else {
        // For other fields, just update the value
        setFormData(prev => ({
          ...prev,
          [name]: value,
        }));

        // Add debugging for location_type specifically
        if (name === 'location_type') {
          console.log(`Location type changed to: "${value}"`);
        }
      }
    };

  // Update handleWysiwygChange to mark field as touched
  const handleWysiwygChange = (name: keyof LocalPostingFormData) => (value: string) => {
    // Mark the field as touched
    setTouchedFields(prev => ({
      ...prev,
      [name]: true,
    }));

    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Update interests change handler to mark field as touched
  const handleInterestsChange = (interests: number[]) => {
    // Mark the field as touched
    setTouchedFields(prev => ({
      ...prev,
      interests: true,
    }));

    setFormData(prev => {
      const updatedFormData = {
        ...prev,
        interests: interests, // Update the interests array
      };
      return updatedFormData;
    });
  };

  // Modify the validation function to only show errors for touched fields or after submit attempt
  const shouldShowValidation = (fieldName: string): boolean => {
    // Always show server validation errors
    if (getFieldError(fieldName)) return true;

    // Don't show client validation while submitting to prevent flash
    if (isSubmitting) return false;

    // For client validation, only show if the field has been touched
    return !!touchedFields[fieldName];
  };

  // Update getClientValidationError to respect the touched fields
  const getClientValidationError = (fieldName: keyof LocalPostingFormData): string | undefined => {
    // Only show client validation in create mode when the form is empty
    // and only for fields that have been touched
    if (!isCreate) return undefined;

    switch (fieldName) {
      case 'title':
        return !formData.title.trim() ? 'Title is required' : undefined;
      case 'type':
        return !formData.type.trim() ? 'Opportunity type is required' : undefined;
      case 'description':
        return !formData.description.trim() ? 'Description is required' : undefined;
      case 'interests':
        return formData.interests.length === 0 ? 'At least one interest is required' : undefined;
      default:
        return undefined;
    }
  };

  // Update hasFieldError to respect the touched fields state
  const hasFieldError = (fieldName: string): boolean => {
    if (!shouldShowValidation(fieldName)) return false;
    return !!getFieldValidationMessage(fieldName);
  };

  // Update handleSubmit to set isSubmitting flag
  const handleSubmit = async (e: React.FormEvent, shouldList: boolean = false) => {
    e.preventDefault();

    if (!isFormValid) {
      // If form is invalid, mark all fields as touched to show all validation errors
      const allFieldsTouched: Record<string, boolean> = {};
      Object.keys(formData).forEach(key => {
        allFieldsTouched[key] = true;
      });
      setTouchedFields(allFieldsTouched);
      return;
    }

    // Set submitting state to true to prevent validation flash
    setIsSubmitting(true);

    // If the avatar editor is editing, save the avatar first
    if (avatarEditorRef.current?.isEditing) {
      await avatarEditorRef.current.save();
    }

    // Create a copy of the formData for submission to avoid modifying original state
    const submissionData: ExtendedPostingFormData = {
      ...formData,
    };

    // Handle location field explicitly, making empty string null
    // This ensures the backend knows to clear the location when needed
    if (!submissionData.location || submissionData.location === '') {
      submissionData.location = null;
    }

    // Handle term field based on type
    // For employment type, term should be null if it's empty
    // For education type, it should have a valid value
    if (submissionData.type === 'employment') {
      // For employment, if term is empty or not selected, set it to null
      if (!submissionData.term || submissionData.term === '') {
        submissionData.term = null;
      }
    } else if (submissionData.type === 'education') {
      // For education, if term is empty, default to 'indefinite'
      if (!submissionData.term || submissionData.term === '') {
        submissionData.term = 'indefinite';
      }
    }

    // Process the date fields to match backend expectations
    // Backend expects visibleStartDate and visibleEndDate as ISO strings with time at UTC midnight
    if (submissionData.start_date || submissionData.end_date) {
      // Convert YYYY-MM-DD to UTC timestamps for the API
      submissionData.visibleStartDate = submissionData.start_date
        ? `${submissionData.start_date}T00:00:00.000Z`
        : null;

      submissionData.visibleEndDate = submissionData.end_date
        ? `${submissionData.end_date}T00:00:00.000Z`
        : null;
    }

    // Add organizationId from posting data
    if (posting?.organizationId) {
      submissionData.organizationId = posting.organizationId;
    } else {
      console.error('No organizationId found in posting data');
      throw new Error('Organization ID is required to save an opportunity');
    }

    // Set interestIds directly from the interests array (ensuring they're numbers)
    submissionData.interestIds = [...formData.interests];

    // Handle graduation year visibility
    if (startYear) {
      submissionData.preferredGraduationYearStart = parseInt(startYear, 10);
      if (endYear) {
        submissionData.preferredGraduationYearEnd = parseInt(endYear, 10);
      } else {
        submissionData.preferredGraduationYearEnd = null;
      }
    } else {
      submissionData.preferredGraduationYearStart = null;
      submissionData.preferredGraduationYearEnd = null;
    }

    // Ensure preferred states is sent as an array
    submissionData.preferredStates = formData.preferred_states || [];

    try {
      // In Edit mode, respect the existing listing status set by the toggle
      // Instead of using shouldList parameter which comes from button click
      const finalShouldList = isCreate ? shouldList : listed;

      // Final sanitization before submission
      const apiSubmissionData = {
        ...submissionData,
        // Ensure these critical fields are explicitly included at the top level
        organizationId: submissionData.organizationId,
        // Explicitly ensure interestIds is an array of numbers
        interestIds: Array.isArray(submissionData.interestIds)
          ? submissionData.interestIds.map(id => (typeof id === 'string' ? parseInt(id, 10) : id))
          : [],
        // Ensure the visibility fields are included explicitly
        preferredGraduationYearStart: submissionData.preferredGraduationYearStart,
        preferredGraduationYearEnd: submissionData.preferredGraduationYearEnd,
        preferredStates: submissionData.preferredStates,
      };

      // Submit the form data with the explicitly structured data
      await onSave(apiSubmissionData as any, finalShouldList);

      // Reset form state to prevent false positive dirty state
      // Make a deep copy to break any object references
      setOriginalData(JSON.parse(JSON.stringify(formData)));
      setOriginalDates({ ...scheduleDates });

      // Use toast notification instead of success message
      toastNotify(
        finalShouldList ? 'Posting saved and listed successfully!' : 'Posting saved successfully!',
        'success'
      );

      // Keep isSubmitting true to prevent validation flash during navigation
    } catch (error) {
      // If there was an error, set submitting to false so validation can show again
      setIsSubmitting(false);
      console.error('Error submitting form:', error);

      // Show error toast if submission fails
      toastNotify('Failed to save posting. Please try again.', 'error');
    }
  };

  // Get organization name from opportunity or store
  const organizationName =
    formData.organization_name || posting.organizationName || posting.organization_name || '';

  // Generate graduation year options for the next 4 years and previous 4 years
  const generateGraduationYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];

    // Previous 4 years
    for (let i = 4; i > 0; i--) {
      const year = currentYear - i;
      years.push({ value: year.toString(), label: year.toString() });
    }

    // Current year
    years.push({ value: currentYear.toString(), label: currentYear.toString() });

    // Next 4 years
    for (let i = 1; i <= 5; i++) {
      const year = currentYear + i;
      years.push({ value: year.toString(), label: year.toString() });
    }

    // Add an option for 'any' year
    years.unshift({ value: '', label: 'Any' });

    return years;
  };

  const graduationYearOptions = generateGraduationYearOptions();

  // Add rendering logging
  const getFormattedDateValue = () => {
    // Format the date picker value for display
    const value =
      scheduleDates.startDate && scheduleDates.endDate
        ? `${formatDisplayDate(scheduleDates.startDate)} - ${formatDisplayDate(scheduleDates.endDate)}`
        : scheduleDates.startDate
          ? formatDisplayDate(scheduleDates.startDate)
          : '';

    return value;
  };

  // Add function to handle error clearing
  const handleClearErrors = () => {
    if (onClearErrors) {
      onClearErrors();
    }
  };

  // Helper function to get field error
  const getFieldError = (fieldName: string): string | undefined => {
    if (!errors || !errors[fieldName] || errors[fieldName].length === 0) {
      return undefined;
    }
    return errors[fieldName][0];
  };

  // Combined function that returns either server validation errors or client validation errors
  const getFieldValidationMessage = (fieldName: string): string | undefined => {
    // First check for server validation errors
    const serverError = getFieldError(fieldName);
    if (serverError) return serverError;

    // Then fall back to client validation
    return getClientValidationError(fieldName as keyof LocalPostingFormData);
  };

  const handleAvatarSave = (file: File) => {
    setFormData(prev => ({
      ...prev,
      organization_logo: file,
    }));
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      // TODO: Show confirmation dialog
      if (window.confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        onCancel();
      }
    } else {
      onCancel();
    }
  };

  const handleDateRangeChange = (dates: { startDate: string; endDate: string }) => {
    // The dates from the DateRangePicker are in YYYY-MM-DD format
    // We store them as-is without timezone conversion
    setScheduleDates(dates);
  };

  return (
    <form
      onSubmit={e => {
        e.preventDefault();
        handleSubmit(e, true);
      }}
    >
      <section className="bg-surface-tertiary pb-8 px-4 pt-10 lg:px-8">
        {/* Back button */}
        <button
          type="button"
          onClick={handleCancel}
          className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-6"
        >
          <FontAwesomeIcon icon={faArrowLeft} className="h-4 w-4 mr-1" />
          <span>Back</span>
        </button>
      </section>

      <section className="pa-container pb-10 bg-surface-tertiary">
        {/* Header section */}
        <div className="flex flex-col md:flex-row justify-between items-start gap-8">
          <div className="flex items-start flex-grow w-full md:w-auto">
            {/* Form fields */}
            <div className="flex-grow grid grid-cols-1 gap-6 md:grid-cols-2">
              {/* Error Messages */}
              {errors && errors._general && errors._general.length > 0 && (
                <div className="col-span-1 md:col-span-2">
                  <span
                    className={clsx('flex gap-2 p-3 text-sm text-red-600 bg-red-50 rounded-lg')}
                  >
                    <button type="button" onClick={handleClearErrors} aria-label="Clear Errors">
                      <span className="sr-only">Clear Errors</span>
                      <FontAwesomeIcon
                        icon={faXmark}
                        className="size-3 text-text-secondary"
                        aria-hidden="true"
                      />
                    </button>
                    <div>
                      <ul className="pl-4">
                        {errors._general.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  </span>
                </div>
              )}

              <div className="col-span-1 md:col-span-2">
                <SiteInput
                  label="Opportunity Name"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  isFailedValidation={hasFieldError('title')}
                  description={getFieldValidationMessage('title')}
                  required
                />
              </div>

              <div className="col-span-1">
                <SelectInput
                  label="Opportunity Type"
                  name="type"
                  value={formData.type}
                  onChange={handleSelectChange('type')}
                  options={opportunityTypeOptions}
                  isFailedValidation={hasFieldError('type')}
                  description={getFieldValidationMessage('type')}
                  required
                />
              </div>

              <div className="col-span-1">
                <SelectInput
                  label="Category"
                  name="category"
                  value={formData.category || ''}
                  onChange={handleSelectChange('category')}
                  options={
                    isEmploymentOpportunity
                      ? opportunitySubtypeOptions[OPPORTUNITY_TYPES.EMPLOYMENT]
                      : isEducationOpportunity
                        ? opportunitySubtypeOptions[OPPORTUNITY_TYPES.EDUCATION]
                        : []
                  }
                  isFailedValidation={hasFieldError('category')}
                  description={getFieldValidationMessage('category')}
                />
              </div>

              <div className="col-span-1">
                <SelectInput
                  label="Location Category"
                  name="location_type"
                  value={formData.location_type}
                  onChange={handleSelectChange('location_type')}
                  options={opportunityLocationTypeOptions}
                  isFailedValidation={hasFieldError('location_type')}
                  description={getFieldValidationMessage('location_type')}
                  required
                />
              </div>

              <div className="col-span-1">
                <LocationSearchSelect
                  label="Specific Location"
                  value={formData.location}
                  onChange={handleSelectChange('location')}
                  locationDisplay={formData.location_display}
                  isFailedValidation={hasFieldError('location')}
                  description={getFieldValidationMessage('location')}
                  required
                />
              </div>

              <div className="col-span-1">
                <SponsorCTALinkInput
                  value={formData.apply_url || ''}
                  onChange={e => handleInputChange(e)}
                  onValidationChange={setIsLinkValid}
                />
              </div>

              {isEducationOpportunity && (
                <div className="col-span-1">
                  <SelectInput
                    label="Term (Duration)"
                    name="term"
                    value={formData.term || 'indefinite'}
                    onChange={handleSelectChange('term')}
                    options={opportunityTermOptions}
                    isFailedValidation={hasFieldError('term')}
                    description={getFieldValidationMessage('term')}
                    required
                  />
                </div>
              )}

              <div className="col-span-1">
                <DateRangePicker
                  label="Schedule Posting"
                  onChange={handleDateRangeChange}
                  value={getFormattedDateValue()}
                  description="Choose a date range when this posting should be listed. Leave blank to manually list and unlist this posting."
                />
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="grid grid-cols-1 gap-y-4 mt-4 shrink-0 md:mt-0">
            {!isCreate ? (
              <div className="flex flex-wrap items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="inline-block min-w-14 text-text-secondary text-sm">
                    {listed ? 'Listed' : 'Unlisted'}
                  </span>
                  <Toggle
                    checked={listed}
                    onChange={handleToggleListed}
                    disabled={toggleOpportunityStatus.isPending}
                  />
                </div>

                <Button
                  type="button"
                  icon={faCheck}
                  iconPosition="right"
                  onClick={e => handleSubmit(e, false)}
                  disabled={!isFormValid || !isLinkValid || isLoadingOrganization}
                >
                  {buttonText.submit}
                </Button>
              </div>
            ) : (
              <>
                <Button
                  type="button"
                  icon={faCheck}
                  iconPosition="right"
                  onClick={e => handleSubmit(e, true)}
                  disabled={!isFormValid || !isLinkValid || isLoadingOrganization}
                >
                  {buttonText.submit}
                </Button>

                <Button
                  type="button"
                  icon={faCheck}
                  variant="text"
                  iconPosition="right"
                  onClick={e => handleSubmit(e, false)}
                  disabled={!isFormValid || !isLinkValid || isLoadingOrganization}
                  className="mx-4"
                >
                  {buttonText.saveOnly}
                </Button>
              </>
            )}
          </div>
        </div>
      </section>

      <section className="pa-container py-10">
        <div className="pa-profile-grid">
          <div className="pa-profile-grid-left">
            <Card>
              <CardHeader title="Description" titleIcon={faBriefcase} className="mb-8" />
              <WysiwygEditor
                value={formData.description}
                onChange={handleWysiwygChange('description')}
                placeholder="Enter opportunity description..."
              />
              {getFieldValidationMessage('description') && (
                <p className="text-sm text-brand-red mt-2">
                  {getFieldValidationMessage('description')}
                </p>
              )}

              <div className="mt-6">
                <InterestsSelector
                  selectedInterests={formData.interests}
                  onChange={handleInterestsChange}
                  className="mb-2"
                  initialInterestData={formData.initialInterestData}
                />
                {/* Show either help message or error message, but not both */}
                {getFieldValidationMessage('interests') ||
                getFieldValidationMessage('interestIds') ? (
                  <p className="text-sm text-brand-red mt-2">
                    {getFieldValidationMessage('interests') ||
                      getFieldValidationMessage('interestIds')}
                  </p>
                ) : (
                  formData.interests.length === 0 && (
                    <p className="text-sm text-text-secondary italic">
                      &#42;Postings need at least one interest.
                    </p>
                  )
                )}
              </div>
            </Card>

            <div className="mb-8">
              <OrganizationCard
                organizationData={{
                  name: organizationName,
                  logo_url: posting.organizationLogo || '',
                  about: posting.organizationAbout || posting.details || formData.details || '',
                  website: posting.organizationWebsite || formData.apply_url || '',
                }}
              >
                <Link
                  href="/account"
                  className="flex items-center gap-2 text-sm text-text-secondary"
                >
                  Manage Organization Details
                  <FontAwesomeIcon icon={faArrowRight} className="text-sm" aria-hidden="true" />
                </Link>
              </OrganizationCard>
            </div>
          </div>

          <div className="pa-profile-grid-right">
            <Card>
              <CardHeader title="Qualifications" titleIcon={faBadgeCheck} className="mb-8" />

              <WysiwygEditor
                value={formData.qualifications}
                onChange={handleWysiwygChange('qualifications')}
                placeholder="Enter required qualifications..."
              />
              {getFieldValidationMessage('qualifications') && (
                <p className="text-sm text-brand-red mt-2">
                  {getFieldValidationMessage('qualifications')}
                </p>
              )}
            </Card>

            <Card>
              <CardHeader title="Responsibilities" titleIcon={faListCheck} className="mb-8" />

              <WysiwygEditor
                value={formData.responsibilities}
                onChange={handleWysiwygChange('responsibilities')}
                placeholder="Enter job responsibilities..."
              />
              {getFieldValidationMessage('responsibilities') && (
                <p className="text-sm text-brand-red mt-2">
                  {getFieldValidationMessage('responsibilities')}
                </p>
              )}
            </Card>

            <Card>
              <CardHeader title="Benefits" titleIcon={faHeart} className="mb-8" />

              <WysiwygEditor
                value={formData.benefits}
                onChange={handleWysiwygChange('benefits')}
                placeholder="Enter benefits and perks..."
              />
              {getFieldValidationMessage('benefits') && (
                <p className="text-sm text-brand-red mt-2">
                  {getFieldValidationMessage('benefits')}
                </p>
              )}
            </Card>

            <Card>
              <CardHeader title="Posting visibility" titleIcon={faEye} className="mb-8" />
              <div className="space-y-6">
                <p className="text-sm text-text-secondary">
                  To make sure your applicants qualify, you may choose to decrease the visibility of
                  this post for users who do not match specific criteria:
                </p>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <SelectInput
                    label="Graduation Start Year"
                    name="start_year_visibility"
                    value={startYear}
                    onChange={setStartYear}
                    options={graduationYearOptions}
                    isFailedValidation={hasFieldError('preferredGraduationYearStart')}
                    description={getFieldValidationMessage('preferredGraduationYearStart')}
                  />

                  <SelectInput
                    label="Graduation End Year (Optional)"
                    name="end_year_visibility"
                    value={endYear}
                    onChange={setEndYear}
                    options={[
                      { value: '', label: 'None' },
                      ...graduationYearOptions.filter(
                        opt =>
                          opt.value === '' ||
                          (startYear && parseInt(opt.value, 10) >= parseInt(startYear, 10))
                      ),
                    ]}
                    disabled={!startYear}
                    isFailedValidation={hasFieldError('preferredGraduationYearEnd')}
                    description={getFieldValidationMessage('preferredGraduationYearEnd')}
                  />
                </div>

                <StatesSelector
                  label="Preferred States"
                  selectedStates={formData.preferred_states || []}
                  onChange={states => setFormData(prev => ({ ...prev, preferred_states: states }))}
                />
                {getFieldValidationMessage('preferredStates') && (
                  <p className="text-sm text-brand-red mt-2">
                    {getFieldValidationMessage('preferredStates')}
                  </p>
                )}
              </div>
            </Card>
          </div>
        </div>
      </section>
    </form>
  );
};

export default PostingFormTemplate;
