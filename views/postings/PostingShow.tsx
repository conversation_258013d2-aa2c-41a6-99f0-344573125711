'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import {
  faArrowLeft,
  faArrowRight,
  faArrowUpRightFromSquare,
  faBadgeCheck,
  faBriefcase,
  faBuilding,
  faCalendar,
  faChartSimpleHorizontal,
  faCopy,
  faEye,
  faHeart,
  faListCheck,
  faPencil,
  faTrash,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useQueryClient } from '@tanstack/react-query';
import SponsorDeleteDialog from '@/components/opportunities/SponsorDeleteDialog';
import OrganizationCard from '@/components/organizations/OrganizationCard';
import Richtext from '@/components/shared/blocks/Richtext';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import Toggle from '@/components/shared/form/Toggle';
import Tag, { CAREER_TAG_ICONS } from '@/components/shared/Tag';
import { useSponsorOpportunity } from '@/hooks/useSponsorOpportunity';
import { OpportunityStatus } from '@/services/sponsorOpportunity.service';
import { useModalStore } from '@/stores/modal.store';
import { STATE_OPTIONS } from '@/utils/constants/states';

type PostingShowProps = {
  posting: any; // TODO: Add proper type
  onEdit: () => void;
  onBack: () => void;
};

type StatCardItem = {
  value: number;
  label: string;
  icon: IconDefinition;
};

export default function PostingShow({ posting, onEdit, onBack }: PostingShowProps) {
  const router = useRouter();
  const [listed, setListed] = useState(false);
  const { open, close } = useModalStore();
  const queryClient = useQueryClient();
  const [isDuplicating, setIsDuplicating] = useState(false);

  // Stats cards data
  const statsItems: StatCardItem[] = [
    { value: 15, label: 'Days Listed', icon: faCalendar },
    { value: 86, label: 'Views', icon: faEye },
    { value: 4, label: 'Action Button Clicks', icon: faArrowUpRightFromSquare },
  ];

  const {
    deleteOpportunity,
    toggleOpportunityStatus,
    isDeleting,
    refreshOpportunities,
    duplicateOpportunity,
  } = useSponsorOpportunity({
    onSuccess: (data, context) => {
      if (context === 'delete') {
        close();
        onBack(); // Navigate back after successful deletion
      } else if (context === 'toggleStatus') {
        // Update the listed state when toggle is successful
        const opportunityData = data as typeof posting;
        setListed(opportunityData.status === OpportunityStatus.LISTED);

        // Invalidate both specific opportunity and opportunities list to ensure UI is updated
        if (opportunityData.id) {
          // Invalidate the specific opportunity
          queryClient.invalidateQueries({ queryKey: ['sponsorOpportunity', opportunityData.id] });
          queryClient.invalidateQueries({
            queryKey: ['opportunities', 'detail', String(opportunityData.id)],
          });

          // Invalidate the opportunities list
          queryClient.invalidateQueries({ queryKey: ['sponsorOpportunities'] });
          queryClient.invalidateQueries({ queryKey: ['opportunities', 'list'] });
        }
      } else if (context === 'duplicate') {
        // Handle successful duplication
        setIsDuplicating(false);

        // Invalidate the opportunities list to show the new duplicated posting
        queryClient.invalidateQueries({ queryKey: ['sponsorOpportunities'] });
        queryClient.invalidateQueries({ queryKey: ['opportunities', 'list'] });

        // Navigate back to the listings page
        onBack();
      }
    },
  });

  // Initialize listed state based on posting status
  useEffect(() => {
    if (posting && posting.status) {
      setListed(posting.status === OpportunityStatus.LISTED);
    }
  }, [posting]);

  const handleToggleListed = () => {
    if (posting?.id) {
      toggleOpportunityStatus.mutate(posting.id);
    }
  };

  const handleDuplicate = () => {
    if (posting?.id) {
      setIsDuplicating(true);
      duplicateOpportunity.mutate(posting.id);
    }
  };

  const handleOpenDeleteModal = () => {
    open(<SponsorDeleteDialog id={posting.id} handleDelete={handleDelete} close={close} />, 'sm');
  };

  const handleDelete = (id: string | number) => {
    deleteOpportunity.mutate(Number(id));
  };

  // Format location type to be more readable
  const formatLocationType = (locationType: string) => {
    if (!locationType) return '';
    return locationType?.charAt(0).toUpperCase() + locationType.slice(1);
  };

  // Format opportunity type to be more readable
  const formatType = (type: string) => {
    return type.toUpperCase();
  };

  // Get organization name from opportunity
  const organizationName = posting.organizationName || posting.organization_name || '';

  // Create a well-formatted location string that handles missing data
  const formattedLocation = posting.location_display
    ? `${formatLocationType(posting.locationType)}${posting.location_display ? ', ' + posting.location_display : ''}`
    : formatLocationType(posting.locationType);

  // Create a display string that combines organization name and location
  const displayInfo = `${organizationName}${formattedLocation ? ' • ' + formattedLocation : ''}`;

  return (
    <>
      <section className="bg-surface-tertiary pb-8 px-4 pt-10 lg:px-8">
        {/* Back button */}
        <button
          onClick={onBack}
          className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-6"
        >
          <FontAwesomeIcon icon={faArrowLeft} className="h-4 w-4 mr-1" />
          <span>Back</span>
        </button>
      </section>

      <section className="pa-container pb-10 bg-surface-tertiary">
        {/* Header section */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div className="flex items-start">
            {/* Company logo */}
            <div className="bg-white p-4 shadow-sm rounded-lg mr-4 flex-shrink-0">
              {posting.organizationLogo ? (
                <Image
                  src={posting.organizationLogo}
                  alt={organizationName || 'Company logo'}
                  width={64}
                  height={64}
                  className="h-16 w-16 object-contain"
                />
              ) : (
                <div className="h-16 w-16 bg-gray-200 rounded flex items-center justify-center text-gray-500">
                  {organizationName?.charAt(0) || 'C'}
                </div>
              )}
            </div>

            {/* Opportunity info */}
            <div className="block space-y-1">
              <span className="pa-eyebrow text-brand-red">{formatType(posting.type)}</span>

              <h1 className="text-2xl md:text-3xl font-bold text-text-primary">{posting.title}</h1>

              <p className="text-xl font-semibold text-text-secondary">{displayInfo}</p>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex flex-wrap items-center gap-4 mt-4 md:mt-0">
            <div className="flex items-center gap-2">
              <span className="text-text-secondary text-sm">{listed ? 'Listed' : 'Unlisted'}</span>
              <Toggle
                checked={listed}
                onChange={handleToggleListed}
                disabled={toggleOpportunityStatus.isPending}
              />
            </div>

            <Button
              variant="text"
              color="red"
              icon={faPencil}
              iconPosition="right"
              aria-label="Edit posting"
              onClick={onEdit}
            >
              Edit
            </Button>

            <Button
              variant="text"
              color="red"
              icon={faCopy}
              iconPosition="right"
              aria-label="Duplicate Posting"
              onClick={handleDuplicate}
              disabled={isDuplicating}
            >
              {isDuplicating ? 'Duplicating...' : 'Duplicate'}
            </Button>

            <Button
              href="https://www.google.com"
              color="blue"
              size="small"
              icon={faArrowRight}
              iconPosition="right"
            >
              Apply
            </Button>
          </div>
        </div>
      </section>

      <section className="pa-container py-10">
        <Card>
          <CardHeader title="posting status" titleIcon={faChartSimpleHorizontal} className="mb-8" />
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {statsItems.map((item, index) => (
              <div key={index} className="p-6 rounded-3xl bg-surface-tertiary shadow-sm">
                <div className="flex justify-between gap-4 flex-wrap">
                  <h3 className="text-3xl font-bold mb-1">{item.value}</h3>
                  <FontAwesomeIcon
                    icon={item.icon}
                    className="text-text-secondary"
                    aria-hidden="true"
                  />
                </div>
                <p className="text-text-secondary">{item.label}</p>
              </div>
            ))}
          </div>
        </Card>
      </section>

      <section className="pa-container pb-10">
        <div className="pa-profile-grid">
          <div className="pa-profile-grid-left">
            {/* Description */}
            <Card>
              <CardHeader title="Description" titleIcon={faBriefcase} className="mb-8" />
              <Richtext content={posting.description} />

              {/* Interests */}
              {posting.interests && posting?.interests.length > 0 && (
                <div className="mb-4 mt-8">
                  <h4 className="font-bold text-text-primary text-sm mb-2">Interests</h4>
                  <div className="flex flex-wrap gap-2">
                    {posting.interests.map((interest: any, index: number) => {
                      // Handle both string format and object format
                      if (typeof interest === 'string') {
                        // Legacy string format - check if it exists in CAREER_TAG_ICONS
                        return (
                          <Tag
                            key={index}
                            label={interest}
                            icon={CAREER_TAG_ICONS[interest] ? interest : ''}
                          />
                        );
                      } else {
                        // Object format with potential icon
                        return (
                          <Tag
                            key={index}
                            label={interest.name}
                            icon={
                              interest.icon ||
                              (CAREER_TAG_ICONS[interest.name] ? interest.name : '')
                            }
                          />
                        );
                      }
                    })}
                  </div>
                </div>
              )}
            </Card>

            {/* Replace existing About Organization card with new component */}
            <OrganizationCard
              organizationData={{
                name: organizationName,
                logo_url: posting.organizationLogo || '',
                about: posting.organizationAbout || posting.details || '',
                website: posting.organizationWebsite || posting.apply_url || '',
              }}
            />
          </div>

          <div className="pa-profile-grid-right">
            {/* Qualifications */}
            <Card>
              <CardHeader title="Qualifications" titleIcon={faBadgeCheck} className="mb-8" />
              <Richtext content={posting.qualifications} />
            </Card>

            {/* Responsibilities */}
            <Card>
              <CardHeader title="Responsibilities" titleIcon={faListCheck} className="mb-8" />
              <Richtext content={posting.responsibilities} />
            </Card>

            {/* Benefits */}
            <Card>
              <CardHeader title="Benefits" titleIcon={faHeart} className="mb-8" />
              <Richtext content={posting.benefits} />
            </Card>

            {/* Posting Visibility */}
            {(posting.preferred_graduation_year_start ||
              posting.preferredGraduationYearStart ||
              posting.preferred_states?.length > 0 ||
              posting.preferredStates?.length > 0) && (
              <Card>
                <CardHeader title="Posting Visibility" titleIcon={faEye} className="mb-8" />
                <p className="text-sm text-text-secondary mb-4">
                  To make sure your applicants qualify, you may choose to decrease the visibility of
                  this post for users who do not match specific criteria:
                </p>
                <div className="mb-4 mt-8">
                  {/* Graduation Year Range */}
                  {(posting.preferred_graduation_year_start ||
                    posting.preferredGraduationYearStart) && (
                    <div className="text-text-secondary mb-2">
                      <span className="text-text-primary font-bold">
                        Preferred Graduation Year:{' '}
                      </span>
                      <span>
                        {posting.preferred_graduation_year_start ||
                          posting.preferredGraduationYearStart}
                        {(posting.preferred_graduation_year_end ||
                          posting.preferredGraduationYearEnd) &&
                          ` - ${posting.preferred_graduation_year_end || posting.preferredGraduationYearEnd}`}
                      </span>
                    </div>
                  )}

                  {/* Preferred States */}
                  {(posting.preferred_states?.length > 0 ||
                    posting.preferredStates?.length > 0) && (
                    <ul className="flex flex-wrap gap-2 mt-1">
                      <li>
                        <span className="text-text-primary font-bold">Preferred States: </span>
                      </li>

                      {(posting.preferred_states || posting.preferredStates || []).map(
                        (stateCode: string, index: number, array: string[]) => {
                          const stateObj = STATE_OPTIONS.find(state => state.value === stateCode);
                          const stateName = stateObj ? stateObj.label : stateCode;
                          const isLast = index === array.length - 1;

                          return (
                            <li key={`state-item-${index}`}>
                              <span className="text-text-secondary">
                                {stateName}
                                {!isLast ? ', ' : ''}
                              </span>
                            </li>
                          );
                        }
                      )}
                    </ul>
                  )}
                </div>
              </Card>
            )}
          </div>
        </div>
      </section>

      <section className="pa-container pb-10">
        <Button
          color="red"
          icon={faTrash}
          iconPosition="right"
          aria-label="Delete posting"
          onClick={handleOpenDeleteModal}
          disabled={isDeleting}
        >
          {isDeleting ? 'Deleting...' : 'Delete Posting'}
        </Button>
      </section>
    </>
  );
}
