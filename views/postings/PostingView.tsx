'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { useOpportunities } from '@/hooks/useOpportunities';
import { useSponsorOpportunity } from '@/hooks/useSponsorOpportunity';
import { OpportunityStatus } from '@/services/sponsorOpportunity.service';
import { usePostingStore } from '@/stores/postingStore';
import type { PostingFormData } from '@/stores/postingStore';
import { prepareFormDataForApi } from '@/utils/apiNormalization';
import PostingEdit from './PostingEdit';
import PostingShow from './PostingShow';

type PostingViewProps = {
  id: string;
};

export default function PostingView({ id }: PostingViewProps) {
  const router = useRouter();
  const { isEditing, setEditing, clearEditing } = usePostingStore();
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const queryClient = useQueryClient();
  const numericId = parseInt(id, 10);

  // Get opportunity data and actions from both hooks
  const { saveOpportunity, isSaving } = useOpportunities();

  // Use the sponsor opportunity hook to get the detailed data including visibility settings
  const { getOpportunity, updateOpportunity, toggleOpportunityStatus } = useSponsorOpportunity({
    onSuccess: (data, context) => {
      if (context === 'toggleStatus' || context === 'update') {
        // When status is toggled or opportunity is updated, refresh the opportunity data
        queryClient.invalidateQueries({ queryKey: ['sponsorOpportunity', numericId] });
        queryClient.invalidateQueries({ queryKey: ['opportunities', 'detail', id] });
      }
    },
  });

  // Use the sponsor opportunity query to get the full data
  const { data: posting, isLoading, error } = getOpportunity(numericId);

  const handleEdit = () => {
    setEditing(id);
  };

  const handleCancelEdit = () => {
    if (unsavedChanges) {
      // TODO: Show confirmation dialog
      if (window.confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        clearEditing();
      }
    } else {
      clearEditing();
    }
  };

  const handleSave = async (formData: PostingFormData, shouldList: boolean = false) => {
    try {
      // Track the network call timing
      const startTime = Date.now();

      // Ensure posting exists and has an organization ID
      if (!posting) {
        throw new Error('Cannot save: Posting data is not available');
      }

      if (!posting.organizationId) {
        throw new Error('Cannot save: Organization ID is missing from posting data');
      }

      // Normalize form data to camelCase for API consistency
      const normalizedData = prepareFormDataForApi(formData);

      // Add any additional fields needed and EXPLICITLY ensure industryIds is preserved
      const requestData = {
        ...normalizedData,
        // Ensure industryIds is explicitly included - this is critical
        industryIds: Array.isArray(formData.industries)
          ? formData.industries.map(id => (typeof id === 'string' ? parseInt(id, 10) : id))
          : [],
        status: shouldList ? OpportunityStatus.LISTED : OpportunityStatus.UNLISTED,
        // Add organizational properties using root-level format
        organizationId: posting.organizationId,
      };

      // Use the updateOpportunity function from the sponsor hook
      await updateOpportunity.mutateAsync({
        id: numericId,
        data: requestData,
      });

      // Clear editing state
      setUnsavedChanges(false);

      // Force a refetch of the current opportunity to ensure we have the latest data
      await queryClient.invalidateQueries({ queryKey: ['sponsorOpportunity', numericId] });
      await queryClient.invalidateQueries({ queryKey: ['opportunities', 'detail', id] });

      return posting;
    } catch (error) {
      console.error('Error saving posting:', error);
      throw error; // Rethrow the error to allow the caller to handle it
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse">
          <div className="h-8 w-32 bg-gray-200 rounded mb-4"></div>
          <div className="h-12 w-3/4 bg-gray-200 rounded mb-4"></div>
          <div className="h-8 w-1/2 bg-gray-200 rounded mb-8"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4">
          <h3 className="text-lg font-medium">Error loading opportunity</h3>
          <p className="mt-2">
            There was a problem loading this opportunity. Please try again later.
          </p>
          <button
            onClick={() => router.back()}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (!posting) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-lg p-4">
          <h3 className="text-lg font-medium">Opportunity not found</h3>
          <p className="mt-2">
            The opportunity you&apos;re looking for doesn&apos;t exist or has been removed.
          </p>
          <button
            onClick={() => router.back()}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return isEditing ? (
    <PostingEdit posting={posting} onCancel={handleCancelEdit} onSave={handleSave} />
  ) : (
    <PostingShow posting={posting} onEdit={handleEdit} onBack={() => router.back()} />
  );
}
