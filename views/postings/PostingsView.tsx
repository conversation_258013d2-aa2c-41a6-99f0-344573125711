'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { faCheck, faPlus, faTrash, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { BaseOpportunity } from '@/components/opportunities/OpportunityCard';
import SponsorDeleteDialog from '@/components/opportunities/SponsorDeleteDialog';
import SponsorOpportunityCard from '@/components/opportunities/SponsorOpportunityCard';
import Button from '@/components/shared/Button';
import SearchInput from '@/components/shared/form/SearchInput';
import { CAREER_TAG_ICONS } from '@/components/shared/Tag';
import { useSponsorOpportunity } from '@/hooks/useSponsorOpportunity';
import { OpportunityStatus } from '@/services/sponsorOpportunity.service';
import { useModalStore } from '@/stores/modal.store';

// Define a type matching the actual API response structure
interface ApiSponsorOpportunity {
  id: number;
  title: string;
  subtitle?: string; // Added based on sample
  description: string;
  details?: string;
  organizationId: number; // From sample
  organizationLogo?: string; // From sample
  term?: string;
  type: string;
  subtype?: string; // Added based on sample
  status: OpportunityStatus;
  industries: { id: number; name: string }[];
  interests: { id: number; name: string; icon?: string | null }[]; // Added interests
  isFeatured: boolean;
  city?: string;
  stateCode?: string; // From sample
  locationType?: string; // From sample
  location?: string; // Keep optional as it might be missing
  applyUrl?: string;
  qualifications?: string;
  responsibilities?: string;
  benefits?: string;
  visibleStartDate?: string | null;
  visibleEndDate?: string | null;
  createdAt: string;
  updatedAt: string;
  userId: number;
  // TODO: Add organization object
}

/**
 * Main component for the Posting page
 */
const PostingsView: React.FC = () => {
  const router = useRouter();
  const { open, close } = useModalStore();
  const [searchValue, setSearchValue] = useState('');
  const [processingItems, setProcessingItems] = useState<
    Record<string, { toggle?: boolean; duplicate?: boolean; delete?: boolean }>
  >({});
  const {
    getOpportunities,
    setSearchParams,
    duplicateOpportunity,
    deleteOpportunity,
    toggleOpportunityStatus,
    isDeleting,
    isDuplicating,
    isTogglingStatus,
    refreshOpportunities,
  } = useSponsorOpportunity({
    onSuccess: (data, context) => {
      if (context === 'duplicate' || context === 'delete' || context === 'toggleStatus') {
        refreshOpportunities();

        // Clear processing state for the specific item
        if (data && 'id' in data) {
          const id = String(data.id);
          setProcessingItems(prev => {
            const newState = { ...prev };
            delete newState[id];
            return newState;
          });
        }
      }
    },
  });

  // Fetch opportunities - Assume the hook returns data matching ApiSponsorOpportunity
  const { data, isLoading, error } = getOpportunities();
  const opportunities = data as ApiSponsorOpportunity[] | undefined; // Type assertion

  // Handle search input changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchValue(query);
    setSearchParams(prev => ({ ...prev, search: query }));
  };

  // Handle adding a new posting
  const handleAddPosting = () => {
    router.push('/postings/create');
  };

  // Handle opportunity actions
  const handleToggleStatus = (id: string | number) => {
    console.log(`Toggling status for opportunity ${id}`);
    // Set this specific item as processing
    setProcessingItems(prev => ({
      ...prev,
      [id]: { ...prev[id], toggle: true },
    }));
    toggleOpportunityStatus.mutate(Number(id));
  };

  const handleDuplicate = (id: string | number) => {
    // Set this specific item as processing
    setProcessingItems(prev => ({
      ...prev,
      [id]: { ...prev[id], duplicate: true },
    }));
    duplicateOpportunity.mutate(Number(id));
  };

  const handleOpenDeleteModal = (id: string | number) => {
    open(<SponsorDeleteDialog id={id} handleDelete={handleDelete} close={close} />, 'md');
  };

  const handleDelete = (id: string | number) => {
    // Set this specific item as processing
    setProcessingItems(prev => ({
      ...prev,
      [id]: { ...prev[id], delete: true },
    }));
    deleteOpportunity.mutate(Number(id));
    close();
  };

  // Format opportunities for OpportunityCard component
  const formatOpportunityForCard = (
    opportunity: ApiSponsorOpportunity // Use the new specific type here
  ): BaseOpportunity & {
    status: OpportunityStatus;
    isFeatured: boolean;
  } => {
    return {
      id: opportunity.id,
      title: opportunity.title,
      description: opportunity.description,
      qualifications: opportunity.qualifications || '',
      responsibilities: opportunity.responsibilities || '',
      benefits: opportunity.benefits || '',
      details: opportunity.details || '',
      location: opportunity.location || '',
      city: opportunity.city || '',
      state_code: opportunity.stateCode || '',
      location_type: opportunity.locationType || '',
      type: opportunity.type,
      subtype: opportunity.subtype || '',
      organization_name: '', // Pass empty string for now
      organization_logo: opportunity.organizationLogo || '/images/positive-athlete-icon.svg',
      industries: opportunity.industries.map(industry => industry.name),
      interests:
        opportunity.interests?.map(interest => ({
          id: interest.id,
          name: interest.name,
          icon: interest.icon || (CAREER_TAG_ICONS[interest.name] ? interest.name : ''),
        })) || [],
      term: opportunity.term,
      status: opportunity.status,
      isFeatured: opportunity.isFeatured,
    };
  };

  return (
    <div className="min-h-screen bg-surface-secondary">
      <div className="pa-container py-8">
        <SearchInput
          placeholder="Search postings..."
          value={searchValue}
          onChange={handleSearchChange}
        />

        <Button icon={faPlus} iconPosition="left" onClick={handleAddPosting} className="my-6">
          Add a New Posting
        </Button>

        {isLoading ? (
          <p className="text-center py-8">Loading opportunities...</p>
        ) : error ? (
          <p className="text-center py-8 text-red-500">
            Error loading opportunities. Please try again later.
          </p>
        ) : opportunities && opportunities.length > 0 ? (
          <ul className="block space-y-6">
            {opportunities.map(opportunity => (
              <li key={opportunity.id}>
                <SponsorOpportunityCard
                  opportunity={formatOpportunityForCard(opportunity)}
                  onToggleStatus={handleToggleStatus}
                  onDuplicate={handleDuplicate}
                  onDelete={handleOpenDeleteModal}
                  isProcessingToggle={!!processingItems[opportunity.id]?.toggle}
                  isProcessingDuplicate={!!processingItems[opportunity.id]?.duplicate}
                  isProcessingDelete={!!processingItems[opportunity.id]?.delete}
                />
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-center py-8">No opportunities found. Create one to get started!</p>
        )}
      </div>
    </div>
  );
};

export default PostingsView;
