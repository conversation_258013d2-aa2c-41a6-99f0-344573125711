'use client';

import React, { useCallback, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { OPPORTUNITY_TERMS } from '@/constants/opportunityEnums';
import type { PostingFormData } from '@/stores/postingStore';
import { usePostingStore } from '@/stores/postingStore';
import { useToastNotify } from '@/stores/toastNotify.store';
import { ValidationErrors } from '@/types/validation';
import { prepareFormDataForApi } from '../../utils/apiNormalization';
import PostingFormTemplate from './PostingFormTemplate';

type PostingEditProps = {
  posting: any; // TODO: Add proper type
  onCancel: () => void;
  onSave: (formData: PostingFormData, shouldList?: boolean) => void;
};

// Extended type that includes additional fields from the API
interface ExtendedPostingData extends Omit<PostingFormData, 'location_type' | 'term'> {
  // CamelCase fields for API
  term: string | null;
  industryIds: number[];
  interestIds: number[];
  locationType: string | null;
  city: string | null;
  stateCode: string | null;
  applyUrl: string | null;
  isFeatured: boolean;
  status?: string;
  visibleStartDate?: string | null;
  visibleEndDate?: string | null;
  // Organization data as root-level properties
  organizationId: number;
  organizationName?: string;
  organizationWebsite?: string;
  organizationAbout?: string;
  organizationLogo?: string | File;
  // Visibility settings
  preferredGraduationYear?: string;
  preferredStates?: string[];
  preferredGraduationYearStart?: number | null;
  preferredGraduationYearEnd?: number | null;
}

export default function PostingEdit({ posting, onCancel, onSave }: PostingEditProps) {
  // Access the query client for cache invalidation
  const queryClient = useQueryClient();
  const { toastNotify } = useToastNotify();

  // Add state for error handling using ValidationErrors type
  const [errors, setErrors] = useState<ValidationErrors>({ _general: [] });

  // Wrap the onSave handler to add debugging and data processing
  const handleSave = useCallback(
    async (formData: PostingFormData, shouldList?: boolean) => {
      // Clear any previous errors
      setErrors({ _general: [] });

      // Normalize field names to camelCase for API consistency
      const normalizedFormData = prepareFormDataForApi(formData);

      // Get the current listing status to preserve it
      const currentStatus = posting?.status || 'unlisted';

      // Format the data for the API with consistent camelCase
      const extendedData = {
        ...normalizedFormData,
        // The formData.industries array from the IndustriesSelector is already numeric IDs
        // We can use it directly without any mapping
        industryIds: Array.isArray(formData.industries) ? formData.industries : [1],
        // Include interestIds for the backend validation
        interestIds: Array.isArray(formData.interests) ? formData.interests : [],
        isFeatured: false,
        // In Edit mode, we should preserve the existing listing status
        // and not change it based on the save button
        status: currentStatus,
      };

      try {
        // Call the original onSave handler with the processed data and await its completion
        // When editing, we always pass false for shouldList since the toggle handles listing status
        const savedOpportunity: any = await onSave(extendedData as any, false);

        // Invalidate queries to ensure UI shows updated data
        await queryClient.invalidateQueries({ queryKey: ['opportunities'] });

        // If we have the saved opportunity ID, also invalidate the specific opportunity cache
        if (savedOpportunity?.id) {
          await queryClient.invalidateQueries({
            queryKey: ['opportunities', 'detail', savedOpportunity.id],
          });
        }

        // Ensure errors are cleared on successful save
        setErrors({ _general: [] });

        // Show success toast notification
        toastNotify('Posting saved successfully!', 'success');

        return savedOpportunity;
      } catch (error: any) {
        console.error('Error saving opportunity:', error);

        // Initialize the structured errors object
        const validationErrors: ValidationErrors = { _general: [] };

        // Handle different error formats
        if (error?.response?.data?.message) {
          // Handle Laravel API error format
          const errorMessage = error.response.data.message;

          // Check if it's a validation error with details
          if (error.response.data.errors) {
            const errorDetails = error.response.data.errors;

            // Map Laravel error fields to our frontend fields
            Object.entries(errorDetails).forEach(([field, messages]) => {
              // Convert field names from backend to frontend format if needed
              const frontendField = mapBackendFieldToFrontend(field);
              validationErrors[frontendField] = messages as string[];
            });

            // Add special handling for graduation year validation errors
            if (
              errorDetails.preferredGraduationYearEnd &&
              errorDetails.preferredGraduationYearEnd.includes('greater than or equal to')
            ) {
              validationErrors.preferredGraduationYearEnd = [
                'Graduation year end must be greater than or equal to graduation year start.',
              ];
            }

            // Add the general message to _general
            validationErrors._general = [errorMessage];
          } else {
            validationErrors._general = [errorMessage];
          }
        } else if (typeof error === 'string') {
          validationErrors._general = [error];
        } else {
          validationErrors._general = ['An unexpected error occurred. Please try again.'];
        }

        setErrors(validationErrors);

        // Show error toast notification
        toastNotify('Failed to save posting. Please check the form for errors.', 'error');

        throw error; // Rethrow to allow the form component to handle the error
      }
    },
    [onSave, queryClient, toastNotify, posting]
  );

  // Map backend field names to frontend field names
  const mapBackendFieldToFrontend = (backendField: string): string => {
    const mapping: Record<string, string> = {
      title: 'title',
      type: 'type',
      subtype: 'category',
      locationType: 'location_type',
      location: 'location',
      description: 'description',
      industryIds: 'industries',
      interestIds: 'interests',
      applyUrl: 'apply_url',
      // Add more mappings as needed
    };

    return mapping[backendField] || backendField;
  };

  return (
    <PostingFormTemplate
      posting={posting}
      onCancel={onCancel}
      onSave={handleSave}
      buttonText={{
        submit: 'Save',
        saveOnly: 'Save',
      }}
      errors={errors}
      onClearErrors={() => setErrors({ _general: [] })}
    />
  );
}
