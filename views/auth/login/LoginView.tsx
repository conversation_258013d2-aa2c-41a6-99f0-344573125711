'use client';

import { useState } from 'react';
import { faArrowRight } from '@fortawesome/pro-regular-svg-icons';
import { z } from 'zod';
import Button from '@/components/shared/Button';
import PasswordInput from '@/components/shared/form/PasswordInput';
import SiteInput from '@/components/shared/form/SiteInput';
import { useAuth } from '@/hooks/useAuth';

// Login form schema
const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const { login } = useAuth();
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<Partial<LoginFormData> & { general?: string }>({});
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});
    setIsLoading(true);

    try {
      // Validate form data
      loginSchema.parse(formData);
      await login(formData);
    } catch (error: unknown) {
      console.error('Login form error:', error);
      if (error instanceof z.ZodError) {
        const formattedErrors: Partial<LoginFormData> = {};
        error.errors.forEach((err: z.ZodIssue) => {
          if (err.path[0]) {
            formattedErrors[err.path[0] as keyof LoginFormData] = err.message;
          }
        });
        setErrors(formattedErrors);
      } else {
        setErrors({
          general: 'Invalid email or password. Please try again.',
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md space-y-8">
      <div>
        <h2 className="text-3xl font-bold text-gray-900">App Login</h2>
      </div>

      <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
        {errors.general && (
          <div className="p-3 text-sm text-red-600 bg-red-50 rounded-lg">{errors.general}</div>
        )}

        <div className="space-y-4">
          <div>
            <SiteInput
              id="email"
              type="email"
              label="Email or Phone Number"
              required
              value={formData.email}
              onChange={e => setFormData({ ...formData, email: e.target.value })}
              placeholder="Enter your email"
              disabled={isLoading}
              isFailedValidation={!!errors.email}
              description={errors.email}
            />
          </div>

          <div>
            <PasswordInput
              label="Password"
              id="password"
              required
              value={formData.password}
              onChange={e => setFormData({ ...formData, password: e.target.value })}
              placeholder="Enter your password"
              disabled={isLoading}
              isFailedValidation={!!errors.password}
              description={errors.password}
            />
          </div>
        </div>

        <div className="text-right">
          <a
            href="/reset-password"
            className="text-sm font-medium text-[#002B5C] hover:text-[#002347]"
          >
            Forgot your Password?
          </a>
        </div>

        <Button
          type="submit"
          size="small"
          icon={faArrowRight}
          iconPosition="right"
          disabled={isLoading}
        >
          {isLoading ? 'Logging in...' : 'Login'}
        </Button>
      </form>

      <div className="mt-6">
        <p className="text-sm text-gray-600">
          Don&apos;t have an account? You&apos;ll need to be nominated as a Positive Athlete or a
          Positive Coach to access the Positive Athlete App.
        </p>
      </div>
    </div>
  );
}
