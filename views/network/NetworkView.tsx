'use client';

import { useState } from 'react';
import { Configure, InstantSearch } from 'react-instantsearch';
import { createInstantSearchRouterNext } from 'react-instantsearch-router-nextjs';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { faSliders, faXmark } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  Transition,
  TransitionChild,
} from '@headlessui/react';
import clsx from 'clsx';
import { NetworkFilters } from '@/components/network/NetworkFilters';
import { NetworkSearchResults } from '@/components/network/NetworkSearchResults';
import { createUserExclusionFilter, searchClient, USERS_INDEX } from '@/config/users-meilisearch';
import { useAuth } from '@/hooks/useAuth';
import { ProfileTypes } from '@/stores/auth.store';
import { useSidebarStore } from '@/stores/useSidebarStore';

export default function NetworkView() {
  const { isExpanded, toggle } = useSidebarStore();
  const [showFilters, setShowFilters] = useState(true);
  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false);
  const { user, profileType } = useAuth();

  // Next.js App Router hooks
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Create a filter to exclude the current user by ID
  const userFilter = createUserExclusionFilter(user?.id);

  // Combine filters if user is a sponsor
  const combinedFilters =
    profileType === ProfileTypes.SPONSOR
      ? `${userFilter} AND profile_type = '${ProfileTypes.POSITIVE_ATHLETE}'`
      : userFilter;

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* InstantSearch Provider with router integration */}
      {/* @ts-ignore - Ignoring type issues with searchClient and router */}
      <InstantSearch
        searchClient={searchClient as any}
        indexName={USERS_INDEX}
        routing={{
          // Use basic state mapping to ensure URL parameters are preserved
          stateMapping: {
            stateToRoute(uiState) {
              return uiState;
            },
            routeToState(routeState) {
              return routeState;
            },
          },
        }}
      >
        {/* Apply the combined filters at the root level */}
        <Configure hitsPerPage={20} filters={combinedFilters} />

        {/* Filters Sidebar */}
        {showFilters && (
          <div
            className={clsx(
              'fixed hidden w-64 border-r border-surface-tertiary bg-white shrink-0 h-screen overflow-y-auto transition-all ease-out duration-300 lg:block',
              isExpanded ? 'left-40' : 'left-20'
            )}
          >
            <div className="mt-36">
              <NetworkFilters />
            </div>
          </div>
        )}

        {/* Search Results */}
        <div className="pa-container py-8 min-h-screen flex-1 lg:ml-64">
          <NetworkSearchResults handleMobileFiltersOpen={() => setMobileFiltersOpen(true)} />
        </div>

        {/* Mobile Filters Drawer */}
        <Dialog
          open={mobileFiltersOpen}
          className="relative z-50"
          onClose={setMobileFiltersOpen}
          unmount={false}
        >
          <DialogBackdrop
            className="fixed inset-0 bg-black/30 transition-opacity duration-500 ease-in-out data-[closed]:opacity-0"
            transition
          />

          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 overflow-hidden">
              <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                <DialogPanel
                  className="pointer-events-auto relative w-screen max-w-md transform transition duration-500 ease-in-out data-[closed]:translate-x-full sm:duration-700"
                  transition
                >
                  <TransitionChild>
                    <div className="absolute left-0 top-0 -ml-8 flex pr-2 pt-4 duration-500 ease-in-out data-[closed]:opacity-0 sm:-ml-10 sm:pr-4 z-50">
                      <button
                        type="button"
                        onClick={() => setMobileFiltersOpen(false)}
                        className="relative rounded-md text-gray-200 hover:text-white focus:outline-none focus:ring-2 focus:ring-white"
                        aria-label="Close panel"
                      >
                        <span className="absolute -inset-2.5" />
                        <span className="sr-only">Close panel</span>
                        <FontAwesomeIcon
                          icon={faXmark}
                          className="!size-5 text-current"
                          aria-hidden="true"
                        />
                      </button>
                    </div>
                  </TransitionChild>

                  <div className="flex h-full flex-col overflow-y-scroll bg-white py-6 shadow-xl">
                    <div className="px-4 sm:px-6">
                      <div className="flex-1">
                        <NetworkFilters />
                      </div>
                      <div className="py-4 border-t border-gray-200 mt-2">
                        <button
                          type="button"
                          className="w-full py-2 px-4 bg-brand-red text-white font-semibold rounded-lg"
                          onClick={() => setMobileFiltersOpen(false)}
                          aria-label="Done"
                        >
                          Done
                        </button>
                      </div>
                    </div>
                  </div>
                </DialogPanel>
              </div>
            </div>
          </div>
        </Dialog>
      </InstantSearch>
    </div>
  );
}
