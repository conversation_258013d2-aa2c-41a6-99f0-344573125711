'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import type { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import {
  faArrowLeft,
  faBadgeCheck,
  faBookmark as faBookmarkRegular,
  faBriefcase,
  faBuilding,
  faHeart,
  faListCheck,
} from '@fortawesome/pro-regular-svg-icons';
import { faArrowRight, faBookmark as faBookmarkSolid } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Richtext from '@/components/shared/blocks/Richtext';
import Button from '@/components/shared/Button';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
import Tag, { CAREER_TAG_ICONS } from '@/components/shared/Tag';
import { useAuth } from '@/hooks/useAuth';
import { useOpportunities } from '@/hooks/useOpportunities';
import { Opportunity } from '@/services/opportunities.service';
import { ProfileTypes } from '@/stores/auth.store';
import { useOpportunityImpressions } from '@/stores/opportunityImpressions.store';

// Extend the Opportunity type to include the interests property
interface ExtendedOpportunity extends Opportunity {
  interests?: {
    id: number;
    name: string;
    icon: string;
  }[];
}

type OpportunityViewProps = {
  id: string;
};

export default function OpportunityView({ id }: OpportunityViewProps) {
  const router = useRouter();
  const { profileType } = useAuth();
  const isParentProfile = profileType === ProfileTypes.PARENT;

  const { useOpportunity, isOpportunityBookmarked, toggleBookmark } = useOpportunities();
  const { data: opportunityData, isLoading, error } = useOpportunity(id);
  const opportunity = opportunityData as ExtendedOpportunity;
  const isBookmarked = isOpportunityBookmarked(id);
  const { trackAction } = useOpportunityImpressions();

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse">
          <div className="h-8 w-32 bg-gray-200 rounded mb-4"></div>
          <div className="h-12 w-3/4 bg-gray-200 rounded mb-4"></div>
          <div className="h-8 w-1/2 bg-gray-200 rounded mb-8"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4">
          <h3 className="text-lg font-medium">Error loading opportunity</h3>
          <p className="mt-2">
            There was a problem loading this opportunity. Please try again later.
          </p>
          <button
            onClick={() => router.back()}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  if (!opportunity) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-lg p-4">
          <h3 className="text-lg font-medium">Opportunity not found</h3>
          <p className="mt-2">
            The opportunity you&apos;re looking for doesn&apos;t exist or has been removed.
          </p>
          <button
            onClick={() => router.back()}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // Map term to human-readable duration
  const getDuration = (term: string) => {
    switch (term) {
      case '<1_year':
        return 'Less than 1 year';
      case '1-2_years':
        return '1-2 years';
      case '2-4_years':
        return '2-4 years';
      case '3-4_years':
        return '3-4 years';
      case '>4_years':
        return 'More than 4 years';
      default:
        return term;
    }
  };

  // Format location type to be more readable
  const formatLocationType = (locationType: string | null | undefined) => {
    if (!locationType) return '';
    return locationType.charAt(0).toUpperCase() + locationType.slice(1);
  };

  // Format opportunity type to be more readable
  const formatType = (type: string) => {
    return type.toUpperCase();
  };

  // Get organization name from opportunity
  const organizationName =
    opportunity.organization?.name ||
    opportunity.organization_name ||
    opportunity.organizationName ||
    '';

  // Create a well-formatted location string that handles missing data
  const formattedLocation = opportunity?.location_type
    ? opportunity?.location_display
      ? `${formatLocationType(opportunity.location_type)}${opportunity?.location_display ? ', ' + opportunity?.location_display : ''}`
      : formatLocationType(opportunity.location_type)
    : '';

  // Create a display string that combines organization name and location
  const displayInfo = `${organizationName}${formattedLocation ? ' • ' + formattedLocation : ''}`;

  return (
    <>
      <section className="pa-container py-6 bg-surface-tertiary">
        {/* Back button */}
        <button
          onClick={() => router.back()}
          className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-6"
        >
          <FontAwesomeIcon icon={faArrowLeft} className="h-4 w-4 mr-1" />
          <span>Back</span>
        </button>

        {/* Header section */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div className="flex items-start">
            {/* Company logo */}
            <div className="bg-white p-4 shadow-sm rounded-lg mr-4 flex-shrink-0">
              {opportunity.organization_logo ? (
                <Image
                  src={opportunity.organization_logo}
                  alt={organizationName || 'Company logo'}
                  width={64}
                  height={64}
                  className="h-16 w-16 object-contain"
                />
              ) : (
                <div className="h-16 w-16 bg-gray-200 rounded flex items-center justify-center text-gray-500">
                  {organizationName?.charAt(0) || 'C'}
                </div>
              )}
            </div>

            {/* Opportunity info */}
            <div className="block space-y-1">
              <span className="pa-eyebrow text-brand-red">{formatType(opportunity.type)}</span>

              <h1 className="text-2xl md:text-3xl font-bold text-text-primary">
                {opportunity.title}
              </h1>
              {/* <p className="text-lg text-gray-700">{organizationName}</p> */}

              {/* <p className="text-sm text-gray-600">
                {getDuration(opportunity.term)} • {formatLocationType(opportunity.location_type)},{' '}
                {opportunity.location}
              </p> */}
              <p className="text-xl font-semibold text-text-secondary">{displayInfo}</p>
            </div>
          </div>

          {/* Action buttons */}
          {!isParentProfile && (
            <div className="flex items-center mt-4 md:mt-0">
              <button
                type="button"
                onClick={() => toggleBookmark(id)}
                className="p-2 mr-3 rounded-lg"
                aria-label={isBookmarked ? 'Remove bookmark' : 'Add bookmark'}
                title={isBookmarked ? 'Remove bookmark' : 'Add bookmark'}
              >
                <span className="sr-only">{isBookmarked ? 'Remove bookmark' : 'Add bookmark'}</span>

                <FontAwesomeIcon
                  icon={isBookmarked ? faBookmarkSolid : faBookmarkRegular}
                  className={`!size-5 ${isBookmarked ? 'text-brand-blue' : 'text-text-secondary'}`}
                />
              </button>

              {/* TODO: When an opportunity "Apply" button is clicked, that needs to count as an impression or "action button clicked" event */}
              <Button
                href={opportunity.apply_url || opportunity.organization?.website}
                color="blue"
                size="small"
                icon={faArrowRight}
                iconPosition="right"
                onClick={() => trackAction(id)}
              >
                Apply
              </Button>
            </div>
          )}
        </div>
      </section>

      <section className="pa-container py-6">
        <div className="grid grid-cols-1 gap-6 mb-10 md:grid-cols-6 xl:grid-cols-5">
          {/* Description */}
          <div className="col-span-1 md:col-span-3 xl:col-span-2">
            <Card className="h-full">
              <CardHeader title="DESCRIPTION" titleIcon={faBriefcase} className="mb-8" />

              <Richtext
                content={opportunity.description || ''}
                className="prose-sm text-text-secondary"
              />
            </Card>
          </div>

          {/* Qualifications */}
          <div className="col-span-1 md:col-span-3">
            <Card className="h-full">
              <CardHeader title="QUALIFICATIONS" titleIcon={faBadgeCheck} className="mb-8" />

              <Richtext
                content={opportunity.qualifications || ''}
                className="prose-sm text-text-secondary"
              />
            </Card>
          </div>

          {/* About */}
          <div className="col-span-1 md:col-span-3 xl:col-span-2">
            <Card className="h-full">
              <CardHeader title="about" titleIcon={faBuilding} className="mb-8" />

              <div className="flex flex-wrap gap-4 items-center">
                {/* Company logo */}
                <div className="flex justify-center mb-4">
                  <div className="p-4 border border-surface-secondary rounded-lg">
                    {opportunity.organization_logo ? (
                      <Image
                        src={opportunity.organization_logo} // add logo
                        alt={organizationName || 'Company logo'}
                        width={0}
                        height={0}
                        className="w-full max-w-28 h-auto object-contain"
                      />
                    ) : (
                      <div className="h-24 w-24 bg-gray-200 rounded flex items-center justify-center text-gray-500 text-2xl">
                        {organizationName?.charAt(0) || 'C'}
                      </div>
                    )}
                  </div>
                </div>

                {/* Company name */}
                <h3 className="text-xl font-semibold text-center mb-4">{organizationName}</h3>
              </div>

              {/* Company description */}
              {opportunity?.organization?.about && (
                <Richtext
                  content={opportunity.organization?.about || ''}
                  className="prose-sm text-text-secondary mb-4"
                />
              )}

              {/* Company website */}
              {opportunity?.organization?.website && (
                <a
                  href={opportunity.organization.website}
                  className="block mb-4 text-text-primary underline transition-colors hover:text-brand-red"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={`Visit ${organizationName} website`}
                >
                  {opportunity.organization.website}
                </a>
              )}

              {/* Interests */}
              {opportunity.interests && opportunity.interests.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium text-gray-900 mb-2">Interests</h4>
                  <div className="flex flex-wrap gap-2">
                    {opportunity.interests.map(interest => (
                      <Tag
                        key={interest.id}
                        label={interest.name}
                        icon={
                          interest.icon || (CAREER_TAG_ICONS[interest.name] ? interest.name : '')
                        }
                      />
                    ))}
                  </div>
                </div>
              )}
            </Card>
          </div>

          <div className="col-span-1 md:col-span-3">
            {/* Responsibilities */}
            <Card>
              <CardHeader title="RESPONSIBILITIES" titleIcon={faListCheck} className="mb-8" />

              <Richtext
                content={opportunity.responsibilities || ''}
                className="prose-sm text-text-secondary"
              />
            </Card>

            {/* Benefits */}
            <Card className="mt-6">
              <CardHeader title="benefits" titleIcon={faHeart} className="mb-8" />

              <Richtext
                content={opportunity.benefits || ''}
                className="prose-sm text-text-secondary"
              />
            </Card>
          </div>
        </div>
      </section>
    </>
  );
}
