'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Configure, InfiniteHits, InstantSearch } from 'react-instantsearch';
import { faSliders } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import OpportunitiesFilterDialog from '@/components/opportunities/OpportunitiesFilterDialog';
import { OpportunitiesFilters } from '@/components/opportunities/OpportunitiesFilters';
import OpportunitiesMeilisearchBox from '@/components/opportunities/OpportunitiesMeilisearchBox';
import OpportunitiesSearchWrapper from '@/components/opportunities/OpportunitiesSearchWrapper';
import { OpportunitiesSponsoredCarousel } from '@/components/opportunities/OpportunitiesSponsoredCarousel';
import OpportunityCard from '@/components/opportunities/OpportunityCard';
import { getAllOpportunities, OPPORTUNITIES_INDEX, searchClient } from '@/config/meilisearch';
import { GeolocationProvider } from '@/context/GeolocationContext';
import useMeiliSearchConnectionTest from '@/hooks/useMeiliSearchConnectionTest';
import { Opportunity } from '@/services/opportunities.service';
import { useOpportunityImpressions } from '@/stores/opportunityImpressions.store';
import { useSidebarStore } from '@/stores/useSidebarStore';
import { fpoSponsoredOpportunities } from '@/views/dashboard/DashboardView';

// Wraps OpportunityCard to track impressions
const TrackedOpportunityCard = ({ hit }: any) => {
  const { trackView } = useOpportunityImpressions();
  const isTracked = useRef(false);

  // Track view when component mounts - only once
  useEffect(() => {
    if (hit && hit.id && !isTracked.current) {
      trackView(hit.id.toString());
      isTracked.current = true;
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Render the original OpportunityCard component
  return <OpportunityCard hit={hit} />;
};

/**
 * Main component for the Opportunities page
 */
export const OpportunitiesView: React.FC = () => {
  const { isExpanded } = useSidebarStore();
  const [showFilters, setShowFilters] = useState(true);
  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false);
  const connectionStatus = useMeiliSearchConnectionTest();
  const [firstOpportunity, setFirstOpportunity] = useState<Opportunity | undefined>(undefined);

  // Get the first opportunity from Meilisearch to pass to our demo component
  useEffect(() => {
    const getFirstOpportunity = async () => {
      try {
        const result = await getAllOpportunities();
        if (result.data?.hits && result.data.hits.length > 0) {
          // Get the first opportunity from the results
          const opportunity = result.data.hits[0] as Opportunity;
          console.log('Found opportunity for demo:', opportunity.id);
          setFirstOpportunity(opportunity);
        }
      } catch (error) {
        console.error('Error getting opportunity for demo:', error);
      }
    };

    if (connectionStatus.success) {
      getFirstOpportunity();
    }
  }, [connectionStatus.success]);

  return (
    <div>
      {/* Use the wrapper to ensure bookmarks are loaded */}
      <OpportunitiesSearchWrapper>
        {/* Wrap everything in GeolocationProvider to make location available */}
        <GeolocationProvider>
          {/* InstantSearch Provider */}
          {/* @ts-ignore - Ignoring type issue with searchClient for now */}
          <InstantSearch searchClient={searchClient} indexName={OPPORTUNITIES_INDEX}>
            {/* Configure the default search parameters - GeoSearchConfig will override when needed */}
            <Configure
              hitsPerPage={12}
              // Default configuration with no geo parameters
              // GeoSearchConfig will add geo parameters when proximity sort is selected
            />

            <div className="min-h-screen bg-surface-secondary flex">
              {/* Filters Sidebar */}
              {showFilters && (
                <div
                  className={clsx(
                    'fixed hidden w-64 border-r border-surface-tertiary bg-white shrink-0 h-screen overflow-y-auto transition-all ease-out duration-300 lg:block',
                    isExpanded ? 'left-40' : 'left-20'
                  )}
                >
                  <div className="mt-36">
                    <OpportunitiesFilters />
                  </div>
                </div>
              )}

              {/* Search Results */}
              <div className="pa-container py-8 flex-1 overflow-x-hidden lg:ml-64">
                <div className="flex items-center flex-wrap gap-4 mb-10">
                  {/* Use custom MeilisearchBox for search */}
                  <div className="flex-1">
                    <OpportunitiesMeilisearchBox />
                  </div>

                  {/* Mobile Filters Drawer Trigger */}
                  <button
                    type="button"
                    className="text-brand-red lg:hidden"
                    aria-label="Open Filters Drawer"
                    onClick={() => setMobileFiltersOpen(true)}
                  >
                    <span className="sr-only">Open Filters Drawer</span>
                    <FontAwesomeIcon icon={faSliders} className="!size-6" aria-hidden="true" />
                  </button>
                </div>

                <div className="mb-6 w-full">
                  <OpportunitiesSponsoredCarousel sponsors={fpoSponsoredOpportunities} autoplay />
                </div>

                {/* Opportunities List using InstantSearch */}
                <div className="grid grid-cols-1 gap-6 mb-8">
                  <InfiniteHits
                    hitComponent={TrackedOpportunityCard}
                    classNames={{
                      list: 'space-y-4',
                      item: 'mb-0',
                      loadMore:
                        'text-sm px-4 py-2 rounded-lg bg-brand-blue flex justify-center mt-8 text-white font-semibold',
                      disabledLoadMore:
                        'pointer-events-none !opacity-10 transition-opacity duration-300 ease-in-out',
                    }}
                    showPrevious={false}
                    translations={{
                      showMoreButtonText: 'Load More Opportunities',
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Mobile Filters Dialog */}
            <OpportunitiesFilterDialog
              isOpen={mobileFiltersOpen}
              onClose={() => setMobileFiltersOpen(false)}
            />
          </InstantSearch>
        </GeolocationProvider>
      </OpportunitiesSearchWrapper>
    </div>
  );
};

export default OpportunitiesView;
