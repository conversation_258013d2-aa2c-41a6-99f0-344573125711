'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { hasHydrated, useAuthStore } from '@/stores/auth.store';

export default function RootPage() {
  const router = useRouter();
  const { isAuthenticated } = useAuthStore();
  const isHydrated = hasHydrated();

  useEffect(() => {
    if (isHydrated) {
      if (isAuthenticated) {
        router.push('/profile');
      } else {
        router.push('/login');
      }
    }
  }, [isAuthenticated, isHydrated, router]);

  // Show loading state while auth store is hydrating
  if (!isHydrated) {
    return <div>Loading...</div>;
  }

  // Return empty fragment while redirect happens
  return <></>;
}
