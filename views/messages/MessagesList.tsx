/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import { useEffect, useRef, useState } from 'react';
import { faArrowLeft, faSpinnerThird } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import clsx from 'clsx';
import ConnectionRequest from '@/components/messaging/ConnectionRequest';
import MessageCard from '@/components/messaging/MessageCard';
import MessageInput from '@/components/messaging/MessageInput';
import MessagesHeader from '@/components/messaging/MessagesHeader';
import Button from '@/components/shared/Button';
import { useAuth } from '@/hooks/useAuth';
import { isDeletedByRecipient, isDeletedBySender, useMessages } from '@/hooks/useMessages';
import { MEDIA_QUERY_DESKTOP, useMediaQuery } from '@/hooks/utils/useMediaQuery';
import { Message, SendMessageRequest } from '@/services/messages.service';
import { ProfileTypes } from '@/stores/auth.store';
import { formatDateTimeLocal } from '@/utils/date-helpers';

interface MessagesListProps {
  selectedUserId: number | null;
  selectedSponsorId: number | null;
  onBackToConversations: () => void;
  onAcceptConnection: (connectionId: number) => void;
  onBlockUser: (userId: number) => void;
  className?: string;
}

export default function MessagesList({
  selectedUserId,
  selectedSponsorId,
  onBackToConversations,
  onAcceptConnection,
  onBlockUser,
  className = '',
}: MessagesListProps) {
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [messageContent, setMessageContent] = useState('');
  const [conversationError, setConversationError] = useState<string | null>(null);

  // Check if mobile view
  const isDesktop = useMediaQuery(MEDIA_QUERY_DESKTOP);
  const isMobileView = !isDesktop;

  // Get current user
  const { user: currentUser, profileType } = useAuth();

  // Get message-related functions from useMessages hook
  const { conversations, useConversation, sendMessage, markAsRead, deleteMessage, editMessage } =
    useMessages();

  // Get conversation data if a user is selected
  const {
    data: conversationData,
    isLoading: isLoadingConversation,
    error: conversationQueryError,
    refetch: refetchConversation,
    isFetching: isFetchingMoreMessages,
    currentPage,
    hasMore,
    isReadOnly,
    loadOlderMessages,
    resetMessages,
  } = useConversation(selectedUserId || 0, selectedSponsorId || undefined);

  // Update conversation error state when query error changes
  useEffect(() => {
    if (conversationQueryError) {
      setConversationError(conversationQueryError.message || 'Failed to load conversation');
    } else {
      setConversationError(null);
    }
  }, [conversationQueryError]);

  // Helper function to scroll to bottom of messages
  const scrollToBottom = () => {
    requestAnimationFrame(() => {
      if (isDesktop && messagesContainerRef.current) {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      } else if (!isDesktop) {
        window.scrollTo(0, document.body.scrollHeight);
      }
    });
  };

  // Handle sending a message
  const handleSendMessage = (content?: string) => {
    const messageToSend = content || messageContent;
    if (!selectedUserId || !messageToSend.trim()) return;

    const messageData: SendMessageRequest = {
      recipientId: selectedUserId,
      content: messageToSend,
      // Add empty arrays for images and videos to match the backend expectations
      images: [],
      videos: [],
    };

    // Send the message regardless of whether it's flagged or not
    // The backend will handle the actual delivery logic
    sendMessage(messageData);
    setMessageContent('');
    // Scroll to bottom after sending a message
    scrollToBottom();
  };

  // Handle marking a message as read
  const handleMarkAsRead = (messageId: number) => {
    markAsRead(messageId);
  };

  // Handle deleting a message
  const handleDeleteMessage = (messageId: number) => {
    deleteMessage(messageId);

    // If we're viewing a conversation, ensure it gets refreshed after deletion
    if (selectedUserId) {
      // Add a small delay to ensure the backend operation completes
      setTimeout(() => {
        refetchConversation();
      }, 300);
    }
  };

  // Handle editing a message
  const handleEditMessage = (messageId: number, content: string) => {
    editMessage(messageId, content);
  };

  // Load older messages
  const handleLoadOlderMessages = () => {
    if (hasMore && !isFetchingMoreMessages) {
      // Remember scroll position before loading more
      const scrollElement = isDesktop ? messagesContainerRef.current : document.documentElement;
      const scrollPosition = scrollElement?.scrollTop || 0;

      // Load older messages
      loadOlderMessages();

      // Store the scroll position to be restored after data loads
      const messagesListElement = isDesktop
        ? messagesContainerRef.current?.querySelector('ul')
        : document.querySelector('ul'); // Assuming only one main ul for messages
      if (messagesListElement) {
        const oldHeight = messagesListElement.scrollHeight;

        // After data loads and DOM updates, restore scroll position
        const restoreScroll = () => {
          if (scrollElement && messagesListElement) {
            const newHeight = messagesListElement.scrollHeight;
            const heightDifference = newHeight - oldHeight;

            // Adjust scroll position by the height difference of new content
            if (heightDifference > 0) {
              scrollElement.scrollTop = scrollPosition + heightDifference;
            }
          }
        };

        // Use a MutationObserver to detect when the DOM is updated with new messages
        const observer = new MutationObserver(() => {
          restoreScroll();
          observer.disconnect();
        });

        observer.observe(messagesListElement, { childList: true, subtree: true });

        // Fallback timer in case MutationObserver doesn't trigger
        setTimeout(() => {
          observer.disconnect();
          restoreScroll();
        }, 500);
      }
    }
  };

  // Restore the initial effect to scroll to bottom on first load
  useEffect(() => {
    if (selectedUserId && conversationData?.messages && !isLoadingConversation) {
      // Only scroll to bottom on first page load or when conversation changes
      if (currentPage === 1) {
        scrollToBottom();
      }
    }
  }, [selectedUserId, conversationData, isLoadingConversation, currentPage]);

  // Add a separate effect to handle new messages being added
  const lastMessageCountRef = useRef(0);

  useEffect(() => {
    const currentMessageCount = conversationData?.messages?.length || 0;

    // If message count increased and we're on page 1, scroll to bottom
    // This handles the case of sending a new message
    if (currentMessageCount > lastMessageCountRef.current && currentPage === 1) {
      scrollToBottom();
    }

    lastMessageCountRef.current = currentMessageCount;
  }, [conversationData?.messages, currentPage]);

  // If no user is selected, show placeholder
  if (!selectedUserId) {
    return (
      <div className={clsx('flex-1 flex items-center justify-center mt-10', className)}>
        <p className="text-gray-500">Select a conversation to start messaging</p>
      </div>
    );
  }

  const isParentProfile = profileType === ProfileTypes.PARENT;

  return (
    <div
      ref={messagesContainerRef}
      className={clsx(
        'flex flex-col overflow-x-hidden',
        {
          'h-full overflow-y-auto': isDesktop,
        },
        className
      )}
    >
      <div className="sticky top-0 z-40 px-4 py-6 border-b bg-surface-tertiary -mx-4 mb-6 lg:px-8 lg:-mx-8 2xl:px-16 2xl:-mx-16">
        {/* Back button for mobile */}
        {isMobileView && (
          <button
            onClick={onBackToConversations}
            className="mb-4 flex items-center text-sm font-medium text-gray-600 hover:text-gray-900"
            aria-label="Back to conversations"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
            <span>Back</span>
          </button>
        )}
        <MessagesHeader
          isLoading={isLoadingConversation}
          error={conversationError}
          messages={conversationData?.messages}
          selectedUserId={selectedUserId}
        />
      </div>

      {/* Check if the conversation has a pending connection status AND the current user is the recipient (not the requester) */}
      {!isLoadingConversation &&
      conversations.find(c => c.otherUserId === selectedUserId)?.connectionStatus === 'pending' &&
      conversations.find(c => c.otherUserId === selectedUserId)?.connectionRequesterId !== null &&
      conversations.find(c => c.otherUserId === selectedUserId)?.connectionRequesterId !==
        currentUser?.id ? (
        <ConnectionRequest
          user={
            conversations.find(c => c.otherUserId === selectedUserId)?.otherUser || {
              id: selectedUserId,
              firstName: 'Unknown',
              lastName: 'User',
              profileImageUrl: null,
            }
          }
          connectionId={
            conversations.find(c => c.otherUserId === selectedUserId)?.connectionId || 0
          }
          message={
            // Create a simplified Message object using the lastMessage text
            conversations.find(c => c.otherUserId === selectedUserId)?.lastMessage
              ? {
                  id: 0, // Use a placeholder ID
                  senderId: selectedUserId,
                  recipientId: 0, // Current user ID will be handled in the component
                  content:
                    conversations.find(c => c.otherUserId === selectedUserId)?.lastMessage || '',
                  readAt: null,
                  createdAt:
                    conversations.find(c => c.otherUserId === selectedUserId)?.lastMessageAt ||
                    new Date().toISOString(),
                  sender:
                    conversations.find(c => c.otherUserId === selectedUserId)?.otherUser || null,
                  recipient: null,
                }
              : null
          }
          disableActions={isParentProfile}
          onAccept={onAcceptConnection}
          onBlock={onBlockUser}
        />
      ) : (
        <>
          {/* Messages */}
          <div className="flex-1">
            {isLoadingConversation ? (
              <div className="flex justify-center p-4">
                <FontAwesomeIcon
                  icon={faSpinnerThird}
                  className="animate-spin size-6 text-gray-500"
                />
              </div>
            ) : conversationError ? (
              <div className="text-red-500 p-4">{conversationError}</div>
            ) : conversationData?.messages?.length === 0 ? (
              <p className="text-center text-gray-500 p-4">No messages yet</p>
            ) : (
              <>
                {/* Load More Button */}
                {hasMore && (
                  <div className="flex justify-center mb-6">
                    <Button
                      size="small"
                      onClick={handleLoadOlderMessages}
                      disabled={isFetchingMoreMessages}
                    >
                      {isFetchingMoreMessages ? (
                        <>
                          <FontAwesomeIcon
                            icon={faSpinnerThird}
                            className="animate-spin mr-2 size-4"
                          />
                          Loading...
                        </>
                      ) : (
                        'Load Older Messages'
                      )}
                    </Button>
                  </div>
                )}

                <ul className="grid grid-cols-4 gap-6 lg:grid-cols-12">
                  {conversationData?.messages?.map((message, index) => {
                    const isFromMe = message.senderId !== selectedUserId;
                    const senderName = `${message.sender?.firstName || ''} ${message.sender?.lastName || ''}`;
                    const recipientName = `${message.recipient?.firstName || ''} ${message.recipient?.lastName || ''}`;

                    // Check if the message has been deleted by the current user
                    const isMessageDeletedByMe =
                      (isFromMe && isDeletedBySender(message)) ||
                      (!isFromMe && isDeletedByRecipient(message));

                    return (
                      <li
                        key={`message-item-${index}-${message.id}`}
                        className={clsx('col-span-3', {
                          'col-start-2 justify-self-end lg:col-span-10 lg:col-start-3': isFromMe,
                          'col-end-4 lg:col-span-10 lg:col-end-11': !isFromMe,
                        })}
                      >
                        {isMessageDeletedByMe ? (
                          <div
                            className={clsx('py-2', {
                              'text-right': isFromMe,
                              'text-left': !isFromMe,
                            })}
                          >
                            <span className="text-gray-500 text-sm">Message Deleted</span>
                            <span className="text-gray-400 text-xs ml-2">
                              {formatDateTimeLocal(message.createdAt)}
                            </span>
                          </div>
                        ) : (
                          <MessageCard
                            message={message}
                            isFromMe={isFromMe}
                            senderName={senderName}
                            recipientName={recipientName}
                            onMarkAsRead={handleMarkAsRead}
                            onDeleteMessage={handleDeleteMessage}
                            onEditMessage={handleEditMessage}
                          />
                        )}
                      </li>
                    );
                  })}
                </ul>
              </>
            )}
          </div>

          {/* Message Input - Only show for non-read-only conversations */}
          {!isReadOnly && (
            <>
              {!isParentProfile && (
                <div className="p-4 w-full mt-6 sticky bottom-0 right-0 z-50">
                  <MessageInput
                    value={messageContent}
                    onChange={e => setMessageContent(e.target.value)}
                    onSend={handleSendMessage}
                    disabled={
                      isLoadingConversation ||
                      !!conversationError ||
                      (conversationData?.connection?.isBlocked ?? false)
                    }
                    placeholder={
                      conversationData?.connection?.isBlocked
                        ? 'You cannot send messages to this user'
                        : 'Type a message...'
                    }
                  />
                </div>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
}
