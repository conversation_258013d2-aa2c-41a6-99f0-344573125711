'use client';

import { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { faSpinnerThird } from '@fortawesome/pro-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { RadioGroup } from '@headlessui/react';
import clsx from 'clsx';
import { debounce } from 'lodash';
import ConversationCard from '@/components/messaging/ConversationCard';
import Button from '@/components/shared/Button';
import SearchInput from '@/components/shared/form/SearchInput';
import { useMessages } from '@/hooks/useMessages';
import { MEDIA_QUERY_DESKTOP, useMediaQuery } from '@/hooks/utils/useMediaQuery';

interface ConversationsListProps {
  onSelectConversation: (userId: number, sponsorId?: number) => void;
  selectedUserId?: number | null;
  onPinConversation: (userId: number) => void;
  onUnpinConversation: (userId: number) => void;
  onBlockUser: (userId: number) => void;
  className?: string;
}

export default function ConversationsList({
  onSelectConversation,
  selectedUserId,
  onPinConversation,
  onUnpinConversation,
  onBlockUser,
  className = '',
}: ConversationsListProps) {
  // Local state for search functionality
  const [inputValue, setInputValue] = useState('');
  const [searchQuery, setSearchQuery] = useState('');

  // Container ref for scroll preservation
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollPositionRef = useRef<{ top: number; height: number } | null>(null);

  // Get conversations data from the useMessages hook
  const {
    conversations,
    isLoadingConversations,
    conversationsError,
    hasMoreConversations,
    isFetchingMoreConversations,
    loadMoreConversations,
    resetConversations,
  } = useMessages();

  const isDesktop = useMediaQuery(MEDIA_QUERY_DESKTOP);

  // Apply debounce to updating the search query
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setSearchQuery(value);
    }, 300),
    []
  );

  // Handle search input change - update input immediately, debounce the search
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value); // Update input field immediately (responsive typing)

    // If clearing the search (empty value), handle immediately
    if (!value.trim()) {
      debouncedSearch.cancel(); // Cancel any pending debounce
      setSearchQuery(''); // Clear search query immediately
      // Don't call resetConversations() as it clears all conversations
    } else {
      debouncedSearch(value); // Debounce the actual search operation for non-empty values
    }
  };

  // Clear search
  const handleClearSearch = () => {
    setInputValue(''); // Clear input field immediately
    debouncedSearch.cancel(); // Cancel any pending debounce
    setSearchQuery(''); // Clear search query immediately
    // Don't call resetConversations() as it clears all conversations
  };

  // Filter conversations based on search query (debounced)
  const filteredConversations = useMemo(() => {
    if (!searchQuery.trim()) {
      return conversations;
    }
    // Split the query into individual terms for more flexible matching
    const searchTerms = searchQuery.toLowerCase().trim().split(/\s+/);
    return conversations.filter(conversation => {
      // Get the text values to search in
      const firstName = conversation.otherUser?.firstName?.toLowerCase() || '';
      const lastName = conversation.otherUser?.lastName?.toLowerCase() || '';
      const fullName = `${firstName} ${lastName}`.toLowerCase();
      const lastMessage = conversation.lastMessage?.toLowerCase() || '';
      // Check if ALL search terms are found somewhere in the relevant fields
      return searchTerms.every(term => {
        return (
          firstName.includes(term) ||
          lastName.includes(term) ||
          fullName.includes(term) ||
          lastMessage.includes(term)
        );
      });
    });
  }, [conversations, searchQuery]);

  // Custom hook to preserve scroll position during renders
  useLayoutEffect(() => {
    const container = isDesktop ? containerRef.current : document.documentElement;
    if (container && scrollPositionRef.current) {
      const newHeight = container.scrollHeight;
      const heightDifference = newHeight - scrollPositionRef.current.height;

      if (heightDifference > 0) {
        // If content was added (height increased), adjust scroll position to keep same relative view
        container.scrollTop = scrollPositionRef.current.top + heightDifference;
      } else {
        // If content height didn't change or decreased, restore previous position
        container.scrollTop = scrollPositionRef.current.top;
      }

      // Clear the scroll position ref after applying
      scrollPositionRef.current = null;
    }
  }, [isDesktop, filteredConversations]);

  // Handle loading more conversations
  const handleLoadMoreConversations = () => {
    if (hasMoreConversations && !isFetchingMoreConversations) {
      // Remember current scroll position and height before loading more
      const container = isDesktop ? containerRef.current : document.documentElement;
      if (container) {
        // Store scroll info in the ref so it survives re-renders
        scrollPositionRef.current = {
          top: container.scrollTop,
          height: container.scrollHeight,
        };

        // Load more conversations - the scroll position will be restored by useLayoutEffect
        loadMoreConversations();
      } else {
        // If container reference isn't available, just load more without scroll handling
        loadMoreConversations();
      }
    }
  };

  return (
    <div
      ref={containerRef}
      className={clsx(
        'overflow-x-hidden',
        {
          'h-full overflow-y-auto': isDesktop,
        },
        className
      )}
    >
      <div className="mb-6">
        <SearchInput
          placeholder="Search conversations"
          value={inputValue} // Use inputValue for the input field
          onChange={handleSearchChange}
        />
      </div>

      {isLoadingConversations && conversations.length === 0 ? (
        <div className="flex justify-center p-4">
          <FontAwesomeIcon icon={faSpinnerThird} className="animate-spin size-6 text-gray-500" />
        </div>
      ) : filteredConversations.length === 0 ? (
        <div className="text-center py-8">
          {conversations.length === 0 ? (
            <p>No conversations found.</p>
          ) : (
            <p>
              No conversations match your search.{' '}
              <button className="text-blue-500 underline" onClick={handleClearSearch}>
                Clear search
              </button>
            </p>
          )}
        </div>
      ) : (
        <RadioGroup
          as="ul"
          className="space-y-4"
          value={selectedUserId?.toString() || ''}
          onChange={value => {
            const userId = parseInt(value, 10);
            const conversation = filteredConversations.find(c => c.otherUserId === userId);

            if (conversation?.isReadonly && conversation?.sponsorUser) {
              onSelectConversation(userId, conversation.sponsorUser.id);
            } else {
              onSelectConversation(userId);
            }
          }}
        >
          {filteredConversations.map((conversation, index) => (
            <li
              key={`conversation-item-${index}-${conversation.otherUserId}`}
              className={clsx('flex items-start gap-4 rounded-4xl transition-colors', {
                'bg-surface-tertiary': selectedUserId === conversation.otherUserId,
                'bg-surface-secondary hover:bg-surface-tertiary':
                  selectedUserId !== conversation.otherUserId,
              })}
            >
              <ConversationCard
                conversation={conversation}
                onSelectConversation={
                  conversation.isReadonly && conversation.sponsorUser
                    ? (userId: number) => {
                        onSelectConversation(userId, conversation.sponsorUser?.id);
                      }
                    : onSelectConversation
                }
                onPinConversation={onPinConversation}
                onUnpinConversation={onUnpinConversation}
                onBlockUser={onBlockUser}
                isReadOnly={conversation.isReadonly}
              />
            </li>
          ))}
        </RadioGroup>
      )}

      {/* Load More Conversations Button */}
      {hasMoreConversations && !searchQuery.trim() && (
        <div className="mt-6 mb-4 flex justify-center">
          <Button
            size="small"
            onClick={handleLoadMoreConversations}
            disabled={isFetchingMoreConversations}
          >
            {isFetchingMoreConversations ? (
              <>
                <FontAwesomeIcon icon={faSpinnerThird} className="animate-spin mr-2 size-4" />
                Loading...
              </>
            ) : (
              'Load More Conversations'
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
