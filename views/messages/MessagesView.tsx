'use client';

import { useState } from 'react';
import clsx from 'clsx';
import { useMessages } from '@/hooks/useMessages';
import { useNetworking } from '@/hooks/useNetworking';
import { MEDIA_QUERY_DESKTOP, useMediaQuery } from '@/hooks/utils/useMediaQuery';
import ConversationsList from './ConversationsList';
import MessagesList from './MessagesList';

export default function MessagesView() {
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [selectedSponsorId, setSelectedSponsorId] = useState<number | null>(null);

  // Use the useMediaQuery hook to detect desktop vs mobile
  const isDesktop = useMediaQuery(MEDIA_QUERY_DESKTOP);
  const isMobileView = !isDesktop;

  // Initialize hooks
  const { pinConversation, unpinConversation, refetchConversations, useConversation } =
    useMessages();

  // Get resetMessages from useConversation
  const { resetMessages } = useConversation(selectedUserId || 0, selectedSponsorId || undefined);

  const {
    acceptConnectionRequest,
    rejectConnectionRequest,
    blockUser,
    unblockUser,
    createConnectionRequest,
  } = useNetworking();

  // Handle selecting a conversation
  const handleSelectConversation = (userId: number, sponsorId?: number) => {
    setSelectedUserId(userId);
    setSelectedSponsorId(sponsorId || null);
    // Reset messages pagination when changing conversations
    resetMessages();
  };

  // Handle going back to conversation list (mobile only)
  const handleBackToConversations = () => {
    setSelectedUserId(null);
    setSelectedSponsorId(null); // Also clear the sponsor ID
  };

  // Handle accepting a connection request
  const handleAcceptConnection = async (connectionId: number) => {
    try {
      await acceptConnectionRequest(connectionId);
      await refetchConversations();
    } catch (error) {
      console.error('Error accepting connection request:', error);
    }
  };

  // Handle pinning a conversation
  const handlePinConversation = (userId: number) => {
    pinConversation(userId);
  };

  // Handle unpinning a conversation
  const handleUnpinConversation = (userId: number) => {
    unpinConversation(userId);
  };

  // Handle blocking a user
  const handleBlockUser = (userId: number) => {
    blockUser(userId);
  };

  return (
    <div className="relative overflow-hidden">
      <div className="relative grid grid-cols-1 lg:grid-cols-2">
        {/* Conversations List - Hidden on mobile when a conversation is selected */}
        <div
          className={clsx('lg:bg-white', {
            hidden: isMobileView && selectedUserId,
            block: !(isMobileView && selectedUserId),
            'h-svh bg-gray-50 border-r': isDesktop,
          })}
        >
          <ConversationsList
            onSelectConversation={handleSelectConversation}
            selectedUserId={selectedUserId}
            onPinConversation={handlePinConversation}
            onUnpinConversation={handleUnpinConversation}
            onBlockUser={handleBlockUser}
            className="px-4 py-6 lg:px-8"
          />
        </div>

        {/* Message List - Hidden on mobile when no conversation is selected */}
        <div
          className={clsx('bg-surface-secondary', {
            hidden: isMobileView && !selectedUserId,
            block: !(isMobileView && !selectedUserId),
            'h-svh': isDesktop,
          })}
        >
          <MessagesList
            selectedUserId={selectedUserId}
            selectedSponsorId={selectedSponsorId}
            onBackToConversations={handleBackToConversations}
            onAcceptConnection={handleAcceptConnection}
            onBlockUser={handleBlockUser}
            className="px-4 pb-6 lg:px-8 2xl:px-16"
          />
        </div>
      </div>
    </div>
  );
}
