'use client';

import { Tab, TabGroup, <PERSON>b<PERSON>ist, Tab<PERSON>anel, Tab<PERSON>anels } from '@headlessui/react';
import { XFactorBrowse } from '@/components/x-factor/XFactorBrowse';
import { XFactorDashboard } from '@/components/x-factor/XFactorDashboard';
import { XFactorHeader } from '@/components/x-factor/XFactorHeader';
import { useXFactorView, XFactorTab } from '@/hooks/x-factor/useXFactorView';

export default function XFactorPage() {
  const { filters, updateFilters } = useXFactorView();

  // Map tab index to activeTab value and vice versa
  const tabIndices = {
    dashboard: 0,
    browse: 1,
    completed: 2,
  };

  const handleTabChange = (index: number) => {
    // Convert index to the corresponding activeTab value
    const tabValues: XFactorTab[] = ['dashboard', 'browse', 'completed'];
    updateFilters({ activeTab: tabValues[index] });
  };

  return (
    <div className="min-h-screen bg-surface-secondary">
      <TabGroup selectedIndex={tabIndices[filters.activeTab]} onChange={handleTabChange}>
        {/* Header with Title and Search */}
        <XFactorHeader
          filters={filters}
          onFilterChange={updateFilters}
          displaySearchBar={filters.activeTab === 'browse'}
        />

        {/* Main Content */}
        <div className="pa-container pt-8 min-h-screen flex-1">
          <div className="transition-opacity duration-200">
            <TabPanels>
              <TabPanel>
                <XFactorDashboard filters={filters} onFilterChange={updateFilters} />
              </TabPanel>

              <TabPanel>
                <XFactorBrowse filters={filters} onFilterChange={updateFilters} />
              </TabPanel>

              <TabPanel>
                <div>Completed courses view (to be implemented)</div>
              </TabPanel>
            </TabPanels>
          </div>
        </div>
      </TabGroup>
    </div>
  );
}
