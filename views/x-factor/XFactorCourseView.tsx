'use client';

import { notFound, useParams } from 'next/navigation';
import { XFactorCourseDetail } from '@/components/x-factor/XFactorCourseDetail';
import { useXFactorCourseDetail } from '@/hooks/x-factor/useXFactorCourseDetail';

export default function XFactorCourseView() {
  const params = useParams();
  const courseId = params?.courseId as string;
  const { course, isLoading, error } = useXFactorCourseDetail(courseId);

  if (error) {
    notFound();
  }

  if (isLoading || !course) {
    return (
      <div className="flex items-center justify-center min-h-[600px]">
        <div className="animate-pulse">Loading...</div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-8">
      <XFactorCourseDetail course={course} />
    </div>
  );
}
