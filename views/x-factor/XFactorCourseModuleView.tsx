'use client';

import { useParams, useRouter } from 'next/navigation';
import XFactorModuleDetail from '@/components/x-factor/XFactorModuleDetail';
import { useXFactorModule } from '@/hooks/x-factor/useXFactorModules';

export default function XFactorCourseModuleView() {
  const params = useParams();
  const router = useRouter();
  const courseId = params?.courseId ? parseInt(params.courseId.toString(), 10) : 0;
  const moduleId = params?.moduleId ? parseInt(params.moduleId.toString(), 10) : 0;

  const { module, courseModule, nextModule, course, isLoading, error } = useXFactorModule(
    moduleId,
    courseId
  );

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    console.error('Error loading module:', error);
    return (
      <div className="max-w-7xl mx-auto py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Error</h1>
          <p className="mt-2 text-gray-600">
            {error instanceof Error ? error.message : 'An error occurred loading the module.'}
          </p>
        </div>
      </div>
    );
  }

  if (!module || !courseModule || !course) {
    console.log('Missing required data:', { module, courseModule, course });
    return (
      <div className="max-w-7xl mx-auto py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Module not found</h1>
          <p className="mt-2 text-gray-600">
            The module you&apos;re looking for doesn&apos;t exist.
          </p>
        </div>
      </div>
    );
  }

  const handleNextModule = () => {
    if (nextModule) {
      router.push(`/x-factor/courses/${courseId}/modules/${nextModule.id}`);
    }
  };

  return (
    <div className="max-w-7xl mx-auto py-8">
      <XFactorModuleDetail
        module={module}
        courseId={courseId}
        courseTitle={course.title}
        moduleOrder={courseModule.order + 1}
        onNextModule={handleNextModule}
        isLastModule={!nextModule}
      />
    </div>
  );
}
