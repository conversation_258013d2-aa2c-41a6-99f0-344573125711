'use client';

import { useParams } from 'next/navigation';
import XFactorModuleDetail from '@/components/x-factor/XFactorModuleDetail';
import { useXFactorModule } from '@/hooks/x-factor/useXFactorModules';

export default function XFactorModuleView() {
  const params = useParams();
  const moduleId = params?.moduleId ? parseInt(params.moduleId as string, 10) : 0;

  const { module, isLoading, error } = useXFactorModule(moduleId);

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-8"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Error</h1>
          <p className="mt-2 text-gray-600">
            {error instanceof Error ? error.message : 'An error occurred loading the module.'}
          </p>
        </div>
      </div>
    );
  }

  if (!module) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900">Module not found</h1>
          <p className="mt-2 text-gray-600">
            The module you&apos;re looking for doesn&apos;t exist.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <XFactorModuleDetail module={module} />
    </div>
  );
}
