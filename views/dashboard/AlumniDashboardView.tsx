'use client';

import { profile } from 'console';
import { useState } from 'react';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import Link from 'next/link';
import { faArrowRight, faUser } from '@fortawesome/free-solid-svg-icons';
import { faHandWave } from '@fortawesome/pro-light-svg-icons';
import { faBriefcase, faListCheck, faShareNodes } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ConnectMessageForm } from '@/components/network/ConnectMessageForm';
import type { Sponsor } from '@/components/opportunities/OpportunitiesSponsoredCarousel';
// import { OpportunitiesSponsoredCarousel } from '@/components/opportunities/OpportunitiesSponsoredCarousel';
import OpportunityCard from '@/components/opportunities/OpportunityCard';
import Avatar from '@/components/shared/Avatar';
import AthleteCard, { CareerInterest } from '@/components/shared/cards/AthleteCard';
import Card from '@/components/shared/cards/Card';
import CardHeader from '@/components/shared/cards/CardHeader';
// import ModuleCarousel from '@/components/shared/interactive/ModuleCarousel';
// import NotificationCarousel from '@/components/shared/NotificationCarousel';
import { XFactorProgress } from '@/components/x-factor/XFactorProgress';
import { useAuth } from '@/hooks/useAuth';
import { useConnectionPermissions } from '@/hooks/useConnectionPermissions';
import { useDashboard } from '@/hooks/useDashboard';
import { useLearningProgress } from '@/hooks/useLearningProgress';
import { usePositiveAthleteProfile } from '@/hooks/usePositiveAthleteProfile';
import type { UserBasicData } from '@/services/networking.service';
import { ProfileTypes } from '@/stores/auth.store';
import { useModalStore } from '@/stores/modal.store';
import {
  fpoNotifications,
  fpoRecommendedConnections,
  fpoRecommendedXFactorModules,
  fpoSponsoredOpportunities,
} from './DashboardView';

const LazyNotificationCarousel = dynamic(() => import('@/components/shared/NotificationCarousel'), {
  ssr: false,
});

const LazyOpportunitiesSponsoredCarousel = dynamic(
  () =>
    import('@/components/opportunities/OpportunitiesSponsoredCarousel').then(
      mod => mod.OpportunitiesSponsoredCarousel
    ),
  {
    ssr: false,
  }
);

const LazyModuleCarousel = dynamic(() => import('@/components/shared/interactive/ModuleCarousel'), {
  ssr: false,
});

// Define types for the interest and sport objects in the API response
interface InterestObject {
  id: number;
  name: string;
  icon?: string;
}

interface SportObject {
  id: number;
  name: string;
}

export default function AlumniDashboardPage() {
  const [selectedThreadId, setSelectedThreadId] = useState<string | null>(null);
  const { open } = useModalStore();
  const checkCanConnect = useConnectionPermissions();
  const { avatarUrl } = usePositiveAthleteProfile();
  const { user, profileType } = useAuth();

  const { investment } = useLearningProgress();

  // Fetch real dashboard data using our new hook
  const {
    recommendedModules,
    isLoadingRecommendedModules,
    recommendedAthletesError,
    recommendedModulesError,

    recommendedAthletes,
    isLoadingRecommendedAthletes,

    recommendedOpportunities,
    isLoadingRecommendedOpportunities,
    recommendedOpportunitiesError,
  } = useDashboard({
    athletesLimit: 4, // Match the current number of displayed athletes
    modulesLimit: 10, // Increased from 5 to 10 to show more modules
    opportunitiesLimit: 4, // Set limit for opportunities
  });

  // Transform recommendedModules into the format expected by ModuleCarousel
  const formattedModules = recommendedModules.map(module => ({
    id: module.id.toString(),
    title: module.title,
    subtitle: module.category,
    coverImage: module.thumbnail,
    href: `/x-factor/modules/${module.id}`,
  }));

  const handleConnect = (user: (typeof fpoRecommendedConnections)[0]) => {
    // Check if the current user can connect with the target user
    const canConnect = checkCanConnect(user.profile_type);

    if (!canConnect) {
      return;
    }

    const recipient: UserBasicData = {
      id: user.id,
      firstName: user.first_name,
      lastName: user.last_name,
      profileImageUrl: user.profile_image_url,
    };

    open(<ConnectMessageForm recipient={recipient} />, 'md');
  };

  // Default investment data if undefined
  const investmentData = investment || {
    hoursSpent: 0,
    modulesCompleted: 0,
    lastActive: null,
    topics: [],
    primaryTopic: undefined,
    primaryTopicPercentage: undefined,
  };

  const isCollegeAthlete = profileType === ProfileTypes.COLLEGE_ATHLETE;
  const isProfessional = profileType === ProfileTypes.PROFESSIONAL;

  return (
    <div className="pa-container py-20 space-y-10">
      {/* Welcome Section */}
      <div className="flex items-center gap-4">
        <Avatar
          src={avatarUrl || undefined}
          firstName={user?.first_name}
          lastName={user?.last_name}
          size="xl"
        />
        <div className="space-y-2">
          <span className="pa-eyebrow text-brand-red">WELCOME!</span>

          <h1 className="text-3xl font-bold text-text-primary">
            {user?.first_name} {user?.last_name}
          </h1>

          <Link
            href="/profile"
            className="flex items-center gap-2 text-text-blue text-sm font-normal"
          >
            Go To Profile
            <FontAwesomeIcon
              icon={faArrowRight}
              className="text-sm text-current"
              aria-hidden="true"
            />
          </Link>
        </div>
      </div>

      {/* Notification Carousel */}
      <LazyNotificationCarousel notifications={fpoNotifications} />

      {/* Recommended Connections */}
      {!isCollegeAthlete && (
        <Card>
          <CardHeader
            title="Recommended Connections"
            titleIcon={faShareNodes}
            customEdit={
              <Link href="/network" className="flex items-center gap-2 text-sm text-brand-red">
                Network
                <FontAwesomeIcon icon={faArrowRight} className="text-sm" aria-hidden="true" />
              </Link>
            }
            className="mb-8"
          />

          <div className="block space-y-4">
            {isLoadingRecommendedAthletes ? (
              <div className="py-4 text-center">Loading recommended connections...</div>
            ) : recommendedAthletesError ? (
              <div className="py-4 text-center text-red-500">
                Error loading recommended connections
              </div>
            ) : recommendedAthletes.length === 0 ? (
              <div className="py-4 text-center">No recommended connections available</div>
            ) : (
              recommendedAthletes.map(athlete => {
                // Extract names from interests and sports objects
                const interestNames = Array.isArray(athlete.interests)
                  ? athlete.interests.map(interest =>
                      typeof interest === 'string' ? interest : (interest as InterestObject).name
                    )
                  : [];

                const sportNames = Array.isArray(athlete.sports)
                  ? athlete.sports.map(sport =>
                      typeof sport === 'string' ? sport : (sport as SportObject).name
                    )
                  : [];

                const connection = {
                  id: athlete.id,
                  first_name: athlete.first_name,
                  last_name: athlete.last_name,
                  profile_type: profileType,
                  profile_image_url: '',
                  state: '',
                  high_school: '',
                  graduation_year: null,
                  career_interests: interestNames,
                };

                const careerInterests: CareerInterest[] = interestNames.map(interest => ({
                  name: interest,
                  count: 1,
                }));

                return (
                  <AthleteCard
                    key={athlete.id}
                    id={athlete.id.toString()}
                    name={athlete.name}
                    graduationYear={''}
                    location={''}
                    highSchool={athlete.school}
                    avatar={athlete?.avatar || ''}
                    careerInterests={careerInterests}
                    onConnect={() => handleConnect(connection as any)}
                    canConnect={checkCanConnect(profileType)}
                  />
                );
              })
            )}
          </div>
        </Card>
      )}

      {/* Recommended Opportunities */}
      {!isProfessional && (
        <Card>
          <CardHeader
            title="Recommended Opportunities"
            titleIcon={faBriefcase}
            customEdit={
              <Link
                href="/opportunities"
                className="flex items-center gap-2 text-sm text-brand-red"
              >
                Opportunities
                <FontAwesomeIcon icon={faArrowRight} className="text-sm" aria-hidden="true" />
              </Link>
            }
            className="mb-8"
          />

          <div className="block space-y-4">
            {isLoadingRecommendedOpportunities ? (
              <div className="py-4 text-center">Loading recommended opportunities...</div>
            ) : recommendedOpportunitiesError ? (
              <div className="py-4 text-center text-red-500">
                Error loading recommended opportunities
              </div>
            ) : recommendedOpportunities.length === 0 ? (
              <div className="py-4 text-center">No recommended opportunities available</div>
            ) : (
              recommendedOpportunities.map(opportunity => (
                <OpportunityCard key={opportunity.id} opportunity={opportunity} />
              ))
            )}
          </div>
        </Card>
      )}

      {/* FPO Sponsored Opportunities Carousel */}
      <LazyOpportunitiesSponsoredCarousel sponsors={fpoSponsoredOpportunities} />

      <div className="pa-profile-grid">
        <div className="pa-profile-grid-left">
          <XFactorProgress
            title="x factor Learning Time"
            headerContent={
              <Link
                href="/x-factor?tab=browse"
                className="pl-4 flex items-center gap-2 text-sm text-brand-red"
              >
                Insights
                <FontAwesomeIcon icon={faArrowRight} className="text-sm" aria-hidden="true" />
              </Link>
            }
            topics={investmentData.topics}
            primaryTopic={investmentData.primaryTopic}
            primaryTopicPercentage={investmentData.primaryTopicPercentage}
            displayAside={false}
          />
        </div>

        <div className="pa-profile-grid-right">
          {/* Featured Modules Carousel */}
          <Card>
            {isLoadingRecommendedModules ? (
              <div className="py-8 text-center">Loading recommended modules...</div>
            ) : recommendedModulesError ? (
              <div className="py-8 text-center text-red-500">Error loading recommended modules</div>
            ) : (
              <LazyModuleCarousel
                overline="Recommended X FACTOR MODULES"
                titleIcon={faListCheck}
                modules={formattedModules}
                navigationContent={
                  <Link
                    href="/x-factor?tab=browse"
                    className="pl-4 flex items-center gap-2 text-sm text-brand-red"
                  >
                    Browse
                    <FontAwesomeIcon icon={faArrowRight} className="text-sm" aria-hidden="true" />
                  </Link>
                }
                className="mb-8"
              />
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}
