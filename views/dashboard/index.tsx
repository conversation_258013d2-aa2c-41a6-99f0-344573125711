'use client';

import { useAuth } from '@/hooks/useAuth';
import { ProfileTypes } from '@/stores/auth.store';
import AlumniDashboardView from './AlumniDashboardView';
import DashboardView from './DashboardView';

export default function DashboardPageSelector() {
  const { profileType } = useAuth();

  if (profileType === ProfileTypes.COLLEGE_ATHLETE || profileType === ProfileTypes.PROFESSIONAL) {
    return <AlumniDashboardView />;
  }

  return <DashboardView />;
}
