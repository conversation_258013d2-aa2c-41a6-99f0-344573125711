export const BADGE_NAME_TO_FILENAME_MAP: Record<string, string> = {
  Rookie: '01_rookie',
  JV: '02_jv',
  Varsity: '03_varsity',
  Starter: '04_starter',
  Playmaker: '05_playmaker',
  'Vice-Captain': '06_vice_captain',
  Captain: '07_captain',
  MVP: '08_mvp',
  'All-State': '09_all_state',
  Champion: '10_champion',
  Legend: '11_legend',
  'Hall of Fame': '12_hall_of_fame',
  'Goat 1': '13_goat_1',
  'Goat 2': '14_goat_2',
  'Goat 3': '15_goat_3',
  'Goat 4': '16_goat_4',
  'Goat 5': '17_goat_5',
};

/**
 * Gets the image path for an achieved badge.
 * Assumes badges displayed on the leaderboard are achieved.
 * @param badgeName The name of the badge from the API.
 * @returns The path to the badge image (e.g., /images/badges/01_rookie_achieved.png) or undefined if no mapping exists.
 */
export function getAchievedBadgeImagePath(
  badgeName: string | null | undefined
): string | undefined {
  if (!badgeName) {
    return undefined;
  }

  const fileIdentifier = BADGE_NAME_TO_FILENAME_MAP[badgeName];

  if (fileIdentifier) {
    return `/images/badges/${fileIdentifier}_achieved.png`;
  }

  // Optional: Fallback to a default image or return undefined
  // console.warn(`No badge image mapping found for: ${badgeName}`);
  return undefined; // Or a path to a default/placeholder badge image if you have one
}
