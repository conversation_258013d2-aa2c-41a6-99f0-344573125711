/**
 * Utility functions for working with sports
 */

import { normalizeSportName, SportName } from '@/types/sports';

// Define fallback image mappings for all sports from the backend
const sportImageMappings: Record<SportName, string> = {
  [SportName.FOOTBALL]: 'football',
  [SportName.BASKETBALL]: 'basketball',
  [SportName.BASEBALL]: 'baseball',
  [SportName.SOFTBALL]: 'baseball', // Use baseball image for softball
  [SportName.SOCCER]: 'soccer',
  [SportName.TRACK_AND_FIELD]: 'track_field',
  [SportName.CROSS_COUNTRY]: 'track_field', // Use track & field for cross country
  [SportName.VOLLEYBALL]: 'volleyball',
  [SportName.TENNIS]: 'tennis',
  [SportName.GOLF]: 'golf',
  [SportName.WRESTLING]: 'weightlifting', // Use weightlifting for wrestling
  [SportName.SWIMMING_AND_DIVING]: 'volleyball', // Use volleyball for swimming
  [SportName.LACROSSE]: 'lacrosse',
  [SportName.FIELD_HOCKEY]: 'field_hockey',
  [SportName.ICE_HOCKEY]: 'ice_hockey',
  [SportName.CHEERLEADING]: 'default', // Use default for cheerleading
  [SportName.DANCE]: 'default', // Use default for dance
  [SportName.GYMNASTICS]: 'default', // Use default for gymnastics
  [SportName.WATER_POLO]: 'volleyball', // Use volleyball for water polo
  [SportName.ROWING]: 'default', // Use default for rowing
  [SportName.BOWLING]: 'bowling',
  [SportName.ULTIMATE_FRISBEE]: 'default', // Use default for ultimate frisbee
  [SportName.RUGBY]: 'rugby',
  [SportName.SKIING]: 'skiing_snowboarding',
  [SportName.SNOWBOARDING]: 'skiing_snowboarding',
  [SportName.MOUNTAIN_BIKING]: 'default', // Use default for mountain biking
  [SportName.CYCLING]: 'default', // Use default for cycling
  [SportName.ARCHERY]: 'shooting', // Use shooting for archery
  [SportName.BADMINTON]: 'badminton',
  [SportName.FENCING]: 'default', // Use default for fencing

  // Frontend-specific sports
  [SportName.SKIING_SNOWBOARDING]: 'skiing_snowboarding',
  [SportName.FLAG_FOOTBALL]: 'football',
  [SportName.ESPORTS]: 'esports',
  [SportName.TABLE_TENNIS]: 'table_tennis',
  [SportName.WEIGHTLIFTING]: 'weightlifting',
  [SportName.SHOOTING]: 'shooting',
  [SportName.HANDBALL]: 'volleyball',
  [SportName.EQUESTRIAN]: 'equestrian',
  [SportName.CRICKET]: 'cricket',
};

// Additional mappings for alternative spellings or variations
const alternativeNames: Record<string, string> = {
  'e-sports': 'esports',
  e_sports: 'esports',
  'track_&_field': 'track_field',
  'swimming_&_diving': 'volleyball',
  crosscountry: 'track_field', // Legacy support for cross country
};

/**
 * Maps a sport name to its corresponding card image path for fallback
 */
export const getSportCardImagePath = (sportName: string): string => {
  if (!sportName) {
    console.warn('getSportCardImagePath: No sport name provided, using default');
    return '/images/sports/card_default_360x280.png';
  }

  try {
    // Try to normalize the sport name first
    const normalizedSport = normalizeSportName(sportName);

    // If we can directly map to the SportName enum, use our main mapping
    if (
      typeof normalizedSport === 'string' &&
      Object.values(SportName).includes(normalizedSport as SportName)
    ) {
      const mappedImage = sportImageMappings[normalizedSport as SportName];
      // console.log(
      //   `Sport mapped directly: "${sportName}" → "${normalizedSport}" → "${mappedImage}"`
      // );
      return `/images/sports/card_${mappedImage}_360x280.png`;
    }

    // Otherwise, try lowercase mapping with underscore replacement
    const lowerCaseSport = (normalizedSport || sportName).toLowerCase().replace(/\s+/g, '_');

    // Check for alternative names
    if (alternativeNames[lowerCaseSport]) {
      // console.log(
      //   `Sport mapped via alternative: "${sportName}" → "${lowerCaseSport}" → "${alternativeNames[lowerCaseSport]}"`
      // );
      return `/images/sports/card_${alternativeNames[lowerCaseSport]}_360x280.png`;
    }

    // As a last resort, try to find a direct match in the SportName enum values
    for (const enumValue of Object.values(SportName)) {
      if (enumValue.toLowerCase().replace(/\s+/g, '_') === lowerCaseSport) {
        const mappedImage = sportImageMappings[enumValue];
        // console.log(
        //   `Sport mapped via enum lookup: "${sportName}" → "${lowerCaseSport}" → "${enumValue}" → "${mappedImage}"`
        // );
        return `/images/sports/card_${mappedImage}_360x280.png`;
      }
    }

    // If all else fails, use the default image
    console.warn(`No image mapping found for sport: "${sportName}", using default`);
    return '/images/sports/card_default_360x280.png';
  } catch (error) {
    console.error(`Error mapping sport image for "${sportName}":`, error);
    return '/images/sports/card_default_360x280.png';
  }
};

/**
 * Maps a sport name to its corresponding background image path for fallback
 */
export const getSportBackgroundImagePath = (sportName: string): string => {
  try {
    // Get the card image path and then transform it for background
    const cardPath = getSportCardImagePath(sportName);

    // Replace "card_" with "background_" and the dimensions
    return cardPath.replace('card_', 'background_').replace('360x280', '700x400');
  } catch (error) {
    console.error(`Error mapping sport background for "${sportName}":`, error);
    return '/images/sports/background_default_700x400.png';
  }
};
