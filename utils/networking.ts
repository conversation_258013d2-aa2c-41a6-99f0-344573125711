import { ProfileType } from '@/stores/auth.store';

/**
 * Determines if a user can connect with another user based on their profile types
 *
 * Business Rules:
 * 1. Users cannot send messages to users who have blocked them (handled server-side)
 * 2. Adult-to-minor communication is restricted:
 *    - Adults cannot communicate with high school students
 *    - Adults can network and communicate with other adults
 *    - Athletes can network and communicate with other athletes
 *    - EXCEPTION: Sponsors can communicate with athletes
 *
 * @param currentUserProfileType The profile type of the current user
 * @param targetUserProfileType The profile type of the target user
 * @returns boolean indicating if the current user can connect with the target user
 */
export const canUserConnect = (
  currentUserProfileType: ProfileType | null,
  targetUserProfileType: string | null
): boolean => {
  // If either profile type is null, connection is not allowed
  if (!currentUserProfileType || !targetUserProfileType) {
    return false;
  }

  // Check if current user is a positive_athlete (minor/high school student)
  const isCurrentUserPositiveAthlete = currentUserProfileType === 'positive_athlete';

  // Check if target user is a positive_athlete (minor/high school student)
  const isTargetUserPositiveAthlete = targetUserProfileType === 'positive_athlete';

  // Check if current user is a sponsor
  const isCurrentUserSponsor = currentUserProfileType === 'sponsor';

  // Check if target user is a sponsor
  const isTargetUserSponsor = targetUserProfileType === 'sponsor';

  // EXCEPTION: Sponsors can connect with athletes and vice versa
  if (isCurrentUserSponsor && isTargetUserPositiveAthlete) {
    return true;
  }

  if (isCurrentUserPositiveAthlete && isTargetUserSponsor) {
    return true;
  }

  // If current user is a positive_athlete, they can only connect with other positive_athletes
  if (isCurrentUserPositiveAthlete) {
    return isTargetUserPositiveAthlete;
  }

  // If target user is a positive_athlete, only other positive_athletes can connect with them
  if (isTargetUserPositiveAthlete) {
    return isCurrentUserPositiveAthlete;
  }

  // For all other combinations (adult-to-adult), connection is allowed
  return true;
};
