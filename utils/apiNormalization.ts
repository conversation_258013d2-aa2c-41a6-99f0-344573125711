/**
 * Utility functions for normalizing data between frontend and API
 */

/**
 * Converts snake_case keys to camelCase
 * @param str String in snake_case
 * @returns String in camelCase
 */
export const snakeToCamel = (str: string): string => {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
};

/**
 * Converts camelCase keys to snake_case
 * @param str String in camelCase
 * @returns String in snake_case
 */
export const camelToSnake = (str: string): string => {
  return str.replace(/([A-Z])/g, (_, letter) => `_${letter.toLowerCase()}`);
};

/**
 * Normalizes an object by converting snake_case keys to camelCase
 * @param obj Object with snake_case keys
 * @returns Object with camelCase keys
 */
export const normalizeToApiFormat = <T extends Record<string, any>>(
  obj: T
): Record<string, any> => {
  const result: Record<string, any> = {};

  // Process each key in the original object
  Object.keys(obj).forEach(key => {
    // Skip empty or null values
    if (obj[key] === null || obj[key] === undefined || obj[key] === '') {
      return;
    }

    // Convert the key to camelCase
    const camelKey = snakeToCamel(key);

    // Copy the value (recursively process objects)
    if (typeof obj[key] === 'object' && !Array.isArray(obj[key]) && obj[key] !== null) {
      result[camelKey] = normalizeToApiFormat(obj[key]);
    } else {
      result[camelKey] = obj[key];
    }
  });

  return result;
};

/**
 * Key mappings for special cases where automatic conversion isn't sufficient
 */
export const fieldMappings: Record<string, string> = {
  // Form to API field mappings
  location_type: 'locationType',
  action_button_link: 'applyUrl',
  organization_name: 'organizationName',
  preferred_graduation_year: 'preferredGraduationYear',
  preferred_states: 'preferredStates',
  // New visibility settings fields
  preferred_graduation_year_start: 'preferredGraduationYearStart',
  preferred_graduation_year_end: 'preferredGraduationYearEnd',
  // Fix for specific issues
  category: 'subtype', // Map Category field to subtype in the backend
  term: 'term', // Explicitly ensure term field is passed through
  location: 'location', // Ensure location maps to location in database
  // Add more as needed
};

/**
 * Converts a form data object to API-ready format with proper casing and field mappings
 * @param formData The original form data with potential snake_case keys
 * @returns API-ready data with normalized keys
 */
export const prepareFormDataForApi = <T extends Record<string, any>>(
  formData: T
): Record<string, any> => {
  const result: Record<string, any> = {};

  if (!formData || typeof formData !== 'object') {
    console.warn('Invalid form data provided to prepareFormDataForApi:', formData);
    return {};
  }

  // Process each key in the form data
  Object.keys(formData).forEach(key => {
    try {
      // Use explicit mapping if available, otherwise convert to camelCase
      const apiKey = fieldMappings[key] || snakeToCamel(key);

      // Only include non-empty values
      if (formData[key] !== null && formData[key] !== undefined && formData[key] !== '') {
        result[apiKey] = formData[key];
      }
    } catch (error) {
      console.error(`Error normalizing field "${key}":`, error);
      // Keep the original key in case of error
      result[key] = formData[key];
    }
  });

  return result;
};
