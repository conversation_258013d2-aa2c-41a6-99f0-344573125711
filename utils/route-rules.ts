import { ProfileType, ProfileTypes } from '@/stores/auth.store';

// Routes that require authentication
export const protectedRoutes = [
  '/dashboard',
  '/profile',
  '/settings',
  '/x-factor',
  '/network',
  '/opportunities',
  '/messages',
  '/account',
  '/postings',
  '/my-school',
] as const;

// Routes only accessible when NOT authenticated
export const publicOnlyRoutes = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
] as const;

// Routes accessible during onboarding
export const onboardingRoutes = ['/onboarding'] as const;

// Define route permissions for each profile type
type RoutePermissions = {
  [K in keyof typeof ProfileTypes]?: string[];
};

// Routes each profile type can access
export const profileRoutePermissions: RoutePermissions = {
  POSITIVE_ATHLETE: [
    '/dashboard',
    '/profile',
    '/settings',
    '/x-factor',
    '/network',
    '/opportunities',
    '/messages',
    '/account',
    // '/my-school',
  ],
  POSITIVE_COACH: [
    '/dashboard',
    '/profile',
    '/settings',
    '/x-factor',
    '/network',
    '/opportunities',
    '/messages',
    '/account',
  ],
  COLLEGE_ATHLETE: [
    '/dashboard',
    '/profile',
    '/settings',
    '/x-factor',
    '/network',
    '/opportunities',
    '/messages',
    '/account',
  ],
  PROFESSIONAL: [
    '/dashboard',
    '/profile',
    '/settings',
    '/x-factor',
    '/network',
    '/opportunities',
    '/messages',
    '/account',
  ],
  SPONSOR: ['/network', '/postings', '/messages', '/account', '/settings'],
  ADMIN: [
    '/dashboard',
    '/profile',
    '/settings',
    '/x-factor',
    '/network',
    '/opportunities',
    '/messages',
    '/account',
    '/postings',
    '/admin',
  ],
  ATHLETICS_DIRECTOR: ['/profile', '/settings', '/messages', '/account', '/my-school'],
  PARENT: ['/profile', '/network', '/opportunities', '/settings', '/messages', '/account'],
  // Add other profile types as needed
};

/**
 * Default routes based on profile type
 *
 * This function serves two important purposes:
 * 1. It provides the initial landing route when a user logs in
 * 2. It serves as the fallback route when a user attempts to access a route they don't have permission for
 *
 * The routes defined here should always be accessible to the corresponding profile type
 * (make sure they're included in profileRoutePermissions)
 */
export const getDefaultRoute = (profileType: ProfileType): string => {
  switch (profileType) {
    case ProfileTypes.POSITIVE_ATHLETE:
      return '/profile';
    case ProfileTypes.POSITIVE_COACH:
      return '/profile';
    case ProfileTypes.ADMIN:
      return '/account';
    case ProfileTypes.PARENT:
      return '/profile';
    case ProfileTypes.SPONSOR:
      return '/network';
    case ProfileTypes.ATHLETICS_DIRECTOR:
      return '/my-school';
    // Add other profile types as needed
    default:
      return '/dashboard';
  }
};

// Helper to check if a path matches any of the routes
export const matchesRoute = (pathname: string, routes: readonly string[]): boolean => {
  return routes.some(route => pathname.startsWith(route));
};

// Check if a profile type has permission to access a route
export const hasRoutePermission = (profileType: ProfileType, pathname: string): boolean => {
  if (!profileType) return false;

  // Convert string profile type to key in ProfileTypes
  const profileKey = Object.entries(ProfileTypes).find(
    ([_, value]) => value === profileType
  )?.[0] as keyof typeof ProfileTypes | undefined;

  if (!profileKey || !profileRoutePermissions[profileKey]) return false;

  return profileRoutePermissions[profileKey]!.some(route => pathname.startsWith(route));
};

// Get all allowed routes for a profile type
export const getAllowedRoutes = (profileType: ProfileType): string[] => {
  if (!profileType) return [];

  const profileKey = Object.entries(ProfileTypes).find(
    ([_, value]) => value === profileType
  )?.[0] as keyof typeof ProfileTypes | undefined;

  return profileKey && profileRoutePermissions[profileKey]
    ? profileRoutePermissions[profileKey]!
    : [];
};
