/**
 * Industry mapping utilities
 */
import {
  INDUSTRY_ID_TO_NAME_MAP,
  INDUSTRY_IDS,
  INDUSTRY_NAME_TO_ID_MAP,
  IndustryId,
  IndustryName,
} from '@/constants/industryEnums';

/**
 * Maps industry name to industry ID
 * @param industryName The name of the industry
 * @returns The ID of the industry or 1 if not found
 */
export const mapIndustryNameToId = (industryName: string): number => {
  // Check if the name is in our mapping
  if (industryName in INDUSTRY_NAME_TO_ID_MAP) {
    return INDUSTRY_NAME_TO_ID_MAP[industryName as IndustryName];
  }

  // Default to Construction if not found
  return INDUSTRY_IDS.CONSTRUCTION;
};

/**
 * Maps industry ID to industry name
 * @param industryId The ID of the industry
 * @returns The name of the industry or 'Construction' if not found
 */
export const mapIndustryIdToName = (industryId: number): string => {
  // Check if the ID is in our mapping
  if (industryId in INDUSTRY_ID_TO_NAME_MAP) {
    return INDUSTRY_ID_TO_NAME_MAP[industryId as IndustryId];
  }

  // Default to Construction if not found
  return INDUSTRY_ID_TO_NAME_MAP[INDUSTRY_IDS.CONSTRUCTION];
};

/**
 * Returns all available industries as an array of objects with id and name
 */
export const getAllIndustries = () => {
  return Object.entries(INDUSTRY_ID_TO_NAME_MAP).map(([id, name]) => ({
    id: Number(id),
    name,
  }));
};
