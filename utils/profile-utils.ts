import {
  positiveAthlete<PERSON>rofile<PERSON>ab<PERSON><PERSON>ist,
  positiveCoachProfileTabsList,
} from '@/components/profile/shared/constants';
import { ProfileType, ProfileTypes } from '@/stores/auth.store';

/**
 * Returns the appropriate tab list based on profile type
 */
export const getTabListItems = (profileType: ProfileType) => {
  switch (profileType) {
    case ProfileTypes.POSITIVE_COACH:
    case ProfileTypes.COACH:
    case ProfileTypes.TEAM_COACH:
      return positiveCoachProfileTabsList;

    case ProfileTypes.POSITIVE_ATHLETE:
      return positiveAthleteProfileTabsList;
    case ProfileTypes.ATHLETICS_DIRECTOR:
    case ProfileTypes.SPONSOR:
    case ProfileTypes.UTILITY:
    case ProfileTypes.PARENT:
    case ProfileTypes.ADMIN:
    case ProfileTypes.COLLEGE_ATHLETE:
    case ProfileTypes.PROFESSIONAL:
    case ProfileTypes.TEAM_STUDENT:
    case ProfileTypes.ATHLETE:
      return positiveAthleteProfileTabsList;
    default:
      return positiveAthleteProfileTabsList;
  }
};
