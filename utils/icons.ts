import { IconProp } from '@fortawesome/fontawesome-svg-core';
import {
  faBolt,
  faBullhorn,
  faCar,
  faChartLine,
  faCode,
  faComments,
  faDesktop,
  faEllipsis,
  faFlask,
  faGavel,
  faGraduationCap,
  faHandshake,
  faHardHat,
  faHashtag,
  faHeart,
  faHeartPulse,
  faHotel,
  faIndustry,
  faLandmark,
  faMedal,
  faMoneyBill,
  faPalette,
  faQuestion,
  faRulerCombined,
  faShield,
  faStethoscope,
  faTractor,
  faTrophy,
  faTruck,
  faUsers,
  faUtensils,
  faVideo,
  faWhistle,
  faWrench,
} from '@fortawesome/pro-solid-svg-icons';

const iconMap: { [key: string]: IconProp } = {
  tractor: faTractor,
  'ruler-combined': faRulerCombined,
  palette: faPalette,
  video: faVideo,
  car: faCar,
  'chart-line': faChartLine,
  whistle: faWhistle,
  comments: faComments,
  'hard-hat': faHardHat,
  utensils: faUtensils,
  'graduation-cap': faGraduationCap,
  bolt: faBolt,
  'money-bill': faMoneyBill,
  landmark: faLandmark,
  'heart-pulse': faHeartPulse,
  hotel: faHotel,
  users: faUsers,
  desktop: faDesktop,
  wrench: faWrench,
  gavel: faGavel,
  shield: faShield,
  industry: faIndustry,
  bullhorn: faBullhorn,
  medal: faMedal,
  stethoscope: faStethoscope,
  handshake: faHandshake,
  flask: faFlask,
  hashtag: faHashtag,
  heart: faHeart,
  trophy: faTrophy,
  code: faCode,
  truck: faTruck,
  question: faQuestion,
  ellipsis: faEllipsis,
};

export const getInterestIcon = (iconName: string | null): IconProp => {
  // Remove 'fa-' prefix if it exists
  const cleanIconName = (iconName || '').replace('fa-', '');
  return iconMap[cleanIconName] || faHashtag;
};
