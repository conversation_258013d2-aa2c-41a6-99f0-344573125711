/**
 * Utility functions for working with badges
 */

/**
 * Maps a badge name and achievement status to the corresponding static fallback image path
 */
export const getBadgeFallbackImagePath = (name: string, isAchieved: boolean): string => {
  // Normalize the badge name by converting hyphens to spaces
  // This handles cases like 'Vice-Captain' -> 'Vice Captain' and 'All-State' -> 'All State'
  const normalizedName = name.replace(/-/g, ' ');

  // Map badge names to their file number prefixes and exact filenames
  const badgeMapping: Record<string, { prefix: string; filename: string }> = {
    Rookie: { prefix: '01', filename: 'rookie' },
    JV: { prefix: '02', filename: 'jv' },
    Varsity: { prefix: '03', filename: 'varsity' },
    Starter: { prefix: '04', filename: 'starter' },
    Playmaker: { prefix: '05', filename: 'playmaker' },
    'Vice Captain': { prefix: '06', filename: 'vice_captain' },
    Captain: { prefix: '07', filename: 'captain' },
    MVP: { prefix: '08', filename: 'mvp' },
    'All State': { prefix: '09', filename: 'all_state' },
    Champion: { prefix: '10', filename: 'champion' },
    Legend: { prefix: '11', filename: 'legend' },
    'Hall of Fame': { prefix: '12', filename: 'hall_of_fame' },
    'GOAT Level 1': { prefix: '13', filename: 'goat_1' },
    'GOAT Level 2': { prefix: '14', filename: 'goat_2' },
    'GOAT Level 3': { prefix: '15', filename: 'goat_3' },
    'GOAT Level 4': { prefix: '16', filename: 'goat_4' },
    'GOAT Level 5': { prefix: '17', filename: 'goat_5' },
  };

  // Get the mapping for the badge name using the normalized name
  const mapping = badgeMapping[normalizedName] || {
    prefix: '',
    filename: normalizedName.toLowerCase().replace(/\s+/g, '_'),
  };

  // Add debug logging to help troubleshoot badge mapping issues
  console.log(
    `Badge mapping: "${name}" -> normalized: "${normalizedName}" -> file prefix: "${mapping.prefix}"`
  );

  // Handle special case for Starter's typo in "Unchieved"
  const statusText = isAchieved
    ? 'achieved'
    : normalizedName === 'Starter'
      ? 'unchieved'
      : 'unachieved';

  // Construct the path to the static image with the new naming format
  return `/images/badges/${mapping.prefix}_${mapping.filename}_${statusText}.png`;
};
