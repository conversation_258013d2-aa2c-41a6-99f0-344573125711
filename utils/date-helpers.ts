import { format, isSameYear, isToday, isYesterday } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';

/**
 * Formats a date string or Date object into the user's local timezone.
 * Falls back to 'America/New_York' if the local timezone cannot be determined.
 * Provides contextual formatting (e.g., "Today", "Yesterday").
 *
 * @param dateInput - The date string (assumed UTC) or Date object.
 * @param includeTime - Whether to include the time in the format (defaults to true).
 * @returns The formatted date string.
 */
export function formatDateTimeLocal(dateInput: string | Date, includeTime: boolean = true): string {
  const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
  let userTimeZone: string;

  try {
    userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (e) {
    console.warn('Could not determine user timezone, falling back to America/New_York.', e);
    userTimeZone = 'America/New_York'; // Fallback timezone
  }

  const timeFormat = 'h:mm a';
  const dateFormatShort = 'MMM d';
  const dateFormatLong = 'MMM d, yyyy';

  const formatWithTime = (baseFormat: string) => `${baseFormat}, ${timeFormat}`;

  if (!includeTime) {
    if (isSameYear(date, new Date())) {
      return formatInTimeZone(date, userTimeZone, dateFormatShort);
    } else {
      return formatInTimeZone(date, userTimeZone, dateFormatLong);
    }
  }

  // Contextual formatting for dates with time
  if (isToday(date)) {
    return formatInTimeZone(date, userTimeZone, timeFormat);
  } else if (isYesterday(date)) {
    return `Yesterday, ${formatInTimeZone(date, userTimeZone, timeFormat)}`;
  } else if (isSameYear(date, new Date())) {
    return formatInTimeZone(date, userTimeZone, formatWithTime(dateFormatShort));
  } else {
    return formatInTimeZone(date, userTimeZone, formatWithTime(dateFormatLong));
  }
}

/**
 * Formats a date string or Date object into the user's local timezone
 * using the 'M/dd/yy • h:mm a' format.
 *
 * @param dateInput - The date string (assumed UTC) or Date object.
 * @returns The formatted date string.
 */
export function formatDateTimeSimple(dateInput: string | Date): string {
  const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
  let userTimeZone: string;

  try {
    userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (e) {
    console.warn('Could not determine user timezone, falling back to America/New_York.', e);
    userTimeZone = 'America/New_York'; // Fallback timezone
  }

  return formatInTimeZone(date, userTimeZone, 'M/dd/yy • h:mm a');
}
