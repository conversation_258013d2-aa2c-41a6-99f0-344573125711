import { ProfileType, ProfileTypes } from '@/stores/auth.store';

export const getPostLoginRedirect = (profileType: ProfileType): string => {
  switch (profileType) {
    case ProfileTypes.POSITIVE_ATHLETE:
      return '/profile'; // Positive athlete goes to their profile
    case ProfileTypes.POSITIVE_COACH:
      return '/profile'; // Future: coach dashboard
    case ProfileTypes.ADMIN:
      return '/account'; // Future: admin dashboard
    case ProfileTypes.PARENT:
      return '/profile'; // Future: parent dashboard
    case ProfileTypes.SPONSOR:
      return '/network'; // Future: sponsor dashboard
    case ProfileTypes.ATHLETICS_DIRECTOR:
      return '/my-school'; // Future: athletic director dashboard
    // Add more cases as needed
    default:
      return '/dashboard'; // Default fallback
  }
};
