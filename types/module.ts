import type { Question } from '@/services/x-factor-exam.service';
import type { TestAttempt } from '@/types/exam';

export interface Test {
  id: number;
  moduleId: number;
  type: 'quiz' | 'exam';
  status: 'not_started' | 'in_progress' | 'completed' | 'expired' | 'pending_review' | 'graded';
  score: number | null;
  questions: Question[];
  attempts: TestAttempt[];
}

export interface ModuleContent {
  videoUrl?: string;
}

export interface Module {
  id: number;
  title: string;
  category: string;
  description: string;
  content: {
    videoUrl?: string;
  };
  hasQuiz: boolean;
  completed: boolean;
  duration?: string;
  locked: boolean;
  test?: Test;
}
