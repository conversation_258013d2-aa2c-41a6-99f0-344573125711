export type TestType = 'exam' | 'quiz';
export type QuestionType = 'multiple_choice' | 'long_text' | 'short_text';
export type TestStatus = 'in_progress' | 'pending_review' | 'complete';

export interface Answer {
  id: number;
  questionId: number;
  answer: string;
  isCorrect: boolean;
}

export interface Question {
  id: number;
  testId: number;
  question: string;
  type: QuestionType;
  answers: Answer[];
}

export interface Test {
  id: number;
  moduleId: number;
  type: TestType;
  questions: Question[];
}

export interface TestAttempt {
  id: number;
  userId: number;
  testId: number;
  startedAt: string | null;
  endsAt: string | null;
  completedAt: string | null;
  score: number | null;
  status: TestStatus;
}

export interface QuestionResponse {
  id: number;
  userId: number;
  questionId: number;
  testAttemptId: number;
  response: string | null;
  correct: boolean | null;
}

export interface Quiz {
  id: number;
  moduleId: number;
  questions: Array<{
    id: number;
    question: string;
    type: QuestionType;
    answers: Array<{
      id: number;
      answer: string;
      isCorrect: boolean;
    }>;
  }>;
  latestAttempt: TestAttempt | null;
  lastCompletedAttempt: TestAttempt | null;
  totalAttempts: number;
  attemptsAllowed: number;
  timeLimit: number | null;
  passingScore: number;
}
