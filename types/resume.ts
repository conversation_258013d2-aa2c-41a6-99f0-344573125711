export type ResumeSectionType =
  | 'profile'
  | 'contact'
  | 'education'
  | 'involvement'
  | 'experience'
  | 'sports';

export interface BaseResumeItem {
  id: string;
  order?: number;
}

export interface ContactInfo extends BaseResumeItem {
  email: string;
  phone: string;
  location: string;
}

export interface Education extends BaseResumeItem {
  schoolName: string;
  dateRange: string;
  gpa?: string;
  classRank?: string;
  achievements: string;
}

export interface Involvement extends BaseResumeItem {
  name: string;
  role: string;
  dateRange: string;
  description: string;
}

export interface Experience extends BaseResumeItem {
  companyName: string;
  role: string;
  dateRange: string;
  description: string;
}

export interface Sport extends BaseResumeItem {
  name: string;
  position?: string;
  level: string;
  dateRange: string;
  achievements: string;
}

export interface ProfileContent {
  name: string;
  description: string;
  showPhoto: boolean;
  pendingAvatarFile?: File | 'USE_PROFILE_AVATAR';
}

export interface ContactContent {
  email: string;
  phone: string;
  location: string;
}

export interface ListContent<T extends BaseResumeItem> {
  items: T[];
}

export type ResumeSectionContent =
  | ProfileContent
  | ContactContent
  | ListContent<Education>
  | ListContent<Involvement>
  | ListContent<Experience>
  | ListContent<Sport>;

export interface ResumeSection {
  id?: string;
  section_type: ResumeSectionType;
  content: ResumeSectionContent;
  is_enabled: boolean;
  order?: number;
}

export interface Resume {
  id: string;
  name: string;
  sections: ResumeSection[];
  avatar?: {
    url: string;
  };
  created_at?: string;
  updated_at?: string;
}

export interface ResumeAvatarData {
  id: number;
  url: string;
  responsive_urls?: {
    [key: string]: string;
  };
}

export interface CreateResumeData {
  name: string;
  sections: {
    section_type: 'profile' | 'work' | 'academic';
    is_enabled: boolean;
    content: ResumeSectionContent;
  }[];
}

export interface UpdateResumeSectionData {
  content: ResumeSectionContent;
  is_enabled?: boolean;
  avatar?: File;
}
