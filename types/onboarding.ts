export interface AccountInfoDTO {
  token: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  street_address: string;
  unit?: string;
  city: string;
  state: string;
  zip_code: string;
  password: string;
}

export interface DetailsDTO {
  token: string;
  state: string;
  county: string;
  high_school: string;
  school_id?: string;
  graduation_year: string;
  current_gpa: number;
  current_class_rank?: string;
  gender: string;
  height: string;
  weight: number;
  career_interests: string[];
  social_links?: {
    [key: string]: string;
  } | null;
  profile_photo?: File | null;
}

export interface SportDTO {
  name: string;
  id?: number;
  is_custom: boolean;
  order: number;
}

export interface SportsDTO {
  token: string;
  sports: SportDTO[];
}

export interface InvolvementItemDTO {
  name: string;
  date_range: string;
  description: string;
  order: number;
}

export interface InvolvementDTO {
  token: string;
  items: InvolvementItemDTO[];
}

export interface WorkExperienceDTO {
  token: string;
  items: InvolvementItemDTO[]; // Same structure as involvement
}

export interface StoryDTO {
  token: string;
  content: string;
}

export interface OnboardingResponse {
  success: boolean;
  data: {
    current_step: string;
    next_step: string | null;
    prefill?: Record<string, any>;
  };
  message: string;
  errors?: Record<string, string[]>;
}
