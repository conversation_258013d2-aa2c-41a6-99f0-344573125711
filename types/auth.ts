interface Athlete {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  recruiter_enabled: boolean;
}

export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  content: string;
  graduation_year: number | null;
  profile_type: 'positive_athlete' | 'coach' | 'recruiter' | 'admin';
  email_verified_at: string | null;
  athlete?: Athlete | null;
  created_at: string;
  updated_at: string;
  has_achievements?: boolean;
  profile_meta: any; // TODO: Add type for profile meta
}

export interface AuthResponse {
  success: boolean;
  data: {
    user: User;
  };
  message: string;
}

export type LoginCredentials = {
  email: string;
  password: string;
};

export interface Session {
  user: User;
  expires: string;
}
