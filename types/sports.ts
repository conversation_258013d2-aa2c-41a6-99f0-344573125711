/**
 * Enum representing all supported sports from the backend
 * This mirrors the sports list in the Laravel backend's SportsSeeder
 */
export enum SportName {
  FOOTBALL = 'Football',
  BASKETBALL = 'Basketball',
  BASEBALL = 'Baseball',
  SOFTBALL = 'Softball',
  SOCCER = 'Soccer',
  TRACK_AND_FIELD = 'Track & Field',
  CROSS_COUNTRY = 'Cross Country',
  VOLLEYBALL = 'Volleyball',
  TENNIS = 'Tennis',
  GOLF = 'Golf',
  WRESTLING = 'Wrestling',
  SWIMMING_AND_DIVING = 'Swimming & Diving',
  LACROSSE = 'Lacrosse',
  FIELD_HOCKEY = 'Field Hockey',
  ICE_HOCKEY = 'Ice Hockey',
  CHEERLEADING = 'Cheerleading',
  DANCE = 'Dance',
  GYMNASTICS = 'Gymnastics',
  WATER_POLO = 'Water Polo',
  ROWING = 'Rowing',
  BOWLING = 'Bowling',
  ULTIMATE_FRISBEE = 'Ultimate Frisbee',
  RUGBY = 'Rugby',
  SKIING = 'Skiing',
  SNOWBOARDING = 'Snowboarding',
  MOUNTAIN_BIKING = 'Mountain Biking',
  CYCLING = 'Cycling',
  ARCHERY = 'Archery',
  BADMINTON = 'Badminton',
  FENCING = 'Fencing',
  // Additional frontend-specific sports
  SKIING_SNOWBOARDING = 'Skiing/Snowboarding',
  FLAG_FOOTBALL = 'Flag Football',
  ESPORTS = 'ESports',
  TABLE_TENNIS = 'Table Tennis',
  WEIGHTLIFTING = 'Weightlifting',
  SHOOTING = 'Shooting',
  HANDBALL = 'Handball',
  EQUESTRIAN = 'Equestrian',
  CRICKET = 'Cricket',
}

/**
 * Type for mappings between sports and their components or assets
 */
export type SportMapping<T> = Record<SportName | string, T>;

/**
 * Helper function to normalize a sport name to match the enum values
 * Handles variations in naming, spacing, and capitalization
 */
export function normalizeSportName(sportName: string): SportName | string {
  if (!sportName) return sportName;

  // Convert to uppercase for case-insensitive comparison
  const normalizedName = sportName
    .toUpperCase()
    .replace(/\s*&\s*/, '_AND_') // Replace & with _AND_
    .replace(/\s*\/\s*/, '_') // Replace / with _
    .replace(/\s+/g, '_'); // Replace spaces with _

  // Handle common variations of sport names
  // Track & Field / Track and Field variations
  if (normalizedName === 'TRACK_FIELD' || normalizedName === 'TRACK_AND_FIELD') {
    return SportName.TRACK_AND_FIELD;
  }

  // Swimming variations
  if (
    normalizedName === 'SWIMMING' ||
    normalizedName === 'DIVING' ||
    normalizedName === 'SWIMMING_DIVING' ||
    normalizedName === 'SWIMMING_AND_DIVING'
  ) {
    return SportName.SWIMMING_AND_DIVING;
  }

  // E-Sports variations
  if (
    normalizedName === 'E_SPORTS' ||
    normalizedName === 'E-SPORTS' ||
    normalizedName === 'ESPORT'
  ) {
    return SportName.ESPORTS;
  }

  // Skiing/Snowboarding variations
  if (normalizedName === 'SKIING_SNOWBOARDING' || normalizedName === 'SKIING_AND_SNOWBOARDING') {
    return SportName.SKIING_SNOWBOARDING;
  }

  // Try to find the sport in the enum by comparing normalized names
  for (const [enumKey, enumValue] of Object.entries(SportName)) {
    const enumNormalized = enumValue
      .toUpperCase()
      .replace(/\s+/g, '_')
      .replace(/\s*&\s*/, '_AND_');
    if (enumNormalized === normalizedName) {
      return enumValue;
    }
  }

  // If we couldn't find a direct match, try to find partial matches for more flexibility
  for (const [enumKey, enumValue] of Object.entries(SportName)) {
    // For example, if someone enters "Flag Football" but our enum has "Flag Football"
    if (normalizedName.includes(enumValue.toUpperCase().replace(/\s+/g, '_'))) {
      return enumValue;
    }
  }

  return sportName;
}
