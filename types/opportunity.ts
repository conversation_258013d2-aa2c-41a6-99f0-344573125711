import { CareerInterest } from '@/stores/careerInterestStore';

export type LearningType =
  | 'Certificate Program'
  | 'Internships'
  | 'Apprenticeships'
  | 'Degree Programs'
  | 'Continuing Education';

export type OpportunityType = 'education' | 'job' | 'internship' | 'scholarship' | 'mentorship';
export type OpportunityStatus = 'active' | 'inactive' | 'pending';
export type OpportunityLocationType = 'onsite' | 'remote' | 'hybrid';
export type OpportunityTerm = '<1_year' | '1-2_years' | '2-4_years' | '>4_years';

// Original frontend Opportunity type (used for mock data)
export interface Opportunity {
  id: number;
  image: string;
  learningType: LearningType;
  title: string;
  organization: string;
  city: string;
  state: string;
  term: string;
  industry: CareerInterest;
  description: string;
  details: string;
}

// MeiliSearch Opportunity type that matches the backend data
export interface MeiliSearchOpportunity {
  id: number;
  title: string;
  description: string;
  details: string;
  qualifications?: string;
  responsibilities?: string;
  benefits?: string;
  type: OpportunityType;
  status: OpportunityStatus;
  location_type: OpportunityLocationType;
  term: OpportunityTerm;
  is_featured: boolean;
  location: string;
  city: string;
  state_code: string;
  state_name?: string;
  organization_name: string;
  organization_logo?: string;
  industries: string[];
  created_at: number;
  updated_at: number;
  subtype?: string;
  _geo?: {
    lat: number;
    lng: number;
  };
}
