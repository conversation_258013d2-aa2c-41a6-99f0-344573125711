export interface PhotoMetadata {
  url: string;
  width: number;
  height: number;
}

export interface ProfileDetails {
  state: string;
  county: string;
  highSchool: string;
  graduationYear: number;
  currentGpa: number;
  classRank: string;
  gender: string;
  height?: string;
  weight?: string;
}

export interface SocialLinks {
  instagram?: string;
  facebook?: string;
  twitter?: string;
  hudl?: string;
  website?: string;
}

export interface CareerInterest {
  id: number;
  name: string;
  slug: string;
}

export interface SportEntry {
  id: string;
  type: 'predefined' | 'custom';
  sport: string;
  customName?: string;
}

export interface Profile {
  id: number;
  firstName: string;
  lastName: string;
  inRecruiterDatabase: boolean;
  details: ProfileDetails;
  avatarUrl?: string;
  photos: Photo[];
  story?: string;
  sports: SportEntry[];
  workExperience: WorkExperience[];
  involvement: Involvement[];
  socialLinks: SocialLinks;
  careerInterests: CareerInterest[];
  has_achievements?: boolean;
}

export interface Photo {
  url: string;
  width: number;
  height: number;
}

export interface WorkExperience {
  id: number;
  title: string;
  organization: string;
  startDate: string;
  endDate?: string;
  description?: string;
}

export interface Involvement {
  id: number;
  name: string;
  role: string;
  description?: string;
}
