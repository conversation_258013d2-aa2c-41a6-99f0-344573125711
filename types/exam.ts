import { Question } from '@/services/x-factor-exam.service';

export interface Exam {
  id: number;
  moduleId: number;
  timeLimit: number;
  passingScore: number;
  waitPeriod: number;
  questions: Question[];
  latestAttempt?: TestAttempt;
  lastCompletedAttempt?: TestAttempt;
}

export interface TestAttempt {
  id: number;
  testId: number;
  userId: number;
  status: TestStatus;
  score: number | null;
  startedAt: string;
  endsAt: string;
  completedAt: string | null;
  gradedAt: string | null;
  feedback: string | null;
  responses: TestResponse[];
}

export interface TestResponse {
  id: number;
  questionId: number;
  response: string;
  responseHtml?: string;
  correct: boolean | null;
}

export type TestStatus = 'in_progress' | 'completed' | 'expired' | 'pending_review' | 'graded';
