# Parent Navigation Implementation

## Overview
This documentation outlines the implementation of parent-specific navigation items in the Positive Athlete platform. Parent users should have restricted access to only specific areas of the application, namely Profile, Network, Opportunities, and Messages routes, while being restricted from Dashboard and X Factor routes.

## Implementation Details

### 1. Parent-Specific Navigation Items
A new constant `authenticatedParentNavigationItems` has been added to `components/navigation/constants.ts` that defines the navigation items specifically for parent users. This constant includes only the allowed routes:

- Profile
- Network
- Opportunities
- Messages

The implementation follows the pattern of other profile-specific navigation items in the file, using the same icon components for visual consistency.

### 2. Integration Instructions
To fully implement the parent-specific navigation restrictions, the following additional changes are needed:

1. Update the main layout component to use the parent-specific navigation items for parent users:
   ```typescript
   // In app/(main)/layout.tsx, update the getNavigationItems function
   const getNavigationItems = () => {
     if (!isLoggedIn) return publicNavigationItems;

     switch (profileType) {
       case ProfileTypes.POSITIVE_COACH:
         return authenticatedCoachNavigationItems;
       case ProfileTypes.POSITIVE_ATHLETE:
         return authenticatedNavigationItems;
       case ProfileTypes.ATHLETICS_DIRECTOR:
       case ProfileTypes.SPONSOR:
         return authenticatedSponsorNavigationItems;
       case ProfileTypes.PARENT:
         return authenticatedParentNavigationItems; // Use parent-specific navigation
       case ProfileTypes.UTILITY:
       case ProfileTypes.ADMIN:
       case ProfileTypes.COLLEGE_ATHLETE:
       case ProfileTypes.PROFESSIONAL:
       case ProfileTypes.TEAM_STUDENT:
       case ProfileTypes.TEAM_COACH:
       case ProfileTypes.ATHLETE:
       case ProfileTypes.COACH:
         return authenticatedNavigationItems;
       case null:
         return publicNavigationItems;
       default:
         return authenticatedNavigationItems;
     }
   };
   ```

2. Verify that route guards properly redirect parent users who attempt to access restricted routes.

## Test Strategy
1. Create or use an existing parent account
2. Log in with the parent account credentials
3. Verify that only the allowed navigation items appear in the sidebar/header
4. Verify that attempting to access restricted routes redirects to the default route
5. Test clicking each allowed navigation item works as expected
6. Test account page accessibility through the sidebar footer section
7. Test with bookmarked restricted URLs and deep links to ensure proper redirection

## Next Steps
1. Implement the layout component updates
2. Ensure proper route guarding for parent accounts
3. Test the implementation thoroughly with different account types 