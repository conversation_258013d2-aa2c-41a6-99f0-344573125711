# Learning Progress Functionality

## Overview

The Learning Progress functionality allows users to track their progress through courses, modules, and other learning activities. It provides statistics, certificates, badges, and investment data related to a user's learning journey.

## Recent Changes

The Learning Progress functionality has been updated to be user ID agnostic, allowing for more flexible access to learning progress data. This change enables:

1. Administrators to view any user's learning progress
2. Users to view their own progress
3. Future features like comparing progress between users or displaying leaderboards

## API Endpoints

The backend API endpoints follow this pattern:

```
/api/v1/users/{userId}/learning/summary
/api/v1/users/{userId}/learning/certificates
/api/v1/users/{userId}/learning/stats
/api/v1/users/{userId}/learning/investment
/api/v1/users/{userId}/learning/badges
```

Where `{userId}` can be:

- A numeric ID (e.g., `1`, `2`, `3`) to access a specific user's data
- The special value `me` to access the currently authenticated user's data

## Frontend Implementation

### Type Definition

We've introduced a specific type for user IDs:

```typescript
export type UserIdParam = number | null;
```

This type enforces that user IDs should be represented as numbers, with `null` indicating that the current user's ID should be automatically resolved.

### Service Layer

The `learningProgressService` has been updated to accept a `UserIdParam` in all methods:

```typescript
// Example method signature
async getLearningStats(userId: UserIdParam = null): Promise<LearningStats>
```

All methods default to `null` for backward compatibility. The service layer handles the conversion from `null` to the special `'me'` value that the API expects.

### React Hook

The `useLearningProgress` hook has been updated to accept a `UserIdParam`:

```typescript
// Example usage
const { stats, certificates, badges } = useLearningProgress(userId);
```

The hook handles:

- Resolving the actual user ID when `null` is provided (using the `useAuth` hook)
- Creating a stable query key for React Query
- Passing the resolved user ID to service methods
- Refreshing data for the specific user
- Exposing the resolved user ID for consumers

## Type Handling

Since the backend uses big integer values for IDs, the frontend code has been updated to properly handle numeric IDs:

1. The service and hook accept either `number` or `null` as the userId type
2. When a numeric ID is provided, it's passed directly to the API
3. When `null` is provided, the hook resolves it to the current user's ID
4. The service layer converts `null` to the special `'me'` value for the API
5. All user IDs are consistently represented as numbers in the application code

## Best Practices

When working with user IDs in the frontend:

1. **Always use numbers for actual user IDs** - This ensures proper type safety and consistency
2. **Use null to refer to the current user** - This allows the hook to automatically resolve the current user's ID
3. **Convert string inputs to numbers** - When accepting user input, convert numeric strings to actual numbers
4. **Validate user inputs** - Ensure inputs are either valid numbers or null

## Example Usage

### Viewing Current User's Progress

```typescript
// Default behavior (current user)
const { stats } = useLearningProgress();
// or explicitly
const { stats } = useLearningProgress(null);
```

### Viewing Another User's Progress

```typescript
// Specific user by ID (always use a number)
const { stats } = useLearningProgress(123);
```

### Dynamic User Selection

```typescript
const [selectedUserId, setSelectedUserId] = useState<UserIdParam>(null);
const { stats, userId: resolvedUserId } = useLearningProgress(selectedUserId);

// Later in your component
<button onClick={() => setSelectedUserId(123)}>View User 123</button>
<button onClick={() => setSelectedUserId(null)}>View My Progress</button>

// Display which user's data is being shown
<p>Viewing data for user ID: {resolvedUserId || 'Loading...'}</p>
```

### Converting User Input

```typescript
// When handling form input
const handleSubmit = (e: FormEvent) => {
  e.preventDefault();

  // Convert to number if it's not meant to be the current user
  const parsedId = userIdInput === 'current' ? null : Number(userIdInput);

  setSelectedUserId(parsedId);
};
```

## Component Implementation

When implementing components that use the learning progress hook:

```typescript
interface MyComponentProps {
  userId?: UserIdParam; // Optional userId parameter
}

export function MyComponent({ userId = null }: MyComponentProps) {
  const {
    stats,
    certificates,
    badges,
    investment,
    userId: resolvedUserId, // Get the resolved user ID from the hook
  } = useLearningProgress(userId);

  // Now you can use the data and the resolved user ID
  return (
    <div>
      <h1>Learning Progress for User {resolvedUserId || 'Loading...'}</h1>
      {/* Component content */}
    </div>
  );
}
```

## Testing

When testing components that use the `useLearningProgress` hook, make sure to:

1. Test with both `null` and numeric IDs
2. Mock the `useAuth` hook to provide a test user ID when needed
3. Verify that query keys are properly invalidated when switching between users
4. Check that the correct API endpoints are called with the appropriate user ID
5. Validate that user inputs are properly converted to the correct types
