{"name": "positive-athlete-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "build": "next build", "start": "next start", "prod": "npm run build && next start -p 3001", "serve": "npm run build && npx serve .next", "lint": "next lint", "format": "prettier --write .", "debug": "npx @agentdeskai/browser-tools-server@1.2.0"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/pro-light-svg-icons": "^6.7.2", "@fortawesome/pro-regular-svg-icons": "^6.7.2", "@fortawesome/pro-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.0", "@meilisearch/instant-meilisearch": "^0.25.0", "@splinetool/react-spline": "^4.0.0", "@tanstack/react-query": "^5.64.2", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-underline": "^2.11.2", "@tiptap/pm": "^2.11.3", "@tiptap/react": "^2.11.3", "@tiptap/starter-kit": "^2.11.3", "@types/echarts": "^5.0.0", "@types/lodash": "^4.17.14", "@types/react-dropzone": "^5.1.0", "@types/react-youtube": "^7.10.0", "@types/uuid": "^10.0.0", "axios": "^1.7.9", "clsx": "^2.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "echarts": "^5.6.0", "html2pdf.js": "^0.10.2", "lodash": "^4.17.21", "meilisearch": "^0.49.0", "next": "^15.1.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-draggable": "^4.4.6", "react-dropzone": "^14.3.5", "react-easy-crop": "^5.2.0", "react-hook-form": "^7.51.0", "react-instantsearch": "^7.15.4", "react-instantsearch-router-nextjs": "^7.15.5", "react-youtube": "^10.1.0", "swiper": "^11.2.1", "tailwind-merge": "^2.6.0", "tailwind-scrollbar": "^3.1.0", "uuid": "^11.0.5", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@netlify/plugin-nextjs": "^5.9.4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.17.14", "@types/react": "^19.0.7", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.4", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.1", "typescript": "^5"}}