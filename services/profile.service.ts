import { Profile, SportEntry } from '@/types/profile';

// Mock data for development
const mockProfiles: Record<number, Profile> = {
  1: {
    id: 1,
    firstName: '<PERSON>',
    lastName: 'Doe',
    inRecruiterDatabase: false,
    details: {
      state: 'Indiana',
      county: 'Marion',
      highSchool: 'Central High School',
      graduationYear: 2025,
      currentGpa: 3.8,
      classRank: 'Top 5%',
      gender: 'Male',
      height: '6\'2"',
      weight: '175 lbs',
    },
    avatarUrl: '/mock/football-avatar.jpg',
    photos: [
      { url: '/mock/profile-photo-1.jpg', width: 1800, height: 2700 },
      { url: '/mock/profile-photo-2.jpg', width: 3504, height: 2336 },
      { url: '/mock/profile-photo-3.jpg', width: 3701, height: 2463 },
      { url: '/mock/profile-photo-4.jpg', width: 1500, height: 2100 },
    ],
    story:
      'High school athlete passionate about basketball and academics. Team captain with a 3.8 GPA.',
    sports: [
      { id: '1', type: 'predefined', sport: 'basketball' },
      { id: '2', type: 'predefined', sport: 'track-field' },
    ],
    workExperience: [
      {
        id: 1,
        title: 'Summer Camp Counselor',
        organization: 'YMCA',
        startDate: '2023-06',
        endDate: '2023-08',
        description: 'Led youth basketball training sessions',
      },
    ],
    involvement: [
      {
        id: 1,
        name: 'Student Government',
        role: 'Class Representative',
        description: 'Represented student interests in school policy decisions',
      },
    ],
    socialLinks: {
      twitter: 'johndoe',
      instagram: 'johndoe.athlete',
      facebook: 'john.doe.athlete',
      hudl: 'johndoe',
    },
    careerInterests: [
      { id: 1, name: 'Sports Marketing/Management', slug: 'sports-marketing-management' },
      { id: 2, name: 'Health Science', slug: 'health-science' },
    ],
  },
  2: {
    id: 2,
    firstName: 'Sarah',
    lastName: 'Smith',
    inRecruiterDatabase: true,
    details: {
      state: 'Ohio',
      county: 'Franklin',
      highSchool: 'Lincoln High School',
      graduationYear: 2024,
      currentGpa: 4.0,
      classRank: 'Top 3%',
      gender: 'Female',
      height: '5\'9"',
      weight: '145 lbs',
    },
    avatarUrl: '',
    photos: [],
    story:
      'Dedicated volleyball player with a passion for leadership and community service. Academic excellence is my priority.',
    sports: [
      { id: '1', type: 'predefined', sport: 'volleyball' },
      { id: '2', type: 'predefined', sport: 'swimming' },
    ],
    workExperience: [
      {
        id: 1,
        title: 'Lifeguard',
        organization: 'City Pool',
        startDate: '2023-05',
        endDate: '2023-09',
        description: 'Ensured safety of pool patrons and taught swimming lessons',
      },
    ],
    involvement: [
      {
        id: 1,
        name: 'National Honor Society',
        role: 'President',
        description: 'Led community service initiatives and mentoring programs',
      },
    ],
    socialLinks: {
      twitter: 'sarahsmith',
      instagram: 'sarah.athlete',
      facebook: '',
      hudl: 'sarahsmith',
    },
    careerInterests: [
      { id: 1, name: 'Technology, Engineering & Math', slug: 'technology-engineering-math' },
      { id: 2, name: 'Science & Research', slug: 'science-research' },
    ],
  },
};

interface Sport {
  name: string;
  position: string;
  yearsPlayed: number;
  achievements?: string[];
}

// Mock sports data
const mockSports = {
  1: [
    {
      name: 'Basketball',
      position: 'Point Guard',
      yearsPlayed: 4,
      achievements: ['Team Captain', 'All-State 2nd Team'],
    },
    {
      name: 'Track & Field',
      position: '400m, 800m',
      yearsPlayed: 3,
      achievements: ['Regional Finalist'],
    },
  ],
};

export async function getSportsData(userId: number): Promise<Sport[]> {
  // Return mock data instead of making API call
  await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
  return mockSports[1];
}

export async function getProfile(userId: number) {
  // Return mock data instead of making API call
  await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
  return { data: { data: mockProfiles[1] } };
}

export async function updateProfile(userId: number, updates: Partial<Profile>) {
  // Mock successful update
  await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
  return {
    ...mockProfiles[1],
    ...updates,
  };
}
