import axios from '@/lib/axios';
import type { Quiz, TestAttempt } from '@/types/quiz';

export const xFactorService = {
  /**
   * Fetch quiz data for a module
   */
  async getQuiz(moduleId: number): Promise<Quiz> {
    const response = await axios.get(`/api/v1/x-factor/modules/${moduleId}/quiz`);
    return response.data;
  },

  /**
   * Start a new quiz attempt
   */
  async startQuizAttempt(testId: number): Promise<TestAttempt> {
    const response = await axios.post(`/api/v1/x-factor/modules/quiz/attempts`, {
      testId,
    });
    return response.data;
  },

  /**
   * Submit quiz responses
   */
  async submitQuizResponses(
    moduleId: number,
    responses: Array<{ questionId: number; answerId: number }>
  ): Promise<void> {
    await axios.post(`/api/v1/x-factor/modules/${moduleId}/quiz/complete`, {
      responses,
    });
  },
};
