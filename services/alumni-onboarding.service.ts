import type { AxiosResponse } from 'axios';
import { useSystemInviteStore } from '@/stores/systemInviteStore';
import axios from '../lib/axios'; // Assuming csrf is handled by the global axios instance

// Payload Interfaces
export interface AlumniLifeStagePayload {
  life_stage: string; // e.g., 'college_student', 'college_graduate', 'professional'
  intended_profile_type: string; // e.g., 'college_athlete', 'professional'
}

export interface AlumniAccountInfoPayload {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  street_address?: string;
  unit?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  password: string;
  password_confirmation: string;
}

export interface AlumniCollegeDetailsPayload {
  college?: string;
  state?: string;
  graduation_year?: string;
  gpa?: string;
  gender?: string;
  height?: string;
  weight?: string;
  career_interests?: string[];
  twitter?: string;
  instagram?: string;
  facebook?: string;
  hudl?: string;
  custom_link?: string;
  profile_photo?: File | null; // For upload
}

export interface AlumniProfessionalDetailsPayload {
  intended_profile_type: 'college_athlete' | 'professional'; // This is already sent in lifeStage, but helps backend confirm
  employer?: string;
  job_title?: string;
  state?: string;
  interests?: string[];
  twitter?: string;
  instagram?: string;
  facebook?: string;
  hudl?: string;
  custom_link?: string;
  profile_photo?: File | null; // For upload
}

// Response type for onboarding steps
export interface AlumniOnboardingStepResponse {
  current_step: string;
  next_step: string | null;
  prefill?: any; // Prefill can contain a mix of data from different steps
}

class AlumniOnboardingService {
  private getToken(): string {
    const inviteData = useSystemInviteStore.getState().inviteData;
    if (!inviteData?.invite?.data?.token) {
      throw new Error('No invite token found. Please start from the invite link.');
    }
    return inviteData.invite.data.token;
  }

  private appendTokenToPayload<T>(data: T): T & { token: string } {
    return {
      ...data,
      token: this.getToken(),
    };
  }

  private appendTokenToFormData(formData: FormData): FormData {
    formData.append('token', this.getToken());
    return formData;
  }

  getNextStep = async (): Promise<AxiosResponse<AlumniOnboardingStepResponse>> => {
    return axios.get(`/api/v1/onboarding/alumni/next-step?token=${this.getToken()}`);
  };

  submitLifeStage = async (
    data: AlumniLifeStagePayload
  ): Promise<AxiosResponse<AlumniOnboardingStepResponse>> => {
    return axios.post('/api/v1/onboarding/alumni/life-stage', this.appendTokenToPayload(data));
  };

  submitAccountInfo = async (
    data: AlumniAccountInfoPayload
  ): Promise<AxiosResponse<AlumniOnboardingStepResponse>> => {
    return axios.post('/api/v1/onboarding/alumni/account-info', this.appendTokenToPayload(data));
  };

  submitCollegeDetails = async (
    data: AlumniCollegeDetailsPayload | FormData
  ): Promise<AxiosResponse<AlumniOnboardingStepResponse>> => {
    const endpoint = '/api/v1/onboarding/alumni/college-details';
    if (data instanceof FormData) {
      return axios.post(endpoint, this.appendTokenToFormData(data), {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    }
    return axios.post(endpoint, this.appendTokenToPayload(data));
  };

  submitProfessionalDetails = async (
    data: AlumniProfessionalDetailsPayload | FormData
  ): Promise<AxiosResponse<AlumniOnboardingStepResponse>> => {
    const endpoint = '/api/v1/onboarding/alumni/professional-details';
    if (data instanceof FormData) {
      return axios.post(endpoint, this.appendTokenToFormData(data), {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    }
    return axios.post(endpoint, this.appendTokenToPayload(data));
  };

  completeOnboarding = async (): Promise<AxiosResponse<{ redirect: string }>> => {
    return axios.post('/api/v1/onboarding/alumni/complete', {
      token: this.getToken(),
    });
  };
}

export const alumniOnboardingService = new AlumniOnboardingService();
