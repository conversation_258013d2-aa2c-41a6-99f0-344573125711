import axios from '@/lib/axios';
import type { ApiResponse, Sport } from './positive-athlete-profile.service';

/**
 * Transforms a sport object to ensure proper type flags
 */
export const transformSport = (sport: Sport): Sport => ({
  ...sport,
  // Ensure isCustom is always a boolean
  isCustom: !!sport.customName,
});

/**
 * Search for platform-curated sports (public endpoint)
 */
export async function searchSports(query: string): Promise<Sport[]> {
  const response = await axios.get<Sport[]>('/api/v1/sports/search', {
    params: { query },
  });
  return response.data.map(transformSport);
}
