import axios from '@/lib/axios';

/**
 * Represents an endorser who has endorsed a user
 */
export interface Endorser {
  id: number;
  name?: string;
  endorserName?: string;
  endorserInfo?: string;
  relation?: string;
  endorserRole?: string;
  avatar?: string;
  endorsedAt?: string;
  created_at?: string;
}

/**
 * Represents a single endorsement category with its endorsers
 */
export interface EndorsementSummary {
  id: number;
  name: string;
  description: string;
  count: number;
  endorsers: Endorser[];
}

class EndorsementService {
  /**
   * Get endorsements for a specific user
   * @param userId The ID of the user to get endorsements for
   */
  async getUserEndorsements(userId: number): Promise<EndorsementSummary[]> {
    try {
      const response = await axios.get<any>(`/api/v1/users/${userId}/endorsements`);

      // Check if we have data in the expected format
      if (response.data && Array.isArray(response.data)) {
        return response.data;
      }

      // If the response has a data property that contains the endorsements array
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        return response.data.data;
      }

      console.error('Unexpected endorsements response format:', response.data);
      return [];
    } catch (error) {
      console.error('Error fetching user endorsements:', error);
      return []; // Return empty array on error
    }
  }
}

export const endorsementService = new EndorsementService();
