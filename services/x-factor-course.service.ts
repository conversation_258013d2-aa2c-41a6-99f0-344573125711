import axios from '@/lib/axios';
import { LaravelPagination } from './types';
import type { CourseEnhancedModule } from './x-factor-module.service';

export interface XFactorCourse {
  id: string;
  title: string;
  description: string;
  presenter: string;
  coverImageUrl: string | null;
  coverImageThumbUrl: string | null;
  isCompleted: boolean;
  progress: number;
  modulesCompleted: number;
  totalModules: number;
  averageScore: number;
  topics: string[];
  lastAccessedAt: string | null;
  createdAt: string;
  totalRuntimeMinutes: number | null;
  modules: CourseEnhancedModule[];
  hasExam?: boolean;
  examCompleted?: boolean;
  examScore?: number;
}

export interface TopicSection {
  id: string;
  name: string;
  courses: XFactorCourse[];
}

export interface CourseBrowseFilters {
  search?: string;
  sort?: 'recent' | 'progress' | 'alphabetical';
  topics?: string[];
  status?: 'not_started' | 'in_progress' | 'completed';
  page?: number;
  perPage?: number;
}

export interface XFactorCourseService {
  getFeaturedCourses(): Promise<XFactorCourse[]>;
  getBrowseIndex(): Promise<TopicSection[]>;
  getTopicCourses(topic: string, page?: number): Promise<LaravelPagination<XFactorCourse>>;
  searchCourses(filters?: CourseBrowseFilters): Promise<LaravelPagination<XFactorCourse>>;
  getCourseDetail(courseId: string): Promise<XFactorCourse>;
}

export class XFactorCourseServiceImpl implements XFactorCourseService {
  /**
   * Get featured courses for the user
   */
  async getFeaturedCourses(): Promise<XFactorCourse[]> {
    const response = await axios.get<XFactorCourse[]>('/api/v1/x-factor/courses/featured');
    return response.data;
  }

  /**
   * Get courses grouped by topics for the browse page
   */
  async getBrowseIndex(): Promise<TopicSection[]> {
    const response = await axios.get<TopicSection[]>('/api/v1/x-factor/courses');
    return response.data;
  }

  /**
   * Get more courses for a specific topic
   */
  async getTopicCourses(
    topic: string,
    page: number = 1
  ): Promise<LaravelPagination<XFactorCourse>> {
    const response = await axios.get<LaravelPagination<XFactorCourse>>(
      `/api/v1/x-factor/courses/topics/${encodeURIComponent(topic)}`,
      {
        params: { page, per_page: 5 },
      }
    );
    return response.data;
  }

  /**
   * Search courses with filters
   */
  async searchCourses(
    filters: CourseBrowseFilters = {}
  ): Promise<LaravelPagination<XFactorCourse>> {
    const response = await axios.get<LaravelPagination<XFactorCourse>>(
      '/api/v1/x-factor/courses/search',
      {
        params: {
          search: filters.search,
          sort: filters.sort,
          topics: filters.topics,
          status: filters.status,
          page: filters.page,
          per_page: filters.perPage,
        },
      }
    );
    return response.data;
  }

  async getCourseDetail(courseId: string): Promise<XFactorCourse> {
    const response = await axios.get<XFactorCourse>(`/api/v1/x-factor/courses/${courseId}`);
    return response.data;
  }
}

export const xFactorCourseService = new XFactorCourseServiceImpl();
