import axios from '@/lib/axios';
import { LaravelPagination } from './types';

export enum ModuleType {
  Video = 'video',
  Article = 'article',
  Exam = 'exam',
}

export interface Course {
  id: number;
  title: string;
  description: string;
  published: boolean;
}

export interface CourseModule {
  id: number;
  course_id: number;
  module_id: number;
  order: number;
}

// Base module interface (existing functionality)
export interface XFactorModule {
  id: number;
  name: string;
  description: string;
  cover_image: string | null;
  published: boolean;
  course_id: number | null;
  course_name: string | null;
  duration_minutes: number | null;
  topics: string[];
  has_quiz: boolean | null;
  has_exam: boolean | null;
  type: ModuleType;
  progress: number | null;
  completed_at: string | null;
  next_attempt_available_at: string | null;
  content: string | null;
  video_url: string | null;
  video_start_time: number | null;
  video_end_time: number | null;
  score: number | null;
  order: number | null;
}

// Extended module interface for course context
export interface CourseEnhancedModule extends XFactorModule {
  course?: Course;
  course_module?: CourseModule;
}

export interface TopicModules {
  id: string;
  name: string;
  modules: XFactorModule[];
}

export interface XFactorModuleServiceImpl {
  getAllModulesByTopics(): Promise<TopicModules[]>;
  getModulesByTopic(topicId: string): Promise<TopicModules>;
  searchModules(query: string): Promise<LaravelPagination<XFactorModule>>;
  getModuleDetail(moduleId: number, courseId?: number): Promise<CourseEnhancedModule>;
}

class XFactorModuleService implements XFactorModuleServiceImpl {
  async getAllModulesByTopics(): Promise<TopicModules[]> {
    const response = await axios.get<TopicModules[]>('/api/v1/x-factor/modules');
    return response.data;
  }

  async getModulesByTopic(topicId: string): Promise<TopicModules> {
    const response = await axios.get<TopicModules>(
      `/api/v1/x-factor/modules/topics/${encodeURIComponent(topicId)}`
    );
    return response.data;
  }

  async searchModules(query: string): Promise<LaravelPagination<XFactorModule>> {
    const response = await axios.get<LaravelPagination<XFactorModule>>(
      '/api/v1/x-factor/modules/search',
      {
        params: { search: query },
      }
    );
    return response.data;
  }

  async getModuleDetail(moduleId: number, courseId?: number): Promise<CourseEnhancedModule> {
    const response = await axios.get<CourseEnhancedModule>(`/api/v1/x-factor/modules/${moduleId}`, {
      params: courseId ? { course_id: courseId } : undefined,
    });
    return response.data;
  }
}

export const xFactorModuleService = new XFactorModuleService();
