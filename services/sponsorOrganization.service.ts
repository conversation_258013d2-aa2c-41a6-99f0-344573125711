import type { AxiosResponse } from 'axios';
import axios from '../lib/axios';

// --- Response Types ---
export interface Organization {
  id: number;
  name: string;
  type?: string;
  about?: string;
  website?: string;
  logo_url?: string;
  active_sponsors_count?: number;
  sponsor_role?: string;
  industries?: Array<string | { name: string; id?: number }>;
}

export interface ApiErrorResponse {
  error: string;
  errors?: Record<string, string[]>;
}

class SponsorOrganizationService {
  /**
   * Get the active organization for the logged-in sponsor
   */
  getOrganization = async (): Promise<AxiosResponse<Organization>> => {
    return axios.get('/api/v1/sponsor/organization');
  };

  /**
   * Get a specific organization by ID for the logged-in sponsor
   */
  getSpecificOrganization = async (id: number): Promise<AxiosResponse<Organization>> => {
    return axios.get(`/api/v1/sponsor/organizations/${id}`);
  };
}

export const sponsorOrganizationService = new SponsorOrganizationService();
