import type { AxiosResponse } from 'axios';
import { useSystemInviteStore } from '@/stores/systemInviteStore';
import axios, { csrf } from '../lib/axios';

// Types for request payloads
export interface AccountInfoPayload {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  street_address: string;
  unit?: string;
  city: string;
  state: string;
  zip_code: string;
  password: string;
  profile_type?: string;
}

export interface StudentDetailsPayload {
  state: string;
  county: string;
  high_school: string;
  school_id?: string;
  graduation_year: string;
  current_gpa: string;
  current_class_rank: string;
  gender: string;
  height: string;
  weight: string;
  career_interests: string[];
}

export interface DetailsPayload {
  state: string;
  county: string;
  high_school: string;
  school_id?: string;
  graduation_year: string;
  current_gpa: number;
  current_class_rank?: string;
  gender: string;
  height: string;
  weight: number;
  career_interests: string[];
  twitter?: string;
  instagram?: string;
  facebook?: string;
  hudl?: string;
  custom_link?: string;
  profile_photo?: string | null;
}

export interface Sport {
  name: string;
  id?: number;
  is_custom: boolean;
  order: number;
}

export interface SportsPayload {
  sports: Sport[];
}

export interface InvolvementItem {
  name: string;
  date_range: string;
  description: string;
  order: number;
}

export interface InvolvementPayload {
  items: InvolvementItem[];
}

export interface WorkExperiencePayload {
  items: InvolvementItem[]; // Same structure as involvement items
}

export interface StoryPayload {
  content: string;
}

// Response type for all onboarding steps
export interface OnboardingStepResponse {
  current_step: string;
  next_step: string | null;
  prefill?: Partial<AccountInfoPayload>;
}

class PositiveAthleteOnboardingService {
  private getToken(): string {
    const inviteData = useSystemInviteStore.getState().inviteData;
    if (!inviteData?.invite?.data?.token) {
      throw new Error('No invite token found. Please start from the invite link.');
    }
    return inviteData.invite.data.token;
  }

  startPositiveAthleteOnboarding = async (): Promise<AxiosResponse<OnboardingStepResponse>> => {
    return axios.post('/api/v1/onboarding/positive-athlete/intro', { token: this.getToken() });
  };

  submitAccountInfo = async (
    data: AccountInfoPayload
  ): Promise<AxiosResponse<OnboardingStepResponse>> => {
    return axios.post('/api/v1/onboarding/positive-athlete/account-info', {
      ...data,
      token: this.getToken(),
    });
  };

  completeOnboarding = async (
    data: AccountInfoPayload
  ): Promise<AxiosResponse<{ redirect: string }>> => {
    return axios.post('/api/v1/onboarding/positive-athlete/complete', {
      ...data,
      token: this.getToken(),
    });
  };

  submitDetails = async (
    data: DetailsPayload | FormData
  ): Promise<AxiosResponse<OnboardingStepResponse>> => {
    // Check if data is FormData (for file uploads)
    if (data instanceof FormData) {
      // Add the token to the FormData
      data.append('token', this.getToken());

      // For FormData, we need to ensure proper headers
      return axios.post('/api/v1/onboarding/positive-athlete/details', data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    }

    // Handle regular JSON data
    return axios.post('/api/v1/onboarding/positive-athlete/details', {
      ...data,
      token: this.getToken(),
    });
  };

  submitSports = async (data: SportsPayload): Promise<AxiosResponse<OnboardingStepResponse>> => {
    return axios.post('/api/v1/onboarding/positive-athlete/sports', {
      ...data,
      token: this.getToken(),
    });
  };

  submitInvolvement = async (
    data: InvolvementPayload
  ): Promise<AxiosResponse<OnboardingStepResponse>> => {
    return axios.post('/api/v1/onboarding/positive-athlete/involvement', {
      ...data,
      token: this.getToken(),
    });
  };

  submitWorkExperience = async (
    data: WorkExperiencePayload
  ): Promise<AxiosResponse<OnboardingStepResponse>> => {
    return axios.post('/api/v1/onboarding/positive-athlete/work-experience', {
      ...data,
      token: this.getToken(),
    });
  };

  submitStory = async (data: StoryPayload): Promise<AxiosResponse<OnboardingStepResponse>> => {
    return axios.post('/api/v1/onboarding/positive-athlete/story', {
      ...data,
      token: this.getToken(),
    });
  };
}

export const positiveAthleteOnboardingService = new PositiveAthleteOnboardingService();
