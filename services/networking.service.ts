import axiosInstance from '@/lib/axios';

/**
 * Represents a connection between users
 */
export interface Connection {
  id: number;
  requesterId: number;
  recipientId: number;
  status: string;
  createdAt: string;
  requester: UserBasicData | null;
  recipient: UserBasicData | null;
}

/**
 * Represents basic user data
 */
export interface UserBasicData {
  id: number;
  firstName: string;
  lastName: string;
  profileImageUrl: string | null;
}

/**
 * Request to create a connection
 */
export interface CreateConnectionRequest {
  recipientId: number;
}

/**
 * Request to block a user
 */
export interface BlockUserRequest {
  userId: number;
}

export interface ConnectWithMessageRequest {
  recipientId: string;
  message: string;
}

export interface CanConnectResponse {
  can_connect: boolean;
}

export interface ConnectWithMessageResponse {
  success: boolean;
  connection: Connection;
  conversation: any; // Replace with proper type when available
  message: any; // Replace with proper type when available
}

export const NetworkingService = {
  /**
   * Get all connections for the authenticated user
   */
  getConnections: async () => {
    const response = await axiosInstance.get<Connection[]>('/api/v1/connections', {
      skipAuthRedirect: true,
    });
    return response.data;
  },

  /**
   * Get accepted connections for the authenticated user
   */
  getAcceptedConnections: async () => {
    const response = await axiosInstance.get<Connection[]>('/api/v1/connections/accepted', {
      skipAuthRedirect: true,
    });
    return response.data;
  },

  /**
   * Get pending connection requests for the authenticated user
   */
  getPendingConnections: async () => {
    const response = await axiosInstance.get<Connection[]>('/api/v1/connections/pending', {
      skipAuthRedirect: true,
    });
    return response.data;
  },

  /**
   * Create a connection request
   */
  createConnectionRequest: async (request: CreateConnectionRequest) => {
    const response = await axiosInstance.post<Connection>('/api/v1/connections', request, {
      skipAuthRedirect: true,
    });
    return response.data;
  },

  /**
   * Accept a connection request
   */
  acceptConnectionRequest: async (connectionId: number) => {
    await axiosInstance.patch(
      `/api/v1/connections/${connectionId}/accept`,
      {},
      {
        skipAuthRedirect: true,
      }
    );
  },

  /**
   * Reject a connection request
   */
  rejectConnectionRequest: async (connectionId: number) => {
    await axiosInstance.patch(
      `/api/v1/connections/${connectionId}/reject`,
      {},
      {
        skipAuthRedirect: true,
      }
    );
  },

  /**
   * Block a user
   */
  blockUser: async (request: BlockUserRequest) => {
    await axiosInstance.post('/api/v1/connections/block', request, {
      skipAuthRedirect: true,
    });
  },

  /**
   * Unblock a user
   */
  unblockUser: async (request: BlockUserRequest) => {
    await axiosInstance.delete(`/api/v1/connections/block/${request.userId}`, {
      skipAuthRedirect: true,
    });
  },

  /**
   * Check if the authenticated user can connect with another user
   */
  canConnect: async (targetUserId: number): Promise<boolean> => {
    const response = await axiosInstance.get<{ can_connect: boolean }>(
      `/api/v1/connections/can-connect/${targetUserId}`,
      {
        skipAuthRedirect: true,
      }
    );
    return response.data.can_connect;
  },

  /**
   * Create a connection and send an initial message
   */
  connectWithMessage: async (
    request: ConnectWithMessageRequest
  ): Promise<ConnectWithMessageResponse> => {
    const response = await axiosInstance.post<ConnectWithMessageResponse>(
      '/api/v1/connections/message',
      request,
      {
        skipAuthRedirect: true,
      }
    );
    return response.data;
  },
};

// Export the NetworkingService as networkingService for backward compatibility
export const networkingService = NetworkingService;
