import axios from '@/lib/axios';
import type {
  Resume,
  ResumeAvatarData,
  ResumeSection,
  ResumeSectionContent,
  ResumeSectionType,
} from '@/types/resume';

export interface CreateResumeData {
  name: string;
  sections: {
    section_type: ResumeSectionType;
    is_enabled: boolean;
    content: ResumeSectionContent;
  }[];
}

export interface UpdateResumeSectionData {
  content: ResumeSectionContent;
  is_enabled?: boolean;
  avatar?: File;
}

export const ResumeService = {
  getResumes: () => axios.get<Resume>('/api/v1/resumes'),

  getResume: (id: string) => axios.get<Resume>(`/api/v1/resumes/${id}`),

  createResume: (data: CreateResumeData) => axios.post<Resume>('/api/v1/resumes', data),

  updateResume: (id: string, name: string) =>
    axios.post<Resume>(`/api/v1/resumes/${id}`, {
      _method: 'PUT',
      name,
    }),

  deleteResume: (id: string) => axios.delete(`/api/v1/resumes/${id}`),

  // Avatar management
  getResumeAvatar: (id: string) => axios.get<ResumeAvatarData>(`/api/v1/resumes/${id}/avatar`),

  updateResumeAvatar: (id: string, file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return axios.post<ResumeAvatarData>(`/api/v1/resumes/${id}/avatar`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  resetResumeAvatar: (id: string) =>
    axios.post<ResumeAvatarData>(`/api/v1/resumes/${id}/avatar/reset`),

  // Section management
  updateResumeSection: (
    resumeId: string,
    type: ResumeSectionType,
    data: UpdateResumeSectionData
  ) => {
    // If we have an avatar, use FormData
    if (data.avatar) {
      const formData = new FormData();
      formData.append('_method', 'PUT');

      // Iterate over content object and append its fields
      // Ensuring nested objects are handled if any (though profile content seems flat)
      Object.entries(data.content).forEach(([key, value]) => {
        if (typeof value === 'boolean') {
          formData.append(`content[${key}]`, value ? '1' : '0');
        } else if (value !== null && value !== undefined) {
          // Avoid appending null/undefined
          formData.append(`content[${key}]`, String(value));
        }
      });

      if (data.is_enabled !== undefined) {
        formData.append('is_enabled', data.is_enabled ? '1' : '0');
      }
      formData.append('avatar', data.avatar);

      return axios.post<Resume>(`/api/v1/resumes/${resumeId}/sections/${type}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    }

    // Otherwise, send as regular JSON
    return axios.put<Resume>(`/api/v1/resumes/${resumeId}/sections/${type}`, {
      content: data.content,
      is_enabled: data.is_enabled,
    });
  },

  uploadResumePdf: (pdfFile: Blob, fileName?: string) => {
    const formData = new FormData();
    // Use the provided filename if available, otherwise use a generic name
    const suggestedFileName = fileName || 'resume.pdf';
    formData.append('pdf_file', pdfFile, suggestedFileName);

    return axios.post('/api/v1/resumes/pdf', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  getResumePdf: () => {
    return axios.get<{ pdf_url: string }>('/api/v1/resumes/pdf');
  },

  deleteResumePdf: () => {
    return axios.delete('/api/v1/resumes/pdf');
  },
};
