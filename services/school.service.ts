import axios from '@/lib/axios';

export interface School {
  id: number;
  name: string;
  county_name?: string;
  state_code?: string;
  state_name?: string;
}

export interface SchoolSearchParams {
  query?: string;
  county_id?: number;
  state_code?: string;
}

export interface SchoolNomineeSport {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

export interface SchoolNomination {
  id: number;
  nominator_name: string;
  nominator_email: string;
  date_nominated: string;
  sport: string;
  relationship: string;
  note: string;
  status: string;
  type: string;
}

export interface SchoolAward {
  id: number;
  name: string;
  year: number;
  is_finalist: boolean;
  is_winner: boolean;
  verification_state: string;
  verified_at: string | null;
  details: string | null;
}

export interface SchoolScholarship {
  id: number;
  name: string;
  year: number;
  is_finalist: boolean;
  is_winner: boolean;
  verification_state: string;
  verified_at: string | null;
  details: string | null;
}

export interface SchoolNominee {
  id: number;
  first_name: string;
  last_name: string;
  full_name: string;
  profile_type: string;
  graduation_year: number | null;
  gender: string | null;
  sports: SchoolNomineeSport[];
  nominations: SchoolNomination[];
  awards: SchoolAward[];
  scholarships: SchoolScholarship[];
  has_awards: boolean;
  has_scholarships: boolean;
  needs_verification: boolean;
  profile_image: string | null;
  updated_at: string;
  created_at: string;
}

export interface SchoolNomineesResponse {
  data: SchoolNominee[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    path: string;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface SchoolNomineesParams {
  search?: string;
  graduation_year?: number;
  sport?: string;
  gender?: string;
  profile_type?: string;
  date_from?: string;
  date_to?: string;
  sort_by?: string;
  sort_direction?: string;
  page?: number;
  per_page?: number;
  include_winners?: boolean;
}

export type FilterParams = {
  search?: string;
  graduation_year?: number;
  sport?: string;
  gender?: string;
  profile_type?: string;
  date_from?: string;
  date_to?: string;
  sort_by?: string;
  sort_direction?: string;
  page?: number;
  per_page?: number;
  include_winners?: boolean;
};

class SchoolService {
  /**
   * Search schools by name with optional county and state filters
   */
  async searchSchools(params: SchoolSearchParams): Promise<School[]> {
    const response = await axios.get<School[]>('/api/v1/schools/search', {
      params,
    });
    return response.data;
  }

  /**
   * Get a school by ID
   */
  async getSchoolById(id: number): Promise<School> {
    const response = await axios.get<School>(`/api/v1/schools/${id}`);
    return response.data;
  }

  /**
   * Get nominees for the athletics director's school
   */
  async getNominees(params: SchoolNomineesParams = {}): Promise<SchoolNomineesResponse> {
    // Convert boolean parameters to ensure they're properly formatted for the API
    const formattedParams = {
      ...params,
      include_winners: params.include_winners === true ? 'true' : 'false',
    };

    const { data } = await axios.get('/api/v1/athletics-director/school/nominees', {
      params: formattedParams,
    });
    return data;
  }
}

export const schoolService = new SchoolService();
