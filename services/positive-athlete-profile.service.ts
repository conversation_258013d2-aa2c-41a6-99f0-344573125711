import axios from '@/lib/axios';
import type { Interest } from './interest.service';
import { transformSport } from './sports.service';

export interface FocalPoint {
  x: number;
  y: number;
}

export interface ProfilePhoto {
  id: string;
  url: string;
  thumbnail_url: string;
  width: number | null;
  height: number | null;
  order: number;
  focal_point: FocalPoint | null;
}

export interface UpdateProfilePhotosRequest {
  photos?: Array<{
    file: File;
    focal_point: FocalPoint;
  }>;
  delete_photo_ids?: string[];
  update_photo_id?: string; // For updating focal point of existing photo
  focal_point?: FocalPoint; // For updating focal point of existing photo
}

export interface UpdateAvatarRequest {
  file: File;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
}

export interface Story {
  content: string | null;
}

export interface UpdateStoryRequest {
  content: string;
}

export interface ProfileDetails {
  state?: string | null;
  county_id?: number | null;
  county_name?: string | null;
  email?: string | null;
  city?: string | null;
  phone?: string | null;
  school_id?: number | null;
  school_name?: string | null;
  college?: string | null;
  graduation_year?: number | null;
  gpa?: number | null;
  class_rank?: string | null;
  employer?: string | null;
  gender?: string | null;
  height_in_inches?: number | null;
  weight?: number | null;
  instagram?: string | null;
  facebook?: string | null;
  twitter?: string | null;
  hudl?: string | null;
  custom_link?: string | null;
  interest_ids: number[];
  interests: Interest[];
}

export type UpdateProfileDetailsRequest = Partial<ProfileDetails>;

export interface CommunityInvolvement {
  id?: number;
  title: string;
  date_range: string;
  description: string;
  order?: number;
}

export interface UpdateInvolvementsRequest {
  involvements: Omit<CommunityInvolvement, 'id' | 'order'>[];
}

export interface WorkExperience {
  id?: number;
  name: string;
  date: string;
  description: string;
  order?: number;
}

export interface UpdateWorkExperiencesRequest {
  experiences: WorkExperience[];
}

interface WorkExperienceResponse {
  id: number;
  name: string;
  date: string;
  description: string;
  order?: number;
}

export interface Sport {
  id: number;
  name: string;
  icon?: string;
  slug?: string;
  customName?: string;
  order?: number;
  isCustom?: boolean;
  uniqueId?: string;
}

export interface SportUpdate {
  id?: number;
  customName?: string;
  order: number;
  isCustom: boolean;
}

export interface UpdateSportsRequest {
  sports: SportUpdate[];
}

interface SearchSportsResponse {
  results: {
    platform: Sport[];
    custom: Sport[];
  };
}

export interface RecruiterStatus {
  enabled: boolean;
}

export interface UpdateRecruiterStatusRequest {
  enabled: boolean;
}

export interface PositiveAthleteProfileService {
  getProfilePhotos(): Promise<ProfilePhoto[]>;
  updateProfilePhotos(data: UpdateProfilePhotosRequest): Promise<ProfilePhoto[]>;
  getAvatar(): Promise<string | null>;
  getAvatarFile(): Promise<File | null>;
  updateAvatar(data: UpdateAvatarRequest): Promise<{ url: string }>;
  getStory(): Promise<Story>;
  updateStory(data: UpdateStoryRequest): Promise<Story>;
  getProfileDetails(): Promise<ProfileDetails>;
  updateProfileDetails(data: UpdateProfileDetailsRequest): Promise<ProfileDetails>;
  getCommunityInvolvements(): Promise<CommunityInvolvement[]>;
  updateCommunityInvolvements(data: UpdateInvolvementsRequest): Promise<CommunityInvolvement[]>;
  getWorkExperiences(): Promise<WorkExperience[]>;
  updateWorkExperiences(data: UpdateWorkExperiencesRequest): Promise<WorkExperience[]>;
  getSports(): Promise<Sport[]>;
  updateSports(data: UpdateSportsRequest): Promise<Sport[]>;
  searchSports(query: string): Promise<{ platform: Sport[]; custom: Sport[] }>;
  getRecruiterStatus(): Promise<RecruiterStatus>;
  updateRecruiterStatus(data: UpdateRecruiterStatusRequest): Promise<RecruiterStatus>;
}

export class PositiveAthleteProfileServiceImpl implements PositiveAthleteProfileService {
  async getProfilePhotos(): Promise<ProfilePhoto[]> {
    const response = await axios.get<ProfilePhoto[]>('/api/v1/profile/photos');
    return response.data;
  }

  async updateProfilePhotos(data: UpdateProfilePhotosRequest): Promise<ProfilePhoto[]> {
    const formData = new FormData();

    if (data.photos) {
      data.photos.forEach((photo, index) => {
        formData.append(`photos[${index}][file]`, photo.file);
        formData.append(`photos[${index}][focal_point][x]`, photo.focal_point.x.toString());
        formData.append(`photos[${index}][focal_point][y]`, photo.focal_point.y.toString());
      });
    }

    if (data.delete_photo_ids) {
      data.delete_photo_ids.forEach((id, index) => {
        formData.append(`delete_photo_ids[${index}]`, id);
      });
    }

    if (data.update_photo_id && data.focal_point) {
      formData.append('update_photo_id', data.update_photo_id);
      formData.append('focal_point[x]', data.focal_point.x.toString());
      formData.append('focal_point[y]', data.focal_point.y.toString());
    }

    const response = await axios.post<ProfilePhoto[]>('/api/v1/profile/photos', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  async getAvatar(): Promise<string | null> {
    const response = await axios.get<{ url: string } | null>('/api/v1/profile/avatar');
    return response.data?.url ?? null;
  }

  async getAvatarFile(): Promise<File | null> {
    const avatarUrl = await this.getAvatar();
    if (!avatarUrl) return null;

    try {
      const response = await fetch(avatarUrl);
      if (!response.ok) throw new Error('Failed to fetch avatar');

      const blob = await response.blob();
      return new File([blob], 'avatar.jpg', { type: 'image/jpeg' });
    } catch (error) {
      console.error('Error fetching avatar file:', error);
      return null;
    }
  }

  async updateAvatar(data: UpdateAvatarRequest): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append('file', data.file);

    const response = await axios.post<{ url: string }>('/api/v1/profile/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  async getStory(): Promise<Story> {
    const response = await axios.get<Story>('/api/v1/profile/story');
    return response.data;
  }

  async updateStory(data: UpdateStoryRequest): Promise<Story> {
    const response = await axios.put<Story>('/api/v1/profile/story', data);
    return response.data;
  }

  async getProfileDetails(): Promise<ProfileDetails> {
    const response = await axios.get<ProfileDetails>('/api/v1/profile');
    return response.data;
  }

  async updateProfileDetails(data: UpdateProfileDetailsRequest): Promise<ProfileDetails> {
    const response = await axios.put<ProfileDetails>('/api/v1/profile', data);
    return response.data;
  }

  async getCommunityInvolvements(): Promise<CommunityInvolvement[]> {
    const response = await axios.get<CommunityInvolvement[]>('/api/v1/profile/involvements');
    return response.data;
  }

  async updateCommunityInvolvements(
    data: UpdateInvolvementsRequest
  ): Promise<CommunityInvolvement[]> {
    const response = await axios.put<CommunityInvolvement[]>('/api/v1/profile/involvements', data);
    return response.data;
  }

  async getWorkExperiences(): Promise<WorkExperience[]> {
    const response = await axios.get<WorkExperience[]>('/api/v1/profile/work-experiences');
    return response.data;
  }

  async updateWorkExperiences(data: UpdateWorkExperiencesRequest): Promise<WorkExperience[]> {
    const response = await axios.put<WorkExperience[]>('/api/v1/profile/work-experiences', data);
    return response.data;
  }

  async getSports(): Promise<Sport[]> {
    const response = await axios.get<{ sports: Sport[] }>('/api/v1/profile/sports');
    return response.data.sports.map(transformSport);
  }

  async updateSports(data: UpdateSportsRequest): Promise<Sport[]> {
    const response = await axios.put<{ sports: Sport[] }>('/api/v1/profile/sports', data);
    return response.data.sports.map(transformSport);
  }

  async searchSports(query: string): Promise<{ platform: Sport[]; custom: Sport[] }> {
    const response = await axios.get<{ results: { platform: Sport[]; custom: Sport[] } }>(
      '/api/v1/profile/sports/search',
      {
        params: { query },
      }
    );

    return {
      platform: response.data.results.platform.map(transformSport),
      custom: response.data.results.custom.map(transformSport),
    };
  }

  async getRecruiterStatus(): Promise<RecruiterStatus> {
    const response = await axios.get<RecruiterStatus>('/api/v1/profile/recruiter');
    return response.data;
  }

  async updateRecruiterStatus(data: UpdateRecruiterStatusRequest): Promise<RecruiterStatus> {
    const response = await axios.post<RecruiterStatus>('/api/v1/profile/recruiter', data);
    return response.data;
  }
}

export const positiveAthleteProfileService = new PositiveAthleteProfileServiceImpl();
