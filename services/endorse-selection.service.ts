import axios from '@/lib/axios';
import type { ApiResponse } from './positive-athlete-profile.service'; // Assuming this can be reused or adapted

export interface PublicEndorsementRequestData {
  userId: number; // ID of the athlete being endorsed
  name: string; // Name of the person submitting the endorsement
  relation: string; // Relation of the endorser to the athlete
  endorsements: number[]; // Changed from endorsementIds to endorsements
}

export interface PublicEndorsementResponseData {
  success: boolean;
  message: string;
  endorsementCount: number;
  // data?: any; // Include if there's a data object in the response, adjust type accordingly
}

class EndorseSelectionService {
  /**
   * Submit public endorsements for a user.
   */
  async submitEndorsement(
    payload: PublicEndorsementRequestData
  ): Promise<PublicEndorsementResponseData> {
    // The backend controller returns a JsonResponse directly, not necessarily wrapped in ApiResponmse<T> with a 'data' key.
    // It sets status codes 200 or 201.
    const response = await axios.post<PublicEndorsementResponseData>(
      '/api/v1/endorsements',
      payload
    );
    return response.data; // Assuming the backend returns PublicEndorsementResponseData directly
  }
}

export const endorseSelectionService = new EndorseSelectionService();
