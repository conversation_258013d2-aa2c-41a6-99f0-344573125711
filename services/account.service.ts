import axios from '@/lib/axios';

export interface ProfileData {
  first_name: string;
  last_name: string;
  email: string;
  notification_email: string | null;
  phone: string | null;
  life_stage: string | null;
  organization_name?: string | null;
  organization_description?: string | null;
  organization_website?: string | null;
  organization_logo?: string | null;
}

export interface UpdateProfileRequest {
  first_name?: string;
  last_name?: string;
  email?: string;
  notification_email?: string | null;
  phone?: string | null;
  life_stage?: string | null;
  organization_name?: string | null;
  organization_description?: string | null;
  organization_website?: string | null;
  organization_logo?: string | null;
}

export interface AddressData {
  street_address_1: string | null;
  street_address_2: string | null;
  city: string | null;
  state_code: string | null;
  zip: string | null;
}

export interface UpdateAddressRequest {
  street_address_1?: string | null;
  street_address_2?: string | null;
  city?: string | null;
  state_code?: string | null;
  zip?: string | null;
}

export interface UpdatePasswordRequest {
  current_password: string;
  new_password: string;
  new_password_confirmation: string;
}

export interface DeleteAccountRequest {
  password: string;
  confirmation: string;
}

export interface AccountService {
  getProfile(): Promise<ProfileData>;
  getAddress(): Promise<AddressData>;
  updateProfile(data: UpdateProfileRequest): Promise<ProfileData>;
  updateAddress(data: UpdateAddressRequest): Promise<AddressData>;
  updatePassword(data: UpdatePasswordRequest): Promise<void>;
  deleteAccount(data: DeleteAccountRequest): Promise<void>;
}

export class AccountServiceImpl implements AccountService {
  async getProfile(): Promise<ProfileData> {
    const response = await axios.get<ProfileData>('/api/v1/account/profile');
    return response.data;
  }

  async getAddress(): Promise<AddressData> {
    const response = await axios.get<AddressData>('/api/v1/account/address');
    return response.data;
  }

  async updateProfile(data: UpdateProfileRequest): Promise<ProfileData> {
    const response = await axios.patch<ProfileData>('/api/v1/account/profile', data);
    return response.data;
  }

  async updateAddress(data: UpdateAddressRequest): Promise<AddressData> {
    const response = await axios.patch<AddressData>('/api/v1/account/address', data);
    return response.data;
  }

  async updatePassword(data: UpdatePasswordRequest): Promise<void> {
    await axios.patch('/api/v1/account/password', data);
  }

  async deleteAccount(data: DeleteAccountRequest): Promise<void> {
    await axios.delete('/api/v1/account', { data });
  }
}

export const accountService = new AccountServiceImpl();
