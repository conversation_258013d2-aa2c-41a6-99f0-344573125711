import axios, { csrf, isAxiosError, resetCsrf } from '@/lib/axios';
import { AuthResponse, LoginCredentials, User } from '@/types/auth';

export const getCurrentUser = async (): Promise<User | null> => {
  try {
    const response = await axios.get<User>('/api/v1/user', {
      // Don't throw on 401/403
      validateStatus: status => status < 500,
      // Don't redirect to logout on 401 - this is expected for public pages
      skipAuthRedirect: true,
    });

    // Only return user data on successful response
    if (response.status === 200) {
      return response.data;
    }

    return null;
  } catch (error) {
    console.error('Get user error:', error);
    return null;
  }
};

export const login = async (credentials: LoginCredentials): Promise<AuthResponse> => {
  try {
    await csrf();
    const response = await axios.post<AuthResponse>('/api/v1/login', credentials);
    return response.data;
  } catch (error) {
    throw handleError(error);
  }
};

export const logout = async (): Promise<void> => {
  try {
    // First, clear local storage
    localStorage.removeItem('auth-storage');

    // Then, call the logout endpoint
    await axios.post(
      '/api/v1/logout',
      {},
      {
        // Don't redirect on 401/403
        validateStatus: status => status >= 200 && status < 500,
        // Don't redirect to logout on 401 - we're already logging out
        skipAuthRedirect: true,
      }
    );
  } catch (error) {
    console.error('Logout error:', error);
  } finally {
    // Always reset CSRF and redirect
    resetCsrf();
    // Force a hard navigation to ensure clean state
    window.location.href = '/login';
  }
};

export const requestPasswordReset = async (email: string): Promise<void> => {
  try {
    await csrf();
    await axios.post('/api/v1/forgot-password', { email });
  } catch (error) {
    throw handleError(error);
  }
};

export const resetPassword = async (data: {
  token: string;
  email: string;
  password: string;
  password_confirmation: string;
}): Promise<void> => {
  try {
    await csrf();
    await axios.post('/api/v1/reset-password', data);
  } catch (error) {
    throw handleError(error);
  }
};

const handleError = (error: any): Error => {
  if (isAxiosError(error)) {
    // Handle rate limiting (429)
    if (error.response?.status === 429) {
      return new Error('Too many requests. Please wait before trying again.');
    }

    // Handle validation errors (422)
    if (error.response?.status === 422) {
      const validationData = error.response.data;

      // If we have validation errors object, return the first error message
      if (validationData?.errors) {
        const errors = validationData.errors;
        // Check for email field error first (most common for forgot password)
        if (errors.email?.[0]) {
          return new Error(errors.email[0]);
        }

        // Check for other field errors
        for (const field of ['token', 'password', 'password_confirmation']) {
          if (errors[field]?.[0]) {
            return new Error(errors[field][0]);
          }
        }
      }

      // Fallback to general validation message
      return new Error(validationData?.message || 'Please check your input and try again.');
    }

    // Handle specific status codes
    if (error.response?.status === 409) {
      // For conflicts (like duplicate email), return the server's message
      return new Error(error.response.data.message || 'Account already exists');
    }

    // Handle other API errors
    return new Error(error.response?.data?.message || 'An error occurred during the request');
  }

  // Handle network errors
  return new Error('Network error. Please check your connection and try again.');
};
