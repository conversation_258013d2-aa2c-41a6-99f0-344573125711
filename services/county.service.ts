import axios from '@/lib/axios';
import type { StateCode } from '@/utils/constants/states';

export interface County {
  id: number;
  name: string;
  state: string;
  state_name: string | null;
}

export interface CountyHierarchy {
  market: {
    id: number;
    name: string;
    region: {
      id: number;
      name: string;
    };
  };
  sub_region: {
    id: number;
    name: string;
  } | null;
}

export interface CountyDetails extends County {
  hierarchy: CountyHierarchy;
}

class CountyService {
  /**
   * Search counties by name and state
   */
  async searchCounties(query: string, state: StateCode | null): Promise<County[]> {
    const response = await axios.get<County[]>('/api/v1/counties/search', {
      params: {
        query,
        state,
      },
    });
    return response.data;
  }

  /**
   * Get detailed county information including regional hierarchy
   */
  async getCountyDetails(countyId: number): Promise<CountyDetails> {
    const response = await axios.get<CountyDetails>(`/api/v1/counties/${countyId}`);
    return response.data;
  }
}

export const countyService = new CountyService();
