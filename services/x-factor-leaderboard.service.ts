import axios from '@/lib/axios';

// We don't directly use LaravelPagination here anymore for the response type

/**
 * Represents a single entry in the X-Factor leaderboard.
 */
export interface XFactorLeaderboardItem {
  id: number;
  rank: number;
  first_name: string;
  last_name: string;
  graduation_year: number | null;
  school_id: number | null;
  school_name: string | null;
  profile_image_url?: string | null; // from avatar_url in response
  completed_modules_count: number;
  badge_name: string | null;
  state_code: string | null;
  sports: string[]; // Added to match backend data
  can_connect?: boolean; // Added from swagger example
  public_profile?: boolean; // Added from swagger example
  // state_code, can_connect, public_profile are also available if needed
}

/**
 * Filters for fetching the X-Factor leaderboard.
 */
export interface XFactorLeaderboardFilters {
  region?: string;
  state?: string;
  graduation_year?: number;
  academic_year?: string;
  all_time?: boolean | number; // Allow boolean for component state, number for API
  start_date?: string;
  end_date?: string;
  page?: number;
  per_page?: number;
}

/**
 * Represents the actual API response structure for the X-Factor leaderboard.
 */
export interface XFactorLeaderboardApiResponse {
  rankings: XFactorLeaderboardItem[];
  current_user: XFactorLeaderboardItem | null; // Can be null if user not on leaderboard
  current_user_rank: number | null;
  region_name: string | null;
  state_name: string | null;
  graduation_year: number | null;
  academic_year: string | null;
  start_date: string | null;
  end_date: string | null;
  all_time: boolean;
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
}

export interface XFactorLeaderboardService {
  getLeaderboard(filters?: XFactorLeaderboardFilters): Promise<XFactorLeaderboardApiResponse>;
}

export class XFactorLeaderboardServiceImpl implements XFactorLeaderboardService {
  async getLeaderboard(
    filters: XFactorLeaderboardFilters = {}
  ): Promise<XFactorLeaderboardApiResponse> {
    const apiParams: Record<string, any> = {
      region: filters.region,
      state: filters.state,
      graduation_year: filters.graduation_year,
      academic_year: filters.academic_year,
      start_date: filters.start_date,
      end_date: filters.end_date,
      page: filters.page,
      per_page: filters.per_page ?? 10, // Default to 10 as per current component state
    };

    if (typeof filters.all_time === 'boolean') {
      apiParams.all_time = filters.all_time ? 1 : 0;
    } else if (typeof filters.all_time === 'number') {
      apiParams.all_time = filters.all_time;
    }

    const response = await axios.get<XFactorLeaderboardApiResponse>(
      '/api/v1/x-factor/leaderboard',
      {
        params: apiParams,
      }
    );
    // Map avatar_url to profile_image_url if necessary
    // The swagger example shows rankings items have avatar_url.
    // Our XFactorLeaderboardItem uses profile_image_url.
    response.data.rankings = response.data.rankings.map(item => ({
      ...(item as any), // Cast to any to allow adding potentially missing fields from type
      profile_image_url: (item as any).avatar_url || item.profile_image_url,
      // Ensure sports is always an array, even if missing or null from API for an item
      sports: Array.isArray((item as any).sports) ? (item as any).sports : [],
    }));

    if (response.data.current_user) {
      response.data.current_user = {
        ...(response.data.current_user as any),
        profile_image_url:
          (response.data.current_user as any).avatar_url ||
          response.data.current_user.profile_image_url,
        sports: Array.isArray((response.data.current_user as any).sports)
          ? (response.data.current_user as any).sports
          : [],
      };
    }

    return response.data;
  }
}

export const xFactorLeaderboardService = new XFactorLeaderboardServiceImpl();
