import axios from '@/lib/axios';
import type { ApiResponse } from './positive-athlete-profile.service';

export interface Industry {
  id: number;
  name: string;
}

class IndustryService {
  /**
   * Search industries by name
   */
  async searchIndustries(query: string): Promise<Industry[]> {
    const response = await axios.get<Industry[]>('/api/v1/industries/search', {
      params: {
        query,
      },
    });

    return response.data;
  }
}

export const industryService = new IndustryService();
