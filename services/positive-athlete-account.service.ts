import axios from '@/lib/axios';

export interface ParentAccountData {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone: string | null;
  avatar_url: string | null;
  status: 'active' | 'pending' | 'expired';
  invitation_sent_at: string | null;
  invitation_expires_at: string | null;
  is_pending_invite: boolean;
}

export interface LinkParentRequest {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string | null;
}

export interface PositiveAthleteAccountService {
  getParents(): Promise<ParentAccountData[]>;
  linkParent(data: LinkParentRequest): Promise<ParentAccountData>;
  unlinkParent(parentId: number): Promise<void>;
  unlinkParentByEmail(email: string): Promise<void>;
}

export class PositiveAthleteAccountServiceImpl implements PositiveAthleteAccountService {
  async getParents(): Promise<ParentAccountData[]> {
    const response = await axios.get<ParentAccountData[]>('/api/v1/athlete/account/parents');
    return response.data;
  }

  async linkParent(data: LinkParentRequest): Promise<ParentAccountData> {
    const response = await axios.post<ParentAccountData>('/api/v1/athlete/account/parents', data);
    return response.data;
  }

  async unlinkParent(parentId: number): Promise<void> {
    await axios.delete(`/api/v1/athlete/account/parents/${parentId}`);
  }

  async unlinkParentByEmail(email: string): Promise<void> {
    await axios.post('/api/v1/athlete/account/parents/unlink-by-email', { email });
  }
}

export const positiveAthleteAccountService = new PositiveAthleteAccountServiceImpl();
