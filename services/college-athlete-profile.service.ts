import axios from '@/lib/axios';
import type { Interest } from './interest.service';

export interface CollegeAthleteDetails {
  state: string;
  college: string;
  graduation_year: number;
  gender: string;
  gpa?: number | null;
  height?: string | null;
  weight?: number | null;
  career_interests?: Interest[] | null;
  twitter?: string | null;
  instagram?: string | null;
  facebook?: string | null;
  hudl?: string | null;
  custom_link?: string | null;
}

export type UpdateCollegeAthleteDetailsRequest = Partial<CollegeAthleteDetails>;

export interface CollegeAthleteProfileService {
  getProfileDetails(): Promise<CollegeAthleteDetails>;
  updateProfileDetails(data: UpdateCollegeAthleteDetailsRequest): Promise<CollegeAthleteDetails>;
}

export class CollegeAthleteProfileServiceImpl implements CollegeAthleteProfileService {
  async getProfileDetails(): Promise<CollegeAthleteDetails> {
    const response = await axios.get<CollegeAthleteDetails>(
      '/api/v1/profile/college-athlete/details'
    );
    return response.data;
  }

  async updateProfileDetails(
    data: UpdateCollegeAthleteDetailsRequest
  ): Promise<CollegeAthleteDetails> {
    const response = await axios.put<CollegeAthleteDetails>(
      '/api/v1/profile/college-athlete/details',
      data
    );
    return response.data;
  }
}

export const collegeAthleteProfileService = new CollegeAthleteProfileServiceImpl();
