import type { AxiosResponse } from 'axios';
import axios from '../lib/axios';

export interface OrganizationData {
  id: number;
  name: string;
  website: string | null;
  about: string | null;
  logo_url: string | null;
  created_at: string;
  updated_at: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface UpdateOrganizationRequest {
  organization_name?: string;
  organization_website?: string | null;
  organization_about?: string | null;
  organization_logo?: File | null;
}

class OrganizationService {
  /**
   * Get the authenticated sponsor's organization details
   */
  getOrganization = async (): Promise<AxiosResponse<OrganizationData>> => {
    return axios.get('/api/v1/organization');
  };

  /**
   * Update the authenticated sponsor's organization details
   */
  updateOrganization = async (
    data: UpdateOrganizationRequest | FormData
  ): Promise<AxiosResponse<OrganizationData>> => {
    // Handle file upload with FormData
    if (data instanceof FormData) {
      return axios.post('/api/v1/organization', data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    }

    // Create FormData if we have a logo file
    if (data.organization_logo instanceof File) {
      const formData = new FormData();
      if (data.organization_name) formData.append('organization_name', data.organization_name);
      if (data.organization_website !== undefined) {
        formData.append('organization_website', data.organization_website || '');
      }
      if (data.organization_about !== undefined) {
        formData.append('organization_about', data.organization_about || '');
      }
      formData.append('organization_logo', data.organization_logo);

      return axios.post('/api/v1/organization', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    }

    // JSON request (including logo removal case)
    return axios.post('/api/v1/organization', data, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
  };
}

export const organizationService = new OrganizationService();
