import { AxiosInstance } from 'axios';
import axiosInstance from '../lib/axios';

/**
 * Represents an industry
 */
export interface Industry {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
  pivot?: {
    opportunity_id: number;
    industry_id: number;
    created_at: string;
    updated_at: string;
  };
}

/**
 * Represents an organization
 */
export interface Organization {
  id: number;
  name: string;
  description?: string;
  website?: string;
  about?: string;
}

/**
 * Represents an opportunity
 */
export interface Opportunity {
  id: string;
  title: string;
  description: string;
  details?: string;
  qualifications?: string;
  responsibilities?: string;
  benefits?: string;
  type: string;
  status: string;
  location_type: string;
  location_display?: string;
  term: string;
  is_featured: boolean;
  location?: string;
  city?: string;
  state_code?: string;
  state_name?: string;
  organization_name?: string;
  organizationName?: string;
  organizationLogo?: string;
  organization_logo?: string;
  organization?: Organization;
  apply_url?: string;
  industries?: Industry[] | string[];
  created_at?: string;
  updated_at?: string;
  isBookmarked?: boolean;
}

/**
 * Represents the bookmark status of an opportunity
 */
export interface BookmarkStatus {
  isBookmarked: boolean;
  opportunityId: string;
}

/**
 * Represents a collection of bookmarked opportunity IDs
 */
export interface BookmarkedIds {
  ids: string[];
  data?: {
    ids: string[];
  };
}

/**
 * Service for interacting with the opportunities API
 */
class OpportunitiesService {
  private apiUrl: string;
  private axiosInstance: AxiosInstance;

  constructor(apiUrl: string, axiosInstance: AxiosInstance) {
    this.apiUrl = apiUrl;
    this.axiosInstance = axiosInstance;
  }

  /**
   * Get a single opportunity by ID
   * @param id - The ID of the opportunity to fetch
   * @returns The opportunity details
   */
  async getOpportunity(id: string): Promise<Opportunity> {
    try {
      console.log(`Fetching opportunity with ID: ${id}`);
      const url = `${this.apiUrl}/opportunities/${id}`;
      console.log(`GET request to: ${url}`);

      const response = await this.axiosInstance.get(url);
      console.log('Opportunity response:', response.data);

      // The response structure should be { data: Opportunity } or directly Opportunity
      if (response.data && response.data.data) {
        return response.data.data;
      } else if (response.data && response.data.id) {
        return response.data;
      }

      // If we don't get the expected structure, log a warning and throw an error
      console.warn('Unexpected response structure for opportunity:', response.data);
      throw new Error('Invalid response from opportunity fetch');
    } catch (error) {
      console.error(`Error fetching opportunity ${id}:`, error);
      throw error;
    }
  }

  /**
   * Toggle the bookmark status of an opportunity
   * @param opportunityId - The ID of the opportunity to toggle
   * @returns The updated bookmark status
   */
  async toggleBookmark(opportunityId: string): Promise<BookmarkStatus> {
    try {
      console.log(`Toggling bookmark for opportunity ${opportunityId}`);
      const url = `${this.apiUrl}/opportunities/${opportunityId}/bookmark`;
      console.log(`POST request to: ${url}`);

      const response = await this.axiosInstance.post(url);
      console.log('Toggle bookmark response:', response.data);

      // The response structure should be { data: { isBookmarked: boolean, opportunityId: string } }
      // or directly { isBookmarked: boolean, opportunityId: string }
      if (
        response.data &&
        response.data.data &&
        typeof response.data.data.isBookmarked === 'boolean'
      ) {
        return response.data.data;
      } else if (response.data && typeof response.data.isBookmarked === 'boolean') {
        return response.data;
      }

      // If we don't get the expected structure, log a warning and throw an error
      console.warn('Unexpected response structure for toggle bookmark:', response.data);
      throw new Error('Invalid response from bookmark toggle');
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      throw error;
    }
  }

  /**
   * Get all bookmarked opportunity IDs for the current user
   * @returns An array of bookmarked opportunity IDs
   */
  async getBookmarkedIds(): Promise<string[]> {
    try {
      console.log('Fetching bookmarked opportunity IDs');
      const url = `${this.apiUrl}/opportunities/bookmarked/ids`;
      console.log(`GET request to: ${url}`);

      const response = await this.axiosInstance.get(url);
      console.log('Bookmarked IDs response:', response.data);

      // The response structure is { ids: Array<string|number> }
      if (response.data && response.data.ids && Array.isArray(response.data.ids)) {
        // Convert all IDs to strings to ensure consistency
        return response.data.ids.map((id: string | number) => String(id));
      }

      // If we don't get the expected structure, log a warning and return empty array
      console.warn('Unexpected response structure for bookmarked IDs:', response.data);
      return [];
    } catch (error) {
      console.error('Error fetching bookmarked opportunity IDs:', error);
      return []; // Return empty array instead of throwing to prevent cascading errors
    }
  }

  /**
   * Get all bookmarked opportunities for the current user
   * @returns An array of bookmarked opportunities
   */
  async getBookmarkedOpportunities(): Promise<Opportunity[]> {
    try {
      // This endpoint doesn't exist in the current API routes
      // We should use the IDs endpoint and then fetch each opportunity individually
      const bookmarkedIds = await this.getBookmarkedIds();
      console.log('Bookmarked IDs:', bookmarkedIds);

      if (!bookmarkedIds || bookmarkedIds.length === 0) {
        console.log('No bookmarked opportunities found');
        return [];
      }

      // Fetch each bookmarked opportunity by ID
      try {
        const opportunities = await Promise.all(bookmarkedIds.map(id => this.getOpportunity(id)));
        return opportunities;
      } catch (fetchError) {
        console.error('Error fetching individual opportunities:', fetchError);
        return [];
      }
    } catch (error) {
      console.error('Error in getBookmarkedOpportunities:', error);
      return []; // Return empty array instead of throwing to prevent cascading errors
    }
  }

  /**
   * Create a new opportunity or update an existing one
   * @param data - The opportunity data to save
   * @param id - Optional ID for updating an existing opportunity
   * @returns The created or updated opportunity
   */
  async createOrUpdateOpportunity(data: any, id?: string): Promise<Opportunity> {
    try {
      console.log(`${id ? 'Updating' : 'Creating'} opportunity`, { data, id });

      const url = id
        ? `${this.apiUrl}/sponsor/opportunities/${id}`
        : `${this.apiUrl}/sponsor/opportunities`;

      const method = id ? 'put' : 'post';
      console.log(`${method.toUpperCase()} request to: ${url}`);

      const response = await this.axiosInstance[method](url, data);
      console.log('Save opportunity response:', response.data);

      // The response structure should be { data: Opportunity } or directly Opportunity
      if (response.data && response.data.data) {
        return response.data.data;
      } else if (response.data && response.data.id) {
        return response.data;
      }

      // If we don't get the expected structure, log a warning and throw an error
      console.warn('Unexpected response structure from save opportunity:', response.data);
      throw new Error('Invalid response from opportunity save');
    } catch (error) {
      console.error(`Error ${id ? 'updating' : 'creating'} opportunity:`, error);
      throw error;
    }
  }
}

// Export a singleton instance
const API_URL = '/api/v1'; // Default API URL
export const opportunitiesService = new OpportunitiesService(API_URL, axiosInstance);
