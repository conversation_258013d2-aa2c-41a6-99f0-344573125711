import axios from '@/lib/axios';

export interface LocationCoordinate {
  id: string;
  city: string;
  state_code: string;
}

export interface LocationOption {
  id: string;
  value: string;
  label: string;
  city: string;
  state_code: string;
}

class LocationService {
  /**
   * Search locations by city name
   */
  async searchCities(query: string): Promise<LocationOption[]> {
    const response = await axios.get<{ success: boolean; data: LocationOption[] }>(
      '/api/v1/locations/search',
      {
        params: {
          query,
        },
      }
    );

    if (response.data.success) {
      return response.data.data;
    }

    return [];
  }

  /**
   * Get a specific location by ID
   */
  async getLocationById(locationId: string): Promise<LocationOption | null> {
    const response = await axios.get<{ success: boolean; data: LocationOption }>(
      `/api/v1/locations/${locationId}`
    );

    if (response.data.success) {
      return response.data.data;
    }

    return null;
  }
}

export const locationService = new LocationService();
