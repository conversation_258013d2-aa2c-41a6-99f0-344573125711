import type { AxiosResponse } from 'axios';
import { useSystemInviteStore } from '@/stores/systemInviteStore';
import axios from '../lib/axios';

// --- Parent Invite Data Type ---
export interface ParentInviteData {
  token: string;
  email: string;
  created_at: number;
  first_name?: string;
  last_name?: string;
  athlete_user_id?: number;
  athlete_name?: string;
}

// --- Request Payloads ---

export interface ParentAccountInfoPayload {
  first_name: string;
  last_name: string;
  email: string; // Email might be prefilled from invite, but still part of the form
  phone: string;
  password: string;
}

// --- Response Types ---

// Based on OnboardingStepResponse DTO in backend
export interface ParentOnboardingStepResponse {
  current_step: string; // e.g., 'account_info', 'completed'
  next_step: string | null;
  prefill?: Record<string, any> | null; // Data relevant to the current step
}

// Error response
export interface ApiErrorResponse {
  error: string;
  errors?: Record<string, string[]>;
}

class ParentOnboardingService {
  private getToken(): string {
    const inviteData = useSystemInviteStore.getState().inviteData;
    if (!inviteData?.invite?.data?.token) {
      // Consider more robust error handling or redirect logic here
      throw new Error('No invite token found. Please start from the invite link.');
    }
    return inviteData.invite.data.token;
  }

  /**
   * Get athlete name from invite data
   */
  getAthleteNameFromInvite(): string | undefined {
    const inviteData = useSystemInviteStore.getState().inviteData;
    const data = inviteData?.invite?.data as ParentInviteData | undefined;
    return data?.athlete_name;
  }

  /**
   * Call the intro endpoint to initialize parent onboarding
   */
  intro = async (): Promise<AxiosResponse<ParentOnboardingStepResponse>> => {
    return axios.post(`/api/v1/onboarding/parent/intro`, {
      token: this.getToken(),
    });
  };

  /**
   * Submit account information for parent onboarding
   */
  submitAccountInfo = async (
    data: ParentAccountInfoPayload
  ): Promise<AxiosResponse<ParentOnboardingStepResponse>> => {
    return axios.post(`/api/v1/onboarding/parent/account-info`, {
      ...data,
      token: this.getToken(),
    });
  };
}

export const parentOnboardingService = new ParentOnboardingService();
