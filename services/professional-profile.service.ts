import axios from '@/lib/axios';
import type { Interest } from './interest.service';

export interface ProfessionalDetails {
  state: string;
  employer: string;
  job_title?: string | null;
  interests?: Interest[] | null;
  twitter?: string | null;
  instagram?: string | null;
  facebook?: string | null;
  hudl?: string | null;
  custom_link?: string | null;
}

export type UpdateProfessionalDetailsRequest = Partial<ProfessionalDetails>;

export interface ProfessionalProfileService {
  getProfileDetails(): Promise<ProfessionalDetails>;
  updateProfileDetails(data: UpdateProfessionalDetailsRequest): Promise<ProfessionalDetails>;
}

export class ProfessionalProfileServiceImpl implements ProfessionalProfileService {
  async getProfileDetails(): Promise<ProfessionalDetails> {
    const response = await axios.get<ProfessionalDetails>('/api/v1/profile/professional/details');
    return response.data;
  }

  async updateProfileDetails(data: UpdateProfessionalDetailsRequest): Promise<ProfessionalDetails> {
    const response = await axios.put<ProfessionalDetails>(
      '/api/v1/profile/professional/details',
      data
    );
    return response.data;
  }
}

export const professionalProfileService = new ProfessionalProfileServiceImpl();
