import type { AxiosResponse } from 'axios';
import { useSystemInviteStore } from '@/stores/systemInviteStore';
import axios from '../lib/axios';

// Types for request payloads
export interface AccountInfoPayload {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  street_address: string;
  unit?: string;
  city: string;
  state: string;
  zip_code: string;
  password: string;
  profile_type?: string;
}

export interface StudentDetailsPayload {
  state: string;
  county: string;
  high_school: string;
  school_id?: string;
  graduation_year: string;
  current_gpa: string;
  current_class_rank: string;
  gender: string;
  height: string;
  weight: string;
  career_interests: string[];
}

export interface DetailsPayload {
  state: string;
  county: string;
  high_school: string;
  school_id?: string;
  graduation_year: string;
  current_gpa: number;
  current_class_rank?: string;
  gender: string;
  height: string;
  weight: number;
  career_interests: string[];
  twitter?: string;
  instagram?: string;
  facebook?: string;
  hudl?: string;
  custom_link?: string;
  profile_photo?: string | null;
}

export interface Sport {
  name: string;
  id?: number;
  is_custom: boolean;
  order: number;
}

export interface SportsPayload {
  sports: Sport[];
}

export interface InvolvementItem {
  name: string;
  date_range: string;
  description: string;
  order: number;
}

export interface InvolvementPayload {
  items: InvolvementItem[];
}

export interface WorkExperiencePayload {
  items: InvolvementItem[]; // Same structure as involvement items
}

export interface StoryPayload {
  content: string;
}

// Response type for all onboarding steps
export interface OnboardingStepResponse {
  current_step: string;
  next_step: string | null;
  prefill?: Partial<AccountInfoPayload>;
}

// --- Payload Types for AD Onboarding ---

// Based on App\\Data\\Onboarding\\AccountInfoDTO used in AD Controller
export interface ADAccountInfoPayload {
  first_name: string;
  last_name: string;
  email: string;
  phone: string; // Assuming phone is optional or can be empty string if not provided
  password: string;
  notification_email?: string;
  // Note: The generic AccountInfoDTO might include address fields.
  // If AD onboarding strictly requires them, they should be added here.
  // For now, aligning with what ADAccountInfoStep collects.
}

// Based on App\\Data\\Onboarding\\AthleticsDirector\\DetailsDTO
export interface ADDetailsPayload {
  title?: string;
  state?: string;
  county?: string;
  school_id?: number;
  // profile_photo is handled by FormData
  twitter?: string;
  instagram?: string;
  facebook?: string;
  hudl?: string;
  custom_link?: string;
}

// For FormData submission including profile_photo for the Details step
export interface ADDetailsFormData extends FormData {
  append(
    name: keyof ADDetailsPayload | 'profile_photo' | 'token',
    value: string | Blob,
    fileName?: string
  ): void;
}

// Based on App\\Data\\Onboarding\\AthleticsDirector\\BioDTO
export interface ADBioPayload {
  content?: string; // From the BioDTO itself, now only content
}

// Based on App\\Data\\Onboarding\\AthleticsDirector\\SchoolSuccessesDTO
export interface SuccessItemStructure {
  name: string;
  date_range: string;
  description: string;
  order: number;
}
export interface ADSchoolSuccessesPayload {
  successes: SuccessItemStructure[];
}

// Based on App\\Data\\Onboarding\\CommunityInvolvementDTO
export interface InvolvementItemStructure {
  name: string;
  date_range: string;
  description: string;
  order: number;
}
export interface ADCommunityInvolvementPayload {
  items: InvolvementItemStructure[];
}

// --- Response Type for AD Onboarding Steps ---
// Based on App\\Data\\Onboarding\\OnboardingStepResponse
export interface ADOnboardingStepResponse {
  current_step: string;
  next_step: string | null;
  prefill?: Partial<ADAccountInfoPayload>; // Prefill typically for account info
  redirect?: string;
}

class ADOnboardingServiceClass {
  private getToken(): string {
    const inviteData = useSystemInviteStore.getState().inviteData;
    if (!inviteData?.invite?.data?.token) {
      // It's better to let the UI handle this, but a fallback error.
      console.error('No invite token found. Onboarding may fail.');
      return ''; // Return empty or handle as critical error upstream
    }
    return inviteData.invite.data.token;
  }

  // Corresponds to AthleticsDirectorOnboardingController@intro
  startADOnboarding = async (): Promise<AxiosResponse<ADOnboardingStepResponse>> => {
    const token = this.getToken();
    if (!token) throw new Error('No invite token available for starting AD onboarding.');
    return axios.post('/api/v1/onboarding/athletics-director/intro', { token });
  };

  // Corresponds to AthleticsDirectorOnboardingController@submitAccountInfo
  submitADAccountInfo = async (
    data: ADAccountInfoPayload
  ): Promise<AxiosResponse<ADOnboardingStepResponse>> => {
    const token = this.getToken();
    if (!token) throw new Error('No invite token available for submitting AD account info.');
    return axios.post('/api/v1/onboarding/athletics-director/account-info', {
      ...data,
      token,
    });
  };

  // Corresponds to AthleticsDirectorOnboardingController@submitDetails
  submitADDetails = async (
    data: ADDetailsPayload | ADDetailsFormData
  ): Promise<AxiosResponse<ADOnboardingStepResponse>> => {
    const token = this.getToken();
    if (!token) throw new Error('No invite token available for submitting AD details.');

    if (data instanceof FormData) {
      data.append('token', token);
      return axios.post('/api/v1/onboarding/athletics-director/details', data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    }
    // Handle regular JSON data if no file, though details usually involves a photo
    return axios.post('/api/v1/onboarding/athletics-director/details', {
      ...data,
      token,
    });
  };

  // Corresponds to AthleticsDirectorOnboardingController@submitBio
  // This method now only submits the bio content.
  submitADBio = async (data: ADBioPayload): Promise<AxiosResponse<ADOnboardingStepResponse>> => {
    const token = this.getToken();
    if (!token) throw new Error('No invite token available for submitting AD bio.');
    return axios.post('/api/v1/onboarding/athletics-director/bio', {
      ...data,
      token,
    });
  };

  // Corresponds to AthleticsDirectorOnboardingController@submitSchoolSuccesses
  submitADSchoolSuccesses = async (
    data: ADSchoolSuccessesPayload
  ): Promise<AxiosResponse<ADOnboardingStepResponse>> => {
    const token = this.getToken();
    if (!token) throw new Error('No invite token available for submitting AD school successes.');
    return axios.post('/api/v1/onboarding/athletics-director/school-successes', {
      ...data,
      token,
    });
  };

  // Corresponds to AthleticsDirectorOnboardingController@submitCommunityInvolvement
  // This is the final step in the AD onboarding flow as per the controller.
  submitADCommunityInvolvement = async (
    data: ADCommunityInvolvementPayload
  ): Promise<AxiosResponse<ADOnboardingStepResponse>> => {
    const token = this.getToken();
    if (!token)
      throw new Error('No invite token available for submitting AD community involvement.');
    return axios.post('/api/v1/onboarding/athletics-director/community-involvement', {
      ...data,
      token,
    });
  };

  // Corresponds to AthleticsDirectorOnboardingController@complete
  completeADOnboarding = async (): Promise<AxiosResponse<ADOnboardingStepResponse>> => {
    const token = this.getToken();
    if (!token) throw new Error('No invite token available for completing AD onboarding.');
    // The 'complete' endpoint in the controller does not expect a body other than the token,
    // which is implicitly handled by middleware or needs to be confirmed if it should be in the body.
    // Assuming token is still needed in body for consistency with other calls or handled by middleware.
    // If the endpoint truly takes no body, this can be an empty object or GET request if applicable.
    // Based on other methods, sending token in body:
    return axios.post('/api/v1/onboarding/athletics-director/complete', { token });
  };
}

export const aDOnboardingService = new ADOnboardingServiceClass();
