import axios from '@/lib/axios';

/**
 * Type for user ID parameter
 * - Use number for actual user IDs (preferred for all user identifiers)
 * - Use null to automatically use the current authenticated user's ID
 */
export type UserIdParam = number | null;

export interface Certificate {
  id: number;
  name: string;
  description: string;
  imageUrl: string;
  earnedAt: string;
  courseId: string;
  courseName: string;
}

export interface Badge {
  id: number;
  name: string;
  description?: string;
  imageUrl?: string;
  earnedAt?: string;
  type?: string;
  // Backend specific fields
  moduleRequirement: number;
  isAchieved: boolean;
  achievedAssetUrl?: string;
  unachievedAssetUrl?: string;
  currentProgress?: number;
  progressPercentage?: number;
}

export interface TopicInvestment {
  name: string;
  percentage: number;
  minutesSpent: number;
  modulesCompleted: number;
}

export interface LearningStats {
  coursesCompleted: number;
  coursesInProgress: number;
  certificatesEarned: number;
  badgesEarned: number;
  totalModulesCompleted: number;
  averageModuleScore?: number;
  hoursSpent?: number;
  modulesCompleted?: number;
  nationalLeaderboardRank?: number;
  stateLeaderboardRank?: number;
  stateName?: string;
  academicYear?: string;
}

export interface LearningInvestment {
  topics?: TopicInvestment[];
  primaryTopic?: string;
  primaryTopicPercentage?: number;
  // Legacy fields for backward compatibility
  hoursSpent?: number;
  modulesCompleted?: number;
  lastActive?: string | null;
}

class LearningProgressService {
  /**
   * Get user's learning statistics
   * @param userId - The ID of the user to get learning statistics for:
   *                 - Pass a number for specific user IDs (preferred)
   *                 - Pass null or omit to use the current authenticated user's ID
   */
  async getLearningStats(userId: UserIdParam = null): Promise<LearningStats> {
    // Default stats object
    const defaultStats: LearningStats = {
      coursesCompleted: 0,
      coursesInProgress: 0,
      certificatesEarned: 0,
      badgesEarned: 0,
      totalModulesCompleted: 0,
    };

    try {
      // Use 'me' in the URL when userId is null or undefined
      // This tells the backend to use the current authenticated user
      const userPath = userId !== null ? userId : 'me';
      const endpoint = `/api/v1/users/${userPath}/learning/stats`;
      const response = await axios.get<any>(endpoint);

      // Helper function to map stats data to our interface format
      const mapStatsData = (data: any): LearningStats => ({
        coursesCompleted: data.coursesCompleted || 0,
        coursesInProgress: 0, // Default value if not provided
        certificatesEarned: 0, // Default value if not provided
        badgesEarned: 0, // Default value if not provided
        totalModulesCompleted: data.modulesCompleted || 0,
        averageModuleScore: data.averageModuleScore,
        hoursSpent: data.hoursSpent,
        modulesCompleted: data.modulesCompleted,
        nationalLeaderboardRank: data.nationalLeaderboardRank,
        stateLeaderboardRank: data.stateLeaderboardRank,
        stateName: data.stateName,
        academicYear: data.academicYear,
      });

      // Check if we have data in the expected format
      if (response.data && typeof response.data === 'object') {
        // If the response is directly the stats object
        if (
          response.data.averageModuleScore !== undefined ||
          response.data.hoursSpent !== undefined ||
          response.data.modulesCompleted !== undefined
        ) {
          return mapStatsData(response.data);
        }

        // If the response has a data property that contains the stats
        if (response.data.data && typeof response.data.data === 'object') {
          return mapStatsData(response.data.data);
        }
      }

      // Return default stats if we couldn't find the data in the expected format
      console.error('Unexpected stats response format:', response.data);
      return defaultStats;
    } catch (error) {
      console.error('Error fetching learning stats:', error);
      // Return default stats data on error
      return defaultStats;
    }
  }

  /**
   * Get user's earned certificates
   * @param userId - The ID of the user to get certificates for:
   *                 - Pass a number for specific user IDs (preferred)
   *                 - Pass null or omit to use the current authenticated user's ID
   */
  async getCertificates(userId: UserIdParam = null): Promise<Certificate[]> {
    try {
      // Use 'me' in the URL when userId is null or undefined
      const userPath = userId !== null ? userId : 'me';
      const endpoint = `/api/v1/users/${userPath}/learning/certificates`;
      const response = await axios.get<any>(endpoint);

      // Check if we have data in the expected format
      if (response.data && Array.isArray(response.data)) {
        return response.data;
      }

      // If the response has a data property that contains the certificates array
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        return response.data.data;
      }

      console.error('Unexpected certificates response format:', response.data);
      return [];
    } catch (error) {
      console.error('Error fetching certificates:', error);
      return []; // Return empty array on error
    }
  }

  /**
   * Get user's earned badges
   * @param userId - The ID of the user to get badges for:
   *                 - Pass a number for specific user IDs (preferred)
   *                 - Pass null or omit to use the current authenticated user's ID
   */
  async getBadges(userId: UserIdParam = null): Promise<Badge[]> {
    try {
      // Use 'me' in the URL when userId is null or undefined
      const userPath = userId !== null ? userId : 'me';
      const endpoint = `/api/v1/users/${userPath}/learning/badges`;
      const response = await axios.get<any>(endpoint);

      // Helper function to map badge data
      const mapBadgeData = (badges: any[]): Badge[] => {
        if (!Array.isArray(badges)) {
          return [];
        }

        return badges.map((badge: any) => ({
          id: badge.id,
          name: badge.name,
          description: badge.description || '',
          // Use achievedAssetUrl as imageUrl if badge is achieved, otherwise use unachievedAssetUrl
          imageUrl: badge.isAchieved ? badge.achievedAssetUrl : badge.unachievedAssetUrl,
          // Map other backend fields
          moduleRequirement: badge.moduleRequirement,
          isAchieved: badge.isAchieved === true, // Ensure boolean conversion
          achievedAssetUrl: badge.achievedAssetUrl,
          unachievedAssetUrl: badge.unachievedAssetUrl,
          currentProgress: badge.currentProgress,
          progressPercentage: badge.progressPercentage,
          // Set earnedAt to current date if badge is achieved (backend doesn't provide this)
          earnedAt: badge.isAchieved ? new Date().toISOString() : undefined,
          type: 'achievement', // Default type
        }));
      };

      // Check if we have data in the expected format
      if (response.data && Array.isArray(response.data)) {
        return mapBadgeData(response.data);
      }

      // If the response has a data property that contains the badges array
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        return mapBadgeData(response.data.data);
      }

      console.error('Unexpected badges response format:', response.data);
      return [];
    } catch (error) {
      console.error('Error fetching badges:', error);
      return []; // Return empty array on error
    }
  }

  /**
   * Get user's learning investment data
   * @param userId - The ID of the user to get learning investment for:
   *                 - Pass a number for specific user IDs (preferred)
   *                 - Pass null or omit to use the current authenticated user's ID
   */
  async getLearningInvestment(userId: UserIdParam = null): Promise<LearningInvestment> {
    // Default investment object
    const defaultInvestment: LearningInvestment = {
      topics: [],
      primaryTopic: undefined,
      primaryTopicPercentage: undefined,
      hoursSpent: 0,
      modulesCompleted: 0,
      lastActive: null,
    };

    try {
      // Use 'me' in the URL when userId is null or undefined
      const userPath = userId !== null ? userId : 'me';
      const endpoint = `/api/v1/users/${userPath}/learning/investment`;
      const response = await axios.get<any>(endpoint);

      // Helper function to map investment data with topics
      const mapInvestmentWithTopics = (data: any): LearningInvestment => {
        if (!data.topics || !Array.isArray(data.topics)) {
          return defaultInvestment;
        }

        return {
          topics: data.topics,
          primaryTopic: data.primaryTopic,
          primaryTopicPercentage: data.primaryTopicPercentage,
          // Calculate legacy fields for backward compatibility
          hoursSpent: data.topics.reduce(
            (total: number, topic: TopicInvestment) => total + Math.ceil(topic.minutesSpent / 60),
            0
          ),
          modulesCompleted: data.topics.reduce(
            (total: number, topic: TopicInvestment) => total + topic.modulesCompleted,
            0
          ),
          lastActive: new Date().toISOString(), // Default to current date
        };
      };

      // Helper function to map legacy investment data
      const mapLegacyInvestment = (data: any): LearningInvestment => ({
        hoursSpent: data.hoursSpent || 0,
        modulesCompleted: data.modulesCompleted || 0,
        lastActive: data.lastActive || null,
        // Add empty topics array for new format compatibility
        topics: [],
        primaryTopic: undefined,
        primaryTopicPercentage: undefined,
      });

      // Check if we have data in the expected format
      if (response.data && typeof response.data === 'object') {
        // Check for new format with topics array
        if (response.data.topics && Array.isArray(response.data.topics)) {
          return mapInvestmentWithTopics(response.data);
        }

        // Check for legacy format
        if (
          response.data.hoursSpent !== undefined ||
          response.data.modulesCompleted !== undefined
        ) {
          return mapLegacyInvestment(response.data);
        }

        // Check for nested data property
        if (response.data.data && typeof response.data.data === 'object') {
          // Check for new format with topics array in nested data
          if (response.data.data.topics && Array.isArray(response.data.data.topics)) {
            return mapInvestmentWithTopics(response.data.data);
          }

          // Check for legacy format in nested data
          if (
            response.data.data.hoursSpent !== undefined ||
            response.data.data.modulesCompleted !== undefined
          ) {
            return mapLegacyInvestment(response.data.data);
          }
        }
      }

      console.error('Unexpected investment response format:', response.data);
      return defaultInvestment;
    } catch (error) {
      console.error('Error fetching learning investment:', error);
      return defaultInvestment;
    }
  }

  /**
   * Get a complete summary of a user's learning progress
   * @param userId - The ID of the user to get learning progress for:
   *                 - Pass a number for specific user IDs (preferred)
   *                 - Pass null or omit to use the current authenticated user's ID
   */
  async getLearningProgressSummary(userId: UserIdParam = null): Promise<{
    certificates: Certificate[];
    stats: LearningStats;
    investment: LearningInvestment;
    badges: Badge[];
  }> {
    try {
      // Use 'me' in the URL when userId is null or undefined
      const userPath = userId !== null ? userId : 'me';
      const endpoint = `/api/v1/users/${userPath}/learning/summary`;
      const response = await axios.get<any>(endpoint);

      if (response.data && typeof response.data === 'object') {
        return {
          certificates: Array.isArray(response.data.certificates) ? response.data.certificates : [],
          stats: response.data.stats || {
            coursesCompleted: 0,
            coursesInProgress: 0,
            certificatesEarned: 0,
            badgesEarned: 0,
            totalModulesCompleted: 0,
          },
          investment: response.data.investment || {
            topics: [],
            primaryTopic: undefined,
            primaryTopicPercentage: undefined,
            hoursSpent: 0,
            modulesCompleted: 0,
            lastActive: null,
          },
          badges: Array.isArray(response.data.badges) ? response.data.badges : [],
        };
      }

      console.error('Unexpected summary response format:', response.data);
      return {
        certificates: [],
        stats: {
          coursesCompleted: 0,
          coursesInProgress: 0,
          certificatesEarned: 0,
          badgesEarned: 0,
          totalModulesCompleted: 0,
        },
        investment: {
          topics: [],
          primaryTopic: undefined,
          primaryTopicPercentage: undefined,
          hoursSpent: 0,
          modulesCompleted: 0,
          lastActive: null,
        },
        badges: [],
      };
    } catch (error) {
      console.error('Error fetching learning progress summary:', error);
      return {
        certificates: [],
        stats: {
          coursesCompleted: 0,
          coursesInProgress: 0,
          certificatesEarned: 0,
          badgesEarned: 0,
          totalModulesCompleted: 0,
        },
        investment: {
          topics: [],
          primaryTopic: undefined,
          primaryTopicPercentage: undefined,
          hoursSpent: 0,
          modulesCompleted: 0,
          lastActive: null,
        },
        badges: [],
      };
    }
  }
}

export const learningProgressService = new LearningProgressService();
