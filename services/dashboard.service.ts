import axios from '@/lib/axios';

export interface RecommendedAthlete {
  id: number;
  name: string;
  first_name: string;
  last_name: string;
  interests: string[];
  sports: string[];
  school?: string;
  avatar?: string;
}

export interface RecommendedModule {
  id: number;
  title: string;
  thumbnail: string;
  type: string;
  category: string;
}

export interface RecommendedOpportunity {
  id: number;
  title: string;
  description: string;
  details: string;
  location: string;
  state_code: string;
  location_type: string;
  subtype: string;
  organization_name: string;
  organization_logo: string;
  interests: string[];
  term: string;
}

export interface DashboardService {
  getRecommendedAthletes(limit?: number): Promise<RecommendedAthlete[]>;
  getRecommendedModules(limit?: number): Promise<RecommendedModule[]>;
  getRecommendedOpportunities(limit?: number): Promise<RecommendedOpportunity[]>;
}

export class DashboardServiceImpl implements DashboardService {
  async getRecommendedAthletes(limit: number = 3): Promise<RecommendedAthlete[]> {
    const response = await axios.get<RecommendedAthlete[]>(
      '/api/v1/dashboard/recommended-athletes',
      {
        params: { limit },
      }
    );
    return response.data;
  }

  async getRecommendedModules(limit: number = 10): Promise<RecommendedModule[]> {
    const response = await axios.get<RecommendedModule[]>('/api/v1/dashboard/recommended-modules', {
      params: { limit },
    });
    return response.data;
  }

  async getRecommendedOpportunities(limit: number = 4): Promise<RecommendedOpportunity[]> {
    const response = await axios.get<RecommendedOpportunity[]>(
      '/api/v1/dashboard/recommended-opportunities',
      {
        params: { limit },
      }
    );
    return response.data;
  }
}

export const dashboardService = new DashboardServiceImpl();
