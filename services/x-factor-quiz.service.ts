import axios from '@/lib/axios';
import type { Quiz, TestAttempt } from '@/types/quiz';

export interface QuizAnswer {
  id: number;
  answer: string;
  isCorrect: boolean;
}

export interface QuizQuestion {
  id: number;
  question: string;
  type: string;
  answers: QuizAnswer[];
}

export interface QuizAttempt {
  id: number;
  testId: number;
  userId?: number;
  status: string;
  startedAt: string;
  endsAt: string;
  completedAt: string | null;
  score: number | null;
  responses: QuizResponse[] | null;
}

export interface QuizResponse {
  id: number;
  questionId: number;
  response: string;
  correct: boolean | null;
}

export interface XFactorQuizServiceImpl {
  getQuiz(moduleId: number): Promise<Quiz>;
  startQuizAttempt(moduleId: number): Promise<TestAttempt>;
  submitQuestionResponse(
    moduleId: number,
    attemptId: number,
    questionId: number,
    response: string
  ): Promise<QuizResponse>;
  completeQuizAttempt(
    moduleId: number,
    responses: Array<{ questionId: number; answerId: number }>
  ): Promise<TestAttempt>;
}

export const xFactorQuizService = {
  /**
   * Get quiz details for a module
   */
  async getQuiz(moduleId: number): Promise<Quiz> {
    const response = await axios.get<Quiz>(`/api/v1/x-factor/modules/${moduleId}/quiz`);
    return response.data;
  },

  /**
   * Start a new quiz attempt
   */
  async startQuizAttempt(moduleId: number): Promise<TestAttempt> {
    const response = await axios.post<TestAttempt>(
      `/api/v1/x-factor/modules/${moduleId}/quiz/attempts`
    );
    return response.data;
  },

  /**
   * Complete a quiz attempt
   */
  async completeQuizAttempt(
    moduleId: number,
    responses: Array<{ questionId: number; answerId: number }>
  ): Promise<TestAttempt> {
    const response = await axios.post<TestAttempt>(
      `/api/v1/x-factor/modules/${moduleId}/quiz/complete`,
      { responses }
    );
    return response.data;
  },
};
