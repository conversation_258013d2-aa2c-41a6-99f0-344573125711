import { isAxiosError } from 'axios';
import axios from '@/lib/axios';

/**
 * Base properties for all invite data types
 */
export interface BaseInviteData {
  email: string;
  token: string;
  created_at: number;
}

/**
 * Nomination data structure for nominated users
 */
interface NominationData {
  note: string;
  type: string;
  email: string;
  sport: string;
  last_name: string;
  first_name: string;
  school_name: string;
  relationship: string;
  nominator_email: string;
  system_invite_id: number;
  nominator_last_name: string;
  nominator_first_name: string;
}

/**
 * Structured invite data for nominated users (athletes, coaches)
 */
interface NominatedInviteData extends BaseInviteData {
  nomination: NominationData;
  nominator_email: string;
  nominator_name: string;
  school_name: string;
  sport: string;
  relationship: string;
  note: string;
}

/**
 * Union type for all possible invite data structures
 * This allows the type to be more flexible while still capturing
 * the common base properties in BaseInviteData
 */
export type InviteData = NominatedInviteData | BaseInviteData;

export interface SystemInviteResponse {
  invite: {
    type: string;
    email: string;
    data: InviteData;
  };
  onboarding: {
    current_state: string;
    profile_type: string;
  };
}

export const SystemInviteService = {
  async getInvite(token: string): Promise<SystemInviteResponse> {
    try {
      const response = await axios.get(`/api/v1/invites/${token}`);

      if (!response.data || !response.data.invite) {
        throw new Error('Invalid invite data structure');
      }

      return response.data;
    } catch (error) {
      if (isAxiosError(error)) {
        console.error('API Error:', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });
      }
      throw error;
    }
  },
};

export default SystemInviteService;
