import axios from '@/lib/axios';
import type { Interest } from './interest.service';

export interface FocalPoint {
  x: number;
  y: number;
}

export interface PublicPhoto {
  id: number;
  url: string;
  width: number | null;
  height: number | null;
  focal_point: FocalPoint | null;
}

export interface PublicPhotos {
  photos: PublicPhoto[];
}

export interface PublicAvatar {
  url: string;
}

export interface PublicStory {
  content: string | null;
}

export interface PublicSport {
  id: number | null;
  name: string;
  is_custom: boolean;
}

export interface PublicSports {
  sports: PublicSport[];
}

export interface PublicInvolvement {
  id: number;
  title: string;
  date_range: string;
  description: string;
  order: number;
}

export interface PublicInvolvements {
  involvements: PublicInvolvement[];
}

export interface PublicWorkExperience {
  id: number;
  name: string;
  date: string;
  description: string;
  order: number;
}

export interface PublicWorkExperiences {
  experiences: PublicWorkExperience[];
}

export interface PublicCareerInterest {
  id?: number;
  name: string;
  icon?: string;
}

export interface PublicCareerInterests {
  interests: PublicCareerInterest[];
}

export interface PublicScholarship {
  id: number;
  name: string;
  year: number;
  amount: number;
  region: string | null;
  state: string | null;
  details: any | null;
}

export interface PublicAward {
  id: number;
  name: string;
  year: number;
  type: string;
  region: string | null;
  state: string | null;
  details: any | null;
}

export interface UserAchievements {
  scholarships: PublicScholarship[];
  awards: PublicAward[];
}

export interface PublicDetails {
  state: string | null;
  county_id: number | null;
  county_name: string | null;
  county?: string | null;
  email: string | null;
  city: string | null;
  phone: string | null;
  school_id: number | null;
  school_name: string | null;
  college: string | null;
  employer: string | null;
  graduation_year: number | null;
  gpa: number | null;
  class_rank: string | null;
  gender: string | null;
  height_in_inches: number | null;
  weight: number | null;
  instagram: string | null;
  facebook: string | null;
  twitter: string | null;
  hudl: string | null;
  custom_link: string | null;
}

export interface PublicProfile {
  id: string;
  first_name: string;
  last_name: string;
  profile_type: string;
  school_name: string | null;
  graduation_year: number | null;
  twitter: string | null;
  linkedin: string | null;
  instagram: string | null;
  facebook: string | null;
  hudl: string | null;
  custom_link: string | null;
  has_achievements?: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
}

export interface PublicProfileService {
  getProfile(userId: string): Promise<PublicProfile>;
  getProfileDetails(userId: string): Promise<PublicDetails>;
  getSports(userId: string): Promise<PublicSports>;
  getStory(userId: string): Promise<PublicStory>;
  getInvolvements(userId: string): Promise<PublicInvolvements>;
  getAvatar(userId: string): Promise<PublicAvatar | null>;
  getPhotos(userId: string): Promise<PublicPhotos>;
  getCareerInterests(userId: string): Promise<PublicCareerInterests>;
  getWorkExperiences(userId: string): Promise<PublicWorkExperiences>;
  getUserAchievements(userId: string): Promise<UserAchievements>;
}

export class PublicProfileServiceImpl implements PublicProfileService {
  async getProfile(userId: string): Promise<PublicProfile> {
    const response = await axios.get<PublicProfile>(`/api/v1/profiles/${userId}`);
    return response.data;
  }

  async getProfileDetails(userId: string): Promise<PublicDetails> {
    const response = await axios.get<PublicDetails>(`/api/v1/profiles/${userId}/details`);
    return response.data;
  }

  async getSports(userId: string): Promise<PublicSports> {
    const response = await axios.get<PublicSports>(`/api/v1/profiles/${userId}/sports`);
    return response.data;
  }

  async getStory(userId: string): Promise<PublicStory> {
    const response = await axios.get<PublicStory>(`/api/v1/profiles/${userId}/story`);
    return response.data;
  }

  async getInvolvements(userId: string): Promise<PublicInvolvements> {
    const response = await axios.get<PublicInvolvements>(`/api/v1/profiles/${userId}/involvements`);
    return response.data;
  }

  async getAvatar(userId: string): Promise<PublicAvatar | null> {
    try {
      const response = await axios.get<PublicAvatar | null>(`/api/v1/profiles/${userId}/avatar`);
      if (!response.data || !response.data.url) {
        return null;
      }
      return response.data;
    } catch (error) {
      console.error('Error fetching avatar:', error);
      return null;
    }
  }

  async getPhotos(userId: string): Promise<PublicPhotos> {
    const response = await axios.get<PublicPhotos>(`/api/v1/profiles/${userId}/photos`);
    return response.data;
  }

  async getCareerInterests(userId: string): Promise<PublicCareerInterests> {
    const response = await axios.get<PublicCareerInterests>(
      `/api/v1/profiles/${userId}/career-interests`
    );
    return response.data;
  }

  async getWorkExperiences(userId: string): Promise<PublicWorkExperiences> {
    try {
      const response = await axios.get<PublicWorkExperiences>(
        `/api/v1/profiles/${userId}/work-experiences`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching work experiences:', error);
      // Return empty experiences array on error
      return { experiences: [] };
    }
  }

  async getUserAchievements(userId: string): Promise<UserAchievements> {
    const response = await axios.get<UserAchievements>(`/api/v1/users/${userId}/achievements`);
    return response.data;
  }
}

// Create a singleton instance
export const publicProfileService = new PublicProfileServiceImpl();
