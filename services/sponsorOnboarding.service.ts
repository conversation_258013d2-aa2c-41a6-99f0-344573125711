import type { AxiosResponse } from 'axios';
import { useSystemInviteStore } from '@/stores/systemInviteStore';
import type { OrganizationSearchResultDTO } from '@/types/organization'; // Assuming this type exists
import axios from '../lib/axios';
import { SystemInviteResponse } from './systemInvite.service';

// --- Request Payloads ---

export interface SponsorAccountInfoPayload {
  first_name: string;
  last_name: string;
  email: string; // Email might be prefilled from invite, but still part of the form
  phone: string;
  password: string;
}

// OrganizationInfoDTO from backend expects multipart/form-data if logo is present
// For the service, we'll accept FormData directly or an object type
export interface SponsorOrganizationInfoPayload {
  organization_id?: number | string | null; // If selected from search
  organization_name?: string; // If creating new or selected
  organization_website?: string | null; // Allow null
  organization_logo?: File | null;
  organization_about?: string | null; // Add rich text about field
}

export interface OrganizationSearchPayload {
  query: string;
}

// --- Response Types ---

// Based on SponsorOnboardingStepResponse DTO in backend
export interface SponsorOnboardingStepResponse {
  current_step: string; // e.g., 'account_info', 'organization_info', 'completed'
  next_step: string | null;
  step_data?: Record<string, any> | null; // Data relevant to the current step
}

// Based on the complete response
export interface SponsorCompleteResponse {
  redirect: string;
  user_id?: number;
}

// Error response
export interface ApiErrorResponse {
  error: string;
  errors?: Record<string, string[]>;
}

class SponsorOnboardingService {
  private getToken(): string {
    const inviteData = useSystemInviteStore.getState().inviteData;
    if (!inviteData?.invite?.data?.token) {
      // Consider more robust error handling or redirect logic here
      throw new Error('No invite token found. Please start from the invite link.');
    }
    return inviteData.invite.data.token;
  }

  /**
   * Gets the onboarding state using the general invite endpoint
   * Since we removed the sponsor-specific show route, we use the general
   * invite endpoint which returns both invite and onboarding data
   */
  getOnboardingState = async (): Promise<AxiosResponse<SystemInviteResponse>> => {
    const token = this.getToken();
    return axios.get(`/api/v1/invites/${token}`);
  };

  submitAccountInfo = async (
    data: SponsorAccountInfoPayload
  ): Promise<AxiosResponse<SponsorOnboardingStepResponse>> => {
    return axios.post(`/api/v1/onboarding/sponsor/account-info`, {
      ...data,
      token: this.getToken(),
    });
  };

  submitOrganizationInfo = async (
    data: SponsorOrganizationInfoPayload | FormData
  ): Promise<AxiosResponse<SponsorOnboardingStepResponse>> => {
    const token = this.getToken();

    // Check if data is FormData (contains file upload)
    if (data instanceof FormData) {
      // Add the token to the FormData
      data.append('token', token);

      return axios.post(`/api/v1/onboarding/sponsor/organization-info`, data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    }

    // Handle regular JSON data (no logo upload or selected existing org)
    return axios.post(`/api/v1/onboarding/sponsor/organization-info`, {
      ...data,
      token,
    });
  };

  searchOrganizations = async (
    data: OrganizationSearchPayload
  ): Promise<AxiosResponse<OrganizationSearchResultDTO[]>> => {
    // This endpoint might not require the token, but for consistency let's include it
    return axios.post('/api/v1/onboarding/sponsor/search-organizations', {
      ...data,
      token: this.getToken(),
    });
  };

  completeOnboarding = async (): Promise<AxiosResponse<SponsorCompleteResponse>> => {
    return axios.post(`/api/v1/onboarding/sponsor/complete`, {
      token: this.getToken(),
    });
  };
}

export const sponsorOnboardingService = new SponsorOnboardingService();
