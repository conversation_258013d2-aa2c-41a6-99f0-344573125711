import type { AxiosResponse } from 'axios';
import { useSystemInviteStore } from '@/stores/systemInviteStore';
import axios from '../lib/axios';

// Types for request payloads
export interface AccountInfoPayload {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  street_address: string;
  unit?: string;
  city: string;
  state: string;
  zip_code: string;
  password: string;
  profile_type?: string;
}

export interface DetailsPayload {
  state?: string;
  county?: string;
  high_school?: string;
  school_id?: number;
  twitter?: string;
  instagram?: string;
  facebook?: string;
  hudl?: string;
  custom_link?: string;
  profile_photo?: File | null;
}

export interface Sport {
  name: string;
  id?: number;
  is_custom: boolean;
  order: number;
}

export interface SportsPayload {
  sports: Sport[];
}

export interface InvolvementItem {
  name: string;
  date_range: string;
  description: string;
  order: number;
}

export interface InvolvementPayload {
  items: InvolvementItem[];
}

export interface TeamSuccessesPayload {
  successes: InvolvementItem[];
  additional_info?: string;
}

export interface StoryPayload {
  content: string;
}

// Response type for all onboarding steps
export interface OnboardingStepResponse {
  current_step: string;
  next_step: string | null;
  prefill?: Partial<AccountInfoPayload>;
}

class PositiveCoachOnboardingService {
  private getToken(): string {
    const inviteData = useSystemInviteStore.getState().inviteData;
    console.log('Invite data:', inviteData);
    if (!inviteData?.invite?.data?.token) {
      console.error('No invite token found in:', inviteData);
      throw new Error('No invite token found. Please start from the invite link.');
    }
    return inviteData.invite.data.token;
  }

  startPositiveCoachOnboarding = async (): Promise<AxiosResponse<OnboardingStepResponse>> => {
    try {
      const token = this.getToken();
      console.log('Using token for onboarding:', token);
      return axios.post('/api/v1/onboarding/positive-coach/intro', { token });
    } catch (error) {
      console.error('Error starting positive coach onboarding:', error);
      throw error;
    }
  };

  submitAccountInfo = async (
    data: AccountInfoPayload
  ): Promise<AxiosResponse<OnboardingStepResponse>> => {
    return axios.post('/api/v1/onboarding/positive-coach/account-info', {
      ...data,
      token: this.getToken(),
    });
  };

  submitDetails = async (
    data: DetailsPayload | FormData
  ): Promise<AxiosResponse<OnboardingStepResponse>> => {
    // Check if data is FormData (for file uploads)
    if (data instanceof FormData) {
      // Add the token to the FormData
      data.append('token', this.getToken());

      // For FormData, we need to ensure proper headers
      return axios.post('/api/v1/onboarding/positive-coach/details', data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    }

    // Handle regular JSON data
    return axios.post('/api/v1/onboarding/positive-coach/details', {
      ...data,
      token: this.getToken(),
    });
  };

  submitSports = async (data: SportsPayload): Promise<AxiosResponse<OnboardingStepResponse>> => {
    return axios.post('/api/v1/onboarding/positive-coach/sports', {
      ...data,
      token: this.getToken(),
    });
  };

  submitCommunityInvolvement = async (
    data: InvolvementPayload
  ): Promise<AxiosResponse<OnboardingStepResponse>> => {
    return axios.post('/api/v1/onboarding/positive-coach/community-involvement', {
      ...data,
      token: this.getToken(),
    });
  };

  submitTeamSuccesses = async (
    data: TeamSuccessesPayload
  ): Promise<AxiosResponse<OnboardingStepResponse>> => {
    return axios.post('/api/v1/onboarding/positive-coach/team-successes', {
      ...data,
      token: this.getToken(),
    });
  };

  submitStory = async (data: StoryPayload): Promise<AxiosResponse<OnboardingStepResponse>> => {
    return axios.post('/api/v1/onboarding/positive-coach/story', {
      ...data,
      token: this.getToken(),
    });
  };

  completeOnboarding = async (
    data: AccountInfoPayload
  ): Promise<AxiosResponse<{ redirect: string }>> => {
    return axios.post('/api/v1/onboarding/positive-coach/complete', {
      ...data,
      token: this.getToken(),
    });
  };
}

export const positiveCoachOnboardingService = new PositiveCoachOnboardingService();
