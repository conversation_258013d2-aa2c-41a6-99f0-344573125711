import axios from '@/lib/axios';
import type { ApiResponse } from './positive-athlete-profile.service';

export interface Interest {
  id: number;
  name: string;
  icon: string | null;
}

class InterestService {
  /**
   * Search interests by name
   */
  async searchInterests(query: string): Promise<Interest[]> {
    const response = await axios.get<ApiResponse<Interest[]>>('/api/v1/interests/search', {
      params: {
        query,
      },
    });

    return response.data.data;
  }
}

export const interestService = new InterestService();
