import axios from '@/lib/axios';

export interface EndorsementFromAPI {
  id: number;
  name: string;
  icon: string; // This is the string from the API, might be an icon name or key
}

class EndorsementsListService {
  /**
   * Fetch all endorsements
   */
  async getEndorsements(): Promise<EndorsementFromAPI[]> {
    const response = await axios.get<EndorsementFromAPI[]>('/api/v1/endorsements');
    return response.data;
  }
}

export const endorsementsListService = new EndorsementsListService();
