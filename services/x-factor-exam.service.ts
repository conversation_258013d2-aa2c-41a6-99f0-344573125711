import { QuestionType } from '@/enums/question-type.enum';
import { TestStatus } from '@/enums/test-status.enum';
import axios from '@/lib/axios';

export interface Answer {
  id: number;
  answer: string;
  isCorrect: boolean;
}

export interface Question {
  id: number;
  question: string;
  type: QuestionType;
  answers?: Answer[];
}

export interface ExamResponse {
  questionId: number;
  response: string;
  responseHtml?: string;
}

export interface ExamAttempt {
  id: number;
  testId: number;
  userId: number;
  status: TestStatus;
  score: number | null;
  startedAt: string;
  endsAt: string;
  completedAt?: string;
  gradedAt?: string;
  feedback?: string;
  responses: ExamResponse[];
}

export interface Exam {
  id: number;
  moduleId: number;
  timeLimit: number;
  passingScore: number;
  waitPeriod: number;
  questions: Question[];
  latestAttempt?: ExamAttempt;
  lastCompletedAttempt?: ExamAttempt;
}

export interface SubmitExamRequest {
  status: TestStatus;
  responses: ExamResponse[];
}

export interface XFactorExamService {
  getExamByModule(moduleId: number): Promise<Exam>;
  startExamAttempt(moduleId: number): Promise<ExamAttempt>;
  submitExam(moduleId: number, request: SubmitExamRequest): Promise<ExamAttempt>;
}

export class XFactorExamServiceImpl implements XFactorExamService {
  /**
   * Get exam details for a module
   */
  async getExamByModule(moduleId: number): Promise<Exam> {
    const { data } = await axios.get(`/api/v1/x-factor/modules/${moduleId}/exam`);
    return data;
  }

  /**
   * Start a new exam attempt
   */
  async startExamAttempt(moduleId: number): Promise<ExamAttempt> {
    const { data } = await axios.post(`/api/v1/x-factor/modules/${moduleId}/exam/attempts`);
    return data;
  }

  /**
   * Submit exam responses
   */
  async submitExam(moduleId: number, request: SubmitExamRequest): Promise<ExamAttempt> {
    const { data } = await axios.post(`/api/v1/x-factor/modules/${moduleId}/exam/submit`, request);
    return data;
  }
}

export const xFactorExamService = new XFactorExamServiceImpl();
