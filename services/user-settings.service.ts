import axios from '@/lib/axios';

export interface ProfileVisibility {
  public_profile: boolean;
}

export interface UserSettingsService {
  getProfileVisibility(): Promise<ProfileVisibility>;
  updateProfileVisibility(data: ProfileVisibility): Promise<ProfileVisibility>;
}

export class UserSettingsServiceImpl implements UserSettingsService {
  async getProfileVisibility(): Promise<ProfileVisibility> {
    const response = await axios.get<ProfileVisibility>('/api/v1/user/settings/profile-visibility');
    return response.data;
  }

  async updateProfileVisibility(data: ProfileVisibility): Promise<ProfileVisibility> {
    const response = await axios.post<ProfileVisibility>(
      '/api/v1/user/settings/profile-visibility',
      data
    );
    return response.data;
  }
}

export const userSettingsService = new UserSettingsServiceImpl();
