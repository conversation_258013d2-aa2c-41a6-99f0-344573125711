import axios from '@/lib/axios';

/**
 * Represents a nomination received by a user
 */
export interface UserNomination {
  id: number;
  nominatorName: string;
  nominatorRole: string;
  date: string;
  content: string;
  sport?: string;
  schoolName?: string;
}

class NominationService {
  /**
   * Get nominations for a specific user
   * @param userId The ID of the user to get nominations for
   */
  async getUserNominations(userId: number): Promise<UserNomination[]> {
    try {
      const response = await axios.get<any>(`/api/v1/users/${userId}/nominations`);

      // Check if we have data in the expected format
      if (response.data && Array.isArray(response.data)) {
        return response.data;
      }

      // If the response has a data property that contains the nominations array
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        return response.data.data;
      }

      console.error('Unexpected nominations response format:', response.data);
      return [];
    } catch (error) {
      console.error('Error fetching user nominations:', error);
      return []; // Return empty array on error
    }
  }
}

export const nominationService = new NominationService();
