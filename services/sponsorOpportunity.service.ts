import type { AxiosResponse } from 'axios';
import axios from '../lib/axios';

// --- Enums ---
export enum OpportunityStatus {
  LISTED = 'listed',
  UNLISTED = 'unlisted',
}

// --- Request Payloads ---

export interface CreateOpportunityPayload {
  title: string;
  description: string;
  details?: string;
  qualifications?: string;
  responsibilities?: string;
  benefits?: string;
  type: string;
  location_type: string;
  term: string;
  location?: string | null; // UUID only, explicitly nullable
  location_display?: string; // City, State format
  city?: string;
  state_code?: string;
  industries?: number[];
  status?: OpportunityStatus;
  isFeatured?: boolean;
  visibleStartDate?: string | null; // Date field for visibility start
  visibleEndDate?: string | null; // Date field for visibility end
  preferredGraduationYearStart?: number | null; // Graduation year start for visibility filtering
  preferredGraduationYearEnd?: number | null; // Graduation year end for visibility filtering
  preferredStates?: string[] | null; // Preferred states for visibility filtering
}

export interface UpdateOpportunityPayload extends Partial<CreateOpportunityPayload> {
  // Update can be partial, allowing only changed fields
  // location can be string, null, or undefined
  location?: string | null;
  // Visibility dates can be updated independently
  visibleStartDate?: string | null;
  visibleEndDate?: string | null;
  // Industry IDs as an array of numbers
  industryIds?: number[];
  // Visibility filter fields
  preferredGraduationYearStart?: number | null;
  preferredGraduationYearEnd?: number | null;
  preferredStates?: string[] | null;
}

export interface GetOpportunitiesParams {
  search?: string;
  status?: OpportunityStatus;
  industry?: number[] | string[];
  per_page?: number;
}

// --- Response Types ---

export interface Industry {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
  pivot?: {
    opportunity_id: number;
    industry_id: number;
    created_at: string;
    updated_at: string;
  };
}

export interface OpportunityData {
  id: number;
  title: string;
  description: string;
  details?: string;
  qualifications?: string;
  responsibilities?: string;
  benefits?: string;
  type: string;
  status: OpportunityStatus; // Using enum instead of string
  location_type: string;
  term: string;
  location?: string;
  location_display?: string;
  city?: string;
  state_code?: string;
  state_name?: string;
  isFeatured: boolean;
  organizationId: number;
  organizationName?: string;
  organizationLogo?: string;
  organizationWebsite?: string;
  organizationAbout?: string;
  industries: Industry[];
  visibleStartDate?: string; // Use camelCase consistently instead of snake_case
  visibleEndDate?: string; // Use camelCase consistently instead of snake_case
  preferredGraduationYearStart?: number; // Graduation year start
  preferredGraduationYearEnd?: number; // Graduation year end
  preferredStates?: string[]; // Preferred states
  created_at: string;
  updated_at: string;
  userId?: number;
}

export interface ApiErrorResponse {
  error: string;
  errors?: Record<string, string[]>;
}

class SponsorOpportunityService {
  /**
   * Get opportunities for the logged-in sponsor
   */
  getOpportunities = async (
    params: GetOpportunitiesParams = {}
  ): Promise<AxiosResponse<OpportunityData[]>> => {
    return axios.get('/api/v1/sponsor/opportunities', { params });
  };

  /**
   * Get a specific opportunity by ID
   */
  getOpportunity = async (id: number): Promise<AxiosResponse<OpportunityData>> => {
    return axios.get(`/api/v1/sponsor/opportunities/${id}`);
  };

  /**
   * Create a new opportunity
   */
  createOpportunity = async (
    data: CreateOpportunityPayload
  ): Promise<AxiosResponse<OpportunityData>> => {
    console.log('Creating opportunity with data:', data);
    return axios.post('/api/v1/sponsor/opportunities', data);
  };

  /**
   * Update an existing opportunity
   *
   * Using POST with _method=PUT for Laravel compatibility
   */
  updateOpportunity = async (
    id: number,
    data: UpdateOpportunityPayload
  ): Promise<AxiosResponse<OpportunityData>> => {
    // First, log the incoming data to see exactly what we're receiving
    // STORE INDUSTRIES DATA BEFORE ANY TRANSFORMATION
    // Make a safe copy of the industryIds early to prevent loss during transformation
    const safeIndustryIds = Array.isArray(data.industryIds)
      ? [...data.industryIds].map(id => (typeof id === 'string' ? parseInt(id, 10) : id))
      : Array.isArray(data.industries)
        ? [...data.industries].map(id => {
            if (typeof id === 'object' && id !== null && 'id' in id) {
              return (id as { id: number }).id;
            }
            return typeof id === 'string' ? parseInt(id, 10) : id;
          })
        : [];

    // Create the payload for the update request - EXPLICITLY preserve industryIds
    const payload: Record<string, any> = {
      ...data,
      _method: 'PUT',
      // Explicitly set industryIds using our safe copy
      industryIds: safeIndustryIds.length > 0 ? safeIndustryIds : [],
    };

    // Handle the location field properly:
    // 1. If location is null, keep it null to clear the field
    // 2. If location is a string, use that value
    if (data.location === null) {
      payload.location = null;
    } else if (data.location === '') {
      // Handle empty string as null for consistency
      payload.location = null;
    }

    // Final safety check - force industryIds to use our safe copy
    if (
      !payload.industryIds ||
      !Array.isArray(payload.industryIds) ||
      payload.industryIds.length === 0
    ) {
      payload.industryIds = safeIndustryIds;
    }

    // Use POST with _method=PUT instead of PUT for Laravel compatibility
    return axios.post(`/api/v1/sponsor/opportunities/${id}`, payload);
  };

  /**
   * Delete an opportunity
   */
  deleteOpportunity = async (id: number): Promise<AxiosResponse<void>> => {
    return axios.delete(`/api/v1/sponsor/opportunities/${id}`);
  };

  /**
   * Duplicate an opportunity
   */
  duplicateOpportunity = async (id: number): Promise<AxiosResponse<OpportunityData>> => {
    return axios.post(`/api/v1/sponsor/opportunities/${id}/duplicate`);
  };

  /**
   * Toggle an opportunity's active status
   *
   * Using POST with _method=PATCH for Laravel compatibility
   */
  toggleOpportunityStatus = async (id: number): Promise<AxiosResponse<OpportunityData>> => {
    // Use POST with _method=PATCH instead of POST for Laravel compatibility
    return axios.post(`/api/v1/sponsor/opportunities/${id}/toggle-status`, {
      _method: 'PATCH',
    });
  };
}

export const sponsorOpportunityService = new SponsorOpportunityService();
