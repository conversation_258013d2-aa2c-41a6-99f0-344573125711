<div class="space-y-6">
    <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Preview</h3>
        <p class="text-xs text-gray-500 dark:text-gray-400 mb-4">This preview shows how the email will look with sample data.</p>
        
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
            <!-- Email Header -->
            <div class="bg-gray-100 dark:bg-gray-700 px-4 py-2 border-b border-gray-200 dark:border-gray-600">
                <div class="text-sm">
                    <span class="font-medium text-gray-700 dark:text-gray-300">Subject:</span>
                    <span class="text-gray-900 dark:text-gray-100">{{ $subject }}</span>
                </div>
                @if($previewText)
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        <span class="font-medium">Preview:</span>
                        {{ $previewText }}
                    </div>
                @endif
            </div>
            
            <!-- Email Body -->
            <div class="p-6">
                <div class="prose prose-sm max-w-none dark:prose-invert">
                    {!! $body !!}
                </div>
            </div>
        </div>
    </div>
    
    @if(!empty($variables))
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">Variables Used in This Template</h4>
            <div class="flex flex-wrap gap-1">
                @foreach($variables as $variable)
                    <code class="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">{{ '{' . $variable . '}' }}</code>
                @endforeach
            </div>
            <p class="text-xs text-blue-600 dark:text-blue-300 mt-2">
                These variables will be replaced with actual data when the email is sent.
            </p>
        </div>
    @endif
</div>
