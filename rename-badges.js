// Script to rename badge image files to a more consistent format
const fs = require('fs');
const path = require('path');

// Path to the badges directory
const badgesDir = path.join(__dirname, 'public', 'images', 'badges');

// Function to create a consistent filename
const createNewFilename = oldName => {
  // Extract the badge number and name
  const match = oldName.match(/^(\d+)\s+(.+?)\s+-\s+(\w+)(@1-1080x1080\.png)/);

  if (!match) {
    // Handle Certificate or other special cases
    if (oldName.includes('Certificate')) {
      return 'certificate.png';
    }
    console.log(`Could not parse: ${oldName}`);
    return oldName;
  }

  const [, number, name, status, suffix] = match;

  // Create new name with underscores
  // Format: 01_rookie_achieved.png, 10_champion_unachieved.png
  const paddedNumber = number.padStart(2, '0');
  const formattedName = name.toLowerCase().replace(/\s+/g, '_');
  const formattedStatus = status.toLowerCase().replace(/\s+/g, '_');

  return `${paddedNumber}_${formattedName}_${formattedStatus}.png`;
};

// Main function to rename files
const renameFiles = () => {
  try {
    // Get all files in the badges directory
    const files = fs.readdirSync(badgesDir);

    // Create a map to store old->new name mappings for reference
    const nameMap = {};

    // Process each file
    files.forEach(file => {
      const oldPath = path.join(badgesDir, file);

      // Skip directories
      if (fs.statSync(oldPath).isDirectory()) return;

      // Get new filename
      const newFilename = createNewFilename(file);
      const newPath = path.join(badgesDir, newFilename);

      // Rename the file
      fs.renameSync(oldPath, newPath);

      // Store the mapping
      nameMap[file] = newFilename;

      console.log(`Renamed: ${file} -> ${newFilename}`);
    });

    // Save the name mapping to a JSON file for reference
    fs.writeFileSync(
      path.join(__dirname, 'badge-name-mapping.json'),
      JSON.stringify(nameMap, null, 2)
    );

    console.log('All files renamed successfully!');
    console.log('Name mapping saved to badge-name-mapping.json');
  } catch (error) {
    console.error('Error renaming files:', error);
  }
};

// Run the script
renameFiles();
