// Script to update all sport components to use the BaseSportCard
const fs = require('fs');
const path = require('path');

// Path to the sports components directory
const sportsDir = path.join(__dirname, 'components', 'shared', 'spline', 'sports');

// Template for the updated component
const getUpdatedComponentContent = (sportName, scene) => `'use client';

import BaseSportCard from './BaseSportCard';
import type { SportsProps } from './types';

export default function Sports${sportName}Card(props: SportsProps) {
  return (
    <BaseSportCard 
      sportName="${sportName}"
      scene="${scene}" 
      {...props} 
    />
  );
}
`;

// Main function to update components
const updateComponents = () => {
  try {
    // Get all files in the sports directory
    const files = fs.readdirSync(sportsDir);

    // Process each Card file
    files.forEach(file => {
      // Only process card components
      if (!file.includes('Card.tsx') || file === 'BaseSportCard.tsx') return;

      const filePath = path.join(sportsDir, file);
      const content = fs.readFileSync(filePath, 'utf8');

      // Extract the sport name from the file name
      const sportName = file.replace('Sports', '').replace('Card.tsx', '');

      // Extract the scene URL from the content
      const sceneMatch = content.match(/scene="([^"]+)"/);
      if (!sceneMatch) {
        console.log(`Could not extract scene URL from ${file}`);
        return;
      }

      const scene = sceneMatch[1];

      // Create the updated content
      const updatedContent = getUpdatedComponentContent(sportName, scene);

      // Write the updated content back to the file
      fs.writeFileSync(filePath, updatedContent);

      console.log(`Updated: ${file}`);
    });

    console.log('All sport components updated successfully!');
  } catch (error) {
    console.error('Error updating components:', error);
  }
};

// Run the script
updateComponents();
